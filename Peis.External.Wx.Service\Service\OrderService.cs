﻿using MediatR;
using Newtonsoft.Json;
using Peis.External.Wx.Service.Model.DTO;
using Peis.External.Wx.Service.Model.FromBody;
using Peis.Model.DataBaseEntity;
using Peis.Model.DataBaseEntity.External;
using Peis.Model.Other.PeEnum;
using Peis.Repository.IRepository;
using Peis.Repository.IRepository.Helper;
using Peis.Service.IService;
using Peis.Service.IService.Helper;
using Peis.Service.Service.MediatR;
using Peis.Utility.Helper;
using SqlSugar;
using System.Buffers;

namespace Peis.External.Wx.Service.Service;

/// <summary>
/// 微信接口
/// </summary>
public class OrderService : IOrderService
{
    private readonly INoGeneration _noGeneration;
    private readonly IDataTranRepository _dataTranRepository;
    private readonly IClusterCombRepository _clusterCombRepository;
    private readonly IRegisterRepository _registerRepository;
    private readonly IRecordRepository _recordRepository;
    private readonly IReportConclusionRepository _reportConclusionRepository;
    private readonly IFeeRepository _feeRepository;
    private readonly ICompanyRepository _companyRepository;
    private readonly IDataRepository<CodeCompany> _codeCompanyRepository;
    private readonly IDataRepository<PeRegister> _peRegisterRepository;
    private readonly IDataRepository<PeRegisterCluster> _peRegisterClusterRepository;
    private readonly IDataRepository<FeeSettlement> _feeSettlementRepository;
    private readonly ISplitTableRepository<PeRegisterComb> _peRegisterCombRepository;
    private readonly ISplitTableRepository<FeeSettlementComb> _feeSettlementCombRepository;
    private readonly IDataRepository<WeChatBookQuestion> _questionRepository;
    private readonly IReportFileRepository _shenShanReportRepository;
    private readonly ISystemParameterService _systemParameterService;
    private readonly IMediator _mediator;
    private readonly ISampleService _sampleService;
    private readonly ISampleRepository _sampleRepository;
    private readonly ILogBusinessNewService _logBusinessNewService;

    public OrderService(
        INoGeneration noGeneration,
        IDataTranRepository dataTranRepository,
        IClusterCombRepository clusterCombRepository,
        IRegisterRepository registerRepository,
        IRecordRepository recordRepository,
        IReportConclusionRepository reportConclusionRepository,
        IFeeRepository feeRepository,
        ICompanyRepository companyRepository,
        IDataRepository<CodeCompany> codeCompanyRepository,
        IDataRepository<PeRegister> peRegisterRepository,
        IDataRepository<PeRegisterCluster> peRegisterClusterRepository,
        IDataRepository<FeeSettlement> feeSettlementRepository,
        ISplitTableRepository<PeRegisterComb> peRegisterCombRepository,
        ISplitTableRepository<FeeSettlementComb> feeSettlementCombRepository,
        IDataRepository<WeChatBookQuestion> questionRepository,
        IReportFileRepository shenShanReportRepository,
        ISystemParameterService systemParameterService,
        IMediator mediator,
        ISampleService sampleService,
        ISampleRepository sampleRepository,
        ILogBusinessNewService logBusinessNewService)
    {
        _noGeneration = noGeneration;
        _dataTranRepository = dataTranRepository;
        _clusterCombRepository = clusterCombRepository;
        _registerRepository = registerRepository;
        _recordRepository = recordRepository;
        _reportConclusionRepository = reportConclusionRepository;
        _feeRepository = feeRepository;
        _companyRepository = companyRepository;
        _codeCompanyRepository = codeCompanyRepository;
        _peRegisterRepository = peRegisterRepository;
        _peRegisterClusterRepository = peRegisterClusterRepository;
        _feeSettlementRepository = feeSettlementRepository;
        _peRegisterCombRepository = peRegisterCombRepository;
        _feeSettlementCombRepository = feeSettlementCombRepository;
        _questionRepository = questionRepository;
        _shenShanReportRepository = shenShanReportRepository;
        _systemParameterService = systemParameterService;
        _mediator = mediator;
        _sampleService = sampleService;
        _sampleRepository = sampleRepository;
        _logBusinessNewService = logBusinessNewService;
    }

    /// <summary>
    /// 获取个人套餐
    /// </summary>
    /// <param name="data"></param>
    /// <param name="personalPackages">返回数据</param>
    /// <param name="msg">信息</param>
    /// <returns></returns>
    public bool GetPersonalPackages(ParamData data, ref PersonalPackage[] personalPackages, ref string msg)
    {
        if (!Enum.IsDefined(typeof(PeCls), data.PeCls))
        {
            msg = "数据传输有误";
            return false;
        }

        personalPackages = _clusterCombRepository.ReadEnabledCluster()
            .Where(x => x.WechatShow && x.PeCls == data.PeCls)
            .Select(x => new PersonalPackage
            {
                ClusCode = x.ClusCode,
                ClusName = x.ClusName,
                Note = x.Note,
                Price = x.Price,
                sex = x.Sex
            }).ToArray();

        return true;
    }

    /// <summary>
    /// 根据套餐编码获取套餐的组合
    /// </summary>
    /// <param name="data"></param>
    /// <param name="clusCombs">套餐包含的组合</param>
    /// <param name="msg">信息</param>
    /// <returns></returns>
    public bool GetCombsByClusCode(ParamData data, ref ClusterCombs[] clusCombs, ref string msg)
    {
        if (string.IsNullOrEmpty(data.ClusCode))
        {
            msg = "数据传输有误";
            return false;
        }

        // 个人/团体获取套餐明细
        if (data.IsCompanyCheck == true)
        {
            clusCombs = _companyRepository.ReadCompanyClusterBindComb(data.ClusCode, false)
                .Select((clusComb, comb) => new ClusterCombs
                {
                    CombCode = comb.CombCode,
                    CombName = comb.CombName,
                    Note = comb.Note,
                    SortIndex = comb.SortIndex,
                }).ToArray();
        }
        else
        {
            clusCombs = _clusterCombRepository.ReadClusterBindComb(false)
                .Where(clusComb => clusComb.ClusCode == data.ClusCode)
                .OrderBy((clusComb, comb, cls) => cls.SortIndex)
                .OrderBy((clusComb, comb, cls) => comb.SortIndex)
                .Select((clusComb, comb) => new ClusterCombs
                {
                    CombCode = comb.CombCode,
                    CombName = comb.CombName,
                    Note = comb.Note,
                    SortIndex = comb.SortIndex,
                }).ToArray();
        }

        return true;
    }

    /// <summary>
    /// 获取所有项目分类下的组合
    /// </summary>
    /// <returns></returns>
    public ItemClsComb[] GetItemClsComb()
    {
        var query = _clusterCombRepository.ReadItemClsComb()
            .Where((cls, comb) => comb.Price > 0)
            .Select((cls, comb) => new
            {
                cls.ClsCode,
                cls.ClsName,
                comb.CombCode,
                comb.CombName,
                comb.Note,
                comb.Price
            })
            .ToList();

        return query.GroupBy(x => new { x.ClsCode, x.ClsName })
               .Select(x => new ItemClsComb
               {
                   ClsCode = x.Key.ClsCode,
                   ClsName = x.Key.ClsName,
                   Children = x.ToList().Select(y => new Children
                   {
                       CombCode = y.CombCode,
                       CombName = y.CombName,
                       Note = y.Note,
                       Price = y.Price
                   }).ToArray()
               }).ToArray();
    }

    #region 团体
    /// <summary>
    /// 团体登录
    /// </summary>
    /// <param name="basicInfo">基本信息</param>
    /// <param name="teamInfo">返回数据</param>
    /// <param name="msg">信息</param>
    /// <returns></returns>
    public bool TeamLogin(BasicInfo basicInfo, ref TeamInfo[] teamInfo, ref string msg)
    {
        if (string.IsNullOrEmpty(basicInfo.CompanyCode) ||
            string.IsNullOrEmpty(basicInfo.CardNo) ||
            string.IsNullOrEmpty(basicInfo.Tel))
        {
            msg = "数据传输有误";
            return false;
        }

        //团体要有套餐且预约时间为空才能预约
        teamInfo = _registerRepository.ReadCompanyRegData()
                .Where(reg => reg.CompanyCode == basicInfo.CompanyCode && reg.CardNo == basicInfo.CardNo && reg.Tel == basicInfo.Tel)
                //.Where(reg => reg.BookType == BookType.团体导入 && !reg.IsActive)
                .Where(reg => !reg.IsActive && reg.BookBeginTime == default && reg.BookEndTime == default)
                .OrderByDescending(reg => reg.RegisterTime)
                .Select((reg, clus, comp) => new TeamInfo
                {
                    Name = reg.Name,
                    CardNo = reg.CardNo,
                    Tel = reg.Tel,
                    RegNo = reg.RegNo,
                    ClusCode = clus.ClusCode,
                    ClusName = clus.ClusName,
                    CompanyCode = comp.CompanyCode,
                    CompanyName = comp.CompanyName
                }).ToArray();

        if (teamInfo.Length == 0)
        {
            msg = "暂无匹配的数据!";
            return false;
        }

        return true;
    }

    /// <summary>
    /// 获取所有单位数据
    /// </summary>
    /// <returns></returns>
    public TjLnc[] GetCompanyList()
    {
        return _codeCompanyRepository.FindAll()
               .Select(x => new TjLnc
               {
                   lnc_Code = x.CompanyCode,
                   lnc_Name = x.CompanyName,
                   lnc_State = false,
                   HospCode = x.HospCode
               }).ToArray();
    }
    #endregion

    #region 同步订单

    /// <summary>
    /// 个人订单数据同步
    /// </summary>
    /// <param name="order"></param>
    /// <param name="regNo"></param>
    /// <param name="msg"></param>
    /// <returns></returns>
    public bool SyncPersonOrder(SyncPersonOrder order, ref string regNo, ref string msg)
    {
        var newRegInfo = CreatePeRegister(order, ref msg);
        if (newRegInfo == null)
            return false;

        var newRegClusters = CreatePeRegisterCluster(newRegInfo.RegNo, order.ClusCode, ref msg);
        if (newRegClusters.Length == 0)
            return false;

        var newRegCombs = CreatePeRegisterComb(newRegInfo, order.ClusCode, order.OperatorName);
        if (newRegCombs.Length == 0)
        {
            msg = $"{newRegInfo.RegNo}套餐包含的组合为空,请核对!";
            return false;
        }

        var question = CreateWeChatBookQuestion(newRegInfo.RegNo, order);

        _dataTranRepository.ExecTran(() =>
        {
            //登记信息 插入
            _peRegisterRepository.Insert(newRegInfo);

            //登记套餐 插入
            if (newRegClusters.Length > 0)
                _peRegisterClusterRepository.Insert(newRegClusters);

            //登记组合 插入
            _peRegisterCombRepository.SplitTableInsert(newRegCombs);

            //登记组合生成试管材料
            InsertTestTubeToRegisterComb(newRegInfo);
            //生成采血费用
            InsertVenousBloodComb(newRegInfo.RegNo, !newRegInfo.IsCompanyCheck);
            //样本绑定条码
            _sampleService.SyncSampleBindBarCodeAll(newRegInfo.RegNo);
            //问卷记录表
            _questionRepository.Insert(question);
            _logBusinessNewService.RegisterNewLog(newRegInfo.RegNo, "微信预约");
            _logBusinessNewService.SaveLogs();
        });

        _mediator.Publish(new SyncPeRegisterOrderHandle.Data(new string[] { newRegInfo.RegNo }));
        //登记组合生成试管材料
        //InsertTestTubeToRegisterComb(newRegInfo.RegNo);            

        regNo = newRegInfo.RegNo;
        return true;
    }

    /// <summary>
    /// 删除个人记录
    /// </summary>
    /// <param name="regNo"></param>
    /// <returns></returns>
    public bool DeletePersonRecord(string regNo)
    {
        //如果有缴费记录，则需要先删除支付数据
        var feeSettle = _feeSettlementRepository.First(x => x.RegNo == regNo);
        if (feeSettle != null)
        {
            var settleCombs = _feeRepository.ReadSettlementCombs(feeSettle.SettlementNo).ToArray();
            _dataTranRepository.ExecTran(() =>
            {
                _feeSettlementCombRepository.SplitTableDelete(settleCombs);
                _feeSettlementRepository.Delete(feeSettle);
            });
        }

        //查出需要删除的组合
        var delCombs = _registerRepository.ReadRegisterCombs(regNo).ToArray();
        _dataTranRepository.ExecTran(() =>
        {
            //登记信息
            _peRegisterRepository.Delete(x => x.RegNo == regNo);
            //套餐信息
            _peRegisterClusterRepository.Delete(x => x.RegNo == regNo);
            //登记组合 插入
            if (delCombs.Length > 0)
                _peRegisterCombRepository.SplitTableDelete(delCombs);
        });

        return true;
    }

    /// <summary>
    /// 插入缴费明细
    /// </summary>
    /// <param name="data"></param>
    /// <param name="msg"></param>
    /// <returns></returns>
    public bool AddPayInfo(ParamData data, ref string msg)
    {
        //查询体检号有无结算记录
        if (_feeRepository.ReadSettlement().Any(x => x.RegNo == data.RegNo))
        {
            msg = "该体检号已有结算记录!";
            return false;
        }

        //查出需要缴费的组合
        var regCombs = _registerRepository.ReadRegisterCombs(data.RegNo, PayStatus.未收费).ToArray();
        if (regCombs.Length <= 0)
        {
            msg = "未找到未收费的项目或已缴费过!";
            return false;
        }

        var dictComb = _clusterCombRepository.ReadComb(regCombs.Select(x => x.CombCode).ToArray())
            .Select(x => new { x.CombCode, x.FeeBasicCls }).ToArray()
            .ToDictionary(x => x.CombCode);

        //生成结算记录
        var fSettile = new FeeSettlement()
        {
            SettlementNo = _noGeneration.NextSettlementNo()[0],
            RegNo = data.RegNo,
            Price = regCombs.Sum(x => x.Price),
            OperatorCode = "WX",
            SettlementTime = DateTime.Now,
            SettlementType = SettlementType.收费,
            Refunded = false,
            HasEndDaySettlement = false,
            HospCode = data.HospCode
        };

        //以登记组合生成结算组合明细
        var fSettleComb = regCombs
            .Select(x => new FeeSettlementComb
            {
                Id = _noGeneration.NextSnowflakeId(),
                SettlementNo = fSettile.SettlementNo,
                RegCombId = x.Id,
                CombCode = x.CombCode,
                CombName = x.CombName,
                OriginalPrice = x.OriginalPrice,
                Price = x.Price,
                Discount = x.Discount,
                FeeCls = dictComb[x.CombCode].FeeBasicCls,
                SettlementTime = (DateTime)fSettile.SettlementTime
            }).ToArray();

        //修改登记组合支付状态
        foreach (var regComb in regCombs)
            regComb.PayStatus = PayStatus.收费;

        _dataTranRepository.ExecTran(() =>
        {
            _feeSettlementCombRepository.SplitTableInsert(fSettleComb);
            _feeSettlementRepository.Insert(fSettile);
            _registerRepository.UpdateColumnsRegisterComb(regCombs, x => x.PayStatus);
        });

        _mediator.Publish(new SyncPeRegisterOrderHandle.Data(new string[] { fSettile.RegNo }));

        return true;
    }

    /// <summary>
    /// 团体订单同步
    /// </summary>
    /// <param name="order"></param>
    /// <param name="msg"></param>
    /// <returns></returns>
    public bool SyncTeamOrder(SyncTeamOrder order, ref string msg)
    {
        //查询是否存在档案号 如果有则返回最新的那次档案号
        var regInfo = _registerRepository.ReadRegister()
                        .First(x => x.RegNo == order.RegNo);
        if (regInfo == null)
        {
            msg = "订单数据不存在";
            return false;
        }

        regInfo.BookType = BookType.微信预约;
        regInfo.BookNo = order.BookNo;
        regInfo.BookBeginTime = order.BeginTime;
        regInfo.BookEndTime = order.BeginTime;

        //问卷
        var question = new WeChatBookQuestion
        {
            RegNo = order.RegNo,
            FamilyMedicalHistory = order.FamilyMedicalHistory ?? "",
            PastMedicalHistory = order.PastMedicalHistory ?? "",
            OperationStatus = order.OperationStatus ?? "",
            SmokingHabit = order.SmokingHabit ?? "",
            DrinkingHabit = order.DrinkingHabit ?? "",
            LivingHabit = order.LivingHabit ?? "",
            CurrentCondition = order.CurrentCondition ?? "",
            QuestionnaireAnswer = order.QuestionnaireAnswer ?? ""
        };

        _dataTranRepository.ExecTran(() =>
        {
            _peRegisterRepository.Update(regInfo);

            //问卷记录表
            _questionRepository.Insert(question);
        });

        return true;
    }

    /// <summary>
    /// 删除个人记录
    /// </summary>
    /// <param name="regNo"></param>
    /// <returns></returns>
    public bool DeleteTeamRecord(string regNo)
    {
        //查询是否存在档案号 如果有则返回最新的那次档案号
        var regInfo = _registerRepository.ReadRegister()
                        .First(x => x.RegNo == regNo);
        if (regInfo == null)
            return false;

        regInfo.BookType = BookType.团体导入; // 默认是团体导入
        regInfo.BookNo = string.Empty;
        regInfo.BookBeginTime = null;
        regInfo.BookEndTime = null;

        _peRegisterRepository.Update(regInfo);
        return true;
    }

    /// <summary>
    /// 补充问卷调查
    /// </summary>
    /// <param name="question"></param>
    /// <param name="msg"></param>
    /// <returns></returns>
    public bool RegisterQuestionSurvey(WeChatBookQuestion question, ref string msg)
    {
        if (!_peRegisterRepository.Any(x => x.RegNo == question.RegNo))
        {
            msg = $"未查询到体检号:{question.RegNo}的记录";
            return false;
        }

        if (_questionRepository.Any(x => x.RegNo == question.RegNo))
        {
            msg = "已填写过问卷,无需重复填写";
            return false;
        }

        //问卷记录表
        _questionRepository.Insert(question);
        msg = "问卷填写成功";
        return true;
    }
    #endregion

    #region 报告

    /// <summary>
    /// 获取报告列表
    /// </summary>
    /// <param name="bsInfo">入参</param>
    /// <param name="reports">返回数据</param>
    /// <param name="msg">信息</param>
    /// <returns></returns>
    public bool GetReportList(BasicInfo bsInfo, ref Report[] reports, ref string msg)
    {
        if (string.IsNullOrEmpty(bsInfo.Name) ||
           string.IsNullOrEmpty(bsInfo.CardNo) ||
           string.IsNullOrEmpty(bsInfo.Tel))
        {
            msg = "数据传输有误";
            return false;
        }

        //获取个人信息
        var regNos = _registerRepository.ReadRegister()
                    .Where(x => x.Name == bsInfo.Name && x.CardNo == bsInfo.CardNo && x.Tel == bsInfo.Tel)
                    .Select(x => x.RegNo).ToArray();

        if (regNos.Length == 0)
        {
            msg = "未查询到个人信息";
            return false;
        }

        //获取报告列表
        //var peStatusArray = new PeStatus[] { PeStatus.已总检, PeStatus.已审核 };//允许查出数据的体检状态
        reports = _reportConclusionRepository.GetReportConclusion(regNos)
                     //.Where(reg => SqlFunc.ContainsArray(peStatusArray, reg.PeStatus))
                     .Where(reg => reg.PeStatus == PeStatus.已审核)
                     .Select((reg, report) => new Report
                     {
                         report_No = reg.RegNo,
                         report_Name = reg.Name,
                         report_Sex = reg.Sex.ToString(),
                         report_Age = reg.Age.ToString(),
                         report_Date = report.AuditTime.Value.ToString("yyyy.MM.dd"),
                         report_Doctor = report.AuditDoctorName
                     }).ToArray();

        return true;
    }

    /// <summary>
    /// 获取报告详情
    /// </summary>
    /// <param name="regNo">体检号</param>
    /// <param name="detail">报告详情</param>
    /// <param name="msg">返回信息</param>
    /// <returns></returns>
    public bool GetReportDetail(string regNo, ref ReportDetail detail, ref string msg)
    {
        //个人信息以及体检报告结论
        //var peStatusArray = new PeStatus[] { PeStatus.已总检, PeStatus.已审核 };//允许查出数据的体检状态
        detail = _reportConclusionRepository.GetReportConclusion(new string[] { regNo })
            .Select((reg, report) => new ReportDetail
            {
                name = reg.Name,
                sex = reg.Sex,
                age = reg.Age,
                regno = reg.RegNo,
                lnc_name = reg.Name,
                reg_date = reg.ActiveTime.Value.ToString("yyyy.MM.dd"),
                con_date = report.AuditTime.Value.ToString("yyyy.MM.dd"),
                doc_name = report.CheckDoctorName,
                sumup = report.Summary,
                sugg_tag = report.Suggestion,
                conclusion = report.Conclusion
            }).First();

        //获取组合及项目结果
        var query = _recordRepository.ReadCombAndItemData(detail.regno)
            .Select((recComb, recItem, comb, item) => new
            {
                item.ItemName,
                comb.CheckCls,
                comb.CombName,
                recComb.CombCode,
                recComb.CombResult,
                recComb.DoctorName,
                recItem.ItemCode,
                recItem.ItemResult,
                recItem.Unit,
                recItem.LowerLimit,
                recItem.UpperLimit,
                recItem.Hint
            }).ToList();

        var peRecord = query.GroupBy(x => new { x.CombCode, x.CombName, x.CombResult, x.CheckCls, x.DoctorName })
            .Select(x => new PeRecord
            {
                comb_code = x.Key.CombCode,
                comb_name = x.Key.CombName,
                res_tag = x.Key.CombResult,
                check_cls = x.Key.CheckCls,
                dept_doct = x.Key.DoctorName,
                detailList = x.ToList().Select(x => new ItemDetail
                {
                    item_code = x.ItemCode,
                    item_name = x.ItemName,
                    rec_result = x.ItemResult,
                    unit = x.Unit,
                    ref_lower = x.LowerLimit,
                    ref_upper = x.UpperLimit,
                    hint = x.Hint
                }).ToList()
            }).ToList();

        if (peRecord.Count < 0)
        {
            msg = "未查询到体检数据!";
            return false;
        }

        detail.itemlist = peRecord;
        return true;
    }

    /// <summary>
    /// 获取报告Pdf地址
    /// </summary>
    /// <param name="regNo">体检号</param>
    /// <param name="url">地址</param>
    /// <returns></returns>
    public bool GetReportPdfUrl(string regNo, ref string url)
    {
        var defReportType = _systemParameterService.DefaultExamReportWx;
        url = _shenShanReportRepository.QueryPeReportFiles(regNo)
            .Where(a => a.ReportType.Equals(defReportType))
            .Where(a => a.IsWithStamp)
            .First()?.FilePath ?? "";
        if (url.IsNullOrEmpty())
        {
            url = _shenShanReportRepository.QueryPeReportFiles(regNo)
            .First()?.FilePath ?? "";
        }

        return true;
    }

    /// <summary>
    /// 获取报告影像图文地址
    /// </summary>
    /// <param name="regNo">体检号</param>
    /// <param name="respData">地址</param>
    /// <returns></returns>
    public bool GetGraphicTextUrl(string regNo, ref string respData)
    {
        var data = _shenShanReportRepository.QueryPeRecordPartFiles(regNo)
                   .Select(x => new
                   {
                       comb_name = SqlFunc.Subqueryable<CodeItemComb>().Where(y => y.CombCode == x.CombCode).Select(y => y.CombName),
                       REPORT_URL = x.FilePath
                   }).ToArray();

        respData = JsonConvert.SerializeObject(data);
        return true;
    }
    #endregion

    #region 本地方法
    /// <summary>
    /// 创建登记信息
    /// </summary>
    /// <param name="order"></param>
    /// <param name="msg"></param>
    /// <returns></returns>
    public PeRegister CreatePeRegister(SyncPersonOrder order, ref string msg)
    {
        var patCode = string.Empty;// 档案号

        //查询是否存在档案号 如果有则返回最新的那次档案号
        var regInfo = _registerRepository.ReadRegister()
            .Where(x => x.CardNo == order.CardNo && x.Name == order.Name)
            .OrderByDescending(x => x.RegisterTime).First();

        if (regInfo == null)
        {
            patCode = _noGeneration.NextPatCode()[0];
        }
        else
        {
            patCode = regInfo.PatCode;
            var peStatuses = new PeStatus[] { PeStatus.未检查, PeStatus.正在检查 };

            //检查该体检类型是否存在已激活未总检的记录
            bool isExis = _registerRepository.ReadRegister()
                .Any(x => x.PatCode == patCode && x.PeCls == order.PeCls && x.IsActive && SqlFunc.ContainsArray(peStatuses, x.PeStatus));

            if (isExis)
            {
                msg = "存在已激活且未完成的体检记录";
                return null;
            }
        }
        //获取指引单格式
        var cluster = _clusterCombRepository.ReadCluster(order.ClusCode).First();
        return new PeRegister()
        {
            RegNo = _noGeneration.NextRegNo()[0],
            PatCode = patCode,
            Name = order.Name,
            Sex = order.Sex,
            Age = order.Age,
            AgeUnit = AgeUnit.岁,
            Birthday = DateTime.Parse(order.Birthday),
            CardType = ((int)order.CardType).ToString(),
            CardNo = order.CardNo,
            NativePlace = string.Empty,
            Address = string.Empty,
            Tel = order.Tel,
            RegisterTimes = regInfo == null ? 1 : regInfo.RegisterTimes + 1,
            RegisterTime = DateTime.Parse(order.BeginTime),
            IsActive = false,
            PeCls = order.PeCls,
            PeStatus = PeStatus.未检查,
            ReportType = cluster.ReportType,
            GuidanceType = cluster.GuidanceType,
            GuidancePrinted = false,
            GuidanceRecycler = string.Empty,
            ReportPrinted = false,
            IsCompanyCheck = order.IsCompanyCheck,
            BookType = BookType.微信预约,
            BookNo = order.BookNo,
            BookBeginTime = DateTime.Parse(order.BeginTime),
            BookEndTime = DateTime.Parse(order.BeginTime),
            IsVIP = false,
            IsLeader = false,
            IsConstitution = false,
            HealthUploaded = false,
            OutsideUploaded = false,
            OperatorCode = order.OperatorCode,
            Introducer = order.OperatorCode,
            HospCode = order.HospCode,
            Note = "",
            ChargeModel = ChargeModel.按组合收费,
            IsOccupation = false,
            IsOrdinary = true,
            PayStatus = PaymentStatus.Init
            //NativePlace       = patient.NativePlace,
            //Address           = patient.Address,
            //MarryStatus       = patient.MarryStatus,
            //PhotoUrl          = patient.PhotoUrl,
            //ActiveTime        = patient.IsActive ? DateTime.Now : null,
            //ReportType        = patient.ReportType,
            //CardType          = patient.CardType,
            //CompanyCode       = patient.CompanyCode,
            //CompanyTimes      = patient.CompanyTimes,
            //CompanyDeptCode   = patient.CompanyDeptCode,
            //JobStatus         = patient.JobStatus,
            //JobCode           = patient.JobCode,
            //JobHistory        = patient.JobHistory,
            //MedicalHistory    = patient.MedicalHistory,
            //RecheckNo         = patient.RecheckNo,
            //FamilyMedicalHistory = patient.FamilyMedicalHistory,
            //GuidancePrintTime    = null,
            //GuidanceRecyclyTime  = null,
            //BookBeginTime        = null,
            //BookEndTime          = null,
        };
    }

    /// <summary>
    /// 创建登记套餐信息
    /// </summary>
    /// <param name="regNo">体检号</param>
    /// <param name="clusCode">套餐编码</param>
    /// <param name="msg"></param>
    /// <returns></returns>
    public PeRegisterCluster[] CreatePeRegisterCluster(string regNo, string clusCode, ref string msg)
    {
        //查询套餐
        var clusters = _clusterCombRepository.ReadCluster(clusCode).ToList();
        if (clusters.Count == 0)
        {
            msg = $"{regNo}未查询到套餐编码{clusCode}的套餐";
            return Array.Empty<PeRegisterCluster>();
        }

        return clusters.Select(clus => new PeRegisterCluster
        {
            RegNo = regNo,
            ClusCode = clus.ClusCode,
            ClusName = clus.ClusName,
            Price = clus.Price,
            IsMain = true
        }).ToArray();
    }

    /// <summary>
    /// 创建登记组合信息（除试管外，试管另单独处理）
    /// </summary>
    /// <param name="newRegInfo"></param>
    /// <param name="clusCode"></param>
    /// <param name="operatorName"></param>
    /// <returns></returns>
    public PeRegisterComb[] CreatePeRegisterComb(PeRegister newRegInfo, string clusCode, string operatorName)
    {
        //通过套餐编码获取组合
        var query = _clusterCombRepository.ReadClusterBindComb(false)
                    .Where(clusComb => clusComb.ClusCode == clusCode)
                    .Select((clusComb, comb, cls) => new
                    {
                        comb.CombCode,
                        comb.CombName,
                        comb.Sex,
                        comb.Price,
                        comb.ExamDeptCode,
                        comb.CheckCls,
                        comb.ClsCode,
                        comb.ReportShow,
                        CombSortIndex = comb.SortIndex,
                        cls.ClsName,
                        ClsSortIndex = cls.SortIndex,
                    })
                    .ToArray();

        return query.Select(comb => new PeRegisterComb
        {
            Id = _noGeneration.NextSnowflakeId(),
            RegNo = newRegInfo.RegNo,
            CombCode = comb.CombCode,
            CombName = comb.CombName,
            ExamDeptCode = comb.ExamDeptCode,
            CheckCls = comb.CheckCls,
            ClsCode = comb.ClsCode,
            ClsName = comb.ClsName ?? "",
            OriginalPrice = comb.Price,
            Price = comb.Price,
            ReportShow = comb.ReportShow,
            Surcharges = 0.0M,
            Discount = 1,
            IsPayBySelf = !newRegInfo.IsCompanyCheck, //个检直接自费，团体读项目自费勾选状态
            PayStatus = PayStatus.未收费,
            ApplicantCode = newRegInfo.OperatorCode,
            ApplicantName = operatorName,
            CreateTime = DateTime.Now,
            RegisterTime = newRegInfo.RegisterTime,
            CombSortIndex = comb.CombSortIndex,
            ClsSortIndex = comb.ClsSortIndex,
            IsOrdinary  = true,
            IsOccupation = false,
        }).ToArray();
    }

    /// <summary>
    /// 创建问卷记录
    /// </summary>
    /// <param name="regNo"></param>
    /// <param name="order"></param>
    /// <returns></returns>
    public WeChatBookQuestion CreateWeChatBookQuestion(string regNo, SyncPersonOrder order)
    {
        return new WeChatBookQuestion
        {
            RegNo = regNo,
            FamilyMedicalHistory = order.FamilyMedicalHistory ?? "",
            PastMedicalHistory = order.PastMedicalHistory ?? "",
            OperationStatus = order.OperationStatus ?? "",
            SmokingHabit = order.SmokingHabit ?? "",
            DrinkingHabit = order.DrinkingHabit ?? "",
            LivingHabit = order.LivingHabit ?? "",
            CurrentCondition = order.CurrentCondition ?? "",
            QuestionnaireAnswer = order.QuestionnaireAnswer ?? ""
        };
    }

    /// <summary>
    /// 插入采血收费组合
    /// </summary>
    /// <param name="regNo"></param>
    /// <param name="isPayBySelf"></param>
    private void InsertVenousBloodComb(string regNo, bool isPayBySelf)
    {
        //获取采血收费组合代码
        var combcode = _systemParameterService.VenousBloodComb;
        if (string.IsNullOrEmpty(combcode))
        {
            return;
        }
        //判断是否有采血组合，无则删除采血组合
        var regCombs = _registerRepository.ReadRegisterCombs(regNo).ToArray();
        if (!regCombs.Any(x => x.CheckCls == CheckCls.检验检查))
        {
            DeleteVenousBloodComb();
            return;
        }
        //获取组合检验试管的绑定关系：血类型，无则删除采血组合
        var combBindTestTubes = _clusterCombRepository.ReadCombBarcodeType(regCombs.Where(x => x.CheckCls == CheckCls.检验检查).Select(x => x.CombCode).ToArray())
            .Where((comb, barType) => SqlFunc.Subqueryable<CodeSample>().Where(samp => samp.SampCode == barType.SampCode && samp.SampName.Contains("血")).Any())
            .Select((comb, barType) => new
            {
                CombCode = comb.CombCode,
                TubeCombCode = barType.FeeCombCode
            }).ToArray();
        if (combBindTestTubes.Length == 0)
        {
            DeleteVenousBloodComb();
            return;
        }
        var beFrom = regCombs.Where(x => combBindTestTubes.Select(c => c.CombCode).ToArray().Contains(x.CombCode)).Select(x => x.Id.ToString()).ToArray();
        //已有采血则更新从属关系
        if (regCombs.Any(x => x.CombCode == combcode))
        {
            var venousBloodComb = regCombs.Where(x => x.CombCode == combcode).First();
            if (venousBloodComb.PayStatus == PayStatus.收费)
            {
                return;
            }
            venousBloodComb.BeFrom = beFrom;
            _peRegisterCombRepository.SplitTableUpdate(venousBloodComb);
            return;
        }
        var combInfo = _clusterCombRepository.ReadComb(combcode)
                .Select(x => new
                {
                    x.CombCode,
                    x.CombName,
                    x.ExamDeptCode,
                    x.CheckCls,
                    x.ClsCode,
                    x.Price,
                    x.ReportShow,
                    x.SortIndex,
                    ClsSortIndex = SqlFunc.Subqueryable<CodeItemCls>().Where(cls => cls.ClsCode == x.ClsCode).Select(cls => cls.SortIndex),
                })
                .First();
        if (combInfo == null) //组合不存在
            return;
        //采血组合
        var newRegComb = new PeRegisterComb
        {
            Id = _noGeneration.NextSnowflakeId(),
            RegNo = regNo,
            CombCode = combInfo.CombCode,
            CombName = combInfo.CombName,
            ExamDeptCode = combInfo.ExamDeptCode,
            CheckCls = combInfo.CheckCls,
            ClsCode = combInfo.ClsCode,
            ClsName = string.Empty,
            OriginalPrice = combInfo.Price,
            Price = combInfo.Price,
            ReportShow = combInfo.ReportShow,
            Discount = 1,
            IsPayBySelf = isPayBySelf, // 
            PayStatus = PayStatus.未收费,
            BeFrom = beFrom,
            Surcharges = 0,
            ApplicantCode = string.Empty,
            ApplicantName = string.Empty,
            CreateTime = DateTime.Now,
            RegisterTime = regCombs[0].RegisterTime,
            CombSortIndex = combInfo.SortIndex,
            ClsSortIndex = combInfo.ClsSortIndex,
        };
        _peRegisterCombRepository.SplitTableInsert(newRegComb);

        void DeleteVenousBloodComb()
        {
            if (!regCombs.Any(x => x.CombCode == combcode))
            {
                return;
            }
            if (regCombs.Any(x => x.CombCode == combcode && x.PayStatus == PayStatus.收费))
            {
                return;
            }
            var deleteComb = _registerRepository.ReadRegisterCombs(regNo).Where(x => x.CombCode == combcode).ToArray();
            _peRegisterCombRepository.SplitTableDelete(deleteComb);
        }
    }

    private void InsertTestTubeToRegisterComb(PeRegister register)
    {
        PeRegisterComb[] peRegisterCombs = _registerRepository.ReadRegisterCombs(register.RegNo).ToArray();
        PeRegisterComb[] regCombs;
        //获取个人未收费/团体未采集的登记组合
        if (register.IsCompanyCheck)
        {
            var gather = _sampleRepository.ReadSample().Where(x => x.RegNo == register.RegNo && x.GatherTime != null).Select(x => x.RegCombId).ToArray();
            regCombs = peRegisterCombs.Where(x => !gather.Any(t => t == x.Id)).ToArray();
            regCombs = regCombs.Except(regCombs.Where(x => x.BeFrom != null && x.BeFrom.All(item => gather.Select(t => t.ToString()).ToArray().Contains(item)))).ToArray();
        }
        else
        {
            regCombs = peRegisterCombs.Where(x => x.PayStatus == PayStatus.未收费).ToArray();
        }
        if (regCombs.Length == 0)
            return;

        var testTubeBefrom = _sampleService.CalculateTestTubeBefrom(regCombs.Select(x => x.CombCode).ToArray());

        List<PeRegisterComb> newRegCombs = new();
        List<PeRegisterComb> updateCombs = new();
        List<PeRegisterComb> deleteCombs = new();

        var allTestTube = _clusterCombRepository.ReadBarcodeTypeFeeComb().Select((barType, comb) => barType.FeeCombCode).Distinct().ToArray();
        var oldRegTestTube = regCombs.Where(x => allTestTube.Contains(x.CombCode)).ToList();

        #region 计算试管数量及从属关系
        foreach (var tube in testTubeBefrom.Where(x => x.TubeCombCode != _systemParameterService.VenousBloodComb))
        {
            var befrom = regCombs.Where(x => SqlFunc.ContainsArray(tube.BeFrom, x.CombCode)).Select(x => x.Id.ToString()).ToArray();
            var tubecomb = oldRegTestTube.Where(x => x.CombCode == tube.TubeCombCode
                        && (x.BeFrom.All(item => befrom.Contains(item) || befrom.All(item => x.BeFrom.Contains(item))))).SingleOrDefault();
            if (tubecomb == null)
            {
                var combInfo = _clusterCombRepository.ReadComb(tube.TubeCombCode)
                .Select(x => new
                {
                    x.CombCode,
                    x.CombName,
                    x.ExamDeptCode,
                    x.CheckCls,
                    x.ClsCode,
                    x.Price,
                    x.ReportShow,
                    x.SortIndex,
                    ClsSortIndex = SqlFunc.Subqueryable<CodeItemCls>().Where(cls => cls.ClsCode == x.ClsCode).Select(cls => cls.SortIndex),
                })
                .First();

                if (combInfo == null) //试管组合不存在
                    continue;
                //新试管组合
                var newRegComb = new PeRegisterComb
                {
                    Id = _noGeneration.NextSnowflakeId(),
                    RegNo = register.RegNo,
                    CombCode = combInfo.CombCode,
                    CombName = combInfo.CombName,
                    ExamDeptCode = combInfo.ExamDeptCode,
                    CheckCls = combInfo.CheckCls,
                    ClsCode = combInfo.ClsCode,
                    ClsName = string.Empty,
                    OriginalPrice = combInfo.Price,
                    Price = combInfo.Price,
                    ReportShow = combInfo.ReportShow,
                    Discount = 1,
                    IsPayBySelf = regCombs.Any(g => g.IsPayBySelf) ? true : false, //
                    PayStatus = PayStatus.未收费,
                    BeFrom = befrom,
                    Surcharges = 0,
                    ApplicantCode = string.Empty,
                    ApplicantName = string.Empty,
                    CreateTime = DateTime.Now,
                    RegisterTime = regCombs[0].RegisterTime,
                    CombSortIndex = combInfo.SortIndex,
                    ClsSortIndex = combInfo.ClsSortIndex,
                };
                newRegCombs.Add(newRegComb);
            }
            else
            {
                tubecomb.BeFrom = befrom;
                updateCombs.Add(tubecomb);
            }
        }
        #endregion

        if (oldRegTestTube.Any(x => !x.BeFrom.All(item => regCombs.Select(x => x.Id.ToString()).ToArray().Contains(item))))
        {
            deleteCombs.AddRange(oldRegTestTube.Where(x => !x.BeFrom.All(item => regCombs.Select(x => x.Id.ToString()).ToArray().Contains(item))).ToArray());
            deleteCombs = deleteCombs.Except(updateCombs).ToList();
        }

        if (deleteCombs.Count > 0)
            _peRegisterCombRepository.SplitTableDelete(deleteCombs);
        if (updateCombs.Count > 0)
            _peRegisterCombRepository.SplitTableUpdate(updateCombs);
        if (newRegCombs.Count > 0)
            _peRegisterCombRepository.SplitTableInsert(newRegCombs);
    }
    #endregion
}
