﻿using SqlSugar;
using System;
using System.Collections.Generic;
using System.Linq;

namespace Peis.ConsoleTest.Test_Yx.SqlSugar
{
    public static class SplitTableJoinTest
    {
        private readonly static SqlSugarScope _db;

        static SplitTableJoinTest()
        {
            _db = new SqlSugarScope(new ConnectionConfig()
            {
                ConnectionString = "Data Source=192.168.1.145;Initial Catalog=SqlSugarTest; User ID=sa;Password=****;",
                DbType = DbType.SqlServer,
                IsAutoCloseConnection = true,
                InitKeyType = InitKeyType.Attribute,
            });

            _db.Aop.OnLogExecuting = (sql, pars) =>
            {
                Console.WriteLine(sql + "\r\n" + _db.Utilities.SerializeObject(pars.ToDictionary(it => it.ParameterName, it => it.Value)));
                Console.WriteLine();
            };
        }

        public static void Invoke()
        {
            InitDatabase();
            InitData();

            var payQuery = _db.Queryable<Payment>().SplitTable(tabs => tabs);

            var result = _db.Queryable<Order>()
                .InnerJoin(payQuery, (a, b) => b.OrderID == a.OrderID)
                .Select((a, b) => new
                {
                    a.OrderID,
                    OrderNote = a.Note,
                    OrderPricee = a.Price,
                    b.PayID,
                    PayPrice = b.Price
                })
                .ToArray();
        }

        private static void InitDatabase()
        {
            _db.DbMaintenance.CreateDatabase();
            _db.CodeFirst.InitTables(typeof(Order));
            //_db.CodeFirst.SplitTables().InitTables(typeof(Payment));

            _db.DbMaintenance.TruncateTable<Order>();

            foreach (var table in _db.SplitHelper<Payment>().GetTables())
            {
                _db.DbMaintenance.TruncateTable(table.TableName);

            }
        }

        private static void InitData()
        {
            var orders = new List<Order>
            {
                new Order{ OrderID = 1, Price = 100M, Note = "订单1", OrderTime = new DateTime(2021, 01, 01) },
                new Order{ OrderID = 2, Price = 500M, Note = "订单2", OrderTime = new DateTime(2022, 01, 01) },
                new Order{ OrderID = 3, Price = 0.0M, Note = "订单3", OrderTime = new DateTime(2022, 01, 01) },
            };

            var pays = new List<Payment>
            {
                new Payment { PayID = 100, PayType = "A", Price = 50M, OrderID = 1, OrderTime = new DateTime(2021, 01, 01) },
                new Payment { PayID = 101, PayType = "B", Price = 50M, OrderID = 1, OrderTime = new DateTime(2021, 01, 01) },
                new Payment { PayID = 102, PayType = "B", Price = 50M, OrderID = 2, OrderTime = new DateTime(2022, 01, 01) },
            };

            _db.Insertable(orders).ExecuteCommand();
            _db.Insertable(pays).SplitTable().ExecuteCommand();
        }

        #region TableModel
        private class Order
        {
            public int OrderID { get; set; }
            public decimal Price { get; set; }
            public string Note { get; set; }
            public DateTime OrderTime { get; set; }
        }

        [SplitTable(SplitType.Year)]
        [SugarTable("Payment_{year}{month}{day}")]
        private class Payment
        {
            public int OrderID { get; set; }
            public int PayID { get; set; }
            public string PayType { get; set; }
            public decimal Price { get; set; }
            [SplitField]
            public DateTime OrderTime { get; set; }
        }
        #endregion
    }
}
