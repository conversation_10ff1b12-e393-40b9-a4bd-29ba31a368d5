﻿using SqlSugar;
using System;

namespace Peis.Model.DataBaseEntity
{
    ///<summary>
    ///体检优先主检表
    ///</summary>
    [SugarTable("PeReportPriority")]
    public class PeReportPriority
    {
        /// <summary>
        /// 体检号
        /// </summary>        
        [SugarColumn(IsNullable = false, IsPrimaryKey = true, Length = 12)]
        public string RegNo { get; set; }

        /// <summary>
        /// 优先级
        /// </summary>        
        [SugarColumn(IsNullable = false)]
        public int Priority { get; set; }

        /// <summary>
        /// 操作员代码
        /// </summary>        
        [SugarColumn(IsNullable = false, Length = 10)]
        public string OperatorCode { get; set; }

        /// <summary>
        /// 操作时间
        /// </summary>        
        [SugarColumn(IsNullable = false)]
        public DateTime OperateTime { get; set; }
    }
}