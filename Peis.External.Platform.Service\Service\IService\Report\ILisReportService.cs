﻿namespace Peis.External.Platform.Service.Service.IService.Report
{
    public interface ILisReportService
    {
        /// <summary>
        /// 检验报告回传
        /// </summary>
        /// <param name="xml"></param>
        /// <param name="respData"></param>
        /// <returns></returns>
        void AcceptReport(string xml, ref string respData);

        /// <summary>
        /// 检验危急值回传
        /// </summary>
        /// <param name="xml"></param>
        /// <param name="respData"></param>
        /// <returns></returns>
        void AcceptCritical(string xml, ref string respData);
    }
}
