﻿using Aspose.Words.MailMerging;

namespace Peis.Model.DTO.CompanyStatistics;

/// <summary>
/// Aspose.Words-自定义邮件合并数据源-各年龄段统计
/// </summary>
public class MailMergeDataSourceOfSummaryDiseaseAge : IMailMergeDataSource
{
    private readonly List<CompanySumDiseaseAge> _customers;
    private int _currentIndex;

    public MailMergeDataSourceOfSummaryDiseaseAge(List<CompanySumDiseaseAge> customers)
    {
        _customers = customers;
        _currentIndex = -1;
    }

    public string TableName => "SummaryAgeList";

    public bool GetValue(string fieldName, out object fieldValue)
    {
        var customer = _customers[_currentIndex];
        var prop = customer.GetType().GetProperty(fieldName);

        if (prop != null)
        {
            fieldValue = prop.GetValue(customer);
            return true;
        }

        fieldValue = default;
        return false;
    }

    public bool MoveNext()
    {
        _currentIndex++;
        return _currentIndex < (_customers?.Count ?? 0);
    }

    public IMailMergeDataSource GetChildDataSource(string tableName)
    {
        return default;
    }
}
