﻿using System.Collections.Concurrent;
using System.Collections.Generic;

namespace Peis.Utility.Cache;

/*
public class AppCache
{
    private ConcurrentDictionary<string, CacheData> _cache = new ConcurrentDictionary<string, CacheData>();

    public CacheData Get(string key)
    {
        return _cache.TryGetValue(key, out var data) ? data : null;
    }

    public void Set(string key, CacheData data)
    {
        _cache[key] = data;
    }

    public void Remove(string key)
    {
        _cache.Remove(key, out _);
    }

    public T Get<T>(string key)
    {
        var data = Get(key);
        return data != null ? (T)data.Value : default;
    }
}
*/