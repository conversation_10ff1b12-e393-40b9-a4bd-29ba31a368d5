﻿using Peis.Quartz.UI.Tools;
using Microsoft.AspNetCore.Http;
using NUglify.Helpers;
using Peis.Model.DataBaseEntity.Occupation;
using Peis.Model.DTO;
using Peis.Model.DTO.Disease;
using Peis.Model.DTO.DoctorStation;
using Peis.Model.DTO.MajorPositive;
using Peis.Model.DTO.Record;
using Peis.Model.DTO.RecordNew;
using Peis.Model.DTO.ReportConclusion;
using Peis.Model.DTO.ReportGraphicText;
using Peis.Model.DTO.WorkloadStatistics;
using Peis.Model.Other.Input;
using Peis.Model.Other.Input.DoctorStation;
using Peis.Model.Other.PeEnum;
using Peis.Repository.IRepository;
using Peis.Service.IService;
using Peis.Service.IService.Helper;
using Peis.Service.IService.Occupation.Record;
using Peis.Utility.PeUser;
using System.Data;
using System.Linq;
using System.Text.RegularExpressions;
using ValueType = Peis.Model.Other.PeEnum.ValueType;
using System.ServiceModel.Channels;

namespace Peis.Service.Service
{
    public class RecordNewService : IRecordNewService
    {
        private readonly INoGeneration _noGeneration;
        private readonly IDiseaseService _diseaseService;
        private readonly IRecordRepository _recordRepository;
        private readonly IDataTranRepository _dataTranRepository;
        private readonly IRegisterRepository _registerRepository;
        private readonly IReportConclusionRepository _reportConclusionRepository;
        private readonly ISystemParameterService _systemParameterService;
        private readonly IHttpContextUser _httpContextUser;
        private readonly IDataRepository<PeImageCollect> _peImageCollectRepository;
        private readonly IDataRepository<PeRegister> _peRegisterRepository;
        private readonly IDataRepository<PeRecordImage> _peRecordImageRepository;
        private readonly IDataRepository<SysOperator> _sysOperatorRepository;
        private readonly IDataRepository<RecordContact> _recordContactRepository;
        private readonly IMapper _mapper;
        private readonly IFileReportGraphicTextService _reportGraphicTextService;
        private readonly ICacheRepository _cacheRepository;
        private readonly IDataRepository<MajorPositiveDetail> _majorPositiveDetailRepository;
        //分表仓储
        private readonly ISplitTableRepository<PeRecordComb> _peRecordCombRepository;
        private readonly ISplitTableRepository<PeRecordCombTag> _peRecordCombTagRepository;
        private readonly ISplitTableRepository<PeRecordItem> _peRecordItemRepository;
        private readonly ISplitTableRepository<PeRecordItemTag> _peRecordItemTagRepository;
        private readonly ISplitTable _splitTable;
        private readonly IOccupationalRecordService _occupationalRecordService;
        private readonly IDataRepository<PeRegisterOccupation> _peRegisterOccupationRepository;
        private readonly IMajorPositiveService _majorPositiveService;
        private readonly ILogBusinessNewService _logBusinessNewService;
        private readonly ITempTableHelper _tempTableHelper;
        private readonly IFileReportGraphicTextService _fileReportGraphicTextService;
        public RecordNewService(
            INoGeneration noGeneration,
            IDiseaseService diseaseService,
            IRecordRepository recordRepository,
            IDataTranRepository dataTranRepository,
            IRegisterRepository registerRepository,
            IReportConclusionRepository reportConclusionRepository,
            ISystemParameterService systemParameterService,
            IHttpContextUser httpContextUser,
            IDataRepository<PeImageCollect> peImageCollectRepository,
            IDataRepository<PeRegister> peRegisterRepository,
            IDataRepository<PeRecordImage> peRecordImageRepository,
            IDataRepository<SysOperator> sysOperatorRepository,
            IDataRepository<RecordContact> recordContactRepository,
            IMapper mapper,
            IFileReportGraphicTextService reportGraphicTextService,

            //分表仓储
            ISplitTableRepository<PeRecordComb> peRecordCombRepository,
            ISplitTableRepository<PeRecordCombTag> peRecordCombTagRepository,
            ISplitTableRepository<PeRecordItem> peRecordItemRepository,
            ISplitTableRepository<PeRecordItemTag> peRecordItemTagRepository,
            ICacheRepository cacheRepository,
            IOccupationalRecordService occupationalRecordService,
            IDataRepository<PeRegisterOccupation> peRegisterOccupationRepository,
            IMajorPositiveService majorPositiveService,
            ILogBusinessNewService logBusinessNewService,
            ITempTableHelper tempTableHelper,
            IDataRepository<MajorPositiveDetail> majorPositiveDetailRepository,
            IFileReportGraphicTextService fileReportGraphicTextService,
            ISplitTable splitTable)
        {
            _noGeneration = noGeneration;
            _diseaseService = diseaseService;
            _recordRepository = recordRepository;
            _dataTranRepository = dataTranRepository;
            _registerRepository = registerRepository;
            _reportConclusionRepository = reportConclusionRepository;
            _systemParameterService = systemParameterService;
            _httpContextUser = httpContextUser;
            _peImageCollectRepository = peImageCollectRepository;
            _peRegisterRepository = peRegisterRepository;
            _peRecordImageRepository = peRecordImageRepository;
            _sysOperatorRepository = sysOperatorRepository;
            _recordContactRepository = recordContactRepository;
            _mapper = mapper;
            _reportGraphicTextService = reportGraphicTextService;

            //分表
            _peRecordCombRepository = peRecordCombRepository;
            _peRecordCombTagRepository = peRecordCombTagRepository;
            _peRecordItemRepository = peRecordItemRepository;
            _peRecordItemTagRepository = peRecordItemTagRepository;
            _cacheRepository = cacheRepository;
            _occupationalRecordService = occupationalRecordService;
            _peRegisterOccupationRepository = peRegisterOccupationRepository;
            _majorPositiveService = majorPositiveService;
            _logBusinessNewService = logBusinessNewService;
            _tempTableHelper = tempTableHelper;
            _majorPositiveDetailRepository = majorPositiveDetailRepository;
            _fileReportGraphicTextService = fileReportGraphicTextService;
            _splitTable = splitTable;
        }

        /// <summary>
        /// 录入结果人员列表
        /// </summary>
        /// <param name="filterDoctor">按医生过滤</param>
        /// <param name="query"></param>
        /// <returns></returns>
        public List<RecordResultPerson> RecordResultPerson(bool filterDoctor, RecordResultPersonQueryNew query, ref int totalNumber, ref int totalPage)
        {
            query.StartTime = query.StartTime.Date;

            // 如果query.KeyWord是体检号则优先查
            var regNoFlag = !string.IsNullOrEmpty(query.Keyword) && query.Keyword.Length == 12
                            && Regex.IsMatch(query.Keyword, @"\d{12}");

            var date = _registerRepository.ReadRegister()
                   .WhereIF(regNoFlag, x => x.RegNo == query.Keyword)
                   .WhereIF(!regNoFlag, x => SqlFunc.Between(x.ActiveTime.Value, query.StartTime, query.EndTime))
                   .Select(x => new RegisterDateRange
                   {
                       MinRegTime = SqlFunc.AggregateMin(x.RegisterTime),
                       MaxRegTime = SqlFunc.AggregateMax(x.RegisterTime)
                   }).First();

            //如果没有数据,直接返回
            if (!date.IsValidDate)
                return new();

            //查询当前医生拥有的科室
            var deptFilter = filterDoctor ? _recordRepository.ReadOperDept(query.OperCode) : Array.Empty<string>();
            if (filterDoctor && !deptFilter.Contains(query.DeptCode))
                throw new BusinessException($"医生代码:{query.OperCode}不存在科室代码：{query.DeptCode}的跨科权限,或科室代码：{query.DeptCode}不存在！");

            var selfCheckRegNos = _recordRepository.ReadCombData(date.MinRegTime, date.MaxRegTime)
                                .Where((reg, rec) => rec.DoctorName == _httpContextUser.UserName)
                                .Where(reg => SqlFunc.Between(reg.RegisterTime, date.MinRegTime, date.MaxRegTime))
                                .WhereIF(filterDoctor, (reg, rec) => rec.ExamDeptCode == query.DeptCode)
                                .Select((reg, rec) => reg.RegNo).Distinct().ToArray();
            var ll = _tempTableHelper.TempTable(selfCheckRegNos);

            var queryable = _recordRepository.ReadRecordResultPerson(date.MinRegTime, date.MaxRegTime, selfCheckRegNos);
            if (regNoFlag)
            {
                queryable = queryable
                    .Where(reg => reg.RegNo == query.Keyword)
                    .WhereIF(filterDoctor, (reg, regComb) => regComb.ExamDeptCode == query.DeptCode);
            }
            else
            {
                queryable = queryable
                    .WhereIF(query.QueryType == 1, (reg, regComb, recComb) => SqlFunc.Between(reg.ActiveTime.Value, query.StartTime, query.EndTime))
                    .WhereIF(query.QueryType == 2, (reg, regComb, recComb) => SqlFunc.Between(recComb.ExamTime, query.StartTime, query.EndTime))
                    .WhereIF(filterDoctor, (reg, regComb) => regComb.ExamDeptCode == query.DeptCode)
                    .WhereIF(!string.IsNullOrEmpty(query.Keyword), reg => reg.Name.Contains(query.Keyword));
            }

            var patientList =
            queryable.Where(reg => reg.IsActive)
           .WhereIF(!string.IsNullOrEmpty(query.CompanyCode), (reg, regComb) => reg.CompanyCode == query.CompanyCode)
           .WhereIF(Enum.IsDefined(typeof(PeCls), query.PeCls), reg => reg.PeCls == query.PeCls)
           .WhereIF(!query.ClusterCode.IsNullOrEmpty(), reg => SqlFunc.Subqueryable<PeRegisterCluster>().Where(x => x.RegNo == reg.RegNo && x.ClusCode == query.ClusterCode).Any())
            //职业病过滤
           .WhereIF(!query.JobStatus.IsNullOrEmpty(), (reg, regComb, recComb, occupation) => occupation.JobStatus == query.JobStatus)
           .WhereIF(!query.HazardousCode.IsNullOrEmpty(), reg => SqlFunc.Subqueryable<PeRegisterOccupationHazard>().Where(x => x.RegNo == reg.RegNo && x.HazardousCode == query.HazardousCode).Any())
           .WhereIF(query.PeStatus == 2 && filterDoctor, (reg, regComb, recComb, occupation, slf) => slf.ColumnName != null)
           .GroupBy(reg => reg.RegNo)
           .HavingIF(query.PeStatus == 1 && filterDoctor, (reg, regComb, recComb) => SqlFunc.AggregateCount(recComb.Id) < SqlFunc.AggregateCount(regComb.Id))
           .HavingIF(query.PeStatus == 2 && filterDoctor, (reg, regComb, recComb) => SqlFunc.AggregateCount(recComb.Id) == SqlFunc.AggregateCount(regComb.Id))
           .HavingIF(query.PeStatus == 1 && !filterDoctor, (reg, regComb, recComb, occupation) => SqlFunc.AggregateMax(reg.PeStatus) < PeStatus.已检完 || SqlFunc.AggregateMax(occupation.PeStatus) < PeStatus.已检完)
           .HavingIF(query.PeStatus == 2 && !filterDoctor, (reg, regComb, recComb, occupation) => SqlFunc.AggregateMax(reg.PeStatus) >= PeStatus.已检完 && (SqlFunc.AggregateMax(occupation.PeStatus) >= PeStatus.已检完 || SqlFunc.EqualsNull(SqlFunc.AggregateMax(occupation.RegNo), null)))
            .Select((reg, regComb, recComb, occupation) => new RecordResultPerson
            {
                RegNo = reg.RegNo,
                Name = SqlFunc.AggregateMax(reg.Name),
                Sex = SqlFunc.AggregateMax(reg.Sex),
                Age = SqlFunc.AggregateMax(reg.Age),
                CardNo = SqlFunc.AggregateMax(reg.CardNo),
                Tel = SqlFunc.AggregateMax(reg.Tel),
                ActiveTime = SqlFunc.AggregateMax(reg.ActiveTime),
                RegisterTime = SqlFunc.AggregateMax(reg.RegisterTime),
                PeStatus = SqlFunc.AggregateMax(reg.PeStatus),
                PeCls = SqlFunc.AggregateMax(reg.PeCls),
                CompanyCode = SqlFunc.AggregateMax(reg.CompanyCode),
                BookType = SqlFunc.AggregateMax(reg.BookType),
            }).MergeTable()
            .OrderBy(query.OrderByList)
            .OrderByIF(query.OrderByList.Count == 0, reg => reg.RegNo)
            .ToMyPageList(query.PageNumber, query.PageSize, ref totalNumber, ref totalPage);

            patientList.ForEach(x =>
            {
                x.CompanyName = _cacheRepository.DictCompany()?.Values
                ?.FirstOrDefault(y => y.CompanyCode == x.CompanyCode)
                ?.CompanyName;
            });

            return patientList;
        }

        /// <summary>
        /// 获取医生跨科科室
        /// </summary>
        /// <param name="operCode"></param>
        /// <returns></returns>
        public Dictionary<string, string> ReadOperDept(string operCode)
        {
            return _recordRepository.ReadOperDeptWithName(operCode);
        }

        /// <summary>
        /// 获取历史报告
        /// </summary>
        /// <param name="regNo"></param>
        /// <returns></returns>
        public HistoryReport GetHistoryReport(string regNo)
        {
            var patInfoThis = _registerRepository.ReadRegister(regNo)
                .Select(x => new { x.PatCode, x.RegNo, x.RegisterTime, x.ActiveTime })
                .First();

            var patInfoLast = _registerRepository.ReadRegister()
                .Where(x => x.PatCode == patInfoThis.PatCode)
                .Where(x => x.PeStatus == PeStatus.已总检 || x.PeStatus == PeStatus.已审核)
                .Where(x => x.RegisterTime < patInfoThis.RegisterTime)
                .OrderByDescending(x => x.ActiveTime)
                .Select(x => new { x.PatCode, x.RegNo, x.ActiveTime })
                .First();

            var compareInfo = new HistoryReport
            {
                ExamTime = patInfoThis.ActiveTime,
                ExamTimeLast = patInfoLast?.ActiveTime,
                Summary = GetSummaryHistory(patInfoThis.RegNo, patInfoLast?.RegNo),
                Suggestion = GetSuggestionHistory(patInfoThis.RegNo, patInfoLast?.RegNo),
                ExamItems = GetExamItemHistory(patInfoThis.RegNo, patInfoLast?.RegNo)
            };

            return compareInfo;

            SummaryHistory GetSummaryHistory(string regNoThis, string regNoLast)
            {
                var summaryHistory = new SummaryHistory
                {
                    Result = _reportConclusionRepository.GetReportConclusion(regNo).Select(x => x.Summary).First()
                };

                if (regNoLast == null)
                    summaryHistory.ResultLast = string.Empty;
                else
                    summaryHistory.ResultLast = _reportConclusionRepository.GetReportConclusion(patInfoLast.RegNo).Select(x => x.Summary).First();

                return summaryHistory;
            }
            SuggestionHistory GetSuggestionHistory(string regNoThis, string regNoLast)
            {
                var summaryHistory = new SuggestionHistory
                {
                    Result = _reportConclusionRepository.GetReportConclusion(regNo).Select(x => x.Suggestion).First()
                };

                if (regNoLast == null)
                    summaryHistory.ResultLast = string.Empty;
                else
                    summaryHistory.ResultLast = _reportConclusionRepository.GetReportConclusion(patInfoLast.RegNo).Select(x => x.Suggestion).First();

                return summaryHistory;
            }
            List<ExamItemHistory> GetExamItemHistory(string regNoThis, string regNoLast)
            {
                var combsThis = _recordRepository.ReadRecordComb(regNoThis)
                    .Select((recComb, comb) => new { recComb.Id, recComb.CombCode, comb.CombName })
                    .ToArray();
                var itemThis = _recordRepository.ReadRecordItem(regNoThis, combsThis.Select(x => x.Id).ToArray())
                    .Select((recItem) => new { recItem.CombCode, recItem.ItemCode, recItem.ItemName, recItem.ItemResult })
                    .ToArray();

                var combsLast = _recordRepository.ReadRecordComb(regNoLast).Select((recComb, comb) => recComb.Id).ToArray();
                var itemLast = _recordRepository.ReadRecordItem(regNoLast, combsLast)
                    .Select((recItem) => new { recItem.CombCode, recItem.ItemCode, recItem.ItemName, recItem.ItemResult })
                    .ToArray();

                return combsThis
                    .Select(combThis => new ExamItemHistory
                    {
                        CombName = combThis.CombName,
                        ItemResults = itemThis.Where(x => x.CombCode == combThis.CombCode)
                            .Select(x => new ItemResultHistory
                            {
                                ItemName = x.ItemName,
                                Result = x.ItemResult,
                                ResultLast = itemLast.FirstOrDefault(sq => sq.ItemCode == x.ItemCode)?.ItemResult
                            })
                            .ToArray()
                    })
                    .ToList();
            }
        }

        /// <summary>
        /// 获取检查科室和组合列表
        /// </summary>
        /// <param name="filterDoctor"></param>
        /// <param name="query"></param>
        /// <param name="msg"></param>
        /// <returns></returns>
        /// <exception cref="BusinessException"></exception>
        public CombNavigation GetCombNavigation(bool filterDoctor, ItemNavigateQuery query, ref string msg)
        {
            var regInfo = _registerRepository.ReadRegister(query.RegNo).First() ?? throw new BusinessException($"体检号不存在：{query.RegNo}");
            var regCombs = _recordRepository.ReadCheckableCombs(regInfo)
                .Select((regComb, dept) => new CheckableRegComb
                {
                    RegCombId = regComb.Id,
                    CombCode = regComb.CombCode,
                    CombName = regComb.CombName,
                    ClsCode = regComb.ClsCode,
                    ExamDeptCode = regComb.ExamDeptCode,
                    CombSortIndex = regComb.CombSortIndex,
                    ExamDeptName = dept.DeptName,
                    Template = dept.Template
                })
                .ToList();
            if (regCombs.Count == 0)
                throw new BusinessException($"没有可检查的组合或未缴费：{regInfo.Name}");
            var recCombDict = _recordRepository.ReadRecordComb(regInfo).ToList().ToDictionary(x => x.RegCombId);

            #region 特殊处理（可卸载代码块）：彩超合并显示
            //var us_RegCombs = regCombs
            //    .Where(x => x.ClsCode == "15" && !_systemParameterService.UltrasonicCombs.Contains(x.CombCode))
            //    .Where(x => recCombDict.ContainsKey(x.RegCombId))
            //    .OrderBy(x => x.CombSortIndex)
            //    .ToList();
            //var beMergedCombs = us_RegCombs.Where(x => recCombDict[x.RegCombId].CombResult == "已合并发报告").ToArray();
            //if (us_RegCombs.Count > beMergedCombs.Length)
            //{
            //    us_RegCombs.RemoveAll(beMergedCombs);
            //    regCombs.RemoveAll(beMergedCombs);

            //    for (int i = 0; i < us_RegCombs.Count; i++)
            //    {
            //        if (us_RegCombs.Count == 1)
            //            us_RegCombs[i].CombName = "彩超合并报告";
            //        else
            //            us_RegCombs[i].CombName = $"彩超合并报告{i + 1}";
            //    }
            //}
            #endregion

            var doctorMainDept = string.Empty;// 医生的主科室
            var doctorExamDepts = Array.Empty<string>();// 医生的跨科科室
            if (filterDoctor)
            {
                doctorMainDept = _sysOperatorRepository.First(x => x.OperatorCode == query.OperCode)?.DeptCode;
                doctorExamDepts = _recordRepository.ReadOperDept(query.OperCode);
            }

            var examDepts = new List<DeptCombs>();
            foreach (var examDeptGroup in regCombs.GroupBy(x => new { x.ExamDeptCode, x.ExamDeptName, x.Template }))
            {
                var deptExamCombs = examDeptGroup
                    .Select(x => new RegCombs
                    {
                        RegCombId = x.RegCombId,
                        CombCode = x.CombCode,
                        CombName = x.CombName,
                        ClsCode = x.ClsCode,
                        SortIndex = x.CombSortIndex,
                        CombStatus = recCombDict.TryGetValue(x.RegCombId, out var recComb) ? (recComb.IsError ? 3 : 2) : 1
                    })
                    .OrderBy(x => x.SortIndex)
                    .ToArray();

                examDepts.Add(new DeptCombs
                {
                    DeptCode = examDeptGroup.Key.ExamDeptCode,
                    DeptName = examDeptGroup.Key.ExamDeptName,
                    Template = examDeptGroup.Key.Template,
                    IsMain = examDeptGroup.Key.ExamDeptCode == doctorMainDept,
                    IsEnabled = !filterDoctor || doctorExamDepts.Contains(examDeptGroup.Key.ExamDeptCode),
                    IsFinish = examDeptGroup.All(x => recCombDict.ContainsKey(x.RegCombId) && !string.IsNullOrEmpty(recCombDict[x.RegCombId].DoctorName)),
                    IsCombError = examDeptGroup.Any(x => recCombDict.ContainsKey(x.RegCombId) && recCombDict[x.RegCombId].IsError),
                    RegCombs = deptExamCombs
                });
            }

            return new CombNavigation
            {
                Person = _mapper.Map<RecordResultPerson>(regInfo),
                DeptCombs = examDepts.OrderByDescending(x => x.IsEnabled).OrderByDescending(x => x.IsMain).ToArray()
            };
        }

        /// <summary>
        /// 获取检查组合导航列表（医生工作站、结果录入）
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        /// <exception cref="BusinessException"></exception>
        public ExamCombNavigationNew GetExamCombList(ExamCombQuery query)
        {
            var regInfo = _registerRepository.ReadRegister(query.RegNo)
                .Includes(x => x.RegisterHazard)
                .Includes(x => x.RegisterOccupation)
                .Includes(x => x.RegisterCluster)
                .First() ?? throw new BusinessException($"体检号不存在：{query.RegNo}");
            var person = new RecordResultPersonNew
            {
                RegNo = regInfo.RegNo,
                RegisterTime = regInfo.RegisterTime,
                ActiveTime = regInfo.ActiveTime,
                Age = regInfo.Age,
                CardNo = regInfo.CardNo,
                HazardousFators = string.Join(",", regInfo.RegisterHazard?.Select(x => x.HazardousName)),
                ClusterName = string.Join(",", regInfo.RegisterCluster?.Select(x => x.ClusName)),
                Jobstatus = regInfo.RegisterOccupation == null ? string.Empty : regInfo.RegisterOccupation.JobStatus.ToString(),
                Name = regInfo.Name,
                PeCls = regInfo.PeCls,
                PeStatus = regInfo.PeStatus,
                Sex = regInfo.Sex,
                Tel = regInfo.Tel,
                IsOccupation = regInfo.IsOccupation,
                IsOrdinary = regInfo.IsOrdinary,
                BookType = regInfo.BookType,
                PhotoUrl = regInfo.PhotoUrl != null ? Path.Combine(Appsettings.GetSectionValue(ResxCommon.ConfigFileServiceUrl), regInfo.PhotoUrl) : string.Empty
            };

            var checkCombQuery = _recordRepository.ReadCheckableCombs(regInfo);

            if (query.FilterDoctor)
            {
                var examDepts = _recordRepository.ReadOperDept(query.DoctorCode);
                if (!examDepts.Contains(query.DeptCode))
                    throw new BusinessException($"医生代码:{query.DoctorCode}不存在科室代码：{query.DeptCode}的跨科权限,或科室代码：{query.DeptCode}不存在！");
                checkCombQuery.Where((regComb, dept) => regComb.ExamDeptCode == query.DeptCode);
            }
            var examCombs = checkCombQuery
                .LeftJoin(_splitTable.GetTableOrDefault<PeReportGraphicTextFile>(regInfo.RegisterTime), (regComb, dept, garph) => regComb.Id == garph.RegCombId)
                .OrderBy((regComb, dept) => regComb.ClsSortIndex)
                .OrderBy((regComb, dept) => regComb.CombSortIndex)
                .Select((regComb, dept, garph) => new ExamComb
                {
                    RegCombId = regComb.Id,
                    CombCode = regComb.CombCode,
                    CombName = regComb.CombName,
                    ClsCode = regComb.ClsCode,
                    IsFinish = false, // 已完成结果
                    IsError = false, // 异常提示标识
                    CombStatus = 1,
                    ExamDeptCode = dept.DeptCode,
                    ExamDeptName = dept.DeptName,
                    Template = dept.Template,             // 模板类型
                    IsShowUnits = dept.IsShowUnits,          // 显示单位
                    IsShowReferenceRange = dept.IsShowReferenceRange, // 显示参考范围
                    IsShowLastResult = dept.IsShowLastResult,     // 显示上次的结果
                    IsGraphFileDeletable = SqlFunc.IIF(SqlFunc.IsNullOrEmpty(garph.ReportUrl), true, false)
                })
                .ToList();
            if (examCombs.Count == 0)
            {
                throw new BusinessException($"体检号'{query.RegNo}'未查询到检查项目，可能项目不存在或未缴费。");
            }
            var recCombDict = _recordRepository.ReadRecordComb(regInfo)
                .Select(x => new
                {
                    x.RegCombId,
                    x.CombCode,
                    x.DoctorName,
                    x.IsError
                })
                .ToList()
                .ToDictionary(x => x.RegCombId);
            var combDict = _cacheRepository.DictComb();

            foreach (var examComb in examCombs)
            {
                if (combDict.ContainsKey(examComb.CombCode))
                {
                    examComb.CombExtensions = combDict[examComb.CombCode].ItemComb.CombExtensions;
                }

                if (!recCombDict.TryGetValue(examComb.RegCombId, out var recComb))
                    continue;

                examComb.IsFinish = !string.IsNullOrEmpty(recComb.DoctorName);
                examComb.IsError = recComb.IsError;
                examComb.CombStatus = examComb.IsError ? 3 : (examComb.IsFinish ? 2 : 1);
            }

            return new ExamCombNavigationNew
            {
                Person = person,
                ExamCombs = examCombs
            };
        }

        /// <summary>
        /// 获取简易项目结果(主检用到)
        /// </summary>
        /// <param name="regNo"></param>
        /// <returns></returns>
        public AllCombAndItem[] GetSimpleItemResult(string regNo, bool isOccupation)
        {
            var regInfo = _registerRepository.ReadRegister(regNo).First() ?? throw new BusinessException($"体检号不存在：{regNo}");
            var major = _majorPositiveDetailRepository.FindAll(x => x.RegNo == regNo && x.IsDeleted == false).ToList();
            var imageClsCode = _systemParameterService.MedicalImageClsCodes;
            var data = _recordRepository.ReadAllCombTagItem(regNo)
                    .WhereIF(isOccupation, regComb => regComb.IsOccupation == true)
                    .WhereIF(!isOccupation, regComb => regComb.IsOrdinary == true)
                    .Select((regComb, recComb, combTag, item, itemTag, dept) => new SimpleResultModel
                    {
                        ItemTagId = itemTag.Id,
                        CombTagId = combTag.Id,
                        ItemSortIndex = item.SortIndex,
                        CombSortIndex = regComb.CombSortIndex,
                        CombName = regComb.CombName,
                        CheckCls = regComb.CheckCls,
                        ClsSortIndex = regComb.ClsSortIndex,
                        CombCode = recComb.CombCode,
                        OperName = recComb.OperName,
                        DoctorName = recComb.DoctorName,
                        CombTag = combTag.CombTag,
                        ItemCode = item.ItemCode,
                        ItemName = item.ItemName,
                        AbnormalType = item.AbnormalType,
                        IsError = item.IsError,
                        Hint = item.Hint,
                        LowerLimit = item.LowerLimit,
                        UpperLimit = item.UpperLimit,
                        ItemTag = itemTag.ItemTag,
                        Unit = item.Unit,
                        IsShowUnits = dept.IsShowUnits,
                        IsShowReferenceRange = dept.IsShowReferenceRange,
                        ReferenceRange = item.ReferenceRange,
                        AbnormalTypeItemTag = itemTag.AbnormalType,
                        AbnormalTypeCombTag = combTag.AbnormalType,
                        ClsCode = regComb.ClsCode,
                    }).ToList();
            List<SimpleResultModel> quoteData;
            var superiorRegNo = _registerRepository.ReadSuperiorRegNo(regNo);
            if (string.IsNullOrEmpty(superiorRegNo))
            {
                quoteData = new();
            }
            else
            {
                quoteData = _recordRepository.ReadAllCombTagItemQuote(regNo, superiorRegNo)
                    .WhereIF(isOccupation, (quote, regComb) => regComb.IsOccupation == true)
                    .WhereIF(!isOccupation, (quote, regComb) => regComb.IsOrdinary == true)
                    .Select((quote, regComb, recComb, combTag, item, itemTag, dept) => new SimpleResultModel
                    {
                        ItemTagId = itemTag.Id,
                        CombTagId = combTag.Id,
                        ItemSortIndex = item.SortIndex,
                        CombSortIndex = regComb.CombSortIndex,
                        CombName = regComb.CombName,
                        CheckCls = regComb.CheckCls,
                        ClsSortIndex = regComb.ClsSortIndex,
                        CombCode = recComb.CombCode,
                        OperName = recComb.OperName,
                        DoctorName = recComb.DoctorName,
                        CombTag = combTag.CombTag,
                        ItemCode = item.ItemCode,
                        ItemName = item.ItemName,
                        AbnormalType = item.AbnormalType,
                        IsError = item.IsError,
                        Hint = item.Hint,
                        LowerLimit = item.LowerLimit,
                        UpperLimit = item.UpperLimit,
                        ItemTag = itemTag.ItemTag,
                        Unit = item.Unit,
                        IsShowUnits = dept.IsShowUnits,
                        IsShowReferenceRange = dept.IsShowReferenceRange,
                        ReferenceRange = item.ReferenceRange,
                        AbnormalTypeItemTag = itemTag.AbnormalType,
                        AbnormalTypeCombTag = combTag.AbnormalType,
                        ClsCode = regComb.ClsCode,
                    }).ToList();
            }

            var allCombAndItems = data.GroupBy(item => new SimpleResultKey
            {
                CombCode = item.CombCode,
                CombName = item.CombName,
                OperName = item.OperName,
                DoctorName = item.DoctorName,
                CheckCls = item.CheckCls,
                CombSortIndex = item.CombSortIndex,
                ClsSortIndex = item.ClsSortIndex,
                IsShowUnits = item.IsShowUnits,
                IsShowReferenceRange = item.IsShowReferenceRange,
                IsQuote = false,
                ClsCode = item.ClsCode,
            },
                (key, items) =>
                {
                    return CreateAllCombAndItem(key, items);
                })
            .ToArray();
            var quoteCombAndItems = quoteData.GroupBy(item => new SimpleResultKey
            {
                CombCode = item.CombCode,
                CombName = item.CombName,
                OperName = item.OperName,
                DoctorName = item.DoctorName,
                CheckCls = item.CheckCls,
                CombSortIndex = item.CombSortIndex,
                ClsSortIndex = item.ClsSortIndex,
                IsShowUnits = item.IsShowUnits,
                IsShowReferenceRange = item.IsShowReferenceRange,
                IsQuote = true,
                ClsCode = item.ClsCode,
            },
                (key, items) =>
                {
                    return CreateAllCombAndItem(key, items);
                })
            .ToArray();
            var reulsts = allCombAndItems.Concat(quoteCombAndItems).ToArray();
            reulsts = BaseCombClsSort.OrderBy(collection: reulsts).ToArray();

            SetLastResult(reulsts);

            return reulsts;
            AllCombAndItem CreateAllCombAndItem(SimpleResultKey key, IEnumerable<SimpleResultModel> items)
            {
                #region 项目结果
                var projects = items.GroupBy(
                    item => new
                    {
                        item.ItemCode,
                        item.ItemName,
                        item.AbnormalType,
                        item.IsError,
                        item.Hint,
                        item.LowerLimit,
                        item.UpperLimit,
                        item.ItemSortIndex,
                        item.ReferenceRange,
                        item.Unit,
                    },
                    (projectKey, projectItems) =>
                    {
                        // 项目结果标签
                        var results = projectItems.Select(item => new ProjectResult
                        {
                            Id = item.ItemTagId,
                            ItemTag = item.ItemTag,
                            AbnormalType = item.AbnormalTypeItemTag,
                        })
                        .GroupBy(c => c.Id)
                        .Select(g => g.First())
                        .ToArray();

                        return new Projects
                        {
                            ItemCode = projectKey.ItemCode,
                            ItemName = projectKey.ItemName,
                            IsError = projectKey.IsError,
                            AbnormalType = projectKey.AbnormalType,
                            Hint = projectKey.Hint,
                            LowerLimit = projectKey.LowerLimit,
                            UpperLimit = projectKey.UpperLimit,
                            ItemSortIndex = projectKey.ItemSortIndex,
                            Unit = projectKey.Unit,
                            ReferenceRange = projectKey.ReferenceRange,
                            Results = results
                        };
                    })
                    .OrderBy(x => x.ItemSortIndex)
                    .ToArray();
                #endregion

                #region 组合小结
                var combinations = items.Select(item => new Combination
                {
                    Id = item.CombTagId,
                    CombTag = item.CombTag,
                    AbnormalType = item.AbnormalTypeCombTag,
                })
                 .GroupBy(c => c.Id)
                 .Select(g => g.First())
                 .ToArray();
                #endregion

                return new AllCombAndItem
                {
                    CombCode = key.CombCode,
                    CombName = key.CombName,
                    OperName = key.OperName,
                    DoctorName = key.DoctorName,
                    CheckCls = key.CheckCls,
                    CombSortIndex = key.CombSortIndex,
                    ClsSortIndex = key.ClsSortIndex,
                    IsShowUnits = key.IsShowUnits,
                    IsShowReferenceRange = key.IsShowReferenceRange,
                    Combinations = combinations,
                    Projects = projects,
                    IsQuote = key.IsQuote,
                    major = major.FirstOrDefault(x => x.CombCode == key.CombCode),
                    IsMedicalImageResult = imageClsCode.Contains(key.ClsCode),
                };
            }

            void SetLastResult(AllCombAndItem[] list)
            {
                var lastItemResult = new List<LastItemResult>();
                /*  上次的项目结果并不是指前一次体检的项目结果
                 *  张三 
                 *  第一次体检  做了  A B C D E 5个项目
                 *  第二次体检  做了  A   C D E 4个项目
                 *  第三次体检  做了  A B C D E 5个项目
                 *  假设查看第三次体检数据的B项目结果, 那B项目的上次结果,是最近一次有B项目的体检的结果(也就是第一次的B项目结果)
                 */
                if (!string.IsNullOrEmpty(regInfo.CardNo))
                {
                    //得出历年的体检号
                    var regNos = _registerRepository.ReadRegister()
                                 .Where(x => x.CardNo == regInfo.CardNo && x.RegNo != regInfo.RegNo)
                                 .Select(x => x.RegNo)
                                 .ToArray();

                    //获取本次体检的项目
                    var itemArray = new List<string>();
                    list.ForEach(x => x.Projects.ForEach(y => itemArray.Add(y.ItemCode)));

                    //查询本次体检项目的所有历史结果
                    lastItemResult = _recordRepository.ReadItemResult(regNos)
                                    .Where((reg, recComb, recItem) => SqlFunc.ContainsArray(itemArray, recItem.ItemCode))
                                    .Where((reg) => SqlFunc.LessThan(reg.ActiveTime, regInfo.ActiveTime))
                                    .OrderBy(reg => reg.RegisterTime, OrderByType.Desc)
                                    .Select((reg, recComb, recItem) => new LastItemResult
                                    {
                                        ItemCode = recItem.ItemCode,
                                        ItemResult = recItem.ItemResult
                                    })
                                    .ToList();
                    foreach (var item in list)
                    {
                        item.Projects.ForEach(x =>
                        {
                            var lastResult = lastItemResult.FirstOrDefault(y => y.ItemCode == x.ItemCode);
                            if (lastResult != null)
                            {
                                x.LastResult = lastResult.ItemResult;
                            }
                        });
                    }
                }
            }
        }

        /// <summary>
        /// 获取组合项目结果
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        public RecordCombData ReadRecordComb(RecordCombQuery query)
        {
            //1.读取组合结果记录
            var combResult = _recordRepository.ReadCombRecord(query.RegNo)
                            .LeftJoin<MajorPositiveDetail>((regComb, recordComb, recordCombTag, major) => recordComb.RegNo == major.RegNo && recordComb.CombCode == major.CombCode && !major.IsDeleted)
                            .Where(regComb => regComb.Id == query.RegCombId)
                            .Select((regComb, recordComb, recordCombTag, major) => new
                            {
                                regComb.CombCode,
                                regComb.CombName,
                                regComb.ClsCode,
                                regComb.ExamDeptCode,
                                recordComb.OperName,
                                recordComb.DoctorName,
                                recordCombTag.CombTag,
                                recordCombTag.DiseaseCode,
                                recordCombTag.DiseaseName,
                                recordCombTag.BindItemTags,
                                RegCombId = regComb.Id,
                                RecCombId = (long?)recordComb.Id,
                                IsError = SqlFunc.IsNull(recordComb.IsError, false),
                                ExamTime = SqlFunc.IsNull(recordComb.ExamTime, DateTime.Now),
                                RecCombTagId = SqlFunc.IsNull(recordCombTag.Id, 0),
                                IsCustom = SqlFunc.IsNull(recordCombTag.IsCustom, false),
                                AbnormalType = SqlFunc.IsNull(recordCombTag.AbnormalType, AbnormalType.正常),
                                major = major
                            }).ToList();

            //2.读取组合包含的项目数据
            var combTags = new List<CombTag>();
            var recordItems = new List<RecordItem>();
            //无结果时
            if (string.IsNullOrEmpty(combResult.FirstOrDefault()?.DoctorName))
            {
                recordItems = ReadDefaultItemData(query);
                combTags = GetDefaultComTag();
            }
            else //有结果时
            {
                recordItems = ReadItemData(query);

                combTags = combResult.Where(x => x.RecCombTagId != 0).Select(x => new CombTag
                {
                    Id = x.RecCombTagId,
                    Tag = x.CombTag,
                    DiseaseCode = x.DiseaseCode,
                    DiseaseName = x.DiseaseName,
                    IsCustom = x.IsCustom,
                    BindItemTags = string.IsNullOrEmpty(x.BindItemTags) ? new() : x.BindItemTags.Split(';').Select(x => Convert.ToInt64(x)).ToList()
                }).ToList();
            }

            var comb = combResult.FirstOrDefault();
            return new RecordCombData
            {
                RecordComb = new RecordComb
                {
                    RegNo = query.RegNo,
                    RegCombId = comb.RegCombId,
                    CombCode = comb.CombCode,
                    CombName = comb.CombName,
                    ClsCode = comb.ClsCode,
                    ExamDeptCode = comb.ExamDeptCode,
                    OperName = comb.OperName,
                    ExamTime = comb.ExamTime,
                    DoctorName = comb.DoctorName,
                    IsError = comb.IsError,
                    RecCombId = comb.RecCombId,
                },
                CombTags = combTags,
                RecordItems = recordItems,
                Major = comb?.major.Id == 0 ? null : comb.major
            };
        }

        /// <summary>
        /// 保存组合项目结果
        /// </summary>
        /// <param name="record"></param>
        /// <exception cref="BusinessException"></exception>
        public void SaveRecordComb(RecordCombData record)
        {
            var regInfo = _registerRepository.ReadRegister(record.RecordComb.RegNo).First() ?? throw new BusinessException("体检信息为空或已删除");

            //判断附加结果是否录入
            _occupationalRecordService.CheckCombExtensionResultCompleted(record.RecordComb.RegNo, record.RecordComb.CombCode);

            // 处理空小结为“未见明显异常”
            if (record.CombTags == null || record.CombTags.Count == 0)
                record.CombTags = GetDefaultComTag();

            // 生成小结记录
            var combRecord = NewPeRecordComb(record.RecordComb, record.CombTags, regInfo.RegisterTime);

            // 生成小结明细标签
            var combResult = record.CombTags.Select(x => new PeRecordCombTag
            {
                Id = x.Id == 0 ? _noGeneration.NextSnowflakeId() : x.Id,
                RecCombId = combRecord.Id,
                CombCode = combRecord.CombCode,
                CombTag = x.Tag,
                DiseaseCode = x.DiseaseCode,
                DiseaseName = x.DiseaseName,
                IsCustom = x.IsCustom,
                AbnormalType = x.AbnormalType,
                BindItemTags = string.Join(';', x.BindItemTags),
                RegisterTime = regInfo.RegisterTime
            }).ToArray();

            // 项目结果记录
            var recItemData = NewRecordItemAndTag(combRecord.Id, combRecord.CombCode, record.RecordItems, regInfo.RegisterTime);

            _dataTranRepository.ExecTran(() =>
            {
                // 删除旧记录
                DeleteRecordComb(regInfo, record.RecordComb.RegCombId, out var oldRecordComb, out var oldCombResult);

                // 保存结果
                _peRecordCombRepository.SplitTableInsert(combRecord);
                _peRecordCombTagRepository.SplitTableInsert(combResult);
                _peRecordItemRepository.SplitTableInsert(recItemData.RecordItem);
                _peRecordItemTagRepository.SplitTableInsert(recItemData.RecordItemTag);
                if (oldRecordComb == null)
                {
                    _logBusinessNewService.RecordNewLog(regInfo.RegNo, combRecord.CombCode);
                }
                else
                {
                    _logBusinessNewService.RecordModifyLog(oldRecordComb, combRecord, oldCombResult, recItemData.RecordItem.ToArray());
                }
                // 更新体检状态
                UpdatePeStatus(regInfo);

                _logBusinessNewService.SaveLogs();
            });

            var recCombs = combResult.Join(recItemData.RecordItem, a => 1, b => 1, (a, b) => new RecComb
            {
                RegNo = record.RecordComb.RegNo,
                RecCombId = a.RecCombId,
                CombCode = a.CombCode,
                CombName = record.RecordComb.CombName,
                ItemCode = b.ItemCode,
                ItemName = b.ItemName,
                ItemResult = b.ItemResult,
                ResultType = b.ResultType,
                LowerLimit = b.LowerLimit,
                UpperLimit = b.UpperLimit,
                OperCode = _httpContextUser.UserId,
            }).ToList();
            QuartzTaskHelper.AddJobAsync<IMajorPositiveService>(nameof(IMajorPositiveService.SaveMajorPositiveWhileSaveRecord), recCombs).ConfigureAwait(false);
        }

        /// <summary>
        /// 删除组合及图文报告记录
        /// </summary>
        /// <param name="record"></param>
        /// <returns></returns>
        public void DeleteRecordCombAndImage(DelRecordComb record, out PeRecordComb recordComb, out PeRecordItem[] recordItems, bool saveLog = true)
        {
            var regInfo = _registerRepository.ReadRegister(record.RegNo).First() ?? throw new BusinessException("体检信息为空或已删除");

            //删除附加结果
            _occupationalRecordService.DeleteCombExtensionResult(regInfo, record.RegCombId);
            // 删除旧记录
            DeleteRecordComb(regInfo, record.RegCombId, out var peRecordComb, out var peRecordItems);
            recordComb = peRecordComb;
            recordItems = peRecordItems;
            // 删除图文报告
            DeleteReportGraphicText(regInfo, record.RegCombId);

            // 删除影像部位图（不删除影像采集图）
            // 无
            if (saveLog)
            {
                _logBusinessNewService.RecordDeleteLog(record.RegNo, recordComb.CombCode);
                _logBusinessNewService.SaveLogs();
            }
            // 更新体检状态
            UpdatePeStatus(regInfo);
        }

        /// <summary>
        /// 按组合获取定义的项目结果，用于医生工作站录入项目结果时选择
        /// </summary>
        /// <param name="combCode"></param>
        /// <returns></returns>
        public List<DefaultItemResultDisease> GetDefaultItemResultByComb(string combCode)
        {
            var defaultItemResultGroupBy = _recordRepository.GetDefaultItemResult()
                .Where((a, b, c) => a.CombCode == combCode)
                .OrderBy((a, b, c) => b.DisplayOrder)
                .Select((a, b, c) => new
                {
                    a.ItemCode,
                    ResultId = SqlFunc.IsNull(b.ResultId, 0),
                    ResultDesc = SqlFunc.IsNull(b.ResultDesc, ""),
                    DiseaseCode = SqlFunc.IsNull(c.DiseaseCode, ""),
                    DiseaseName = SqlFunc.IsNull(c.DiseaseName, ""),
                    AbnormalType = SqlFunc.IsNull(b.AbnormalType, AbnormalType.正常)
                })
                .ToList()
                .Distinct()
                .GroupBy(a => new
                {
                    a.ItemCode
                });

            var defaultItemResults = new List<DefaultItemResultDisease>();

            foreach (var item in defaultItemResultGroupBy)
            {
                var defaultItemResult = new DefaultItemResultDisease
                {
                    ItemCode = item.Key.ItemCode,
                    ResultDiseases = item.Where(a => a.ResultId != 0)
                    .Select(a => new DefaultItemResultDisease.ResultDisease
                    {
                        ResultId = a.ResultId,
                        ResultDesc = a.ResultDesc.ToString(),
                        DiseaseCode = a.DiseaseCode,
                        DiseaseName = a.DiseaseName,
                        AbnormalType = a.AbnormalType
                    })
                    .ToList()
                };

                defaultItemResults.Add(defaultItemResult);
            }

            return defaultItemResults;
        }

        /// <summary>
        /// 按项目获取定义的项目结果，用于医生工作站录入项目结果时选择
        /// </summary>
        /// <param name="itemCode"></param>
        /// <returns></returns>
        public DefaultItemResultDisease GetDefaultItemResultByItem(string itemCode)
        {
            var defaultItemResults = _recordRepository.GetDefaultItemResult()
                .Where(a => a.ItemCode == itemCode)
                .OrderBy((a, b, c) => b.DisplayOrder)
                .Select((a, b, c) => new
                {
                    ResultId = SqlFunc.IsNull(b.ResultId, 0),
                    ResultDesc = SqlFunc.IsNull(b.ResultDesc, ""),
                    DiseaseCode = SqlFunc.IsNull(c.DiseaseCode, ""),
                    DiseaseName = SqlFunc.IsNull(c.DiseaseName, ""),
                    AbnormalType = SqlFunc.IsNull(b.AbnormalType, AbnormalType.正常)
                })
                .ToList()
                .Distinct();

            var defaultItemResultDisease = new DefaultItemResultDisease
            {
                ItemCode = itemCode,
                ResultDiseases = defaultItemResults.Where(a => a.ResultId != 0)
                .Select(a => new DefaultItemResultDisease.ResultDisease
                {
                    ResultId = a.ResultId,
                    ResultDesc = a.ResultDesc.ToString(),
                    DiseaseCode = a.DiseaseCode,
                    DiseaseName = a.DiseaseName,
                    AbnormalType = a.AbnormalType
                })
                .ToList()
            };

            return defaultItemResultDisease;
        }

        /// <summary>
        /// 获取项目历史结果
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        public ItemHistoryResult[] GetItemHistoryResult(ItemHistoryQuery query)
        {
            //通过证件号查出历年体检号
            var regNos = _registerRepository.ReadRegister()
                            .Where(x => x.CardNo == query.CardNo)
                            .Select(x => x.RegNo).ToArray();


            return _recordRepository.ReadItemResult(regNos)
                  .Where((reg, recComb, recItem) => recItem.ItemCode == query.ItemCode)
                  .Select((reg, recComb, recItem) => new ItemHistoryResult
                  {
                      ExamTime = recComb.ExamTime,
                      AbnormalType = recItem.AbnormalType,
                      ItemResult = recItem.ItemResult
                  })
                  .ToArray();
        }

        /// <summary>
        /// 编辑项目标签
        /// </summary>
        /// <param name="editItem"></param>
        /// <returns></returns>
        /// <exception cref="Exception"></exception>
        public EditItemTagCallback EditItemTag(EditItemTag editItem)
        {
            return editItem.EditMode switch
            {
                ItemTagEditMode.新增 => AddItemTag(editItem),
                ItemTagEditMode.修改 => AlterItemTag(editItem),
                ItemTagEditMode.删除 => DeleteItemTag(editItem),
                _ => throw new Exception("操作模式不正确"),
            };
        }

        /// <summary>
        /// 获取组合情况
        /// </summary>
        /// <param name="regNo">体检号</param>
        /// <param name="msg">提示信息</param>
        public CombsInfo ReadCombInfo(string regNo, ref string msg)
        {
            var register = _registerRepository.ReadRegister(regNo).First();
            if (register == null)
            {
                msg = "暂无当前体检号记录";
                return null;
            }

            //获取体检号的组合
            var query = _recordRepository.ReadCombsInfo(regNo)
                        .Select((regComb, recComb) => new Comb
                        {
                            RegCombId = regComb.Id,
                            RegNo = regComb.RegNo,
                            CombCode = regComb.CombCode,
                            CombName = regComb.CombName,
                            DoctorName = recComb.DoctorName
                        }).ToArray();

            return new CombsInfo
            {
                UnfinishCombs = query.Where(x => string.IsNullOrWhiteSpace(x.DoctorName)).ToArray(),// unfinish
                FinishCombs = query.Where(x => x.DoctorName != "弃检" && !string.IsNullOrWhiteSpace(x.DoctorName)).ToArray(),// finish
                RefuseCombs = query.Where(x => x.DoctorName == "弃检").ToArray()// refuse
            };
        }

        /// <summary>
        /// 弃检组合
        /// </summary>
        /// <param name="abandonComb"></param>
        /// <param name="msg"></param>
        /// <returns></returns>
        public bool AbandonComb(AbandonCombData abandonComb, ref string msg)
        {
            //通过体检号,登记组合Id查询出组合
            var recordCombs = _registerRepository.ReadRegisterCombs(abandonComb.RegNo, abandonComb.RegCombId)
                .Select(x => new RecordComb
                {
                    RegNo = x.RegNo,
                    RegCombId = x.Id,
                    CombCode = x.CombCode,
                    CombName = x.CombName,
                    ClsCode = x.ClsCode,
                    ExamDeptCode = x.ExamDeptCode,
                }).ToList();

            return SaveAbandonComb(abandonComb, recordCombs, ref msg);
        }

        /// <summary>
        /// 取消 弃检组合
        /// </summary>
        /// <param name="regNo"></param>
        /// <param name="regCombId"></param>
        /// <param name="msg"></param>
        /// <returns></returns>
        public bool CancleAbandonComb(string regNo, long regCombId, ref string msg)
        {
            //查出分表依据
            var regInfo = _registerRepository.ReadRegister(regNo).First();
            if (regInfo == null)
            {
                msg = "暂无此人员数据";
                return false;
            }

            if (!string.IsNullOrEmpty(regInfo.GuidanceRecycler))
            {
                msg = "收表后不能进行拒检操作!";
                return false;
            }

            //查出组合记录
            var combRecord = _peRecordCombRepository.First(regInfo.RegisterTime, x => x.RegNo == regNo && x.RegCombId == regCombId);
            if (combRecord == null)
            {
                msg = "组合数据为空!";
                return false;
            }

            //查出组合小结
            var combTag = _peRecordCombTagRepository.FindAll(regInfo.RegisterTime, x => x.RecCombId == combRecord.Id).ToArray();

            //事务
            _dataTranRepository.ExecTran(() =>
            {
                //删除PeRecordCombTag
                if (combTag.Length > 0)
                    _peRecordCombTagRepository.SplitTableDelete(combTag);

                //删除PeRecordComb  
                _peRecordCombRepository.SplitTableDelete(combRecord);
            });

            UpdatePeStatus(regInfo);
            return true;
        }

        /// <summary>
        /// 根据化验类型拒检组合
        /// </summary>
        /// <param name="abandonComb"></param>
        /// <param name="msg"></param>
        /// <returns></returns>
        public bool RefuseCombsByAssayType(AbandonCombData abandonComb, ref string msg)
        {
            //查出符合化验类型的组合
            var recordCombs = _recordRepository.ReadCombBarType(abandonComb.RegNo)
                .Where((regComb, recComb, comb, barcode)
                    => string.IsNullOrWhiteSpace(recComb.DoctorName) && barcode.SampCode == abandonComb.SampCode)
                .Select(regComb => new RecordComb
                {
                    RegNo = regComb.RegNo,
                    RegCombId = regComb.Id,
                    CombCode = regComb.CombCode,
                    CombName = regComb.CombName,
                    ClsCode = regComb.ClsCode,
                    ExamDeptCode = regComb.ExamDeptCode,
                }).ToList();

            return SaveAbandonComb(abandonComb, recordCombs, ref msg);
        }

        #region 电话联系记录
        /// <summary>
        /// 获取电话联系记录
        /// </summary>
        /// <param name="regNo">体检号</param>
        /// <returns></returns>
        public RecordContact[] ReadContactRecord(string regNo)
        {
            return _recordContactRepository.FindAll(x => x.RegNo == regNo).ToArray();
        }

        /// <summary>
        /// 新增电话联系记录
        /// </summary>
        /// <param name="record"></param>
        /// <returns></returns>
        public bool CreateContactRecord(RecordContact record, ref string msg)
        {
            try
            {
                //查出相同内容电话记录
                if (_recordContactRepository.Any(x => x.RegNo == record.RegNo && x.Content == record.Content))
                {
                    msg = "联系记录已存在";
                    return false;
                }

                record.CreateTime = DateTime.Now;
                _recordContactRepository.Insert(record);
                return true;
            }
            catch (Exception e)
            {
                msg = e.Message;
                return false;
            }
        }

        /// <summary>
        /// 删除电话联系记录
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public bool DeleteContactRecord(int id)
        {
            return _recordContactRepository.DeleteById(id);
        }
        #endregion

        #region 图文报告（第三方系统）
        /// <summary>
        /// 获取图文报告列表（来自第三方系统）
        /// </summary>
        /// <param name="regNo"></param>
        /// <returns></returns>
        public List<ReportGraphicTextFileInfo> GetReportGraphicTextList(string regNo)
        {
            return _reportGraphicTextService.GetReportGraphicTextList(regNo);
        }
        /// <summary>
        /// 删除图文报告
        /// </summary>
        /// <param name="regNo"></param>
        /// <param name="regCombId"></param>
        /// <exception cref="BusinessException"></exception>
        public void DeleteReportGraphicText(string regNo, long regCombId)
        {
            var regInfo = _registerRepository.ReadRegister(regNo).First() ?? throw new BusinessException("体检信息为空或已删除");
            DeleteReportGraphicText(regInfo, regCombId);
        }
        /// <summary>
        /// 删除图文报告
        /// </summary>
        /// <param name="regInfo"></param>
        /// <param name="regCombId"></param>
        private void DeleteReportGraphicText(PeRegister regInfo, long regCombId)
        {
            _reportGraphicTextService.DeleteReportGraphicText(regInfo, regCombId);
        }
        #endregion

        #region 体检影像采集（未完善）
        /// <summary>
        /// 获取pacs科室代码
        /// </summary>
        /// <returns></returns>
        public string GetPacsDept()
        {
            return _systemParameterService.UltrasonicDept;
        }

        /// <summary>
        /// 获取体检者的pacs组合信息
        /// </summary>
        /// <param name="regNo"></param>
        /// <param name="deptCode"></param>
        /// <returns></returns>
        public List<PacsComb> GetPacsComb(string regNo, string deptCode)
        {
            return _registerRepository.ReadRegisterCombs(regNo)
                .Where(a => !a.IsPayBySelf || a.PayStatus == PayStatus.收费)
                .Where(a => a.ExamDeptCode == deptCode && a.ReportShow)
                .Select(a => new PacsComb
                {
                    RegCombId = a.Id,
                    CombCode = a.CombCode,
                    CombName = a.CombName
                })
                .ToList();
        }

        /// <summary>
        /// 保存体检者pacs图像
        /// </summary>
        /// <param name="formFiles"></param>
        /// <param name="operatorCode"></param>
        /// <param name="regNo"></param>
        /// <param name="deptCode"></param>
        /// <param name="regCombId"></param>
        /// <param name="msg"></param>
        /// <returns></returns>
        public List<string> SaveCollectedPacsImage(IFormFileCollection formFiles, string operatorCode, string regNo, string deptCode, long? regCombId, ref string msg)
        {
            #region 判断有没有传文件
            if (formFiles.Count == 0)
            {
                msg = $"没有上传图片文件";
                return null;
            }
            #endregion

            #region 判断组合是否存在
            string combCode = null;

            if (regCombId != null)
            {
                var combCodeQuery = _registerRepository
                .ReadRegisterCombs(regNo, (long)regCombId)
                .Select(a => new
                {
                    a.CombCode
                })
                .First();

                if (combCodeQuery == null)
                {
                    msg = $"查询不到 体检号：{regNo}，登记组合id：{regCombId} 的组合记录";
                    return null;
                }

                combCode = combCodeQuery.CombCode;
            }
            #endregion

            var imagePaths = new List<string>();//返回文件路径

            var baseDirectory = AppContext.BaseDirectory;

            var imageFolder = "pacsImages";

            var directory = Path.Combine(baseDirectory, $@"wwwroot\{imageFolder}", regNo);

            //生成文件夹
            if (!Directory.Exists(directory))
            {
                Directory.CreateDirectory(directory);
            }

            #region 生成文件
            foreach (var file in formFiles)
            {
                //文件目录
                var fileExtension = Path.GetExtension(file.FileName);
                var fileName = Guid.NewGuid().ToString("N") + fileExtension;
                var filePath = Path.Combine(directory, fileName);

                while (File.Exists(filePath))
                {
                    fileName = Guid.NewGuid().ToString("N") + fileExtension;
                    filePath = Path.Combine(directory, fileName);
                }

                using (var stream = new FileStream(filePath, FileMode.Create, FileAccess.Write))
                {
                    file.CopyToAsync(stream).Wait();
                    stream.Flush();
                }

                imagePaths.Add(Path.Combine(imageFolder, regNo, fileName));//虚拟路径
            }
            #endregion

            #region 保存数据
            var peImageCollectList = new List<PeImageCollect>();

            foreach (var item in imagePaths)
            {
                var peImageCollectNew = new PeImageCollect
                {
                    RegNo = regNo,
                    DeptCode = deptCode,
                    RegCombId = regCombId,
                    CombCode = combCode,
                    ImagePath = item,
                    CollectOperator = operatorCode,
                    CollectTime = DateTime.Now
                };

                peImageCollectList.Add(peImageCollectNew);
            }

            _dataTranRepository.ExecTran(() =>
            {
                //保存图像采集表
                _peImageCollectRepository.Insert(peImageCollectList);
            });
            #endregion

            return imagePaths;
        }

        /// <summary>
        /// 获取体检者pacs图像
        /// </summary>
        /// <param name="regNo"></param>
        /// <param name="deptCode"></param>
        /// <param name="regCombId"></param>
        /// <returns></returns>
        public PeImageCollect[] GetCollectedPacsImage(string regNo, string deptCode, long? regCombId)
        {
            var data = _peImageCollectRepository.FindAll(a => a.RegNo == regNo && a.DeptCode == deptCode);
            if (regCombId == 0)
                return data.OrderBy(a => a.RegCombId)
                 .ThenByDescending(a => a.RecImageId)
                 .ThenBy(a => a.CollectTime)
                 .ToArray();
            else
                return data
                .WhereIF(regCombId != null, a => a.RegCombId == regCombId)
                .ToArray();
        }

        /// <summary>
        /// 获取影像图例列表
        /// </summary>
        /// <param name="regNo">体检号</param>
        /// <returns>RecordImageDtos</returns>
        public RecordImageDto[] GetRecordImages(string regNo)
        {
            if (!CheckHelper.VerifyRegNo(regNo, out _)) return default;

            var recordImages = _peRecordImageRepository
                .FindAll(a => a.RegNo == regNo).ToArray();

            if (recordImages.IsNullOrEmpty()) return default;

            var regCombs = _registerRepository.ReadRegisterCombs(regNo)
                 .Where(x => SqlFunc.ContainsArray(recordImages.Select(a => a.RegCombId).ToArray(), x.Id))
                 .Select(x => new { x.Id, x.CombCode, x.CombName })
                 .ToArray();
            var path = Appsettings.GetSectionValue(ResxCommon.ConfigFileServiceUrl);

            return regCombs.Select(x =>
            new RecordImageDto(
                x.Id,
                x.CombCode,
                x.CombName,
                recordImages.Where(a => a.RegCombId == x.Id)
                .Select(a => new Uri(Path.Combine(path, a.ImagePath)).ToString()).ToArray()
            )).ToArray();
        }

        /// <summary>
        /// 删除体检者pacs图像
        /// </summary>
        /// <param name="operatorCode"></param>
        /// <param name="regNo"></param>
        /// <param name="collectIds"></param>
        /// <param name="msg"></param>
        /// <returns></returns>
        public bool DeleteCollectedPacsImage(string operatorCode, string regNo, long[] collectIds, ref string msg)
        {
            var peImageCollects = _peImageCollectRepository
                .FindAll(a => a.RegNo == regNo && SqlFunc.ContainsArray(collectIds, a.Id))
                .ToList();

            #region 限制
            var isExist = peImageCollects.Any(a => a.CollectOperator != operatorCode);

            if (isExist)
            {
                msg = $"不能删除其他人采集的图像";

                return false;
            }
            #endregion

            var imagePaths = peImageCollects.Select(a => a.ImagePath).ToList();

            _dataTranRepository.ExecTran(() =>
            {
                //删除记录
                _peImageCollectRepository.Delete(peImageCollects);

                //删除文件
                foreach (var item in imagePaths)
                {
                    var path = Path.Combine(AppContext.BaseDirectory, item[1..]);

                    if (File.Exists(path))
                    {
                        File.Delete(path);
                    }
                }
            });

            return true;
        }

        /// <summary>
        /// 选中采集的pacs图像指定组合
        /// </summary>
        /// <param name="operatorCode"></param>
        /// <param name="regNo"></param>
        /// <param name="regCombId"></param>
        /// <param name="collectIds"></param>
        /// <param name="msg"></param>
        /// <returns></returns>
        public bool CheckCollectedPacsImage4Comb(string operatorCode, string regNo, long regCombId, long[] collectIds, ref string msg)
        {
            #region 判断组合是否存在
            var combCodeQuery = _registerRepository
                .ReadRegisterCombs(regNo, regCombId)
                .Select(a => new
                {
                    a.CombCode
                })
                .First();

            if (combCodeQuery == null)
            {
                msg = $"查询不到 体检号：{regNo}，登记组合id：{regCombId} 的组合记录";
                return false;
            }
            #endregion

            var imagePaths = new List<string>();//返回路径

            #region 更新数据
            var combCode = combCodeQuery.CombCode;

            var peImageCollects = _peImageCollectRepository
                .FindAll(a => a.RegNo == regNo && SqlFunc.ContainsArray(collectIds, a.Id))
                .ToList();

            #region 限制
            var isExist = peImageCollects.Any(a => !string.IsNullOrEmpty(a.CheckOperator) && a.CheckOperator != operatorCode);

            if (isExist)
            {
                msg = $"不能选择其他人选过的图像";

                return false;
            }
            #endregion

            var datetime = DateTime.Now;

            foreach (var peImageCollect in peImageCollects)
            {
                peImageCollect.RegCombId = regCombId;
                peImageCollect.CombCode = combCode;
                peImageCollect.CheckOperator = operatorCode;
                peImageCollect.CheckTime = datetime;

                imagePaths.Add(peImageCollect.ImagePath);
            }

            _dataTranRepository.ExecTran(() =>
            {
                _peImageCollectRepository.Update(peImageCollects);
            });
            #endregion

            return true;
        }

        /// <summary>
        /// 组合取消选中采集的pacs图像
        /// </summary>
        /// <param name="operatorCode"></param>
        /// <param name="collectIds"></param>
        /// <param name="msg"></param>
        /// <returns></returns>
        public bool UncheckCollectedPacsImage4Comb(string operatorCode, long[] collectIds, ref string msg)
        {
            //用于组合赋空
            var peImageCollects = _peImageCollectRepository
                .FindAll(a => SqlFunc.ContainsArray(collectIds, a.Id))
                .ToList();

            #region 限制
            var isExist = peImageCollects.Any(a => !string.IsNullOrEmpty(a.CheckOperator) && a.CheckOperator != operatorCode);

            if (isExist)
            {
                msg = $"不能取消其他人选择的图像";

                return false;
            }
            #endregion

            //返回路径
            var imagePaths = peImageCollects
                .Select(a => a.ImagePath)
                .ToList();

            //图像记录id，用于删除图像记录
            var recImageIds = peImageCollects
                .Select(a => a.RecImageId)
                .ToArray();

            //用于删除图像记录
            var peRecordImages = _peRecordImageRepository
                .FindAll(a => SqlFunc.ContainsArray(recImageIds, a.Id))
                .ToList();

            foreach (var item in peImageCollects)
            {
                item.RegCombId = null;
                item.CombCode = null;
                item.RecImageId = null;
                item.CheckOperator = null;
                item.CheckTime = null;
            }

            _dataTranRepository.ExecTran(() =>
            {
                _peImageCollectRepository.Update(peImageCollects);

                _peRecordImageRepository.Update(peRecordImages);
            });

            return true;
        }

        /// <summary>
        /// 选中组合中的采集的pacs图像用于报告打印
        /// </summary>
        /// <param name="operatorCode"></param>
        /// <param name="collectIds"></param>
        /// <param name="msg"></param>
        /// <returns></returns>
        public bool CheckPacsImage2Print(string operatorCode, long[] collectIds, ref string msg)
        {
            var peImageCollects = _peImageCollectRepository
                .FindAll(a => SqlFunc.ContainsArray(collectIds, a.Id) && !SqlFunc.IsNullOrEmpty(a.RegCombId) && a.RecImageId == null)
                .ToList();

            #region 限制

            #endregion

            //图像记录表
            var peRecordImagesNews = new List<PeRecordImage>();

            var datetime = DateTime.Now;

            foreach (var item in peImageCollects)
            {
                item.RecImageId = _noGeneration.NextSnowflakeId();//图像记录id

                peRecordImagesNews.Add(new PeRecordImage
                {
                    Id = (long)item.RecImageId,
                    RegNo = item.RegNo,
                    RegCombId = (long)item.RegCombId,
                    CombCode = item.CombCode,
                    ImagePath = item.ImagePath,
                    OperatorCode = operatorCode,
                    OperateTime = datetime
                });
            }

            _dataTranRepository.ExecTran(() =>
            {
                _peImageCollectRepository.Update(peImageCollects);

                _peRecordImageRepository.Insert(peRecordImagesNews);
            });

            return true;
        }

        /// <summary>
        /// 取消选中组合中的采集的pacs图像用于报告打印
        /// </summary>
        /// <param name="operatorCode"></param>
        /// <param name="recImageIds"></param>
        /// <param name="msg"></param>
        /// <returns></returns>
        public bool UncheckPacsImage2Print(string operatorCode, long[] recImageIds, ref string msg)
        {
            #region 限制
            var isExist = _peRecordImageRepository.Any(a => SqlFunc.ContainsArray(recImageIds, a.Id) && a.OperatorCode != operatorCode);

            if (isExist)
            {
                msg = "不能取消其他人选择的图像";

                return false;
            }
            #endregion

            var peImageCollects = _peImageCollectRepository
                .FindAll(a => SqlFunc.ContainsArray(recImageIds, a.RecImageId))
                .ToList();

            foreach (var item in peImageCollects)
            {
                item.RecImageId = null;
            }

            _dataTranRepository.ExecTran(() =>
            {
                _peImageCollectRepository.Update(peImageCollects);

                _peRecordImageRepository.Delete(a => SqlFunc.ContainsArray(recImageIds, a.Id));
            });

            return true;
        }

        /// <summary>
        /// 获取用于报告打印的pacs图像
        /// </summary>
        /// <param name="regNo"></param>
        /// <param name="regCombId"></param>
        /// <returns></returns>
        public List<string> GetPacsImage2Print(string regNo, long regCombId)
        {
            return _peRecordImageRepository
                .FindAll(a => a.RegNo == regNo && a.RegCombId == regCombId)
                .Select(a => a.ImagePath)
                .ToList();
        }

        /// <summary>
        /// 获取打印图例
        /// </summary>
        /// <param name="regNo"></param>
        /// <returns></returns>
        public PrintImage[] ReadPrintImage(string regNo)
        {
            var list = _recordRepository.ReadPeRecordImage(regNo)
                    .Select(x => new PrintImage
                    {
                        CombName = SqlFunc.Subqueryable<CodeItemComb>().Where(y => y.CombCode == x.CombCode).Select(x => x.CombName),
                        ImagePath = Appsettings.GetSectionValue("WebserviceHub:FileServiceUrl") + x.ImagePath
                    }).ToList();

            List<PrintImage> result = new List<PrintImage>();
            int i = 1;
            string combName = string.Empty;

            foreach (var item in list)
            {
                if (combName == item.CombName)
                    i++;
                else
                    i = 1;

                PrintImage image = new PrintImage();
                var array = list.Where(x => x.CombName == item.CombName).Select(x => x.CombName).ToArray();
                if (array.Length != 1)
                    image.CombName = item.CombName + i;
                else
                    image.CombName = item.CombName;

                image.ImagePath = item.ImagePath;
                result.Add(image);
                combName = item.CombName;
            }

            return result.ToArray();
        }
        #endregion

        #region 本地私有方法
        /// <summary>
        /// 读取项目结果记录(无项目结果数据)
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        /// <exception cref="ArgumentNullException"></exception>
        private List<RecordItem> ReadDefaultItemData(RecordCombQuery query)
        {
            if (query.RegNo is null)
                throw new ArgumentNullException(message: $"{nameof(query.RegNo)}不能为空", null);

            #region 读取体检号数据
            var regInfo = _registerRepository.ReadRegister(query.RegNo)
                          .Select(x => new
                          {
                              x.Sex,
                              x.Age,
                              x.CardNo,
                              x.RegisterTime
                          })
                          .First();
            #endregion

            #region 获取组合包含的项目数据
            var regCombItems = _recordRepository.ReadCombItem(regInfo.RegisterTime, query.RegCombId)
                   .Select((regComb, mapItemComb, item, recComb, recItem) => new
                   {
                       item.ItemCode,
                       ItemName = SqlFunc.IsNull(recItem.ItemName, item.ItemName),
                       item.ReferenceValue,
                       item.Unit,
                       SortIndex = SqlFunc.IsNull(recItem.SortIndex, item.SortIndex),
                       recCombId = SqlFunc.IsNull(recComb.Id, 0),
                       ResultType = item.ValueType,
                       AbnormalType = SqlFunc.IsNull(recItem.AbnormalType, AbnormalType.正常),
                   }).ToArray();
            #endregion

            #region 获取项目的参考范围
            var itemBound = _recordRepository.ReadItemBound()
                          .Where(itemBound => SqlFunc.ContainsArray(regCombItems.Select(x => x.ItemCode).ToArray(), itemBound.ItemCode))
                          .Select((itemBound, bound) => new
                          {
                              ItemCode = itemBound.ItemCode ?? "",
                              LowerLimit = itemBound.LowerLimit ?? "",
                              UpperLimit = itemBound.UpperLimit ?? "",
                              DangerLowerLimit = itemBound.DangerLowerLimit ?? "",
                              DangerUpperLimit = itemBound.DangerUpperLimit ?? "",
                              PositiveLowerLimit = itemBound.PositiveLowerLimit ?? "",
                              PositiveUpperLimit = itemBound.PositiveUpperLimit ?? "",
                              bound.Sex,
                              bound.LowerAgeLimit,
                              bound.UpperAgeLimit
                          }).ToList();
            #endregion

            #region 根据模板判断是否需要上次的项目结果
            //接收模型
            var lastItemResult = new List<LastItemResult>();
            /*  上次的项目结果并不是指前一次体检的项目结果
             *  张三 
             *  第一次体检  做了  A B C D E 5个项目
             *  第二次体检  做了  A   C D E 4个项目
             *  第三次体检  做了  A B C D E 5个项目
             *  假设查看第三次体检数据的B项目结果, 那B项目的上次结果,是最近一次有B项目的体检的结果(也就是第一次的B项目结果)
             */
            if (query.Template != DoctorStationTemplate.多选 && !string.IsNullOrEmpty(regInfo.CardNo))
            {
                //得出历年的体检号
                var regNos = _registerRepository.ReadRegister()
                             .Where(x => x.CardNo == regInfo.CardNo && x.RegNo != query.RegNo)
                             .Select(x => x.RegNo)
                             .ToArray();

                //获取本次体检的项目
                var itemArray = regCombItems.Select(x => x.ItemCode).ToArray();

                //查询本次体检项目的所有历史结果
                lastItemResult = _recordRepository.ReadItemResult(regNos)
                                .Where((reg, recComb, recItem) => SqlFunc.ContainsArray(itemArray, recItem.ItemCode))
                                .OrderBy(reg => reg.RegisterTime, OrderByType.Desc)
                                .Select((reg, recComb, recItem) => new LastItemResult
                                {
                                    ItemCode = recItem.ItemCode,
                                    ItemResult = recItem.ItemResult
                                })
                                .ToList();
            }
            #endregion

            //组装数据
            var recItems = new List<RecordItem>();
            foreach (var item in regCombItems)
            {
                //获取项目参考范围
                var itembound = GetItemBound(item.ItemCode);
                recItems.Add(new RecordItem
                {
                    ItemCode = item.ItemCode,
                    ItemName = item.ItemName,
                    ReferenceValue = item.ReferenceValue,
                    ResultType = item.ResultType,
                    Hint = string.Empty,
                    AbnormalType = item.AbnormalType,
                    Unit = item.Unit,
                    SortIndex = item.SortIndex,
                    LowerLimit = itembound?.LowerLimit ?? "",
                    UpperLimit = itembound?.UpperLimit ?? "",
                    DangerLowerLimit = itembound?.DangerLowerLimit ?? "",
                    DangerUpperLimit = itembound?.DangerUpperLimit ?? "",
                    NumberResult = item.ReferenceValue,
                    ItemTags = GetItemResult(item.ReferenceValue, item.ItemCode),
                    LastItemResult = GetLastItemResult(item.ItemCode)
                });
            }

            return recItems
                   .OrderBy(x => x.SortIndex)
                   .ToList();

            #region 获取项目结果
            List<ItemTag> GetItemResult(string referenceValue, string itemCode)
            {
                if (string.IsNullOrEmpty(referenceValue))
                    return new();
                var itembound = GetItemBound(itemCode);
                return regCombItems.Where(x => x.ItemCode == itemCode).Select(x => new ItemTag
                {
                    Id = _noGeneration.NextSnowflakeId(),
                    ItemCode = x.ItemCode,
                    Tag = x.ReferenceValue,
                    AbnormalType = AbnormalType.正常,
                    ResultId = -1,
                    IsCalcResul = false,
                    CalcItemTagIds = new List<long>(),
                    LowerLimit = itembound?.LowerLimit ?? "",
                    UpperLimit = itembound?.UpperLimit ?? "",
                }).ToList();
            }
            #endregion

            #region 获取上次项目结果
            string GetLastItemResult(string ItemCode)
            {
                if (lastItemResult == null || lastItemResult.Count <= 0)
                    return "";

                return lastItemResult.Where(x => x.ItemCode == ItemCode).Select(x => x.ItemResult).FirstOrDefault();
            }
            #endregion

            #region 获取项目参考范围
            CodeItemBound GetItemBound(string ItemCode)
            {
                return itemBound
                    .Where(x => x.ItemCode == ItemCode)
                    .Where(x => x.Sex == Sex.通用 || x.Sex == regInfo.Sex)
                    .Where(x => int.Parse(x.LowerAgeLimit) <= regInfo.Age && int.Parse(x.UpperAgeLimit) >= regInfo.Age)
                    .Select(x => new CodeItemBound
                    {
                        LowerLimit = x.LowerLimit,
                        UpperLimit = x.UpperLimit,
                        DangerLowerLimit = x.DangerLowerLimit,
                        DangerUpperLimit = x.DangerUpperLimit,
                        PositiveLowerLimit = x.PositiveLowerLimit,
                        PositiveUpperLimit = x.PositiveUpperLimit
                    })
                    .FirstOrDefault();
            }
            #endregion
        }

        /// <summary>
        /// 读取项目结果记录(已有项目结果数据)
        /// </summary>
        /// <param name="regNo">体检号</param>
        /// <returns></returns>
        private List<RecordItem> ReadItemData(RecordCombQuery query)
        {
            // 读取体检号数据
            var regInfo = _registerRepository.ReadRegister(query.RegNo)
                .Select(x => new
                {
                    x.Sex,
                    x.Age,
                    x.CardNo,
                    x.RegisterTime
                })
                .First();

            // 读取项目结果记录
            var regCombItems = _recordRepository.ReadCombItemRecord(query.RegNo)
                .Where((regComb, recordComb, recordItem) => regComb.Id == query.RegCombId)
                .Where((regComb, recordComb, recordItem) => SqlFunc.HasNumber(recordItem.Id))
                .Select((regComb, recordComb, recordItem) => new
                {
                    recCombId = recordComb.Id,
                    recordItem.ItemCode,
                    recordItem.ItemName,
                    recordItem.Unit,
                    recordItem.Hint,
                    recordItem.LowerLimit,
                    recordItem.UpperLimit,
                    recordItem.ReferenceRange,
                    recordItem.SortIndex,
                    recordItem.ResultType,
                    recordItem.AbnormalType,
                }).ToArray();
            if (regCombItems.IsNullOrEmpty())
                return new();

            // 获取项目结果标签记录
            var recItemTags = _recordRepository.ReadItemResult(regInfo.RegisterTime, regCombItems.First().recCombId)
                .Select((comb, item, itemTag) => new RecItemTag
                {
                    Hint = item.Hint,
                    ItemCode = item.ItemCode,
                    ItemTagId = itemTag.Id,
                    ItemTag = itemTag.ItemTag,
                    BindResultId = itemTag.BindResultId,
                    IsCalcResul = itemTag.IsCalcResul,
                    CalcItemTagIds = itemTag.CalcItemTagIds,
                    AbnormalType = itemTag.AbnormalType,
                    LowerLimit = item.LowerLimit,
                    UpperLimit = item.UpperLimit,
                    ReferenceRange = item.ReferenceRange,
                })
                .ToArray()
                .GroupBy(x => x.ItemCode)
                .ToDictionary(x => x.Key, x => x.ToList());

            #region 根据模板判断是否需要上次的项目结果
            //接收模型
            var lastItemResult = new List<LastItemResult>();
            /*  上次的项目结果并不是指前一次体检的项目结果
             *  张三 
             *  第一次体检  做了  A B C D E 5个项目
             *  第二次体检  做了  A   C D E 4个项目
             *  第三次体检  做了  A B C D E 5个项目
             *  假设查看第三次体检数据的B项目结果, 那B项目的上次结果,是最近一次有B项目的体检的结果(也就是第一次的B项目结果)
             */
            if (query.Template != DoctorStationTemplate.多选 && !string.IsNullOrEmpty(regInfo.CardNo))
            {
                //得出历年的体检号
                var regNos = _registerRepository.ReadRegister()
                             .Where(x => x.CardNo == regInfo.CardNo && x.RegNo != query.RegNo)
                             .Select(x => x.RegNo)
                             .ToArray();

                //获取本次体检的项目
                var itemArray = regCombItems.Select(x => x.ItemCode).ToArray();

                //查询本次体检项目的所有历史结果
                lastItemResult = _recordRepository.ReadItemResult(regNos)
                                .Where((reg, recComb, recItem) => SqlFunc.ContainsArray(itemArray, recItem.ItemCode))
                                .OrderBy(reg => reg.RegisterTime, OrderByType.Desc)
                                .Select((reg, recComb, recItem) => new LastItemResult
                                {
                                    ItemCode = recItem.ItemCode,
                                    ItemResult = recItem.ItemResult
                                })
                                .ToList();
            }
            #endregion

            //组装数据
            var recItems = new List<RecordItem>();
            foreach (var item in regCombItems)
            {
                recItems.Add(new RecordItem
                {
                    ItemCode = item.ItemCode,
                    ItemName = item.ItemName,
                    ResultType = item.ResultType,
                    Hint = item.Hint,
                    AbnormalType = item.AbnormalType,
                    Unit = item.Unit,
                    SortIndex = item.SortIndex,
                    LowerLimit = item.LowerLimit ?? "",
                    UpperLimit = item.UpperLimit ?? "",
                    ReferenceRange = item.ReferenceRange,
                    DangerLowerLimit = "",
                    DangerUpperLimit = "",
                    NumberResult = GetNumberResult(item.ItemCode, item.ResultType),
                    ItemTags = GetItemResult(item.ItemCode),
                    LastItemResult = GetLastItemResult(item.ItemCode)
                });
            }

            return recItems
                   .OrderBy(x => x.SortIndex)
                   .ToList();

            #region 获取数值结果
            string GetNumberResult(string itemCode, ValueType valueType)
            {
                if (valueType == ValueType.数值 && recItemTags.TryGetValue(itemCode, out var value))
                    return value.First().ItemTag;
                return string.Empty;
            }
            #endregion

            #region 获取项目结果
            List<ItemTag> GetItemResult(string itemCode)
            {
                if (recItemTags.TryGetValue(itemCode, out var value))
                {
                    return value.Select(x => new ItemTag
                    {
                        ItemCode = x.ItemCode,
                        Id = x.ItemTagId,
                        Tag = x.ItemTag,
                        AbnormalType = x.AbnormalType,
                        ResultId = x.BindResultId,
                        IsCalcResul = x.IsCalcResul,
                        CalcItemTagIds = x.CalcItemTagIds.Split(';').Where(x => !string.IsNullOrEmpty(x)).Select(x => Convert.ToInt64(x)).ToList(),
                        LowerLimit = x.LowerLimit ?? "",
                        UpperLimit = x.UpperLimit ?? ""
                    }).ToList();
                }

                return new();
            }
            #endregion

            #region 获取上次项目结果
            string GetLastItemResult(string ItemCode)
            {
                if (lastItemResult == null || lastItemResult.Count <= 0)
                    return "";

                return lastItemResult.Where(x => x.ItemCode == ItemCode).Select(x => x.ItemResult).FirstOrDefault();
            }
            #endregion
        }

        /// <summary>
        /// 创建组合记录
        /// </summary>
        /// <returns></returns>
        private PeRecordComb NewPeRecordComb(RecordComb recComb, List<CombTag> recCombTags, DateTime registerTime)
        {
            var regComb = _registerRepository.ReadRegisterCombs(recComb.RegNo, recComb.RegCombId).First();
            return new PeRecordComb
            {
                Id = _noGeneration.NextSnowflakeId(),
                RegNo = recComb.RegNo,
                RegCombId = recComb.RegCombId,
                CombCode = recComb.CombCode,
                ClsCode = recComb.ClsCode,
                ExamDeptCode = regComb.ExamDeptCode,
                OperName = _httpContextUser.UserName,
                DoctorName = recComb.DoctorName,
                IsError = recComb.IsError,
                OperTime = DateTime.Now,
                ExamTime = recComb.ExamTime,
                RegisterTime = registerTime,
                CombResult = string.Join(';', recCombTags.Select(x => x.Tag).ToArray() ?? Array.Empty<string>())
            };
        }

        /// <summary>
        /// 获取项目记录
        /// </summary>
        /// <returns></returns>
        private ItemAndResult NewRecordItemAndTag(long recCombId, string combCode, List<RecordItem> recordItems, DateTime registerTime)
        {
            List<PeRecordItem> peRecordItems = new List<PeRecordItem>();
            List<PeRecordItemTag> peRecordTags = new List<PeRecordItemTag>();

            // TODO: 空引用
            foreach (var item in recordItems)
            {
                var recItemId = _noGeneration.NextSnowflakeId();
                peRecordItems.Add(new PeRecordItem
                {
                    Id = recItemId,
                    RecCombId = recCombId,
                    CombCode = combCode,
                    ItemCode = item.ItemCode,
                    ItemName = item.ItemName,
                    SortIndex = item.SortIndex,
                    ItemResult = string.Join(';', item.ItemTags?.Select(x => x.Tag).ToArray() ?? Array.Empty<string>()),
                    ResultType = item.ResultType,
                    Unit = item.Unit,
                    LowerLimit = item.LowerLimit,
                    UpperLimit = item.UpperLimit,
                    Hint = item.Hint,
                    AbnormalType = item.AbnormalType,
                    IsError = item.AbnormalType != AbnormalType.正常,
                    RegisterTime = registerTime
                });

                //组合小结
                if (item.ItemTags != null && item.ItemTags.Count > 0)
                {
                    peRecordTags.AddRange(item.ItemTags.Select(x => new PeRecordItemTag
                    {
                        Id = x.IsNew ? _noGeneration.NextSnowflakeId() : x.Id.Value,
                        RecItemId = recItemId,
                        BindResultId = x.ResultId,
                        ItemCode = x.ItemCode,
                        ItemTag = x.Tag,
                        AbnormalType = x.AbnormalType,
                        IsCalcResul = x.IsCalcResul,
                        CalcItemTagIds = string.Join(';', x.CalcItemTagIds ?? new()),
                        RegisterTime = registerTime
                    }).ToList());
                }
            }

            return new ItemAndResult { RecordItem = peRecordItems, RecordItemTag = peRecordTags };
        }

        /// <summary>
        /// 获取项目结果,仅支持数值类型的公式计算!（项目结果表达式）
        /// </summary>
        /// <param name="calcItemTag"></param>
        /// <param name="fullItemTags"></param>
        /// <param name="allNumItemCodes"></param>
        /// <returns></returns>
        private List<ItemTag> GetItemTagByItemExp(ItemTag calcItemTag, List<ItemTag> fullItemTags, List<RecordItem> fullItems)
        {
            var allNumItemCodes = fullItems.Where(x => x.ResultType == ValueType.数值).Select(x => x.ItemCode).ToArray();
            if (allNumItemCodes.Length == 0 || !allNumItemCodes.Contains(calcItemTag.ItemCode))
                return new();

            List<ItemTag> itemTags = new();
            var numItemTags = fullItemTags.Where(x => x.ItemCode != calcItemTag.ItemCode && allNumItemCodes.Contains(x.ItemCode)).ToDictionary(x => x.ItemCode);
            numItemTags.Add(calcItemTag.ItemCode, calcItemTag);

            // 查询与当前编辑标签相关的项目结果表达式
            var resultExps = _recordRepository.ReadItemResultExp()
                 //.Where(x => SqlFunc.ContainsArray(allNumItemCodes, x.ItemCode))
                 .Where(x => x.Expression.Contains(calcItemTag.ItemCode))
                 .Select(x => new
                 {
                     x.ItemCode,
                     x.Expression
                 })
                 .ToList();

            foreach (var resultExp in resultExps)
            {
                // 跳过不存在项目列表中的项目结果表达式。
                if (!fullItems.Any(x => x.ItemCode == resultExp.ItemCode))
                    continue;

                var expItemCodes = Regex.Matches(resultExp.Expression, @"(?<=\[)[\da-z]+(?=\])").Select(x => x.Value).Distinct().ToArray();
                // 跳过不满足公式所需项目条件的结果表达式
                if (expItemCodes.Any(x => !numItemTags.ContainsKey(x) || !double.TryParse(numItemTags[x].Tag, out _)))
                    continue;

                // 套用结果到公式模板
                string resultExpStr = resultExp.Expression;
                foreach (var expItemCode in expItemCodes)
                    resultExpStr = Regex.Replace(resultExpStr, $@"\[{expItemCode}\]", numItemTags[expItemCode].Tag, RegexOptions.IgnoreCase);

                try
                {
                    var numbResult = new DataTable().Compute(resultExpStr, null);
                    itemTags.Add(new ItemTag
                    {
                        Id = _noGeneration.NextSnowflakeId(),
                        ItemCode = resultExp.ItemCode,
                        //Tag = ((double)numbResult).ToString("F"),// 计算公式得到结果,限制保留两位小数
                        Tag = (Convert.ToDouble(numbResult)).ToString("F"),// 计算公式得到结果,限制保留两位小数
                        IsCalcResul = true,
                        CalcItemTagIds = expItemCodes.Select(x => (long)numItemTags[x].Id).ToList(),
                    });
                }
                catch (Exception)
                {
                }
            }

            return itemTags;
        }

        /// <summary>
        /// 修改体检状态
        /// </summary>
        /// <param name="regInfo"></param>
        /// <returns></returns>
        public void UpdatePeStatus(PeRegister regInfo)
        {
            if (regInfo == null)
                return;

            var peRecordComb = _recordRepository.ReadAllCheckCombs(regInfo.RegNo)
                .Select((regComb, recComb) => new
                {
                    regComb.CombName,
                    recComb.DoctorName,
                    regComb.IsOccupation,
                    regComb.IsOrdinary
                }).ToList();

            // 普检
            var regCount = peRecordComb.Count(x => x.IsOrdinary);
            var reportedCount = peRecordComb.Count(x => !string.IsNullOrEmpty(x.DoctorName) && x.IsOrdinary);
            if (reportedCount == 0)
                ;
            // regInfo.PeStatus = PeStatus.未检查;
            else if (reportedCount < regCount)
                regInfo.PeStatus = PeStatus.正在检查;
            else
                regInfo.PeStatus = PeStatus.已检完;

            // 职检
            if (regInfo.IsOccupation)
            {
                var occupation = _peRegisterOccupationRepository.First(x => x.RegNo == regInfo.RegNo);
                var regCount2 = peRecordComb.Count(x => x.IsOccupation);
                var reportedCount2 = peRecordComb.Count(x => !string.IsNullOrEmpty(x.DoctorName) && x.IsOccupation);

                if (reportedCount2 == 0)
                    occupation.PeStatus = PeStatus.未检查;
                else if (reportedCount2 < regCount2)
                    occupation.PeStatus = PeStatus.正在检查;
                else
                    occupation.PeStatus = PeStatus.已检完;

                // 仅职检
                if (regInfo.IsOccupation && !regInfo.IsOrdinary)
                    regInfo.PeStatus = occupation.PeStatus;

                _peRegisterOccupationRepository.Update(occupation, x => x.PeStatus);
            }

            _peRegisterRepository.Update(regInfo, x => x.PeStatus);
        }

        /// <summary>
        /// 添加项目标签
        /// </summary>
        /// <param name="editItem"></param>
        /// <returns></returns>
        private EditItemTagCallback AddItemTag(EditItemTag editItem)
        {
            List<CombTag> fullCombTags = editItem.RecordComb.CombTags;
            List<ItemTag> fullItemTags = new();
            List<ItemTag> newItemTags = new();
            List<ItemTag> alterItemTags = new();

            // 定位当前编辑的标签
            var currentItemTag = editItem.RecordComb.RecordItems.First(x => x.ItemCode == editItem.CurrentItemTagCopy.ItemCode).ItemTags
                .FirstOrDefault(x => x.Tag == editItem.CurrentItemTagCopy.Tag && x.ResultId == editItem.CurrentItemTagCopy.ResultId);
            if (currentItemTag == null)
            {
                currentItemTag = editItem.CurrentItemTagCopy;
                fullItemTags.Add(currentItemTag);
            }

            currentItemTag.Id = _noGeneration.NextSnowflakeId();// 当前编辑的标签赋值Id
            List<ItemTag> calcItemTags = new() { currentItemTag };// 计算疾病的项目标签列表

            foreach (var recItem in editItem.RecordComb.RecordItems)
                fullItemTags.AddRange(recItem.ItemTags);// 加载所有的项目标签

            var producedItemTags = GetItemTagByItemExp(currentItemTag, fullItemTags, editItem.RecordComb.RecordItems);// 获取项目标签
            foreach (var producedItemTag in producedItemTags)
            {
                // 本该是计算带出但提前手动录入了，比如“BMI指数”
                var oldItemTag = fullItemTags.FirstOrDefault(x => x.ItemCode == producedItemTag.ItemCode);
                if (oldItemTag != null)
                {
                    fullItemTags.Remove(oldItemTag);
                    producedItemTag.Id = oldItemTag.Id;
                    alterItemTags.Add(producedItemTag);

                    var delCombTags = fullCombTags.Where(x => x.BindItemTags.Contains((long)oldItemTag.Id)).ToArray();
                    fullCombTags = fullCombTags.Except(delCombTags).ToList();

                    var reCalcItemTagIds = delCombTags.SelectMany(x => x.BindItemTags).Where(x => x != oldItemTag.Id).ToArray();
                    calcItemTags.AddRange(fullItemTags.Where(x => reCalcItemTagIds.Contains((long)x.Id)));
                }
                else
                {
                    newItemTags.Add(producedItemTag);
                }

                calcItemTags.Add(producedItemTag);
                fullItemTags.Add(producedItemTag);
            }

            // 获取疾病
            var items = _mapper.Map<List<RecordItem>, ItemRangeLimit[]>(editItem.RecordComb.RecordItems);
            var newCombTags = _diseaseService.GetDisease(calcItemTags, items, fullItemTags);
            fullCombTags.AddRange(newCombTags);

            var newCombBingIds = newCombTags
                .SelectMany(x => x.BindItemTags)
                .Select(x => (long?)x)
                .ToArray();

            // 通知UI更新异常标识
            foreach (var alterItemTag in calcItemTags.Union(fullItemTags.Where(x => newCombBingIds.Contains(x.Id))))
            {
                if (currentItemTag.Equals(alterItemTag) || newItemTags.Any(x => x.Equals(alterItemTag)) || alterItemTags.Any(x => x.Equals(alterItemTag)))
                    continue;

                alterItemTags.Add(alterItemTag);
            }

            // 处理疾病包含关系或生成默认小结
            if (fullCombTags.Count == 0)
            {
                fullCombTags = GetDefaultComTag();
            }
            else if (fullCombTags.Count > 1)
            {
                fullCombTags.RemoveAll(x => x.Tag == "未见明显异常");
                _diseaseService.GetDistinctDisease(fullCombTags, items, fullItemTags);// 有产生的新小结时，处理疾病包含关系
            }

            // 小结的特殊处理
            _diseaseService.BloodPressureDiseaseHandle(fullCombTags, items, fullItemTags);

            return new EditItemTagCallback
            {
                CurrentItemTag = currentItemTag,
                OtherAddItemTags = newItemTags,
                OtherAlterItemTags = alterItemTags,
                CombTags = fullCombTags
            };
        }

        /// <summary>
        /// 修改项目标签
        /// </summary>
        /// <param name="editItem"></param>
        /// <returns></returns>
        private EditItemTagCallback AlterItemTag(EditItemTag editItem)
        {
            var fullCombTags = editItem.RecordComb.CombTags;
            var fullItemTags = new List<ItemTag>();
            var newItemTags = new List<ItemTag>();
            var alterItemTags = new List<ItemTag>();

            // 定位当前编辑的标签
            var currentItemTag = editItem.RecordComb.RecordItems.First(x => x.ItemCode == editItem.CurrentItemTagCopy.ItemCode).ItemTags
                .FirstOrDefault(x => x.Tag == editItem.CurrentItemTagCopy.Tag);
            var calcItemTags = new List<ItemTag> { currentItemTag };// 重新计算疾病的项目标签列表

            foreach (var recItem in editItem.RecordComb.RecordItems)
                fullItemTags.AddRange(recItem.ItemTags);// 加载所有的项目标签

            // 获取过期的计算公式项目标签
            var oldCalcItemTags = fullItemTags.Where(x => x.IsCalcResul && x.CalcItemTagIds.Contains((long)editItem.CurrentItemTagCopy.Id)).ToList();
            fullItemTags.RemoveAll(oldCalcItemTags);// 剔除过期的计算公式项目标签

            // 获取过期的小结标签
            var oldItemIds = oldCalcItemTags.Select(x => (long)x.Id).Append((long)editItem.CurrentItemTagCopy.Id).ToArray();
            var oldCombTags = fullCombTags.Where(x => x.BindItemTags.Any(sq => oldItemIds.Contains(sq))).ToArray();

            // 更新结果快捷生成小结的内容
            foreach (var combTag in oldCombTags.Where(x => x.IsCustom == true && x.BindItemTags.Count > 0))
            {
                var itemTag = fullItemTags.FirstOrDefault(x => x.Id == combTag.BindItemTags[0]);
                combTag.Tag = itemTag.Tag;
            }

            // 删除过期的组合标签
            foreach (var delCombTag in oldCombTags.Where(x => x.IsCustom == false))
            {
                var recalcIds = delCombTag.BindItemTags.Where(x => !oldItemIds.Contains(x) && !calcItemTags.Exists(sq => sq.Id == x)).ToArray();
                calcItemTags.AddRange(fullItemTags.Join(recalcIds, a => a.Id, b => b, (a, b) => a));// 添加重新计算疾病的项目标签（过滤已过期的项目标签）
                fullCombTags.Remove(delCombTag);// 删除组合标签
            }

            var producedItemTags = GetItemTagByItemExp(currentItemTag, fullItemTags, editItem.RecordComb.RecordItems);// 获取项目标签
            foreach (var calcItemTag in producedItemTags)
            {
                var oldItemTag = oldCalcItemTags.FirstOrDefault(x => x.ItemCode == calcItemTag.ItemCode);
                if (oldItemTag != null)
                {
                    calcItemTag.Id = oldItemTag.Id;// 继承id
                    alterItemTags.Add(calcItemTag);// 通知UI修改项目标签

                    var delCombTagsforOldItem = fullCombTags.Where(x => x.BindItemTags.Contains((long)oldItemTag.Id)).ToArray();
                    fullCombTags = fullCombTags.Except(delCombTagsforOldItem).ToList();

                    var reCalcItemTagIds = delCombTagsforOldItem.SelectMany(x => x.BindItemTags).Where(x => x != oldItemTag.Id).ToArray();
                    calcItemTags.AddRange(fullItemTags.Where(x => reCalcItemTagIds.Contains((long)x.Id)));
                }
                else
                {
                    newItemTags.Add(calcItemTag);// 通知UI添加项目标签
                }

                calcItemTags.Add(calcItemTag);
                fullItemTags.Add(calcItemTag);
            }

            // 获取疾病
            var items = _mapper.Map<List<RecordItem>, ItemRangeLimit[]>(editItem.RecordComb.RecordItems);
            var newCombTags = _diseaseService.GetDisease(calcItemTags, items, fullItemTags);
            fullCombTags.AddRange(newCombTags);

            var newCombBingIds = newCombTags
                .SelectMany(x => x.BindItemTags)
                .Select(x => (long?)x)
                .ToArray();

            // 通知UI更新异常标识
            foreach (var alterItemTag in calcItemTags.Union(fullItemTags.Where(x => newCombBingIds.Contains(x.Id))))
            {
                if (currentItemTag.Equals(alterItemTag) || newItemTags.Any(x => x.Equals(alterItemTag)) || alterItemTags.Any(x => x.Equals(alterItemTag)))
                    continue;

                alterItemTags.Add(alterItemTag);
            }

            // 处理疾病包含关系或生成默认小结
            if (fullCombTags.Count == 0)
            {
                fullCombTags = GetDefaultComTag();
            }
            else if (fullCombTags.Count > 1)
            {
                fullCombTags.RemoveAll(x => x.Tag == "未见明显异常");
                _diseaseService.GetDistinctDisease(fullCombTags, items, fullItemTags);// 有产生的新小结时，处理疾病包含关系
            }

            // 小结的特殊处理
            _diseaseService.BloodPressureDiseaseHandle(fullCombTags, items, fullItemTags);

            return new EditItemTagCallback
            {
                CurrentItemTag = currentItemTag,
                OtherAddItemTags = newItemTags,
                OtherAlterItemTags = alterItemTags,
                CombTags = fullCombTags
            };
        }

        /// <summary>
        /// 删除项目标签
        /// </summary>
        /// <param name="editItem"></param>
        /// <returns></returns>
        private EditItemTagCallback DeleteItemTag(EditItemTag editItem)
        {
            var fullCombTags = editItem.RecordComb.CombTags;
            var fullItemTags = new List<ItemTag>();
            var calcItemTags = new List<ItemTag>();// 计算疾病的项目标签列表

            // 加载所有的项目标签
            foreach (var recItem in editItem.RecordComb.RecordItems)
                fullItemTags.AddRange(recItem.ItemTags);

            // 获取删除关联的计算公式项目标签
            var delCalcItemTags = fullItemTags.Where(x => x.IsCalcResul && x.CalcItemTagIds.Contains((long)editItem.CurrentItemTagCopy.Id)).ToList();
            fullItemTags.RemoveAll(delCalcItemTags);// 剔除过期的计算公式项目标签

            // 删除过期的组合标签
            var oldItemIds = delCalcItemTags.Select(x => (long)x.Id).Append((long)editItem.CurrentItemTagCopy.Id).ToArray();
            var delCombTags = fullCombTags.Where(x => x.BindItemTags.Any(sq => oldItemIds.Contains(sq))).ToArray();
            foreach (var delCombTag in delCombTags)
            {
                var recalcIds = delCombTag.BindItemTags.Where(x => !oldItemIds.Contains(x) && !calcItemTags.Exists(sq => sq.Id == x)).ToArray();
                calcItemTags.AddRange(fullItemTags.Join(recalcIds, a => a.Id, b => b, (a, b) => a));// 添加重新计算疾病的项目标签（过滤已过期的项目标签）
                fullCombTags.Remove(delCombTag);// 删除组合标签
            }

            // 获取疾病
            var items = _mapper.Map<List<RecordItem>, ItemRangeLimit[]>(editItem.RecordComb.RecordItems);
            var newCombTags = _diseaseService.GetDisease(calcItemTags, items, fullItemTags);
            fullCombTags.AddRange(newCombTags);

            // 处理疾病包含关系或生成默认小结
            if (fullCombTags.Count == 0)
            {
                fullCombTags = GetDefaultComTag();
            }
            else if (fullCombTags.Count > 1)
            {
                fullCombTags.RemoveAll(x => x.Tag == "未见明显异常");
                _diseaseService.GetDistinctDisease(fullCombTags, items, fullItemTags);// 有产生的新小结时，处理疾病包含关系
            }

            // 小结的特殊处理
            _diseaseService.BloodPressureDiseaseHandle(fullCombTags, items, fullItemTags);

            return new EditItemTagCallback
            {
                OtherDelItemTags = delCalcItemTags,
                CombTags = fullCombTags
            };
        }

        /// <summary>
        /// 自定义未见异常组合标签返回
        /// </summary>
        /// <returns></returns>
        private List<CombTag> GetDefaultComTag()
        {
            var CombTags = new List<CombTag>
            {
                new() { Id = _noGeneration.NextSnowflakeId(), Tag = "未见明显异常", IsCustom = false, BindItemTags = new() }
            };
            return CombTags;
        }

        /// <summary>
        /// 弃检组合
        /// </summary>
        /// <returns></returns>
        private bool SaveAbandonComb(AbandonCombData abandonComb, List<RecordComb> recordCombs, ref string msg)
        {
            if (recordCombs.Count == 0)
            {
                msg = "拒检项目不能为空";
                return false;
            }

            var regInfo = _registerRepository.ReadRegister(abandonComb.RegNo).First();
            if (regInfo == null)
            {
                msg = "体检信息不存在";
                return false;
            }

            if (string.IsNullOrEmpty(abandonComb.SampCode) &&
                _peRecordCombRepository.SplitTableAny(regInfo.RegisterTime, x => x.RegNo == abandonComb.RegNo && x.RegCombId == abandonComb.RegCombId))
            {
                msg = "此项目已录入结果,不能拒检!";
                return false;
            }

            var recordComb = new List<PeRecordComb>();
            var recordCombTag = new List<PeRecordCombTag>();
            foreach (var recComb in recordCombs)
            {
                var recCombId = _noGeneration.NextSnowflakeId();
                recordComb.Add(new PeRecordComb
                {
                    Id = recCombId,
                    RegNo = abandonComb.RegNo,
                    RegCombId = recComb.RegCombId,
                    CombCode = recComb.CombCode,
                    ClsCode = recComb.ClsCode,
                    ExamDeptCode = recComb.ExamDeptCode,
                    CombResult = "弃检",
                    DoctorName = "弃检",
                    OperName = abandonComb.OperName,
                    IsError = false,
                    OperTime = DateTime.Now,
                    ExamTime = DateTime.Now,
                    RegisterTime = regInfo.RegisterTime
                });

                recordCombTag.Add(new PeRecordCombTag
                {
                    Id = _noGeneration.NextSnowflakeId(),
                    RecCombId = recCombId,
                    CombCode = recComb.CombCode,
                    CombTag = "弃检",
                    IsCustom = false,
                    BindItemTags = "",
                    RegisterTime = regInfo.RegisterTime
                });
            }

            _dataTranRepository.ExecTran(() =>
            {
                _peRecordCombRepository.SplitTableInsert(recordComb);
                _peRecordCombTagRepository.SplitTableInsert(recordCombTag);
            });

            UpdatePeStatus(regInfo);
            return true;
        }

        /// <summary>
        /// 删除小结及项目结果
        /// </summary>
        /// <param name="regInfo"></param>
        /// <param name="regCombId"></param>
        /// <exception cref="BusinessException"></exception>
        private void DeleteRecordComb(PeRegister regInfo, long regCombId, out PeRecordComb recordComb, out PeRecordItem[] recordItems)
        {
            if (regInfo.PeStatus == PeStatus.已总检 || regInfo.PeStatus == PeStatus.已审核)
                throw new BusinessException($"操作失败，报告已总检或已审核：{regInfo.Name}");

            // 获取小结记录
            var recComb = _peRecordCombRepository.First(regInfo.RegisterTime, x => x.RegNo == regInfo.RegNo && x.RegCombId == regCombId);
            if (recComb == null)
            {
                recordComb = null;
                recordItems = null;
                return;
            }

            // 获取项目结果记录
            var recItems = _peRecordItemRepository.FindAll(regInfo.RegisterTime, x => x.RecCombId == recComb.Id).ToArray();

            // 删除小结、小结标签记录 
            _peRecordCombRepository.SplitTableDelete(recComb);
            _peRecordCombTagRepository.SplitTableDelete(regInfo.RegisterTime, x => x.RecCombId == recComb.Id);

            // 删除项目结果、项目结果标签记录
            _peRecordItemRepository.SplitTableDelete(recItems);
            _peRecordItemTagRepository.SplitTableDelete(regInfo.RegisterTime, x => SqlFunc.ContainsArray(recItems.Select(x => x.Id).ToArray(), x.RecItemId));
            recordComb = recComb;
            recordItems = recItems;
        }
        #endregion
    }
}
