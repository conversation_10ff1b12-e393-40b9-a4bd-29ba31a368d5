﻿using Peis.Model.DataBaseEntity.Occupation;
using Peis.Model.Other.PeEnum.Occupation;
using Peis.Model.TableFilter;

namespace Peis.Model.DataBaseEntity;

///<summary>
///单位信息
///</summary>
[SugarTable("CodeCompany")]
public class CodeCompany: IHospCodeFilter
{
    /// <summary>
    /// 单位编码
    /// </summary>        
    [SugarColumn(IsNullable = false, IsPrimaryKey = true, Length = 6)]
    public string CompanyCode { get; set; }

    /// <summary>
    /// 单位名称
    /// </summary>        
    [SugarColumn(IsNullable = false, Length = 100)]
    public string CompanyName { get; set; }

    /// <summary>
    /// 单位别名
    /// </summary>        
    [SugarColumn(IsNullable = true, Length = 100)]
    public string CompanyAlias { get; set; }

    /// <summary>
    /// 单位分类
    /// </summary>        
    [SugarColumn(IsNullable = false, Length = 10)]
    public string CompanyClsCode { get; set; }

    /// <summary>
    /// 父级
    /// </summary>        
    [SugarColumn(IsNullable = false, Length = 10)]
    public string Parent { get; set; }

    /// <summary>
    /// 院内联系人
    /// </summary>        
    [SugarColumn(IsNullable = true, Length = 10)]
    public string HospContact { get; set; }

    /// <summary>
    /// 单位联系人
    /// </summary>        
    [SugarColumn(IsNullable = true, Length = 50)]
    public string CompanyContact { get; set; }

    /// <summary>
    /// 电话
    /// </summary>        
    [SugarColumn(IsNullable = true, Length = 16)]
    public string Tel { get; set; }

    /// <summary>
    /// 传真
    /// </summary>        
    [SugarColumn(IsNullable = true, Length = 16)]
    public string Fax { get; set; }

    /// <summary>
    /// 地址
    /// </summary>        
    [SugarColumn(IsNullable = true, Length = 100)]
    public string Address { get; set; }

    /// <summary>
    /// 邮编
    /// </summary>        
    [SugarColumn(IsNullable = true, Length = 10)]
    public string PostCode { get; set; }

    /// <summary>
    /// 开户银行
    /// </summary>        
    [SugarColumn(IsNullable = true, Length = 60)]
    public string OpenBank { get; set; }

    /// <summary>
    /// 银行账号
    /// </summary>        
    [SugarColumn(IsNullable = true, Length = 20)]
    public string BankAccount { get; set; }

    /// <summary>
    /// 拼音码
    /// </summary>        
    [SugarColumn(IsNullable = true, Length = 10)]
    public string PinYinCode { get; set; }

    /// <summary>
    /// 五笔码
    /// </summary>        
    [SugarColumn(IsNullable = true, Length = 10)]
    public string WuBiCode { get; set; }

    /// <summary>
    /// 备注
    /// </summary>        
    [SugarColumn(IsNullable = true, Length = 100)]
    public string Note { get; set; }

    /// <summary>
    /// 院区编码
    /// </summary>        
    [SugarColumn(IsNullable = false, Length = 1)]
    public string HospCode { get; set; }

    /// <summary>
    /// 社会信用代码
    /// </summary>
    [SugarColumn(Length = 20, IsNullable = true)]
    public string CreditCode { get; set; }

    /// <summary>
    /// 职工人数
    /// </summary>
    [SugarColumn(IsNullable = true)]
    public int EmployeesNumber { get; set; }

    /// <summary>
    /// 接触危害因素职工人数
    /// </summary>
    [SugarColumn(IsNullable = true)]
    public int ContactHazardEmployeesNumber { get; set; }

    /// <summary>
    /// 行业分类代码
    /// </summary>
    [SugarColumn(Length = 10, IsNullable = true)]
    public string IndustryCode { get; set; }

    /// <summary>
    /// 经济类型代码
    /// </summary>
    [SugarColumn(Length = 10, IsNullable = true)]
    public string EconomicCode { get; set; }

    /// <summary>
    /// 企业规模代码
    /// </summary>
    [SugarColumn(IsNullable = true)]
    public OccupationalEnterpriseSize EnterpriseSizeCode { get; set; }

    /// <summary>
    /// 地区编码
    /// </summary>
    [SugarColumn(Length = 10, IsNullable = true)]
    public string AddressCode { get; set; }

    /// <summary>
    /// 显示顺序
    /// </summary>        
    [SugarColumn(IsNullable = false)]
    public int SortIndex { get; set; }

    /// <summary>
    /// 是否已上传职业病平台
    /// </summary>
    [SugarColumn(IsNullable = true)]
    public bool IsOccupationUploaded { get; set; }

    #region Ext
    /// <summary>
    /// 父级单位名称
    /// </summary>     
    [SugarColumn(IsIgnore = true)]
    public string ParentCompanyName { get; set; }

    /// <summary>
    /// 单位次数
    /// </summary>        
    [SugarColumn(IsIgnore = true)]
    [Navigate(NavigateType.OneToMany, nameof(CodeCompany.CompanyCode))]
    public List<CodeCompanyTimes> CodeCompanyTimes { get; set; }

    /// <summary>
    /// 地区码层级
    /// </summary>
    [SugarColumn(IsIgnore = true)]
    public Dictionary<string, string> AddreassRelation { get; set; }
    /// <summary>
    /// 行业类别
    /// </summary>        
    [SugarColumn(IsIgnore = true)]
    [Navigate(NavigateType.OneToOne, nameof(CodeCompany.IndustryCode))]
    public CodeOccupationalIndustry CodeOccupationalIndustry { get; set; }

    /// <summary>
    /// 经济类型
    /// </summary>        
    [SugarColumn(IsIgnore = true)]
    [Navigate(NavigateType.OneToOne, nameof(CodeCompany.EconomicCode))]
    public CodeOccupationalEconomicType CodeOccupationalEconomicType { get; set; }

    #endregion
}