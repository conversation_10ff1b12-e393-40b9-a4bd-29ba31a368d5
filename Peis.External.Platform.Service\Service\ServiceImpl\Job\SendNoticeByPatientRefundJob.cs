﻿using Peis.Quartz.UI.BaseService;
using Peis.External.Platform.Service.Service.IService.System;

namespace Peis.External.Platform.Service.Service.ServiceImpl.Job;

/// <summary>
/// 作业：每日需要推送当日退费人员列表,14点、17点分别汇总推送当日退费情况
/// </summary>
public class SendNoticeByPatientRefundJob : IJobService
{
    readonly INoticeBusinessService _businessNoticeService;

    public SendNoticeByPatientRefundJob(INoticeBusinessService businessNoticeService)
    {
        _businessNoticeService = businessNoticeService;
    }

    public string ExecuteService(string parameter)
    {
        int.TryParse(parameter, out var preDays);
        string msg = "执行成功";
        _businessNoticeService.SendNoticeByPatientRefund(out msg, preDays);
        return msg;
    }
}
