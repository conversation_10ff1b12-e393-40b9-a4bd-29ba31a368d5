﻿using Peis.Model.Other.PeEnum;
using Peis.Model.TableFilter;
using System.Text.Json.Serialization;

namespace Peis.Model.DataBaseEntity;

[SplitTable(SplitType.Year)]
[SugarTable("LogBusiness_{yyyy}", "业务日志表")]
[SugarIndex("index_RegNo_", nameof(RegNo), OrderByType.Asc)]
public class LogBusinessNew:IHospCodeFilter
{
    /// <summary>
    /// id
    /// </summary>
    [JsonIgnore]
    [SugarColumn(IsPrimaryKey = true)]
    public long Id { get; set; }
    /// <summary>
    /// 体检号
    /// </summary>
    [SugarColumn(IsNullable = true, Length = 12)]
    public string RegNo { get; set; }
    /// <summary>
    /// 日志类型
    /// </summary>
    [SugarColumn(IsNullable = false)]
    public LogType LogType { get; set; }
    /// <summary>
    /// 操作类型
    /// </summary>
    [SugarColumn(IsNullable = false)]
    public OperateType OperateType { get; set; }
    /// <summary>
    /// 组合代码
    /// </summary>
    [SugarColumn(IsNullable = true,Length =10)]
    public string CombCode { get; set; }
    /// <summary>
    /// 组合名
    /// </summary>
    [SugarColumn(IsNullable = true,Length =200)]
    public string CombName { get; set; }
    /// <summary>
    /// 日志内容
    /// </summary>
    [SugarColumn(IsNullable = true, ColumnDataType = StaticConfig.CodeFirst_BigString)]
    public string Message { get; set; }
    /// <summary>
    /// 操作员
    /// </summary>
    [SugarColumn(IsNullable = false, Length = 20)]
    public string Operator { get; set; }
    /// <summary>
    /// Ip地址
    /// </summary>
    [SugarColumn(IsNullable = true, Length = 20)]
    public string Ip { get; set; }
    /// <summary>
    /// 院区编码
    /// </summary>
    [SugarColumn(IsNullable = false, Length = 1)]
    public string HospCode { get; set; }
    /// <summary>
    /// 记录时间
    /// </summary>
    [SugarColumn(IsNullable = false)]
    [SplitField]
    public DateTime LogTime { get; set; }
}
