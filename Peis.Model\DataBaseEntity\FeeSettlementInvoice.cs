﻿using Peis.Model.Other.PeEnum;
using SqlSugar;
using System;

namespace Peis.Model.DataBaseEntity
{
    ///<summary>
    ///结算发票关联表
    ///</summary>
    [SugarTable("FeeSettlementInvoice")]
    public class FeeSettlementInvoice
    {
        /// <summary>
        /// 自增Id
        /// </summary>        
        [SugarColumn(IsNullable = false, IsPrimaryKey = true, IsIdentity = true)]
        public long Id { get; set; }

        /// <summary>
        /// 发票分配表的Id
        /// </summary>        
        [SugarColumn(IsNullable = false)]
        public int AllocateId { get; set; }

        /// <summary>
        /// 结算号
        /// </summary>        
        [SugarColumn(IsNullable = false, Length = 10)]
        public string SettlementNo { get; set; }

        /// <summary>
        /// 发票前缀
        /// </summary>        
        [SugarColumn(IsNullable = true, Length = 2)]
        public string Prefix { get; set; }

        /// <summary>
        /// 发票顺序号
        /// </summary>        
        [SugarColumn(IsNullable = false)]
        public int Number { get; set; }

        /// <summary>
        /// 发票号
        /// </summary>        
        [SugarColumn(IsNullable = false, Length = 10)]
        public string InvoiceNo { get; set; }

        /// <summary>
        /// 发票金额
        /// </summary>        
        [SugarColumn(IsNullable = false, Length = 12, DecimalDigits = 2)]
        public decimal Price { get; set; }

        /// <summary>
        /// 结算时间
        /// </summary>        
        [SugarColumn(IsNullable = true)]
        public DateTime? SettlementTime { get; set; }

        /// <summary>
        /// 发票使用状态 1:结算 2:作废 3:损坏 4:其他
        /// </summary>        
        [SugarColumn(IsNullable = false)]
        public InvoiceUseStatus Status { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        [SugarColumn(IsNullable = true, Length = 100)]
        public string Note { get; set; }

        /// <summary>
        /// 发票抬头
        /// </summary>
        [SugarColumn(IsNullable = true, Length = 100)]
        public string InvoiceHeader { get; set; }
    }
}