﻿using Peis.Model.Other.PeEnum;
using Peis.Utility.Helper;

namespace Peis.Model.DTO.DataQuery
{
    /// <summary>
    /// 
    /// </summary>
    public class PeReportProcessData
    {
        /// <summary>
        /// 体检号
        /// </summary>
        public string RegNo { get; set; }

        /// <summary>
        /// 姓名
        /// </summary>
        public string Name { get; set; }

        /// <summary>
        /// 性别
        /// </summary>
        public Sex? Sex { get; set; }

        /// <summary>
        /// 年龄
        /// </summary>
        public int? Age { get; set; }

        /// <summary>
        /// 证件号
        /// </summary>
        public string CardNo { get; set; }

        /// <summary>
        /// 操作者
        /// </summary>
        public string AuditDoctor { get; set; }

        private int? _interval = null;
        /// <summary>
        /// 时间间隔
        /// </summary>
        public int Interval
        {
            get
            {
                if (BeginDate.Equals(DateTime.MinValue) || EndDate.Equals(DateTime.MinValue)) return 0;
                if (_interval.HasValue) return _interval.Value;

                var interval = DatetimeHelper.CountWorkdays(BeginDate.Date, EndDate.Date);
                _interval = interval < 0 ? 1 : interval;

                return _interval.Value;
            }
        }

        /// <summary>
        /// 开始时间
        /// </summary>
        public DateTime BeginDate { get; set; }

        /// <summary>
        /// 结束时间
        /// </summary>
        public DateTime EndDate { get; set; }

        /// <summary>
        /// 单位名称
        /// </summary>
        public string CompanyName { get; set; }
        /// <summary>
        /// 指引单回收备注
        /// </summary>
        public string GuidanceRecycleNote { get; set; }
    }
}
