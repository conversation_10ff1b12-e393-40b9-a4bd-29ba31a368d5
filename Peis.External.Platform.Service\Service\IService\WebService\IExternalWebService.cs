﻿using System.ServiceModel;

namespace Peis.External.Platform.Service.Service.IService.WebService;


/// <summary>
/// 外部统一调用体检WebService接口
/// </summary>
[ServiceContract(Namespace = "http://tempuri.org/")]
public interface IExternalWebService
{
    /// <summary>
    /// 检测用,无具体逻辑
    /// </summary>
    [OperationContract]
    void Check();

    /// <summary>
    /// 统一请求入口
    /// </summary>
    /// <param name="action">方法名</param>
    /// <param name="reqMsg">入参内容</param>
    /// <returns>string</returns>
    [OperationContract]
    string Operation(string action, string reqMsg);
}
