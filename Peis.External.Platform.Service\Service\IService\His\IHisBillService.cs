﻿using Peis.Model.DTO;

namespace Peis.External.Platform.Service.Service.IService.His
{
    public interface IHisBillService
    {
        /// <summary>
        /// 创建HIS数据表
        /// </summary>
        /// <param name="register"></param>
        /// <param name="regCombAdds"></param>
        /// <param name="regCombDels"></param>
        /// <returns></returns>
        void SyncBillInfo(PeRegister register, PeRegisterComb[] regCombAdds, long[] regCombDels);

        /// <summary>
        /// 推送订单通知（His、主索引、EMR）
        /// </summary>
        /// <param name="regNo"></param>
        void SendNotice(string regNo);

        /// <summary>
        /// HIS退费预操作 支持全退半退
        /// </summary>
        /// <param name="regNo"></param>
        /// <param name="deleteIds"></param>
        /// <param name="msg"></param>
        /// <returns></returns>
        bool OrderRefund(string regNo, long[] deleteIds, out string msg);

        /// <summary>
        /// 撤销HIS预退费
        /// </summary>
        /// <param name="regNo"></param>
        /// <param name="msg"></param>
        /// <returns></returns>
        public bool CancelOrderRefund(string regNo, out string msg);

        /// <summary>
        /// 自动推送申请单任务
        /// </summary>
        void SyncOrderAuto();

        /// <summary>
        /// 获取病人门诊索引号
        /// </summary>
        /// <param name="hisCardNo"></param>
        /// <returns></returns>
        string GetHisIndex(string hisCardNo);

        /// <summary>
        /// 根据门诊卡号获取病人信息
        /// </summary>
        /// <param name="hisCardNo"></param>
        /// <returns></returns>
        HistoryArchives GetHistoryArchivesByHisCard(string hisCardNo);
    }
}
