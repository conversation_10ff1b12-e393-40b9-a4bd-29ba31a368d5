﻿using Peis.Model.TableFilter;
using System.Text.Json.Serialization;

namespace Peis.Model.DataBaseEntity;

/// <summary>
/// 短信批量发送记录表
/// </summary>
[SplitTable(SplitType.Year)]
[SugarTable("PeBatchImportShortMessage_{yyyy}", "短信批量发送记录表")]

public class PeBatchImportShortMessage:IHospCodeFilter
{
    /// <summary>
    /// 主键Id
    /// </summary>        
    [SugarColumn(IsNullable = false, IsPrimaryKey = true)]
    public long Id { get; set; }

    /// <summary>
    /// 档案号
    /// </summary>        
    [SugarColumn(IsNullable = false, Length = 12)]
    public string PatCode { get; set; }
    /// <summary>
    /// 患者姓名
    /// </summary>        
    [SugarColumn(IsNullable = false, Length = 50)]
    [JsonPropertyName("name")]
    public string PatientName { get; set; }

    /// <summary>
    /// 手机号
    /// </summary>        
    [SugarColumn(IsNullable = false, Length = 20)]
    [JsonPropertyName("tel")]
    public string PhoneNumber { get; set; }

    /// <summary>
    /// 院区编码
    /// </summary>        
    [SugarColumn(IsNullable = false, Length = 1)]
    public string HospCode { get; set; }

    /// <summary>
    /// 短信内容
    /// </summary>        
    [SugarColumn(IsNullable = false, ColumnDataType = "nvarchar(1500)")]
    public string ShortMsgContent { get; set; }

    /// <summary>
    /// 发送时间
    /// </summary>        
    [SugarColumn(IsNullable = true)]
    public DateTime? SentTime { get; set; }

    /// <summary>
    /// 发送状态
    /// </summary>        
    [SugarColumn(IsNullable = false)]
    public bool Success { get; set; }

    /// <summary>
    /// 创建人Code
    /// </summary>        
    [SugarColumn(IsNullable = false, Length = 10, IsOnlyIgnoreUpdate = true)]
    public string CreatorCode { get; set; }

    /// <summary>
    /// 创建人名称
    /// </summary>        
    [SugarColumn(IsNullable = false, Length = 50, IsOnlyIgnoreUpdate = true)]
    public string CreatorName { get; set; }

    /// <summary>
    /// 创建时间
    /// </summary>        
    [SplitField]
    [SugarColumn(IsNullable = false, IsOnlyIgnoreUpdate = true)]
    public DateTime CreatedTime { get; set; }

    /// <summary>
    /// 错误信息
    /// </summary>
    [SugarColumn(IsNullable = true, ColumnDataType = StaticConfig.CodeFirst_BigString)]
    public string ErrorMsg { get; set; }

    public void SetSentTime()
    {
        SentTime = DateTime.Now;
        Success = true;
    }

}
