﻿using Peis.External.Platform.Service.Service.IService.BaiHui;
using Peis.Model.DataBaseEntity.External;
using Peis.Model.DTO.External.BaiHui;
using Peis.Model.DTO.External.DataMaintenances;
using Peis.Service.IService.ExternalSystem;

namespace Peis.External.Platform.Service.Service.ServiceImpl.MainData;

/// <summary>
/// 维护第三方系统的字典相关内容同步服务
/// </summary>
public class ExternalSystemDataSyncService : IExternalSystemDataSyncService
{

    #region 仓储       
    readonly IMapper _mapper;
    readonly IBHBasicService _bhBasicService;
    readonly IDataRepository<CodeItemComb> _codeItemCombRepository;
    readonly IDataRepository<CodeHisChargeItem> _codeHisChargeItemRepository;
    readonly IDataRepository<MapCodeItemCombHisChargeItem> _mapCodeItemCombChargeRepository;
    readonly IDataRepository<CodeHisOrderItem> _codeHisOrderItemRepository;
    readonly IDataRepository<CodeHisOrderItemCharge> _codeHisOrderItemChargeRepository;
    readonly IShenShanMainDataRepository _shenShanMainDataRepository;
    readonly IDataRepository<MapCodeLisItem> _mapCodeLisItemRepository;
    readonly IDataRepository<CodeHisDrugItem> _codeHisDrugItemRepository;
    #endregion

    #region 构造函数
    public ExternalSystemDataSyncService(
        IMapper mapper,
        IBHBasicService bhBasicService,
        IDataRepository<CodeItemComb> codeItemCombRepository,
        IDataRepository<CodeHisChargeItem> codeHisChargItemRepository,
        IDataRepository<MapCodeItemCombHisChargeItem> mapCodeItemCombHisChargeItemRepository,
        IDataRepository<CodeHisOrderItem> codeHisOrderItemRepository,
        IShenShanMainDataRepository shenShanMainDataRepository,
        IDataRepository<CodeHisOrderItemCharge> codeHisOrderItemChargeRepository,
        IDataRepository<MapCodeLisItem> mapCodeLisItemRepository,
        IDataRepository<CodeHisDrugItem> codeHisDrugItemRepository)
    {
        _mapper = mapper;
        _bhBasicService = bhBasicService;
        _codeItemCombRepository = codeItemCombRepository;
        _codeHisChargeItemRepository = codeHisChargItemRepository;
        _mapCodeItemCombChargeRepository = mapCodeItemCombHisChargeItemRepository;
        _codeHisOrderItemRepository = codeHisOrderItemRepository;
        _shenShanMainDataRepository = shenShanMainDataRepository;
        _codeHisOrderItemChargeRepository = codeHisOrderItemChargeRepository;
        _mapCodeLisItemRepository = mapCodeLisItemRepository;
        _codeHisDrugItemRepository = codeHisDrugItemRepository;
    }
    #endregion

    #region 体检类代码

    #region 体检项目对应

    /// <summary>
    /// 同步更新体检项目对应信息
    /// </summary>
    /// <param name="pageSize">每页大小</param>
    /// <param name="msg">消息</param>
    /// <returns>bool</returns>
    public bool SyncCodeLisItems(int pageSize, out string msg)
    {
        int totalNumber = 0, totalPage = 0;
        var updateMapLisItems = new List<MapCodeLisItem>();
        var mapCodeLisItems = _shenShanMainDataRepository.QueryMapCodeLisItems()
             .ToPageList(1, pageSize, ref totalNumber, ref totalPage);
        for (int i = 1; i <= totalPage; i++)
        {
            if (i > 1)
                mapCodeLisItems = _shenShanMainDataRepository.QueryMapCodeLisItems()
                    .ToPageList(i, pageSize);

            foreach (var item in mapCodeLisItems)
            {
                var codeLisItemSrc = _shenShanMainDataRepository.QueryViewCodeLisItems()
                    .Where(x => x.LisCombCode.Equals(item.LisCombCode))
                    .Where(x => x.LisItemCode.Equals(item.LisItemCode))
                    .First();
                if (codeLisItemSrc.IsNullOrEmpty()) continue;

                item.SetName(codeLisItemSrc);
                updateMapLisItems.Add(item);
            }
        }

        if (!updateMapLisItems.IsNullOrEmpty())
            _mapCodeLisItemRepository.Update(updateMapLisItems);

        msg = string.Format(ResxCommon.SuccessTotal, updateMapLisItems.Count);
        return true;
    }

    #endregion

    #region 体检组合信息 CodeItemComb

    #region 体检组合与His收费项目信息
    /// <summary>
    /// 同步His系统的收费项目至本库（按分页循环同步）
    /// </summary>
    /// <param name="pageSize">每页大小</param>
    /// <param name="msg">消息</param>
    /// <returns>Boolean</returns>
    public bool SyncCodeHisChargeItems(int pageSize, out string msg)
    {
        var query = new BHApiRecordsQuery();
        query.UsePage(0, 1);
        var resultData = GetBHCodeHisChargeItems(query);
        if (resultData.TotalNumberOfRecords == 0)
        {
            msg = ResxCommon.NotExistRecord;
            return false;
        }

        // 按分页逻辑循环同步
        int totalPage = (int)Math.Ceiling(resultData.TotalNumberOfRecords * 1m / pageSize);
        var chargeItems = new List<CodeHisChargeItemDto>();
        for (int i = 0; i < totalPage; i++)
        {
            query.UsePage(i, pageSize);
            var thatChargeItems = GetBHCodeHisChargeItems(query)
                .Records.CodeHisChargeItems;
            if (!thatChargeItems.IsNullOrEmpty())
                chargeItems.AddRange(thatChargeItems);
        }

        // 保存明细
        var flag = TruncateAndSaveCodeHisChargeItems(chargeItems, out msg);
        if (!flag) return flag;

        msg = string.Format(ResxCommon.SuccessTotal, resultData.TotalNumberOfRecords);
        return true;
    }

    /// <summary>
    /// 获取最新百慧His收费项目信息
    /// </summary>
    /// <param name="query">内容条件</param>
    /// <returns>BHApiPageData</returns>
    BHApiPageData<CodeHisChargeItemRecords> GetBHCodeHisChargeItems(BHApiRecordsQuery query)
    {
        query.NotNullAndEmpty(nameof(query));
        var bhRequestMsg = new BHRequestMsg(ResxExternal.BHApiMDM_queryChargeItems, XmlHelper.Serialize(query));
        var resPageInfo = _bhBasicService.PostMsgTransferPage<CodeHisChargeItemRecords>(bhRequestMsg);

        return resPageInfo.ResultData;
    }

    /// <summary>
    /// 清除源数据并保存体检组合的His收费项目基本信息 (适用一次性更新所有)
    /// </summary>
    /// <param name="codeHisChargeItems">His收费项目集合</param>
    /// <param name="msg">消息</param>
    /// <returns>Boolean</returns>
    bool TruncateAndSaveCodeHisChargeItems(List<CodeHisChargeItemDto> codeHisChargeItems, out string msg)
    {
        if (codeHisChargeItems.IsNullOrEmpty(nameof(codeHisChargeItems), out msg)) return false;

        var batchInsertEntities = new List<CodeHisChargeItem>();
        foreach (var item in codeHisChargeItems)
        {
            if (item.PId.IsNullOrEmpty() || item.ChargeItemCode.IsNullOrEmpty())
                continue;

            var codeHisChargeItem = _mapper.Map<CodeHisChargeItem>(item);
            batchInsertEntities.Add(codeHisChargeItem);
        }

        // 清空表
        _shenShanMainDataRepository.TruncateCodeHisChargeItemTable();
        // 新增
        _codeHisChargeItemRepository.BulkCopy(batchInsertEntities);

        msg = ResxCommon.Success;
        return true;
    }

    /// <summary>
    /// 同步更新体检组合价格
    /// </summary>
    /// <param name="combCodes">组合代码，为空时，更新全部</param>
    /// <param name="msg">消息</param>
    /// <returns>Boolean</returns>
    public bool SyncCodeItemCombPrice(List<string> combCodes, out string msg)
    {
        int count;
        if (!combCodes.IsNullOrEmpty())
            count = BatchUpdateCodeItemCombPriceByCode(combCodes);
        else
            count = BatchUpdateCodeItemCombPriceByPage(); // combCodes 空，全部分页操作

        msg = string.Format(ResxCommon.SuccessTotal, count);
        return true;
    }

    /// <summary>
    /// 根据组合Code，批量更新组合价
    /// </summary>
    /// <param name="combCodes">组合Code</param>
    /// <returns>int</returns>
    int BatchUpdateCodeItemCombPriceByCode(List<string> combCodes)
    {
        combCodes.NotNullAndEmpty(nameof(combCodes));

        combCodes = combCodes.Distinct().ToList();
        var codeItemCombs = _codeItemCombRepository
            .FindAll(a => combCodes.Contains(a.CombCode))
            .ToList();

        return BatchUpdateCodeItemCombPrice(codeItemCombs);
    }

    /// <summary>
    /// 批量分页更新所有组合价
    /// </summary>
    /// <returns>int</returns>
    int BatchUpdateCodeItemCombPriceByPage()
    {
        int count = 0;
        int totalNumber = 0;
        int pageSize = 100;
        int totalPage = 0;
        var pageList = GetCodeItemCombsByPage(1, pageSize, ref totalNumber, ref totalPage);

        for (int i = 1; i <= totalPage; i++)
        {
            if (i > 1)
            {
                pageList.Clear();
                var thatPageList = GetCodeItemCombsByPage(i, pageSize, ref totalNumber, ref totalPage);
                pageList.AddRange(thatPageList);
            }

            count += BatchUpdateCodeItemCombPrice(pageList);
        }

        return count;
    }

    /// <summary>
    /// 分页获取组合
    /// </summary>
    /// <param name="pageNumber"></param>
    /// <param name="pageSize"></param>
    /// <param name="totalNumber"></param>
    /// <param name="totalPage"></param>
    /// <returns>CodeItemComb - List</returns>
    public List<CodeItemComb> GetCodeItemCombsByPage(int pageNumber, int pageSize, ref int totalNumber, ref int totalPage)
    {
        return _shenShanMainDataRepository.QueryCodeItemCombs()
                    .ToPageList(pageNumber, pageSize, ref totalNumber, ref totalPage);
    }

    /// <summary>
    /// 批量更新组合价
    /// </summary>
    /// <param name="combCodes">组合编码</param>
    /// <returns></returns>
    int BatchUpdateCodeItemCombPrice(List<CodeItemComb> combCodes)
    {
        if (combCodes.IsNullOrEmpty()) return 0;

        var batchUpdateEntities = new List<CodeItemComb>();
        foreach (var combCode in combCodes.Distinct())
        {
            var latestPrice = GetLatestCombPrice(combCode);
            if (combCode.Price != latestPrice)
            {
                combCode.Price = latestPrice;
                batchUpdateEntities.Add(combCode);
            }
        }

        if (batchUpdateEntities.IsNullOrEmpty()) return 0;

        var flag = _codeItemCombRepository.Update(batchUpdateEntities, x => x.Price);

        return flag ? batchUpdateEntities.Count : 0;
    }

    /// <summary>
    /// 获取最新收费项目组合价
    /// </summary>
    /// <param name="codeItemComb">组合信息</param>
    decimal GetLatestCombPrice(CodeItemComb codeItemComb)
    {
        if (codeItemComb.IsNullOrEmpty()) return 0;

        var mapChargeItems = _mapCodeItemCombChargeRepository.FindAll(x => x.CombCode.Equals(codeItemComb.CombCode));
        if (mapChargeItems.IsNullOrEmpty()) return 0;

        var mapChargeItemDtos = _mapper.Map<List<MapCodeItemCombHisChargeItemDto>>(mapChargeItems);
        return mapChargeItemDtos.Sum(x => x.HisChargeItemTotalPrice);
    }

    #endregion

    #region 体检组合与His医嘱项目信息
    /// <summary>
    /// 同步His系统的医嘱项目至本库（按分页循环同步）
    /// </summary>
    /// <param name="pageSize">每页大小</param>
    /// <param name="msg">消息</param>
    /// <returns>Boolean</returns>
    public bool SyncCodeHisOrderItems(int pageSize, out string msg)
    {
        var query = new BHApiRecordsQuery();
        query.UsePage(0, 1);
        var resultData = GetBHCodeHisOrderItems(query);
        if (resultData.TotalNumberOfRecords == 0)
        {
            msg = ResxCommon.NotExistRecord;
            return false;
        }

        // 按分页逻辑循环同步
        int totalPage = (int)Math.Ceiling(resultData.TotalNumberOfRecords * 1m / pageSize);
        var orderItems = new List<CodeHisOrderItemDto>();
        for (int i = 0; i < totalPage; i++)
        {
            query.UsePage(i, pageSize);
            var thatOrderItems = GetBHCodeHisOrderItems(query)
                .Records.CodeHisOrderItems;
            if (!thatOrderItems.IsNullOrEmpty())
                orderItems.AddRange(thatOrderItems);
        }

        var flag = TruncateAndSaveCodeHisOrderItems(orderItems, out msg);
        if (!flag) return flag;

        msg = string.Format(ResxCommon.SuccessTotal, resultData.TotalNumberOfRecords);
        return true;
    }

    /// <summary>
    /// 获取最新BH医嘱项目信息
    /// </summary>
    /// <param name="query">内容条件</param>
    /// <returns>BHApiPageData</returns>
    BHApiPageData<CodeHisOrderItemRecords> GetBHCodeHisOrderItems(BHApiRecordsQuery query)
    {
        query.NotNullAndEmpty(nameof(query));
        var bhRequestMsg = new BHRequestMsg(ResxExternal.BHApiMDM_queryOrderItems, XmlHelper.Serialize(query));

        var resPageInfo = _bhBasicService.PostMsgTransferPage<CodeHisOrderItemRecords>(bhRequestMsg);

        return resPageInfo.ResultData;
    }

    /// <summary>
    /// 清除源数据并保存体检组合的His医嘱基本信息 (适用一次性更新所有)
    /// </summary>
    /// <param name="codeHisOrderItems">His收费项目集合</param>
    /// <param name="msg">消息</param>
    /// <returns>Boolean</returns>
    bool TruncateAndSaveCodeHisOrderItems(List<CodeHisOrderItemDto> codeHisOrderItems, out string msg)
    {
        if (codeHisOrderItems.IsNullOrEmpty(nameof(codeHisOrderItems), out msg)) return false;

        var batchInsertEntities = new List<CodeHisOrderItem>();
        var batchInsertChargeEntities = new List<CodeHisOrderItemCharge>();
        foreach (var item in codeHisOrderItems)
        {
            if (item.PId.IsNullOrEmpty() || item.OrderItemCode.IsNullOrEmpty())
                continue;

            var codeHisOrderItem = _mapper.Map<CodeHisOrderItem>(item);
            if (!item.SubOrderItemCharges.IsNullOrEmpty() &&
                !item.SubOrderItemCharges.CodeHisOrderItemCharges.IsNullOrEmpty())
            {
                var codeHisOrderItemCharges = _mapper.Map<List<CodeHisOrderItemCharge>>(item.SubOrderItemCharges.CodeHisOrderItemCharges);
                batchInsertChargeEntities.AddRange(codeHisOrderItemCharges);
                codeHisOrderItem.ItemPrice = codeHisOrderItemCharges.Sum(x => x.BasePrice);
            }

            batchInsertEntities.Add(codeHisOrderItem);
        }

        // 清空表
        _shenShanMainDataRepository.TruncateCodeHisOrderItemTable();
        // 新增
        _codeHisOrderItemRepository.BulkCopy(batchInsertEntities);

        // 清空表
        _shenShanMainDataRepository.TruncateCodeHisOrderItemChargeTable();
        // 新增
        _codeHisOrderItemChargeRepository.BulkCopy(batchInsertChargeEntities);

        msg = ResxCommon.Success;
        return true;
    }

    #endregion

    #region 药品信息
    /// <summary>
    /// 同步His系统的药品信息至本库（按分页循环同步）
    /// </summary>
    /// <param name="pageSize">每页大小</param>
    /// <param name="msg">消息</param>
    /// <returns>Boolean</returns>
    public bool SyncCodeHisDrugItems(int pageSize, out string msg)
    {
        var query = new BHApiRecordsQuery();
        query.UsePage(0, 1);
        var resultData = GetBHCodeHisDrugItems(query);
        if (resultData.TotalNumberOfRecords == 0)
        {
            msg = ResxCommon.NotExistRecord;
            return false;
        }

        // 按分页逻辑循环同步
        int totalPage = (int)Math.Ceiling(resultData.TotalNumberOfRecords * 1m / pageSize);
        var drugItems = new List<CodeHisDrugItemDto>();
        for (int i = 0; i < totalPage; i++)
        {
            query.UsePage(i, pageSize);
            var thatChargeItems = GetBHCodeHisDrugItems(query).Records.CodeHisDrugItems;
            if (!thatChargeItems.IsNullOrEmpty())
                drugItems.AddRange(thatChargeItems);
        }

        // 保存明细
        var flag = TruncateAndSaveCodeHisDrugItems(drugItems, out msg);
        if (!flag) return flag;

        msg = string.Format(ResxCommon.SuccessTotal, resultData.TotalNumberOfRecords);
        return true;
    }

    /// <summary>
    /// 获取最新百慧His药品信息
    /// </summary>
    /// <param name="query">内容条件</param>
    /// <returns>BHApiPageData</returns>
    BHApiPageData<CodeHisDrugItemRecords> GetBHCodeHisDrugItems(BHApiRecordsQuery query)
    {
        query.NotNullAndEmpty(nameof(query));
        var bhRequestMsg = new BHRequestMsg(ResxExternal.BHApiMDM_queryDrugs, XmlHelper.Serialize(query));
        var resPageInfo = _bhBasicService.PostMsgTransferPage<CodeHisDrugItemRecords>(bhRequestMsg);

        return resPageInfo.ResultData;
    }

    /// <summary>
    /// 清除源数据并保存体检组合的His药品基本信息 (适用一次性更新所有)
    /// </summary>
    /// <param name="codeHisDrugItems">His药品基本信息集合</param>
    /// <param name="msg">消息</param>
    /// <returns>Boolean</returns>
    bool TruncateAndSaveCodeHisDrugItems(List<CodeHisDrugItemDto> codeHisDrugItems, out string msg)
    {
        if (codeHisDrugItems.IsNullOrEmpty(nameof(codeHisDrugItems), out msg)) return false;

        var batchInsertEntities = new List<CodeHisDrugItem>();
        foreach (var item in codeHisDrugItems)
        {
            if (item.PId.IsNullOrEmpty() || item.DrugCode.IsNullOrEmpty())
                continue;

            var codeHisDrugItem = _mapper.Map<CodeHisDrugItem>(item);
            batchInsertEntities.Add(codeHisDrugItem);
        }

        // 清空表
        _shenShanMainDataRepository.TruncateCodeHisDrugItemTable();
        // 新增
        _codeHisDrugItemRepository.BulkCopy(batchInsertEntities);

        msg = ResxCommon.Success;
        return true;
    }
    #endregion

    #endregion

    #endregion

}
