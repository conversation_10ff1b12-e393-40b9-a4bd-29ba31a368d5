﻿using SqlSugar;

namespace Peis.Model.DataBaseEntity
{
    ///<summary>
    ///用户绑定角色信息
    ///</summary>
    [SugarTable("SysOperatorRole")]
    public class SysOperatorRole
    {
        /// <summary>
        /// 用户代码
        /// </summary>        
        [SugarColumn(IsNullable = false, IsPrimaryKey = true, Length = 10)]
        public string OperatorCode { get; set; }

        /// <summary>
        /// 角色代码
        /// </summary>        
        [SugarColumn(IsNullable = false, IsPrimaryKey = true, Length = 10)]
        public string RoleCode { get; set; }
    }
}