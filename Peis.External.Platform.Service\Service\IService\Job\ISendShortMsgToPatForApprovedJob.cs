﻿namespace Peis.External.Platform.Service.Service.IService.Job;

public interface ISendShortMsgToPatForApprovedJob
{
    /// <summary>
    /// 添加任务
    /// </summary>
    /// <param name="regNo"></param>
    /// <returns></returns>
    Task AddJob(string regNo);

    /// <summary>
    /// 删除任务
    /// </summary>
    /// <param name="regNo"></param>
    /// <returns></returns>
    Task RemoveJob(string regNo);
}
