﻿namespace Peis.Model.DataBaseEntity.Occupation
{
    /// <summary>
    /// 岗位状态
    /// </summary>
    [SugarTable]
    public class CodeOccupationalPositionStatus
    {
        /// <summary>
        /// 代码
        /// </summary>
        [SugarColumn(Length =4,IsPrimaryKey = true)]
        public string StatusCode { get; set; }
        /// <summary>
        /// 名称
        /// </summary>
        [SugarColumn(Length =20,IsNullable = false)]
        public string StatusName { get; set; }
    }
}
