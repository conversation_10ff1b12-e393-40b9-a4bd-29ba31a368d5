﻿using SqlSugar;

namespace Peis.Model.DataBaseEntity
{
    ///<summary>
    ///条码分类
    ///</summary>
    [SugarTable("CodeBarcodeType")]
    public class CodeBarcodeType
    {
        /// <summary>
        /// 类型代码
        /// </summary>        
        [SugarColumn(IsNullable = false, IsPrimaryKey = true, Length = 4)]
        public string Barcode { get; set; }

        /// <summary>
        /// 类型名称
        /// </summary>        
        [SugarColumn(IsNullable = false, Length = 20)]
        public string BarcodeName { get; set; }

        /// <summary>
        /// 颜色
        /// </summary>        
        [SugarColumn(IsNullable = true, Length = 10)]
        public string Color { get; set; }

        /// <summary>
        /// 颜色名称
        /// </summary>        
        [SugarColumn(IsNullable = true, Length = 200)]
        public string ColorName { get; set; }

        /// <summary>
        /// 注意事项
        /// </summary>        
        [SugarColumn(IsNullable = true, Length = 255)]
        public string Attention { get; set; }

        /// <summary>
        /// 标本类型
        /// </summary>        
        [SugarColumn(IsNullable = true, Length = 4)]
        public string SampCode { get; set; }

        /// <summary>
        /// 条码前缀
        /// </summary>        
        [SugarColumn(IsNullable = true, Length = 255)]
        public string PreNum { get; set; }

        /// <summary>
        /// 序号前缀
        /// </summary>        
        [SugarColumn(IsNullable = true, Length = 20)]
        public string PreText { get; set; }

        /// <summary>
        /// 条码打印份数
        /// </summary>        
        [SugarColumn(IsNullable = false)]
        public int PrintTimes { get; set; }

        /// <summary>
        /// 备注
        /// </summary>        
        [SugarColumn(IsNullable = true, Length = 255)]
        public string Note { get; set; }

        /// <summary>
        /// 试管组合代码
        /// </summary>        
        [SugarColumn(IsNullable = true, Length = 20)]
        public string FeeCombCode { get; set; }
    }
}