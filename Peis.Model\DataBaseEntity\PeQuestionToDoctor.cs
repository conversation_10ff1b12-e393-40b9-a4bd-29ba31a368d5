﻿using Peis.Model.TableFilter;
using SqlSugar;
using System;

namespace Peis.Model.DataBaseEntity
{
    ///<summary>
    ///医生问题单
    ///</summary>
    [SugarTable("PeQuestionToDoctor")]
    public class PeQuestionToDoctor: IHospCodeFilter
    {
        /// <summary>
        /// Id
        /// </summary>        
        [SugarColumn(IsNullable = false, IsPrimaryKey = true, IsIdentity = true)]
        public int Id { get; set; }

        /// <summary>
        /// 体检号
        /// </summary>        
        [SugarColumn(IsNullable = false, Length = 12)]
        public string RegNo { get; set; }

        /// <summary>
        /// 组合代码
        /// </summary>        
        [SugarColumn(IsNullable = false, Length = 8)]
        public string CombCode { get; set; }

        /// <summary>
        /// 组合名称
        /// </summary>        
        [SugarColumn(IsNullable = false, Length = 200)]
        public string CombName { get; set; }

        /// <summary>
        /// 咨询医生代码
        /// </summary>        
        [SugarColumn(IsNullable = false, Length = 10)]
        public string QuestionerCode { get; set; }

        /// <summary>
        /// 咨询医生名称
        /// </summary>        
        [SugarColumn(IsNullable = false, Length = 50)]
        public string QuestionerName { get; set; }

        /// <summary>
        /// 答复医生名称
        /// </summary>        
        [SugarColumn(IsNullable = false, Length = 50)]
        public string ReplyerName { get; set; }

        /// <summary>
        /// 咨询内容
        /// </summary>        
        [SugarColumn(IsNullable = false, ColumnDataType = StaticConfig.CodeFirst_BigString)]
        public string QuestionContent { get; set; }

        /// <summary>
        /// 答复内容
        /// </summary>        
        [SugarColumn(IsNullable = true, ColumnDataType = StaticConfig.CodeFirst_BigString)]
        public string ReplyContent { get; set; }

        /// <summary>
        /// 咨询时间
        /// </summary>        
        [SugarColumn(IsNullable = false)]
        public DateTime QuestionTime { get; set; }

        /// <summary>
        /// 答复时间
        /// </summary>        
        [SugarColumn(IsNullable = true)]
        public DateTime? ReplyTime { get; set; }

        /// <summary>
        /// 解锁时间
        /// </summary>        
        [SugarColumn(IsNullable = true)]
        public DateTime? UnlockTime { get; set; }

        /// <summary>
        /// 院区编码
        /// </summary>        
        [SugarColumn(IsNullable = false, Length = 1)]
        public string HospCode { get; set; }
    }
}