﻿using Peis.Model.DataBaseViewModel.External;
using Peis.Model.DTO.External.DataMaintenances;

namespace Peis.Model.DataBaseEntity.External;

///<summary>
/// 基础数据：体检项目与Lis检验项目对应关系中间表
///</summary>
[SugarTable(nameof(MapCodeLisItem), TableDescription = "体检项目与Lis检验项目对应关系中间表")]
public class MapCodeLisItem
{
    public MapCodeLisItem() { }

    public MapCodeLisItem([NotNull] CodeLisItemDto codeLisItem)
    {
        ItemCode = codeLisItem.ItemCode;
        LisCombCode = codeLisItem.LisCombCode;
        LisCombCNName = codeLisItem.LisCombCNName;
        LisItemCode = codeLisItem.LisItemCode;
        LisItemCNName = codeLisItem.LisItemCNName;
        LisItemENName = codeLisItem.LisItemENName;
    }

    public void SetName([NotNull] v_lis_item codeLisItem)
    {
        LisCombCNName = codeLisItem.LisCombCNName;
        LisItemCNName = codeLisItem.LisItemCNName;
        LisItemENName = codeLisItem.LisItemENName;
    }

    /// <summary>
    /// 体检项目代码
    /// </summary>        
    [SugarColumn(IsPrimaryKey = true, Length = 6)]
    public string ItemCode { get; set; }

    /// <summary>
    /// Lis组合代码
    /// </summary>        
    [SugarColumn(IsPrimaryKey = true, Length = 64)]
    public string LisCombCode { get; set; }

    /// <summary>
    /// 检验组合中文名称
    /// </summary>     
    [SugarColumn(IsNullable = true, Length = 256)]
    public string LisCombCNName { get; set; }

    /// <summary>
    /// 项目代码
    /// </summary>        
    [SugarColumn(IsPrimaryKey = true, Length = 64)]
    public string LisItemCode { get; set; }

    /// <summary>
    /// 项目中文名称
    /// </summary>        
    [SugarColumn(IsNullable = true, Length = 256)]
    public string LisItemCNName { get; set; }

    /// <summary>
    /// 项目英文名称
    /// </summary>        
    [SugarColumn(IsNullable = true, Length = 128)]
    public string LisItemENName { get; set; }
}