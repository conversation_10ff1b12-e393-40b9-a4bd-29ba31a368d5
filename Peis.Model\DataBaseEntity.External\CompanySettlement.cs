﻿using Peis.Model.DTO.CompanySettlement;
using Peis.Model.DTO.External.His;
using Peis.Model.Other.PeEnum;

namespace Peis.Model.DataBaseEntity.External
{
    /// <summary>
    /// 团体结算记录主表
    /// </summary>
    [SugarTable]
    public class CompanySettlement
    {
        /// <summary>
        /// Id
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public long Id { get; set; }
        /// <summary>
        /// 当次结算流水号
        /// </summary>
        [SugarColumn(IsNullable = false, Length = 10, IsPrimaryKey = true)]
        public string BillSeqNo { set; get; }
        /// <summary>
        /// 单位代码
        /// </summary>
        [SugarColumn(IsNullable = false, Length = 6)]
        public string CompanyCode { set; get; }
        /// <summary>
        /// 单位次数
        /// </summary>
        [SugarColumn(IsNullable = false)]
        public int CompanyTimes { set; get; }
        /// <summary>
        /// 结算次数
        /// </summary>
        [SugarColumn(IsNullable = false)]
        public int BillTimes { set; get; }
        /// <summary>
        /// 体检人数
        /// </summary>
        [SugarColumn(IsNullable = false)]
        public int BillPersonCount { set; get; }
        /// <summary>
        /// 发票名称
        /// </summary>
        [SugarColumn(IsNullable = false, Length = 100)]
        public string InvoiceName { set; get; }
        /// <summary>
        /// 发票ID
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public int? InvoiceId { set; get; }
        /// <summary>
        /// 发票编号
        /// </summary>
        [SugarColumn(IsNullable = true, Length = 20)]
        public string InvoiceNo { set; get; }
        /// <summary>
        /// 经手人
        /// </summary>
        [SugarColumn(IsNullable = false, Length = 10)]
        public string SendOperator { set; get; }
        /// <summary>
        /// 创建时间
        /// </summary>
        [SugarColumn(IsNullable = false)]
        public DateTime CreateTime { set; get; }
        /// <summary>
        /// 收费状态
        /// </summary>
        [SugarColumn(IsNullable = false)]
        public PayStatus PayStatus { set; get; }
        /// <summary>
        /// 结算时间
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public DateTime? ChargeTime { set; get; }
        /// <summary>
        /// 支付类型
        /// </summary>
        [SugarColumn(IsNullable = false)]
        public FeeType FeeType { set; get; }
        /// <summary>
        /// 原始金额
        /// </summary>
        [SugarColumn(IsNullable = false, Length = 12, DecimalDigits = 2)]
        public decimal OriginalPrice { set; get; }
        /// <summary>
        /// 实收金额
        /// </summary>
        [SugarColumn(IsNullable = false, Length = 12, DecimalDigits = 2)]
        public decimal ActuallyPrice { set; get; }
        /// <summary>
        /// 统计类型
        /// </summary>
        [SugarColumn(IsNullable = false)]
        public StatisticType CaluateType { set; get; }
        /// <summary>
        /// 审核人
        /// </summary>
        [SugarColumn(IsNullable = true, Length = 10)]
        public string AuditOperator { set; get; }
        /// <summary>
        /// 审核时间
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public DateTime? AuditTime { set; get; }

        /// <summary>
        /// 税号
        /// </summary>        
        [SugarColumn(IsNullable = true, Length = 50)]
        public string TaxID { get; set; }

        /// <summary>
        /// 收费人员
        /// </summary>
        [SugarColumn(IsNullable = true, Length = 20)]
        public string FeeOperator { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        [SugarColumn(IsNullable = true, Length = 255)]
        public string Note { get; set; }

        /// <summary>
        /// 统计时间范围
        /// </summary>
        [SugarColumn(IsNullable = true, Length = 50)]
        public string ChargeDateScope { get; set; }

        /// <summary>
        /// 电子发票号
        /// </summary>
        [SugarColumn(IsNullable = true, Length = 20)]
        public string ElectronicInvoiceNo { get; set; }
    }
}
