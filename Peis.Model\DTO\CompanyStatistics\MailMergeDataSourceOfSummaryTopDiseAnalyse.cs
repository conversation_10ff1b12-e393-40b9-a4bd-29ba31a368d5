﻿using Aspose.Words.MailMerging;

namespace Peis.Model.DTO.CompanyStatistics;

/// <summary>
/// Aspose.Words-自定义邮件合并数据源-前几项分析表
/// </summary>
public class MailMergeDataSourceOfSummaryTopDiseAnalyse : IMailMergeDataSource
{
    private readonly List<CompanySumDiseaseCls> _customers;
    private int _currentIndex;

    public MailMergeDataSourceOfSummaryTopDiseAnalyse(List<CompanySumDiseaseCls> customers)
    {
        _customers = customers;
        _currentIndex = -1;
    }

    public string TableName => "SummaryTopDiseAnalyseList";

    public bool GetValue(string fieldName, out object fieldValue)
    {
        var customer = _customers[_currentIndex];
        var prop = customer.GetType().GetProperty(fieldName);

        if (prop != null)
        {
            fieldValue = prop.GetValue(customer);
            return true;
        }

        fieldValue = default;
        return false;
    }

    public bool MoveNext()
    {
        _currentIndex++;
        return _currentIndex < (_customers?.Count ?? 0);
    }

    public IMailMergeDataSource GetChildDataSource(string tableName)
    {
        return default;
    }
}
