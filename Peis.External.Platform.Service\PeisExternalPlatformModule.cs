﻿using Microsoft.AspNetCore.Builder;
using Peis.External.Platform.Service.Repository.RepositoryImpl;
using Peis.External.Platform.Service.Service.IService.WebService;
using Peis.Repository;
using Peis.Service;
using Peis.Utility.Middleware;
using SoapCore;
using System.Runtime.CompilerServices;
using System.ServiceModel.Channels;
using Volo.Abp;
using Volo.Abp.Modularity;

namespace Peis.External.Platform.Service
{
    [DependsOn(typeof(PeisRepositoryModule))]
    [DependsOn(typeof(PeisServiceModule))]
    public class PeisExternalPlatformModule : AbpModule
    {
        public override void ConfigureServices(ServiceConfigurationContext context)
        {
            var services = context.Services;
            var builder = services.GetContainerBuilder();
            RegisterRepository(builder);
            RegisterService(builder);
            // 添加Soap
            services.AddSoapCore();
        }

        public override void OnApplicationInitialization(ApplicationInitializationContext context)
        {
            var app = context.GetApplicationBuilder();          
        }

        public override void OnPostApplicationInitialization(ApplicationInitializationContext context)
        {
            var app = context.GetApplicationBuilder();
            app.UseReqResLoggerMiddleware("/WebServices");
            app.UseSoapEndpointShenShan();
        }
        #region 私有方法 注入仓储和服务
        private void RegisterRepository(ContainerBuilder builder)
        {
            var baseTypes = Assembly.GetExecutingAssembly().GetTypes()
                                    .Where(x => x.IsClass && !x.IsSealed && !x.IsAbstract && !x.IsDefined(typeof(CompilerGeneratedAttribute), false))
                                    .Where(x => x.Namespace.StartsWith("Peis.External.Platform.Service.Repository.RepositoryImpl"))
                                    .ToArray();
            builder.RegisterTypes(baseTypes).AsImplementedInterfaces().InstancePerLifetimeScope();

        }

        private void RegisterService(ContainerBuilder builder)
        {
            var baseTypes = Assembly.GetExecutingAssembly().GetTypes()
                                     .Where(x => x.IsClass && !x.IsSealed && !x.IsAbstract && !x.IsDefined(typeof(CompilerGeneratedAttribute), false))
                                     .Where(x => x.Namespace.StartsWith("Peis.External.Platform.Service.Service.ServiceImpl"))
                                     .ToArray();

            // 注册自定义服务到Autofac容器中
            builder.RegisterTypes(baseTypes)
                    .AsImplementedInterfaces()
                    .InstancePerLifetimeScope();
        }
        #endregion
    }
}


/// <summary>
/// 深汕Setup
/// </summary>
internal static class ShenShanSetup
{
    /// <summary>
    /// 使用WebService
    /// </summary>
    /// <param name="builder"></param>
    /// <returns></returns>
    public static IApplicationBuilder UseSoapEndpointShenShan(this IApplicationBuilder builder)
    {
        // 使用 SoapCore 终结点
        var soapOpts = new SoapEncoderOptions[]
        {
            new SoapEncoderOptions { MessageVersion = MessageVersion.Soap11 },
            new SoapEncoderOptions { MessageVersion = MessageVersion.Soap12WSAddressing10 },
        };
        builder.UseSoapEndpoint<IExternalWebService>("/WebServices/External.asmx", soapOpts, SoapSerializer.DataContractSerializer);

        return builder;
    }
}