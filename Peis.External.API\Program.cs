using Peis.Utility.Helper;
using Serilog;
using Serilog.Events;

namespace Peis.External.API;

public class Program
{
    public static void Main(string[] args)
    {
        CreateLogger();

        try
        {
            Log.Information("Starting web host.");
            var builder = CreateWebApplicationBuilder(args);
            var app = builder.Build();
            app.InitializeApplication();
            ServiceLocator.Initialize(app);

            Log.Information("- {Urls}", Appsettings.GetSectionArraryValue("AppSettings:RunUrls"));
            app.Run();
        }
        catch (Exception ex)
        {
            Log.Fatal(ex, "Host terminated unexpectedly!");
        }
        finally
        {
            Log.CloseAndFlush();
        }
    }

    static WebApplicationBuilder CreateWebApplicationBuilder(string[] args)
    {
        var builder = WebApplication.CreateBuilder(args);
        builder.WebHost.UseUrls(Appsettings.GetSectionArraryValue("AppSettings:RunUrls"));
        builder.Host.UseAutofac();
        builder.Host.UseSerilog();
        builder.Services.ReplaceConfiguration(builder.Configuration);
        builder.Services.AddApplication<PeisExternalApiModule>();
        return builder;
    }

    static void CreateLogger()
    {
        var consoleTemplate = "[{Timestamp:HH:mm:ss} {Level:u3}] {CorrelationId} {Message:lj}{NewLine}{Exception}";
        Log.Logger = new LoggerConfiguration()
        .MinimumLevel.Information()
        .MinimumLevel.Override("Microsoft", LogEventLevel.Warning)
        .MinimumLevel.Override("Volo.Abp", LogEventLevel.Warning)
        .Enrich.FromLogContext()
        .WriteTo.Console(outputTemplate: consoleTemplate)
        .WriteTo.Async(congfig => congfig.File(
            path: $@"{AppContext.BaseDirectory}\logs\log_.log",
            restrictedToMinimumLevel: LogEventLevel.Information,
            outputTemplate: consoleTemplate,
            rollingInterval: RollingInterval.Day,
            retainedFileCountLimit: 180))
        .CreateLogger();
    }
}
