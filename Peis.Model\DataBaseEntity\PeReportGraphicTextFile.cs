﻿using Peis.Model.DTO.ReportGraphicText;
using System.IO;

namespace Peis.Model.DataBaseEntity;

///<summary>
/// 体检图文报告表
///</summary>
[SplitTable(SplitType.Year)]
[SugarTable("PeReportGraphicTextFile_{yyyy}", "体检图文报告表")]
[SugarIndex("index_PeReportGraphicTextFile_RegNo_RegT", nameof(RegNo), OrderByType.Asc, nameof(RegisterTime), OrderByType.Asc)]
public class PeReportGraphicTextFile
{
    public void SetFileInfo(
        [NotNull] string filePath,
        [NotNull] FileInfo file,
        [NotNull] string updatedCode,
        [NotNull] string updatedName)
    {
        FilePath = filePath.Replace(@"\", @"/");
        FileName = Path.GetFileNameWithoutExtension(file.Name);
        FileSize = file.Length;
        FileType = file.Extension;
        Status = EnumReportGraphicTextStatus.Completed;
        SetUpdateValue(updatedCode, updatedName);
    }

    public void SetUpdateValue([NotNull] string updatedCode, [NotNull] string updatedName)
    {
        UpdatedCode = updatedCode ?? "root";
        UpdatedName = updatedName ?? "root";
        UpdatedTime = DateTime.Now;
    }

    /// <summary>
    /// 雪花id
    /// </summary>        
    [SugarColumn(IsNullable = false, IsPrimaryKey = true)]
    public long Id { get; set; }

    /// <summary>
    /// 体检号
    /// </summary>        
    [SugarColumn(IsNullable = false, Length = 12)]
    public string RegNo { get; set; }

    /// <summary>
    /// 体检登记时间（分表依据）
    /// </summary>        
    [SplitField]
    [SugarColumn(IsNullable = false, IsOnlyIgnoreUpdate = true)]
    public DateTime RegisterTime { get; set; }

    /// <summary>
    /// 登记组合id
    /// </summary>        
    [SugarColumn(IsNullable = false)]
    public long RegCombId { get; set; }

    /// <summary>
    /// 组合码
    /// </summary>        
    [SugarColumn(IsNullable = false, Length = 8)]
    public string CombCode { get; set; }

    /// <summary>
    /// 文件名称
    /// </summary>        
    [SugarColumn(IsNullable = true, Length = 90)]
    public string FileName { get; set; }

    /// <summary>
    /// 文件路径
    /// </summary>        
    [SugarColumn(IsNullable = true, Length = 256)]
    public string FilePath { get; set; }

    /// <summary>
    /// 文件大小，单位字节
    /// </summary>        
    [SugarColumn(IsNullable = false)]
    public long FileSize { get; set; }

    /// <summary>
    /// 文件格式
    /// </summary>        
    [SugarColumn(IsNullable = true, Length = 30)]
    public string FileType { get; set; }

    /// <summary>
    /// 拉取报告地址
    /// </summary>        
    [SugarColumn(IsNullable = false, Length = 512)]
    public string ReportUrl { get; set; }

    /// <summary>
    /// 状态：10-待生成报告，20-已生成报告
    /// </summary>        
    [SugarColumn(IsNullable = false)]
    public EnumReportGraphicTextStatus Status { get; set; }

    /// <summary>
    /// 创建人Code
    /// </summary>        
    [SugarColumn(IsNullable = false, Length = 10, IsOnlyIgnoreUpdate = true)]
    public string CreatorCode { get; set; }

    /// <summary>
    /// 创建人名称
    /// </summary>        
    [SugarColumn(IsNullable = false, Length = 50, IsOnlyIgnoreUpdate = true)]
    public string CreatorName { get; set; }

    /// <summary>
    /// 创建时间
    /// </summary>        
    [SugarColumn(IsNullable = false, IsOnlyIgnoreUpdate = true)]
    public DateTime CreatedTime { get; set; }

    /// <summary>
    /// 修改人Code
    /// </summary>        
    [SugarColumn(IsNullable = true, Length = 10)]
    public string UpdatedCode { get; set; }

    /// <summary>
    /// 修改人名称
    /// </summary>        
    [SugarColumn(IsNullable = true, Length = 50)]
    public string UpdatedName { get; set; }

    /// <summary>
    /// 修改时间
    /// </summary>        
    [SugarColumn(IsNullable = true)]
    public DateTime? UpdatedTime { get; set; }
}
