﻿using SqlSugar;

namespace Peis.Model.DataBaseEntity
{
    ///<summary>
    ///菜单功能接口绑定
    ///</summary>
    [SugarTable("SysFuncApi")]
    public class SysFuncApi
    {
        /// <summary>
        /// 功能代码
        /// </summary>        
        [SugarColumn(IsNullable = false, IsPrimaryKey = true, Length = 50)]
        public string FuncCode { get; set; }

        /// <summary>
        /// 接口代码
        /// </summary>        
        [SugarColumn(IsNullable = false, IsPrimaryKey = true)]
        public int ApiCode { get; set; }
    }
}