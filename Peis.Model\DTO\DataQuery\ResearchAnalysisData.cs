﻿using Peis.Model.Other.PeEnum;
using System.Collections.Generic;

namespace Peis.Model.DTO.DataQuery
{
    /// <summary>
    /// 科研分析数据
    /// </summary>
    public class ResearchAnalysisData
    {
        /// <summary>
        /// 表头
        /// </summary>
        public Dictionary<string, string> Header { get; set; }

        /// <summary>
        /// 表数据
        /// </summary>
        public List<Dictionary<string, string>> BodyData { get; set; }
    }

    /// <summary>
    /// 科研分析
    /// </summary>
    public class ResearchAnalysis
    {
        /// <summary>
        /// 单位
        /// </summary>
        public string CompanyName { get; set; }

        /// <summary>
        /// 单位部门
        /// </summary>
        public string DeptName { get; set; }

        /// <summary>
        /// 体检号
        /// </summary>
        public string RegNo { get; set; }

        /// <summary>
        /// 姓名
        /// </summary>
        public string Name { get; set; }

        /// <summary>
        /// 性别
        /// </summary>
        public Sex? Sex { get; set; }

        /// <summary>
        /// 年龄
        /// </summary>
        public int? Age { get; set; }

        /// <summary>
        /// 电话
        /// </summary>
        public string Tel { get; set; }

        /// <summary>
        /// 证件号
        /// </summary>
        public string CardNo { get; set; }

        /// <summary>
        /// 体检日期
        /// </summary>
        public string ActiveDate { get; set; }

        /// <summary>
        /// 体检医生
        /// </summary>
        public string DoctorName { get; set; }

        /// <summary>
        /// 动态列(组合名称/项目名称)
        /// </summary>
        public string DynamicColumn { get; set; }

        /// <summary>
        ///动态列结果(组合小结/项目结果)
        /// </summary>
        public string DynamicColumnTag { get; set; }
    }
}
