﻿using Microsoft.AspNetCore.Mvc;
using Peis.Model.DTO.DataQuery;
using Peis.Model.Other.Input.DataQuery;
using Peis.Service.IService;

namespace Peis.API.Controllers.Business
{
    [Route("api/[controller]")]
    [ApiController]
    public class FollowUpController : BaseApiController
    {
        private readonly IFollowUpService _followUpService;

        public FollowUpController(IFollowUpService followUpService)
        {
            _followUpService = followUpService;
        }

        /// <summary>
        /// 体检随访查询
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        [HttpPost("PeFollowUpQuery")]
        [ProducesResponseType(typeof(FollowUpData[]), 200)]
        public IActionResult PeFollowUpQuery([FromBody] FollowUpQuery query)
        {
            result.ReturnData = _followUpService.PeFollowUpQuery(query);
            return Ok(result);
        }

        /// <summary>
        /// 保存体检随访
        /// </summary>
        /// <param name="data"></param>
        /// <returns></returns>
        [HttpPost("SavePeFollowUp")]
        public IActionResult SavePeFollowUp([FromBody] FollowUpData data)
        {
            result.Success = _followUpService.SavePeFollowUp(data);
            return Ok(result);
        }

        /// <summary>
        /// 新增体检随访
        /// </summary>
        /// <param name="data"></param>
        /// <returns></returns>
        [HttpPost("InsertPeFollowUp")]
        public IActionResult InsertPeFollowUp([FromBody] InsertFollowUp data)
        {
            string msg = string.Empty;
            result.Success = _followUpService.InsertPeFollowUp(data);
            result.ReturnMsg = msg;
            return Ok(result);
        }

        /// <summary>
        /// 删除体检随访
        /// </summary>
        /// <param name="followUpId">随访记录ID</param>
        /// <returns></returns>    
        [HttpPost("DeletePeFollowUp")]
        public IActionResult DeletePeFollowUp([FromQuery] long followUpId)
        {
            result.Success = _followUpService.DeletePeFollowUp(followUpId);
            return Ok(result);
        }

        /// <summary>
        /// 手动新增体检随访
        /// </summary>
        /// <param name="data"></param>
        /// <returns></returns>
        [HttpPost("ManualInsertPeFollowUp")]
        public IActionResult ManualInsertPeFollowUp([FromBody] ManualInsertFollowUp data)
        {
            string msg = string.Empty;
            result.Success = _followUpService.ManualInsertPeFollowUp(data);
            result.ReturnMsg = msg;
            return Ok(result);
        }

        /// <summary>
        /// 是否已随访
        /// </summary>
        /// <param name="regNo"></param>
        /// <returns></returns>
        [HttpPost("HasFollowUp")]
        public IActionResult HasFollowUp([FromQuery] string regNo)
        {
            result.ReturnData = _followUpService.HasFollowUp(regNo);
            return Ok(result);
        }

        /// <summary>
        /// 是否有未处理的随访
        /// </summary>
        /// <param name="regNo"></param>
        /// <returns></returns>
        [HttpPost("HasNotHandledFollowUp")]
        public IActionResult HasNotHandledFollowUp([FromQuery] string regNo)
        {
            result.ReturnData = _followUpService.HasNotHandledFollowUp(regNo);
            return Ok(result);
        }

        /// <summary>
        /// 恢复体检随访
        /// </summary>
        /// <param name="followUpIds">随访记录ID</param>
        /// <returns></returns>    
        [HttpPost("RestorePeFollowUp")]
        public IActionResult RestorePeFollowUp([FromBody] long[] followUpIds)
        {
            result.Success = _followUpService.RestorePeFollowUp(followUpIds);
            return Ok(result);
        }

        /// <summary>
        /// 体检随访查询
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        [HttpPost("QueryDeletePeFollowUp")]
        [ProducesResponseType(typeof(FollowUpData[]), 200)]
        public IActionResult QueryDeletePeFollowUp([FromBody] FollowUpQuery query)
        {
            result.ReturnData = _followUpService.QueryDeletePeFollowUp(query);
            return Ok(result);
        }

        /// <summary>
        /// 彻底删除体检随访
        /// </summary>
        /// <param name="followUpIds">随访记录ID</param>
        /// <returns></returns>    
        [HttpPost("CompletelyDeletePeFollowUp")]
        public IActionResult CompletelyDeletePeFollowUp([FromBody] long[] followUpIds)
        {
            result.Success = _followUpService.CompletelyDeletePeFollowUp(followUpIds);
            return Ok(result);
        }
    }
}
