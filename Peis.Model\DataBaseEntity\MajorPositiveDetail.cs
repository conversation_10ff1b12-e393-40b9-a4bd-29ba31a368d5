﻿using Peis.Model.Other.PeEnum;

namespace Peis.Model.DataBaseEntity
{
    [SugarTable("MajorPositiveDetail")]
    [SugarIndex("index_RegNo_MajorPositiveDetail", nameof(RegNo),OrderByType.Asc)]
    public class MajorPositiveDetail
    {
        /// <summary>
        /// Id
        /// </summary>
        [SugarColumn(IsPrimaryKey = true)]
        public long Id { get; set; }
        /// <summary>
        /// 体检号
        /// </summary>        
        [SugarColumn(IsNullable = false, Length = 12)]
        public string RegNo { get; set; }

        /// <summary>
        /// 重大阳性代码
        /// </summary>        
        [SugarColumn(IsNullable = true, Length = 8)]
        public string PositiveCode { get; set; }

        /// <summary>
        /// 重大阳性名称
        /// </summary>        
        [SugarColumn(IsNullable = false, Length = 200)]
        public string PositiveName { get; set; }

        /// <summary>
        /// 重大阳性类型 1:A类 2:B类
        /// </summary>        
        [SugarColumn(IsNullable = false)]
        public PositiveType PositiveType { get; set; }

        /// <summary>
        /// 记录人
        /// </summary>
        [SugarColumn(IsNullable = false, Length = 50)]
        public string RecordOperator { get; set; }

        /// <summary>
        /// 记录时间
        /// </summary>
        [SugarColumn(IsNullable = false)]
        public DateTime RecordTime { get; set; }

        /// <summary>
        /// 是否确认
        /// </summary>
        [SugarColumn(IsNullable = false)]
        public bool IsConfirm { get; set; }
        /// <summary>
        /// 确认人
        /// </summary>
        [SugarColumn(IsNullable = true, Length = 50)]
        public string ConfirmOperator { get; set; }

        /// <summary>
        /// 确认时间
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public DateTime? ConfirmTime { get; set; }

        /// <summary>
        /// 绑定随访ID
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public long? FollowUpId { get; set; }

        /// <summary>
        /// 是否删除
        /// </summary>
        [SugarColumn(IsNullable = false)]
        public bool IsDeleted { get; set; }

        /// <summary>
        /// 删除时间
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public DateTime? DeletedTime { get; set; }

        /// <summary>
        /// 绑定组合代码
        /// </summary>
        [SugarColumn(IsNullable = true, Length = 10)]
        public string CombCode { get; set; }

        /// <summary>
        /// 绑定组合下项目内容
        /// </summary>
        [Navigate(NavigateType.OneToMany, nameof(MajorPositiveDetailItem.DetailId))]
        public List<MajorPositiveDetailItem> Items { get; set; }

        #region Ext
        /// <summary>
        /// 绑定组合名称
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public string CombName { get; set; }

        /// <summary>
        /// 体检人信息
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public PeRegister RegInfo { get; set; }
        #endregion
    }
}
