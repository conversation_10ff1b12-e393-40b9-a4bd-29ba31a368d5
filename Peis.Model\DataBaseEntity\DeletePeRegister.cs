﻿using Peis.Model.Other.PeEnum;
using Peis.Model.TableFilter;

namespace Peis.Model.DataBaseEntity
{
    ///<summary>
    ///登记删除表
    ///</summary>
    [SugarTable("DeletePeRegister")]
    public class DeletePeRegister: IHospCodeFilter
    {
        /// <summary>
        /// 体检号
        /// </summary>        
        [SugarColumn(IsNullable = false, IsPrimaryKey = true, Length = 12)]
        public string RegNo { get; set; }

        /// <summary>
        /// 档案号
        /// </summary>        
        [SugarColumn(IsNullable = false, Length = 12)]
        public string PatCode { get; set; }

        /// <summary>
        /// 姓名
        /// </summary>        
        [SugarColumn(IsNullable = false, Length = 50)]
        public string Name { get; set; }

        /// <summary>
        /// 性别（0通用1男2女）
        /// </summary>        
        [SugarColumn(IsNullable = false)]
        public Sex Sex { get; set; }

        /// <summary>
        /// 年龄
        /// </summary>        
        [SugarColumn(IsNullable = false)]
        public int Age { get; set; }

        /// <summary>
        /// 年龄单位
        /// </summary>        
        [SugarColumn(IsNullable = true)]
        public AgeUnit? AgeUnit { get; set; }

        /// <summary>
        /// 出生日期
        /// </summary>        
        [SugarColumn(IsNullable = true)]
        public DateTime? Birthday { get; set; }

        /// <summary>
        /// 证件类型
        /// </summary>        
        [SugarColumn(IsNullable = true, Length = 2)]
        public string CardType { get; set; }

        /// <summary>
        /// 证件号
        /// </summary>        
        [SugarColumn(IsNullable = true, Length = 18)]
        public string CardNo { get; set; }

        /// <summary>
        /// 手机号
        /// </summary>        
        [SugarColumn(IsNullable = true, Length = 32)]
        public string Tel { get; set; }

        /// <summary>
        /// 籍贯
        /// </summary>        
        [SugarColumn(IsNullable = true, Length = 10)]
        public string NativePlace { get; set; }

        /// <summary>
        /// 地址
        /// </summary>        
        [SugarColumn(IsNullable = true, Length = 100)]
        public string Address { get; set; }

        /// <summary>
        /// 婚姻状态
        /// </summary>        
        [SugarColumn(IsNullable = true)]
        public MarryStatus? MarryStatus { get; set; }

        /// <summary>
        /// 相片地址
        /// </summary>        
        [SugarColumn(IsNullable = true, Length = 250)]
        public string PhotoUrl { get; set; }

        /// <summary>
        /// 登记时间
        /// </summary>        
        [SugarColumn(IsNullable = false)]
        public DateTime RegisterTime { get; set; }

        /// <summary>
        /// 登记次数
        /// </summary>        
        [SugarColumn(IsNullable = false)]
        public int RegisterTimes { get; set; }

        /// <summary>
        /// 激活标识
        /// </summary>        
        [SugarColumn(IsNullable = false)]
        public bool IsActive { get; set; }

        /// <summary>
        /// 激活时间
        /// </summary>        
        [SugarColumn(IsNullable = true)]
        public DateTime? ActiveTime { get; set; }

        /// <summary>
        /// 激活人
        /// </summary>        
        [SugarColumn(IsNullable = true, Length = 10)]
        public string Activator { get; set; }

        /// <summary>
        /// 体检分类
        /// </summary>        
        [SugarColumn(IsNullable = false)]
        public PeCls PeCls { get; set; }

        /// <summary>
        /// 体检状态（0：未检查；1：正在检查；2：已检完；3：已总检；4：已审核）
        /// </summary>        
        [SugarColumn(IsNullable = true)]
        public PeStatus PeStatus { get; set; }

        /// <summary>
        /// 指引单类型
        /// </summary>        
        [SugarColumn(IsNullable = false, Length = 32)]
        public string GuidanceType { get; set; }

        /// <summary>
        /// 指引单打印标识
        /// </summary>        
        [SugarColumn(IsNullable = true)]
        public bool? GuidancePrinted { get; set; }

        /// <summary>
        /// 指引单打印时间
        /// </summary>        
        [SugarColumn(IsNullable = true)]
        public DateTime? GuidancePrintTime { get; set; }

        /// <summary>
        /// 指引单回收人
        /// </summary>        
        [SugarColumn(IsNullable = true, Length = 20)]
        public string GuidanceRecycler { get; set; }

        /// <summary>
        /// 指引单回收时间
        /// </summary>        
        [SugarColumn(IsNullable = true)]
        public DateTime? GuidanceRecyclyTime { get; set; }

        /// <summary>
        /// 报告格式
        /// </summary>        
        [SugarColumn(IsNullable = false, Length = 32)]
        public string ReportType { get; set; }

        /// <summary>
        /// 报告打印标识
        /// </summary>        
        [SugarColumn(IsNullable = false)]
        public bool ReportPrinted { get; set; }

        /// <summary>
        /// 报告打印时间
        /// </summary>        
        [SugarColumn(IsNullable = true)]
        public DateTime? ReportPrintedTime { get; set; }

        /// <summary>
        /// 团检标识
        /// </summary>        
        [SugarColumn(IsNullable = false)]
        public bool IsCompanyCheck { get; set; }

        /// <summary>
        /// 单位编码
        /// </summary>        
        [SugarColumn(IsNullable = true, Length = 6)]
        public string CompanyCode { get; set; }

        /// <summary>
        /// 单位次数
        /// </summary>        
        [SugarColumn(IsNullable = true)]
        public int? CompanyTimes { get; set; }

        /// <summary>
        /// 单位部门编码
        /// </summary>        
        [SugarColumn(IsNullable = true, Length = 8)]
        public string CompanyDeptCode { get; set; }

        /// <summary>
        /// 预约类型
        /// </summary>        
        [SugarColumn(IsNullable = true)]
        public BookType BookType { get; set; }

        /// <summary>
        /// 预约号
        /// </summary>        
        [SugarColumn(IsNullable = true, Length = 100)]
        public string BookNo { get; set; }

        /// <summary>
        /// 预约起始时间
        /// </summary>        
        [SugarColumn(IsNullable = true)]
        public DateTime? BookBeginTime { get; set; }

        /// <summary>
        /// 预约截止时间
        /// </summary>        
        [SugarColumn(IsNullable = true)]
        public DateTime? BookEndTime { get; set; }

        /// <summary>
        /// 职业
        /// </summary>        
        [SugarColumn(IsNullable = true, Length = 40)]
        public string JobCode { get; set; }

        /// <summary>
        /// 职业历史
        /// </summary>        
        [SugarColumn(IsNullable = true, Length = 255)]
        public string JobHistory { get; set; }

        /// <summary>
        /// 病史
        /// </summary>        
        [SugarColumn(IsNullable = true, Length = 255)]
        public string MedicalHistory { get; set; }

        /// <summary>
        /// 家庭病史
        /// </summary>        
        [SugarColumn(IsNullable = true, Length = 255)]
        public string FamilyMedicalHistory { get; set; }

        /// <summary>
        /// VIP标识
        /// </summary>        
        [SugarColumn(IsNullable = true)]
        public bool? IsVIP { get; set; }

        /// <summary>
        /// 领导标识
        /// </summary>        
        [SugarColumn(IsNullable = true)]
        public bool? IsLeader { get; set; }

        /// <summary>
        /// 体质辨识标识
        /// </summary>        
        [SugarColumn(IsNullable = true)]
        public bool? IsConstitution { get; set; }

        /// <summary>
        /// 复查标识
        /// </summary>        
        [SugarColumn(IsNullable = true)]
        public bool? IsRecheck { get; set; }

        /// <summary>
        /// 复查号
        /// </summary>        
        [SugarColumn(IsNullable = true, Length = 12)]
        public string RecheckNo { get; set; }

        /// <summary>
        /// 健康证上传标识
        /// </summary>        
        [SugarColumn(IsNullable = true)]
        public bool? HealthUploaded { get; set; }

        /// <summary>
        /// 外检同步标识
        /// </summary>        
        [SugarColumn(IsNullable = true)]
        public bool? OutsideUploaded { get; set; }

        /// <summary>
        /// 操作员编码
        /// </summary>        
        [SugarColumn(IsNullable = false, Length = 10)]
        public string OperatorCode { get; set; }

        /// <summary>
        /// 介绍人
        /// </summary>        
        [SugarColumn(IsNullable = true, Length = 10)]
        public string Introducer { get; set; }

        /// <summary>
        /// 备注
        /// </summary>        
        [SugarColumn(IsNullable = true, Length = 255)]
        public string Note { get; set; }

        /// <summary>
        /// 收费模式
        /// </summary>        
        [SugarColumn(IsNullable = false)]
        public ChargeModel ChargeModel { get; set; }

        /// <summary>
        /// 院区编码
        /// </summary>        
        [SugarColumn(IsNullable = false, Length = 1)]
        public string HospCode { get; set; }

        /// <summary>
        /// 订单总金额
        /// </summary>        
        [SugarColumn(IsNullable = false, DecimalDigits = 2)]
        public decimal TotalPrice { get; set; }

        /// <summary>
        /// 缴费状态
        /// </summary>        
        [SugarColumn(IsNullable = false)]
        public PaymentStatus PayStatus { get; set; }

        /// <summary>
        /// 职检标识
        /// </summary>        
        [SugarColumn(IsNullable = false, DefaultValue = "0")]
        public bool IsOccupation { get; set; }

        /// <summary>
        /// 普检标识
        /// </summary>
        [SugarColumn(IsNullable = false, DefaultValue = "1")]
        public bool IsOrdinary { get; set; }

        /// <summary>
        /// 被删除的时间
        /// </summary>        
        [SugarColumn(IsNullable = true)]
        public DateTime? DeleteTime { get; set; }
    }
}