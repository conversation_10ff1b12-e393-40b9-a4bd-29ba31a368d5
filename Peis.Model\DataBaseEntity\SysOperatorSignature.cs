﻿namespace Peis.Model.DataBaseEntity;

///<summary>
///用户签名表
///</summary>
[SugarTable("SysOperatorSignature")]
public class SysOperatorSignature
{
    /// <summary>
    /// 代码
    /// </summary>        
    [SugarColumn(IsNullable = false, IsPrimaryKey = true, Length = 10)]
    public string OperatorCode { get; set; }
    /// <summary>
    /// 签名图片地址
    /// </summary>
    [SugarColumn(IsNullable = false, Length = 500)]
    public string SignaturePath { get; set; }
}
