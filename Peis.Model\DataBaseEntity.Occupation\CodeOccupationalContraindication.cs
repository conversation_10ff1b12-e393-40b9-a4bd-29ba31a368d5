﻿namespace Peis.Model.DataBaseEntity.Occupation
{
    /// <summary>
    /// 职业禁忌证字典表
    /// </summary>
    [SugarTable]
    public class CodeOccupationalContraindication
    {
        /// <summary>
        /// 职业禁忌证代码
        /// </summary>
        [SugarColumn(Length = 5, IsPrimaryKey = true)]
        public string ContraindicationCode { get; set; }
        /// <summary>
        /// 职业禁忌证名称
        /// </summary>
        [SugarColumn(Length = 200, IsNullable = false)]
        public string ContraindicationName { get; set; }
    }
}
