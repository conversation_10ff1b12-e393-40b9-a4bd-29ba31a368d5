﻿using System.Threading.Tasks;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc.Controllers;
using Microsoft.Extensions.Logging;
using Peis.Utility.Helper.RSA;

namespace Peis.Utility.Middleware
{
    public  class RequestDecryptWeChatMiddleware
    {
        private readonly RequestDelegate _next;
        private readonly ILogger _logger;

        public RequestDecryptWeChatMiddleware(RequestDelegate next, ILogger<RequestDecryptWeChatMiddleware> logger)
        {
            _next = next;
            _logger = logger;
        }

        public async Task InvokeAsync(HttpContext context)
        {
            var actionDes = context.GetEndpoint()?.Metadata?.GetMetadata<ControllerActionDescriptor>();
            if (actionDes == null || actionDes.Parameters.Count == 0)
            {
                await _next(context);
                return;
            }

            //var bodyCiphertext = string.Empty;

            //try
            //{
            //    using StreamReader sr = new(context.Request.Body);
            //    bodyCiphertext = await sr.ReadToEndAsync();
            //    var encStrObj = JsonConvert.DeserializeObject<RequestEncStr>(bodyCiphertext);
            //    if (encStrObj != null)
            //    {
            //        MemoryStream newRequestBody = new(); //创建读写权限流
            //        using StreamWriter sw = new(newRequestBody, null, -1, true);
            //        string deText = Decrypt(encStrObj.encryStr);
            //        await sw.WriteAsync(deText);
            //        sw.Flush();
            //        newRequestBody.Position = 0;
            //        context.Request.Body = newRequestBody;
            //    }
            //}
            //catch (Exception)
            //{
            //    context.Response.StatusCode  = StatusCodes.Status400BadRequest;
            //    context.Response.ContentType = MediaTypeNames.Text.Plain;

            //    _logger.LogError("加密不符合约定：Request starting {Method} {Path}{QueryString} {ContentType} {RequestBody}",
            //        context.Request.Method,
            //        context.Request.Path.Value,
            //        context.Request.QueryString.Value,
            //        context.Request.ContentType ?? "-",
            //        bodyCiphertext);

            //    await context.Response.WriteAsync("解析请求发生错误：加密不符合约定");
            //    return;
            //}

            await _next(context);
        }

        /// <summary>
        /// 解密
        /// </summary>
        /// <param name="cipherText"></param>
        /// <returns></returns>
        private static string Decrypt(string cipherText)
        {
            return RSACryptoHelper.Decrypt(cipherText, EncryptConfig.privateKey);
        }

        private class RequestEncStr
        {
            public string encryStr { get; set; }
        }

        /// <summary>
        /// 加解密配置
        /// </summary>
        public class EncryptConfig
        {
            public static string privateKey = @"**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************";

            public static string publicKey = @"-----BEGIN PUBLIC KEY-----
MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQC/5TvzKQWv/zSOvTI9wYYYF9hS
Xpruz247khWEfFqHFEO8/E6JfeXVa0vGxVZha+uuFGx2vsadfSh8yJWKROO3tnVZ
hT7Tdhslfi9oeiUM2FtQ9u32jXS5diMlDj5vuNVGkLBgxmRTNSZCZl/cM35B4j74
oqJunTXM9KQXdqMkhQIDAQAB
-----END PUBLIC KEY-----";
        }
    }

    public static class RequestDecryptWeChatMiddlewareExtensions
    {
        public static IApplicationBuilder UseRequestDecryptWeChatMiddleware(this IApplicationBuilder builder)
        {
            return builder.UseMiddleware<RequestDecryptWeChatMiddleware>();
        }
    }
}
