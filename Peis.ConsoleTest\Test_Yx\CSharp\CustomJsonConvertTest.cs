﻿using Peis.Utility.JsonConverter;
using System.Text.Json;

namespace Peis.ConsoleTest.Test_Yx.CSharp
{
    internal class CustomJsonConvertTest
    {
        static void IntConverterTest()
        {
            var options = new JsonSerializerOptions()
            {
                WriteIndented = true,
                Converters =
                {
                    new IntNullableConverter()
                }
            };
        }

        static void DateTimeConverterTest()
        {
            var options = new JsonSerializerOptions()
            {
                WriteIndented = true,
                Converters =
                {
                    new DateTimeConverter(),
                    new DateTimeNullableConverter()
                }
            };
        }

        public static void Invoke()
        {
            LongConverterTest.Invoke();
        }
    }

    internal class LongConverterTest
    {
        public class TestValue
        {
            public long Value { get; set; }
            public long? ValueNullable { get; set; }
            public long[] Arrary { get; set; }
        }

        public static void Invoke()
        {
            var options = new JsonSerializerOptions(new JsonSerializerOptions(JsonSerializerDefaults.Web))
            {
                WriteIndented = true,
                Converters =
                {
                    new LongConverter(),
                    new LongNullableConverter()
                }
            };

            var json1 = @"{
  ""value"": ""1553302296811671551"",
  ""valueNullable"": ""1553302296811671552"",
  ""arrary"": [
    ""1553302296811671553"",
    ""1553302296811671554"",
    ""1553302296811671555""
  ]
}";
            var jsonToObj1 = JsonSerializer.Deserialize<TestValue>(json1, options);

            var json2 = @"{
    ""value"" : 1553302296811671551,
    ""valueNullable"" : 1553302296811671552,
    ""arrary"" : [
        1553302296811671553,
        1553302296811671554,
        1553302296811671555
    ]
}";
            var jsonToObj2 = JsonSerializer.Deserialize<TestValue>(json2, options);

            var obj = new TestValue()
            {
                Value = 1553302296811671551,
                ValueNullable = 1553302296811671552,
                Arrary = new long[]
                {
                    1553302296811671553,
                    1553302296811671554,
                    1553302296811671555
                }
            };

            var objToJson = JsonSerializer.Serialize(obj, options);
        }
    }
}
