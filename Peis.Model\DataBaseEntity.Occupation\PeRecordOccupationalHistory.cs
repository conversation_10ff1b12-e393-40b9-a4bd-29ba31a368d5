﻿namespace Peis.Model.DataBaseEntity.Occupation;

/// <summary>
/// 职业史结果
/// </summary>
[SugarTable]
public class PeRecordOccupationalHistory
{
    /// <summary>
    /// 体检号
    /// </summary>
    [SugarColumn(IsPrimaryKey = true, Length = 12)]
    public string RegNo { get; set; }
    /// <summary>
    /// 排序
    /// </summary>
    [SugarColumn(IsPrimaryKey = true)]
    public int SortIndex { get; set; }
    /// <summary>
    /// 1放射 2非放射
    /// </summary>
    [SugarColumn(IsNullable = false,Length =1)]
    public string HazardFactorType { get; set; }
    /// <summary>
    /// 开始日期
    /// </summary>
    [SugarColumn(IsNullable = false)]
    public DateTime BeginDate { get; set; }
    /// <summary>
    /// 结束日期
    /// </summary>
    [SugarColumn(IsNullable = false)]
    public DateTime EndDate { get; set; }
    /// <summary>
    /// 公司名称
    /// </summary>
    [SugarColumn(IsNullable = false, Length = 50)]
    public string CompanyName { get; set; }
    /// <summary>
    /// 车间
    /// </summary>
    [SugarColumn(IsNullable = true, Length = 50)]
    public string Workshop { get; set; }
    /// <summary>
    /// 工种
    /// </summary>
    [SugarColumn(IsNullable = false, Length = 50)]
    public string Job { get; set; }
    /// <summary>
    /// 危害因素 
    /// </summary>
    [SugarColumn(IsNullable = false, Length = 512)]
    public string ContactHazardousElements { get; set; }
    /// <summary>
    /// (放射)每日工作时数或工作量
    /// </summary>
    [SugarColumn(IsNullable = true, Length = 100)]
    public string WorkloadPerDay { get; set; }
    /// <summary>
    /// (放射)职业史累积受照剂量
    /// </summary>
    [SugarColumn(IsNullable = true, Length = 100)]
    public string ExposureDose { get; set; }
    /// <summary>
    /// (放射)职业史过量照射史
    /// </summary>
    [SugarColumn(IsNullable = true, Length = 100)]
    public string OverExposureStatus { get; set; }
    /// <summary>
    /// (放射)职业照射种类
    /// </summary>
    [SugarColumn(IsNullable = true, Length = 512)]
    public string IrradiationType { get; set; }
    /// <summary>
    /// (放射)职业照射种类代码
    /// </summary>
    [SugarColumn(IsNullable = true, Length = 200)]
    public string IrradiationTypeCode { get; set; }
    /// <summary>
    /// (放射)放射线种类
    /// </summary>
    [SugarColumn(IsNullable = true, Length = 200)]
    public string RadiationType { get; set; }
    /// <summary>
    /// (非放射)防护措施 类型编码为：2时，填写
    /// </summary>
    [SugarColumn(IsNullable = true, Length = 50)]
    public string ProtectiveMeasures { get; set; }
    /// <summary>
    /// 检查日期
    /// </summary>
    [SugarColumn(IsNullable = false)]
    public DateTime ExamTime { get; set; }
    /// <summary>
    /// 检查医生
    /// </summary>
    [SugarColumn(IsNullable = false, Length = 50)]
    public string DoctorName { get; set; }
}
