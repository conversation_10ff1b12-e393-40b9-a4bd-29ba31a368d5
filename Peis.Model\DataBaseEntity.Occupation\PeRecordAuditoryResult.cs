﻿using Peis.Model.Other.PeEnum.Occupation;

namespace Peis.Model.DataBaseEntity.Occupation
{
    /// <summary>
    /// 纯音测听结果表
    /// </summary>
    [SugarTable]
    public class PeRecordAuditoryResult
    {
        /// <summary>
        /// 体检号
        /// </summary>
        [SugarColumn(Length = 12, IsPrimaryKey = true)]
        public string RegNo { get; set; }
        /// <summary>
        /// 左气导500ghz
        /// </summary>
        [SugarColumn(IsNullable = false)]
        public int LeftGas500 { get; set; }
        /// <summary>
        /// 左气导1000ghz
        /// </summary>
        [SugarColumn(IsNullable = false)]
        public int LeftGas1000 { get; set; }
        /// <summary>
        /// 左气导2000ghz
        /// </summary>
        [SugarColumn(IsNullable = false)]
        public int LeftGas2000 { get; set; }
        /// <summary>
        /// 左气导3000ghz
        /// </summary>
        [SugarColumn(IsNullable = false)]
        public int LeftGas3000 { get; set; }
        /// <summary>
        /// 左气导4000ghz
        /// </summary>
        [SugarColumn(IsNullable = false)]
        public int LeftGas4000 { get; set; }
        /// <summary>
        /// 左气导6000ghz
        /// </summary>
        [SugarColumn(IsNullable = false)]
        public int LeftGas6000 { get; set; }
        /// <summary>
        /// 左气导8000ghz
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public int? LeftGas8000 { get; set; }
        /// <summary>
        /// 右气导500ghz
        /// </summary>
        [SugarColumn(IsNullable = false)]
        public int RightGas500 { get; set; }
        /// <summary>
        /// 右气导1000ghz
        /// </summary>
        [SugarColumn(IsNullable = false)]
        public int RightGas1000 { get; set; }
        /// <summary>
        /// 右气导2000ghz
        /// </summary>
        [SugarColumn(IsNullable = false)]
        public int RightGas2000 { get; set; }
        /// <summary>
        /// 右气导3000ghz
        /// </summary>
        [SugarColumn(IsNullable = false)]
        public int RightGas3000 { get; set; }
        /// <summary>
        /// 右气导4000ghz
        /// </summary>
        [SugarColumn(IsNullable = false)]
        public int RightGas4000 { get; set; }
        /// <summary>
        /// 右气导6000ghz
        /// </summary>
        [SugarColumn(IsNullable = false)]
        public int RightGas6000 { get; set; }
        /// <summary>
        /// 右气导8000ghz
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public int? RightGas8000 { get; set; }
        /// <summary>
        /// 左骨导500ghz
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public int? LeftBone500 { get; set; }
        /// <summary>
        /// 左骨导1000ghz
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public int? LeftBone1000 { get; set; }
        /// <summary>
        /// 左骨导2000ghz
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public int? LeftBone2000 { get; set; }
        /// <summary>
        /// 左骨导3000ghz
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public int? LeftBone3000 { get; set; }
        /// <summary>
        /// 左骨导4000ghz
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public int? LeftBone4000 { get; set; }
        /// <summary>
        /// 左骨导6000ghz
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public int? LeftBone6000 { get; set; }
        /// <summary>
        /// 右骨导500ghz
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public int? RightBone500 { get; set; }
        /// <summary>
        /// 右骨导1000ghz
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public int? RightBone1000 { get; set; }
        /// <summary>
        /// 右骨导2000ghz
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public int? RightBone2000 { get; set; }
        /// <summary>
        /// 右骨导3000ghz
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public int? RightBone3000 { get; set; }
        /// <summary>
        /// 右骨导4000ghz
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public int? RightBone4000 { get; set; }
        /// <summary>
        /// 右骨导6000ghz
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public int? RightBone6000 { get; set; }
        /// <summary>
        /// 左气导500ghz校正值
        /// </summary>
        [SugarColumn(IsNullable = false)]
        public int LeftGasCorrected500 { get; set; }
        /// <summary>
        /// 左气导1000ghz校正值
        /// </summary>
        [SugarColumn(IsNullable = false)]
        public int LeftGasCorrected1000 { get; set; }
        /// <summary>
        /// 左气导2000ghz校正值
        /// </summary>
        [SugarColumn(IsNullable = false)]
        public int LeftGasCorrected2000 { get; set; }
        /// <summary>
        /// 左气导3000ghz校正值
        /// </summary>
        [SugarColumn(IsNullable = false)]
        public int LeftGasCorrected3000 { get; set; }
        /// <summary>
        /// 左气导4000ghz校正值
        /// </summary>
        [SugarColumn(IsNullable = false)]
        public int LeftGasCorrected4000 { get; set; }
        /// <summary>
        /// 左气导6000ghz校正值
        /// </summary>
        [SugarColumn(IsNullable = false)]
        public int LeftGasCorrected6000 { get; set; }
        /// <summary>
        /// 右气导500ghz校正值
        /// </summary>
        [SugarColumn(IsNullable = false)]
        public int RightGasCorrected500 { get; set; }
        /// <summary>
        /// 右气导1000ghz校正值
        /// </summary>
        [SugarColumn(IsNullable = false)]
        public int RightGasCorrected1000 { get; set; }
        /// <summary>
        /// 右气导2000ghz校正值
        /// </summary>
        [SugarColumn(IsNullable = false)]
        public int RightGasCorrected2000 { get; set; }
        /// <summary>
        /// 右气导3000ghz校正值
        /// </summary>
        [SugarColumn(IsNullable = false)]
        public int RightGasCorrected3000 { get; set; }
        /// <summary>
        /// 右气导4000ghz校正值
        /// </summary>
        [SugarColumn(IsNullable = false)]
        public int RightGasCorrected4000 { get; set; }
        /// <summary>
        /// 右气导6000ghz校正值
        /// </summary>
        [SugarColumn(IsNullable = false)]
        public int RightGasCorrected6000 { get; set; }
        /// <summary>
        /// 左骨导500ghz校正值
        /// </summary>
        [SugarColumn(IsNullable = false)]
        public int? LeftBoneCorrected500 { get; set; }
        /// <summary>
        /// 左骨导1000ghz校正值
        /// </summary>
        [SugarColumn(IsNullable = false)]
        public int? LeftBoneCorrected1000 { get; set; }
        /// <summary>
        /// 左骨导2000ghz校正值
        /// </summary>
        [SugarColumn(IsNullable = false)]
        public int? LeftBoneCorrected2000 { get; set; }
        /// <summary>
        /// 左骨导3000ghz校正值
        /// </summary>
        [SugarColumn(IsNullable = false)]
        public int? LeftBoneCorrected3000 { get; set; }
        /// <summary>
        /// 左骨导4000ghz校正值
        /// </summary>
        [SugarColumn(IsNullable = false)]
        public int? LeftBoneCorrected4000 { get; set; }
        /// <summary>
        /// 左骨导6000ghz校正值
        /// </summary>
        [SugarColumn(IsNullable = false)]
        public int? LeftBoneCorrected6000 { get; set; }
        /// <summary>
        /// 右骨导500ghz校正值
        /// </summary>
        [SugarColumn(IsNullable = false)]
        public int? RightBoneCorrected500 { get; set; }
        /// <summary>
        /// 右骨导1000ghz校正值
        /// </summary>
        [SugarColumn(IsNullable = false)]
        public int? RightBoneCorrected1000 { get; set; }
        /// <summary>
        /// 右骨导2000ghz校正值
        /// </summary>
        [SugarColumn(IsNullable = false)]
        public int? RightBoneCorrected2000 { get; set; }
        /// <summary>
        /// 右骨导3000ghz校正值
        /// </summary>
        [SugarColumn(IsNullable = false)]
        public int? RightBoneCorrected3000 { get; set; }
        /// <summary>
        /// 右骨导4000ghz校正值
        /// </summary>
        [SugarColumn(IsNullable = false)]
        public int? RightBoneCorrected4000 { get; set; }
        /// <summary>
        /// 右骨导6000ghz校正值
        /// </summary>
        [SugarColumn(IsNullable = false)]
        public int? RightBoneCorrected6000 { get; set; }
        /// <summary>
        /// 检查时间
        /// </summary>
        [SugarColumn(IsNullable = false)]
        public DateTime ExamTime { get; set; }
        /// <summary>
        /// 检查医生姓名
        /// </summary>
        [SugarColumn(Length = 50, IsNullable = false)]
        public string DoctorName { get; set; }
        /// <summary>
        /// 检查结论
        /// </summary>
        [SugarColumn(Length = 500, IsNullable = false)]
        public string ExamResult { get; set; }
        /// <summary>
        /// 原始听力图保存地址
        /// </summary>
        [SugarColumn(Length = 500, IsNullable =true)]
        public string FilePath { get; set; }
        /// <summary>
        /// 校正听力图保存地址
        /// </summary>
        [SugarColumn(Length = 500, IsNullable = true)]
        public string FilePathCorrected { get; set; }
        /// <summary>
        /// 左气导500ghz方向
        /// </summary>
        [SugarColumn(IsNullable = false)]
        public AuditoryResultType LeftGas500Type { get; set; }
        /// <summary>
        /// 左气导1000ghz方向
        /// </summary>
        [SugarColumn(IsNullable = false)]
        public AuditoryResultType LeftGas1000Type { get; set; }
        /// <summary>
        /// 左气导2000ghz方向
        /// </summary>
        [SugarColumn(IsNullable = false)]
        public AuditoryResultType LeftGas2000Type { get; set; }
        /// <summary>
        /// 左气导3000ghz方向
        /// </summary>
        [SugarColumn(IsNullable = false)]
        public AuditoryResultType LeftGas3000Type { get; set; }
        /// <summary>
        /// 左气导4000ghz方向
        /// </summary>
        [SugarColumn(IsNullable = false)]
        public AuditoryResultType LeftGas4000Type { get; set; }
        /// <summary>
        /// 左气导6000ghz方向
        /// </summary>
        [SugarColumn(IsNullable = false)]
        public AuditoryResultType LeftGas6000Type { get; set; }
        /// <summary>
        /// 左气导8000ghz方向
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public AuditoryResultType? LeftGas8000Type { get; set; }
        /// <summary>
        /// 右气导500ghz方向
        /// </summary>
        [SugarColumn(IsNullable = false)]
        public AuditoryResultType RightGas500Type { get; set; }
        /// <summary>
        /// 右气导1000ghz方向
        /// </summary>
        [SugarColumn(IsNullable = false)]
        public AuditoryResultType RightGas1000Type { get; set; }
        /// <summary>
        /// 右气导2000ghz方向
        /// </summary>
        [SugarColumn(IsNullable = false)]
        public AuditoryResultType RightGas2000Type { get; set; }
        /// <summary>
        /// 右气导3000ghz方向
        /// </summary>
        [SugarColumn(IsNullable = false)]
        public AuditoryResultType RightGas3000Type { get; set; }
        /// <summary>
        /// 右气导4000ghz方向
        /// </summary>
        [SugarColumn(IsNullable = false)]
        public AuditoryResultType RightGas4000Type { get; set; }
        /// <summary>
        /// 右气导6000ghz方向
        /// </summary>
        [SugarColumn(IsNullable = false)]
        public AuditoryResultType RightGas6000Type { get; set; }
        /// <summary>
        /// 右气导8000ghz方向
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public AuditoryResultType? RightGas8000Type { get; set; }
        /// <summary>
        /// 左骨导500ghz方向
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public AuditoryResultType? LeftBone500Type { get; set; }
        /// <summary>
        /// 左骨导1000ghz方向
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public AuditoryResultType? LeftBone1000Type { get; set; }
        /// <summary>
        /// 左骨导2000ghz方向
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public AuditoryResultType? LeftBone2000Type { get; set; }
        /// <summary>
        /// 左骨导3000ghz方向
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public AuditoryResultType? LeftBone3000Type { get; set; }
        /// <summary>
        /// 左骨导4000ghz方向
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public AuditoryResultType? LeftBone4000Type { get; set; }
        /// <summary>
        /// 左骨导6000ghz方向
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public AuditoryResultType? LeftBone6000Type { get; set; }
        /// <summary>
        /// 右骨导500ghz方向
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public AuditoryResultType? RightBone500Type { get; set; }
        /// <summary>
        /// 右骨导1000ghz方向
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public AuditoryResultType? RightBone1000Type { get; set; }
        /// <summary>
        /// 右骨导2000ghz方向
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public AuditoryResultType? RightBone2000Type { get; set; }
        /// <summary>
        /// 右骨导3000ghz方向
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public AuditoryResultType? RightBone3000Type { get; set; }
        /// <summary>
        /// 右骨导4000ghz方向
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public AuditoryResultType? RightBone4000Type { get; set; }
        /// <summary>
        /// 右骨导6000ghz方向
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public AuditoryResultType? RightBone6000Type { get; set; }
        /// <summary>
        /// 组合代码
        /// </summary>
        [SugarColumn(IsNullable = false, Length = 20)]
        public string CombCode { get; set; }

        #region 计算字段
        /// <summary>
        /// 右耳语频平均值气导
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public int RightGasLowerRateAverage => (int)decimal.Round((RightGasCorrected500 + RightGasCorrected1000 + RightGasCorrected2000) / 3m, 0,MidpointRounding.AwayFromZero);
        /// <summary>
        /// 左耳语频平均值气导
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public int LeftGasLowerRateAverage => (int)decimal.Round((LeftGasCorrected500 + LeftGasCorrected1000 + LeftGasCorrected2000) / 3m, 0,MidpointRounding.AwayFromZero);
        /// <summary>
        /// 右耳语频平均值骨导
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public int? RightBoneLowerRateAverage => RightBoneCorrected500.HasValue&&RightBoneCorrected1000.HasValue&&RightBoneCorrected2000.HasValue? (int)decimal.Round((RightBoneCorrected500.Value + RightBoneCorrected1000.Value + RightBoneCorrected2000.Value) / 3m, 0,MidpointRounding.AwayFromZero):null;
        /// <summary>
        /// 左耳语频平均值骨导
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public int? LeftBoneLowerRateAverage => LeftBoneCorrected500.HasValue && LeftBoneCorrected1000.HasValue && LeftBoneCorrected2000.HasValue ? (int)decimal.Round((LeftBoneCorrected500.Value + LeftBoneCorrected1000.Value + LeftBoneCorrected2000.Value) / 3m, 0, MidpointRounding.AwayFromZero) : null;
        /// <summary>
        /// 右耳语频加权值气导
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public int RightGasLowerRateWeighted => (int)decimal.Round((RightGasCorrected500 + RightGasCorrected1000 + RightGasCorrected2000) / 3m * 0.9m + RightGasCorrected4000 * 0.1m, 0, MidpointRounding.AwayFromZero);
        /// <summary>
        /// 左耳语频加权值气导
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public int LeftGasLowerRateWeighted => (int)decimal.Round((LeftGasCorrected500 + LeftGasCorrected1000 + LeftGasCorrected2000) / 3m * 0.9m + LeftGasCorrected4000 * 0.1m, 0, MidpointRounding.AwayFromZero);
        /// <summary>
        /// 右耳语频加权值骨导
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public int? RightBoneLowerRateWeighted => RightBoneCorrected500.HasValue && RightBoneCorrected1000.HasValue && RightBoneCorrected2000.HasValue && RightBoneCorrected4000.HasValue ?
            (int)decimal.Round((RightBoneCorrected500.Value + RightBoneCorrected1000.Value + RightBoneCorrected2000.Value) / 3m * 0.9m + RightBoneCorrected4000.Value * 0.1m, 0, MidpointRounding.AwayFromZero):null;
        /// <summary>
        /// 左耳语频加权值骨导
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public int? LeftBoneLowerRateWeighted => LeftBoneCorrected500 .HasValue && LeftBoneCorrected1000.HasValue && LeftBoneCorrected2000.HasValue && LeftBoneCorrected4000.HasValue ? 
            (int)(decimal.Round((LeftBoneCorrected500.Value + LeftBoneCorrected1000.Value + LeftBoneCorrected2000.Value) / 3m * 0.9m + LeftBoneCorrected4000.Value * 0.1m, 0, MidpointRounding.AwayFromZero)):null;
        /// <summary>
        /// 双耳高频平均听阈气导
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public int BinauralGasHigherRateAverage => (int)decimal.Round((LeftGasCorrected3000 + LeftGasCorrected4000 + LeftGasCorrected6000 + RightGasCorrected3000 + RightGasCorrected4000 + RightGasCorrected6000) / 6m, 0, MidpointRounding.AwayFromZero);
        /// <summary>
        /// 双耳高频平均听阈骨导
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public int? BinauralBoneHigherRateAverage => LeftBoneCorrected3000.HasValue && LeftBoneCorrected4000.HasValue && LeftBoneCorrected6000.HasValue && RightBoneCorrected3000.HasValue && RightBoneCorrected4000.HasValue && RightBoneCorrected6000.HasValue ? 
            (int)decimal.Round((LeftBoneCorrected3000.Value + LeftBoneCorrected4000.Value + LeftBoneCorrected6000.Value + RightBoneCorrected3000.Value + RightBoneCorrected4000.Value + RightBoneCorrected6000.Value) / 6m, 0, MidpointRounding.AwayFromZero):null;
        /// <summary>
        /// 双耳高频平均听阈右骨左气
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public int? BinauralBoneHigherRateLGRB => RightBoneCorrected3000.HasValue && RightBoneCorrected4000.HasValue && RightBoneCorrected6000.HasValue?
            (int)decimal.Round((LeftGasCorrected3000 + LeftGasCorrected4000 + LeftGasCorrected6000 + RightBoneCorrected3000.Value + RightBoneCorrected4000.Value + RightBoneCorrected6000.Value) / 6m, 0, MidpointRounding.AwayFromZero) : null;
        /// <summary>
        /// 双耳高频平均听阈左骨右气
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public int? BinauralBoneHigherRateLBRG => LeftBone3000.HasValue && LeftBone4000.HasValue && LeftBone6000.HasValue?
            (int)decimal.Round((LeftBoneCorrected3000.Value + LeftBoneCorrected4000.Value + LeftBoneCorrected6000.Value + RightGasCorrected3000 + RightGasCorrected4000 + RightGasCorrected6000) / 6m, 0, MidpointRounding.AwayFromZero) : null;
        #endregion
    }

    
}
