﻿namespace Peis.Model.DataBaseEntity.Occupation
{
    /// <summary>
    /// 疑似职业病字典表
    /// </summary>
    [SugarTable]
    public class CodeOccupationalDisease
    {
        /// <summary>
        /// 职业病代码
        /// </summary>
        [SugarColumn(Length = 5, IsPrimaryKey = true)]
        public string DiseaseCode { get; set; }
        /// <summary>
        /// 职业病名称
        /// </summary>
        [SugarColumn(Length = 50, IsNullable = false)]
        public string DiseaseName { get; set; }
    }
}
