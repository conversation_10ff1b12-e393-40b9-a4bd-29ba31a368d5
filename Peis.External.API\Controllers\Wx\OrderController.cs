﻿using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using Peis.External.Wx.Service.Model.DTO;
using Peis.External.Wx.Service.Model.FromBody;
using Peis.External.Wx.Service.Model.Response;
using Peis.External.Wx.Service.Service;
using Peis.Model.DataBaseEntity.External;

namespace Peis.External.API.Controllers.Wx
{
    /// <summary>
    /// 微信套餐及同步接口
    /// </summary>
    [ApiController]
    [Route("api/Peis/Wx/[controller]")]
    public class OrderController : ControllerBase
    {
        private readonly ResponsResult result = new();//返回实例
        private readonly IOrderService _orderService;

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="orderService"></param>
        public OrderController(IOrderService orderService)
        {
            _orderService = orderService;
        }


        /// <summary>
        /// 获取个人套餐
        /// </summary>
        /// <param name="data">data.peCls 体检分类</param>
        /// <returns></returns>
        [HttpPost("GetPersonalPackage")]
        [ProducesResponseType(typeof(PersonalPackage[]), 200)]
        public IActionResult GetPersonalPackage([FromBody] ParamData data)
        {
            var msg = string.Empty;
            var personalPackages = Array.Empty<PersonalPackage>();
            result.Success = _orderService.GetPersonalPackages(data, ref personalPackages, ref msg);
            result.ReturnMsg = msg;
            result.ReturnData = personalPackages;
            return Ok(result);
        }


        /// <summary>
        /// 根据套餐编码获取套餐的组合
        /// </summary>
        /// <param name="data">data.clusCode 套餐编码</param>
        /// <returns></returns>
        [HttpPost("GetCombsByClusCode")]
        [ProducesResponseType(typeof(ClusterCombs[]), 200)]
        public IActionResult GetCombsByClusCode([FromBody] ParamData data)
        {
            var msg = string.Empty;
            var clusCombs = Array.Empty<ClusterCombs>();
            result.Success = _orderService.GetCombsByClusCode(data, ref clusCombs, ref msg);
            result.ReturnMsg = msg;
            result.ReturnData = clusCombs;
            return Ok(result);
        }


        /// <summary>
        /// 获取所有项目分类下的组合
        /// </summary>
        /// <returns></returns>
        [HttpPost("GetItemClsComb")]
        [ProducesResponseType(typeof(ItemClsComb[]), 200)]
        public IActionResult GetItemClsComb()
        {
            result.ReturnData = _orderService.GetItemClsComb();
            return Ok(result);
        }

        #region 团体

        ///// <summary>
        ///// 团体登录
        ///// </summary>
        ///// <param name="basicInfo"></param>
        ///// <returns></returns>
        //[HttpPost("TeamLogin")]
        //[ProducesResponseType(typeof(TeamInfo[]), 200)]
        //public IActionResult TeamLogin([FromBody] BasicInfo basicInfo)
        //{
        //    var msg = string.Empty;
        //    var teamInfo = Array.Empty<TeamInfo>();
        //    result.Success = _orderService.TeamLogin(basicInfo, ref teamInfo, ref msg);
        //    result.ReturnMsg = msg;
        //    result.ReturnData = teamInfo;
        //    return Ok(result);
        //}

        /// <summary>
        /// 团体登录
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPost("TeamLogin")]
        [ProducesResponseType(typeof(TeamInfo[]), 200)]
        public IActionResult TeamLogin([FromBody] TeamLoginRequest request)
        {
            var msg = string.Empty;
            var teamInfo = Array.Empty<TeamInfo>();
            result.Success = _orderService.TeamLogin(request, ref teamInfo, ref msg);
            result.ReturnMsg = msg;
            result.ReturnData = teamInfo;
            return Ok(result);
        }

        /// <summary>
        /// 获取团体单位数据
        /// </summary>
        /// <returns></returns>
        [HttpPost("GetCompanyList")]
        [ProducesResponseType(typeof(TjLnc[]), 200)]
        public IActionResult GetCompanyList()
        {
            result.ReturnData = _orderService.GetCompanyList();
            return Ok(result);
        }

        #endregion

        #region 同步订单

        /// <summary>
        /// 个人订单同步
        /// </summary>
        /// <param name="order"></param>
        /// <returns></returns>
        [HttpPost("SyncPersonOrder")]
        public IActionResult SyncPersonOrder([FromBody] SyncPersonOrder order)
        {
            var msg = string.Empty;
            var regNo = string.Empty;
            result.Success = _orderService.SyncPersonOrder(order, ref regNo, ref msg);
            result.ReturnMsg = msg;
            result.ReturnData = regNo;
            return Ok(result);
        }


        /// <summary>
        /// 插入缴费明细
        /// </summary>
        /// <param name="data"></param>
        /// <returns></returns>
        [HttpPost("AddPayInfo")]
        public IActionResult AddPayInfo([FromBody] ParamData data)
        {
            var msg = string.Empty;
            result.Success = _orderService.AddPayInfo(data, ref msg);
            result.ReturnData = msg;
            return Ok(result);
        }


        /// <summary>
        /// 删除个人记录
        /// </summary>
        /// <param name="data"></param>
        /// <returns></returns>
        [HttpPost("DeletePersonRecord")]
        public IActionResult DeletePersonRecord([FromBody] ParamData data)
        {
            if (!_orderService.DeletePersonRecord(data.RegNo ?? ""))
            {
                result.Success = false;
                result.ReturnMsg = "删除失败,请重试!";
                return Ok(result);
            }

            result.ReturnMsg = "删除成功";
            return Ok(result);
        }


        /// <summary>
        /// 团体订单同步
        /// </summary>
        /// <param name="order"></param>
        /// <returns></returns>
        [HttpPost("SyncTeamOrder")]
        public IActionResult SyncTeamOrder([FromBody] SyncTeamOrder order)
        {
            var msg = string.Empty;
            result.Success = _orderService.SyncTeamOrder(order, ref msg);
            result.ReturnMsg = msg;
            return Ok(result);
        }


        /// <summary>
        /// 删除团体订单记录
        /// </summary>
        /// <param name="data"></param>
        /// <returns></returns>
        [HttpPost("DeleteTeamRecord")]
        public IActionResult DeleteTeamRecord([FromBody] ParamData data)
        {
            if (!_orderService.DeleteTeamRecord(data.RegNo ?? ""))
            {
                result.Success = false;
                result.ReturnMsg = "删除失败,请重试!";
                return Ok(result);
            }

            result.ReturnMsg = "删除成功";
            return Ok(result);
        }


        /// <summary>
        /// 补充问卷调查
        /// </summary>
        /// <returns></returns>
        [HttpPost("RegisterQuestionSurvey")]
        public IActionResult RegisterQuestionSurvey([FromBody] WeChatBookQuestion question)
        {
            var msg = string.Empty;
            result.Success = _orderService.RegisterQuestionSurvey(question, ref msg);
            result.ReturnMsg = msg;
            return Ok(result);
        }

        #endregion

        #region 报告

        /// <summary>
        /// 获取报告列表
        /// </summary>
        /// <param name="bsInfo"></param>
        /// <returns></returns>
        [HttpPost("GetReportList")]
        public IActionResult GetReportList([FromBody] BasicInfo bsInfo)
        {
            var msg = string.Empty;
            var reports = Array.Empty<Report>();
            result.Success = _orderService.GetReportList(bsInfo, ref reports, ref msg);
            result.ReturnMsg = msg;
            result.ReturnData = JsonConvert.SerializeObject(reports);
            return Ok(result);
        }


        /// <summary>
        /// 获取报告详情
        /// </summary>
        /// <param name="data"></param>
        /// <returns></returns>
        [HttpPost("GetReportDetail")]
        public IActionResult GetReportDetail([FromBody] ParamData data)
        {
            var msg = string.Empty;
            var detail = new ReportDetail();
            result.Success = _orderService.GetReportDetail(data.RegNo ?? "", ref detail, ref msg);
            result.ReturnMsg = msg;
            result.ReturnData = JsonConvert.SerializeObject(detail);
            return Ok(result);
        }


        /// <summary>
        /// 获取报告Pdf地址
        /// </summary>
        /// <param name="data"></param>
        /// <returns></returns>
        [HttpPost("GetReportPdfUrl")]
        public IActionResult GetReportPdfUrl([FromBody] ParamData data)
        {
            string url = string.Empty;
            result.Success = _orderService.GetReportPdfUrl(data.RegNo, ref url);
            result.ReturnData = url;
            return Ok(result);
        }


        /// <summary>
        /// 获取报告影像图文地址
        /// </summary>
        /// <param name="data"></param>
        /// <returns></returns>
        [HttpPost("GetGraphicTextUrl")]
        public IActionResult GetGraphicTextUrl([FromBody] ParamData data)
        {
            string respData = string.Empty;
            result.Success = _orderService.GetGraphicTextUrl(data.RegNo, ref respData);
            result.ReturnData = respData;
            return Ok(result);
        }

        #endregion
    }
}
