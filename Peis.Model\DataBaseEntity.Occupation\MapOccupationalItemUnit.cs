﻿namespace Peis.Model.DataBaseEntity.Occupation
{
    /// <summary>
    /// 项目与项目单位对应表
    /// </summary>
    [SugarTable]
    public class MapOccupationalItemUnit
    {
        /// <summary>
        /// 项目代码
        /// </summary>
        [SugarColumn(Length = 10, IsPrimaryKey = true)]
        public string ItemCode { get; set; }

        /// <summary>
        /// 项目单位代码
        /// </summary>
        [SugarColumn(Length =10,IsPrimaryKey = true)]
        public string UnitCode { get; set; }
    }
}
