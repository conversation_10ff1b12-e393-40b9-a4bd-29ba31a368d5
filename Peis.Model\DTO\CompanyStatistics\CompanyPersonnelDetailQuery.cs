﻿using Peis.Model.Other.PeEnum;

namespace Peis.Model.DTO.CompanyStatistics
{
    /// <summary>
    /// 单位人员明细查询
    /// </summary>
    public class CompanyPersonnelDetailQuery : StatisticalQueryBase
    {
        /// <summary>
        /// 时间类型
        /// </summary>
        public TimeType TimeType { get; set; }

        /// <summary>
        /// 套餐编码
        /// </summary>
        public string ClusCode { get; set; }

        /// <summary>
        /// 体检分类
        /// </summary>
        public PeCls PeCls { get; set; }

        /// <summary>
        /// 单位人员体检状态
        /// </summary>
        public CompanyPersonStatus Status { get; set; }

        /// <summary>
        /// 单位部门代码
        /// </summary>
        public string CompanyDeptCode { get; set; }
    }
}
