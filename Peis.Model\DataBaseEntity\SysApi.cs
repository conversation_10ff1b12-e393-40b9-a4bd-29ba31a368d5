﻿using SqlSugar;

namespace Peis.Model.DataBaseEntity
{
    ///<summary>
    ///接口代码表
    ///</summary>
    [SugarTable("SysApi")]
    public class SysApi
    {
        /// <summary>
        /// 代码
        /// </summary>        
        [SugarColumn(IsNullable = false, IsPrimaryKey = true, IsIdentity = true)]
        public int ApiCode { get; set; }

        /// <summary>
        /// 名称
        /// </summary>        
        [SugarColumn(IsNullable = false, Length = 50)]
        public string Name { get; set; }

        /// <summary>
        /// 地址
        /// </summary>        
        [SugarColumn(IsNullable = false, Length = 500)]
        public string ApiPath { get; set; }

        /// <summary>
        /// 分组名称
        /// </summary>        
        [SugarColumn(IsNullable = false, Length = 50)]
        public string GroupName { get; set; }

        /// <summary>
        /// 状态：1启用，0禁用
        /// </summary>        
        [SugarColumn(IsNullable = false)]
        public bool IsEnabled { get; set; }

        /// <summary>
        /// 描述
        /// </summary>        
        [SugarColumn(IsNullable = false, Length = 100)]
        public string Note { get; set; }
    }
}