﻿using Microsoft.AspNetCore.Mvc;
using Peis.Model.DataBaseEntity;
using Peis.Model.Other;
using Peis.Service.IService;
using System.Collections.Generic;

namespace Peis.API.Controllers.PermissionSetting
{
    /// <summary>
    /// 系统菜单
    /// </summary>
    [Route("api/[controller]")]
    [ApiController]
    public class SystemMenuController : BaseApiController
    {
        private readonly ISystemSettingService _systemSettingService;

        public SystemMenuController(
            ISystemSettingService systemSettingService)
        {
            _systemSettingService = systemSettingService;
        }

        /// <summary>
        /// 获取所有菜单
        /// </summary>
        /// <returns></returns>
        [HttpPost("GetAllMenu")]
        public IActionResult GetAllMenu()
        {
            result.ReturnData = _systemSettingService.GetAllMenu();
            return Ok(result);
        }

        /// <summary>
        /// 获取所有api列表
        /// </summary>
        /// <returns></returns>
        [HttpPost("GetAllApiList")]
        public IActionResult GetAllApiList()
        {
            result.ReturnData = _systemSettingService.GetAllApiList();
            return Ok(result);
        }

        /// <summary>
        /// 添加api数据
        /// </summary>
        /// <returns></returns>
        [HttpPost("AddApi")]
        public IActionResult AddApi([FromBody] SysApi sysApi)
        {
            string errMsg = "";
            var api = _systemSettingService.AddApi(sysApi, ref errMsg);
            if (api == null)
            {
                result.Success = false;
                result.ReturnMsg = errMsg;
                return Ok(result);

            }
            result.ReturnData = api;
            return Ok(result);
        }

        /// <summary>
        /// 修改Api数据
        /// </summary>
        /// <param name="sysApi"></param>
        /// <returns></returns>
        [HttpPost("UpdateApi")]
        public IActionResult UpdateApi([FromBody] SysApi sysApi)
        {
            var codeOper = _systemSettingService.UpdateApi(sysApi);
            if (codeOper == null)
            {
                result.Success = false;
                result.ReturnMsg = "修改信息失败";
                return Ok(result);
            }
            result.ReturnData = codeOper;
            return Ok(result);
        }

        /// <summary>
        /// 删除Api数据
        /// </summary>
        /// <param name="apiCodes">["apiCode","apiCode"]</param>
        /// <returns></returns>
        [HttpPost("DeleteApi")]
        public IActionResult DeleteApi([FromBody] int[] apiCodes)
        {
            var codeOper = _systemSettingService.DeleteApi(apiCodes);
            if (!codeOper)
            {
                result.Success = false;
                result.ReturnMsg = "删除数据失败";
                return Ok(result);
            }
            result.ReturnData = codeOper;
            return Ok(result);
        }

        /// <summary>
        /// 添加菜单
        /// </summary>
        /// <returns></returns>
        [HttpPost("AddMenu")]
        public IActionResult AddMenu([FromBody] SysMenu sysMenu)
        {
            string errMsg = string.Empty;
            var rootMenu = _systemSettingService.AddMenu(sysMenu, ref errMsg);
            if (rootMenu == null)
            {
                result.Success = false;
                result.ReturnMsg = errMsg;
                return Ok(result);
            }
            result.ReturnData = rootMenu;
            return Ok(result);
        }

        /// <summary>
        /// 修改菜单
        /// </summary>
        /// <param name="sysMenu"></param>
        /// <returns></returns>
        [HttpPost("UpdateMenu")]
        public IActionResult UpdateMenu([FromBody] SysMenu sysMenu)
        {
            var menu = _systemSettingService.UpdateMenu(sysMenu);
            if (menu == null)
            {
                result.Success = false;
                result.ReturnMsg = "修改失败!";
                return Ok(result);
            }
            result.ReturnData = menu;
            return Ok(result);
        }

        /// <summary>
        /// 删除菜单
        /// </summary>
        /// <param name="menuCode">菜单编码</param>
        /// <returns></returns>
        [HttpPost("DeleteMenu")]
        public IActionResult DeleteMenu([FromQuery] string menuCode)
        {
            var flag = _systemSettingService.DeleteMenu(menuCode);
            if (!flag)
            {
                result.Success = false;
                result.ReturnMsg = "删除失败!";
                return Ok(result);
            }
            return Ok(result);
        }

        /// <summary>
        /// 添加/修改功能
        /// </summary>
        /// <param name="sysFunc"></param>
        /// <returns></returns>
        [HttpPost("AddOrUpdateFunction")]
        public IActionResult AddOrUpdateFunction([FromBody] SysFunc sysFunc)
        {
            var flag = _systemSettingService.AddOrUpdateFunction(sysFunc);
            if (!flag)
            {
                result.Success = false;
                result.ReturnMsg = "处理数据失败!";
                return Ok(result);
            }
            return Ok(result);
        }

        /// <summary>
        /// 删除页面功能
        /// </summary>
        /// <param name="funcCode"></param>
        /// <returns></returns>
        [HttpPost("DeleteFunction")]
        public IActionResult DeleteFunction([FromQuery] string funcCode)
        {
            result.Success = _systemSettingService.DeleteFunction(funcCode, out string msg);
            result.ReturnMsg = msg;
            return Ok(result);
        }

        /// <summary>
        /// 根据菜单编码获取功能
        /// </summary>
        /// <param name="menuCode">菜单编码</param>
        /// <returns></returns>
        [HttpPost("GetFuncByMenuCode")]
        public IActionResult GetFuncByMenuCode([FromQuery] string menuCode)
        {
            result.ReturnData = _systemSettingService.GetFuncByMenuCode(menuCode);
            return Ok(result);
        }

        /// <summary>
        /// 关联API
        /// </summary>
        /// <param name="funcs"></param>
        /// <returns></returns>
        [HttpPost("RelevanceApi")]
        public IActionResult RelevanceApi([FromBody] List<MenuData> funcs)
        {
            string errMsg = string.Empty;
            var flag = _systemSettingService.RelevanceApi(funcs, ref errMsg);
            if (!flag)
            {
                result.Success = false;
                result.ReturnMsg = errMsg;
                return Ok(result);
            }
            return Ok(result);
        }
    }
}
