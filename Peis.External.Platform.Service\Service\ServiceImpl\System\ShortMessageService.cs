﻿using Microsoft.Extensions.Logging;
using Peis.External.Platform.Service.Service.IService.BaiHui;
using Peis.Model.DTO.External.ShortMessage;
using Peis.Repository.IRepository;
using Peis.Service.IService;
using Peis.Service.IService.ExternalSystem;
using Peis.Utility.PeUser;
using System.Text;

namespace Peis.External.Platform.Service.Service.ServiceImpl.System;

/// <summary>
/// 短信服务
/// </summary>
public class ShortMessageService : IExternalSystemShortMessageService
{
    private readonly IBHBasicService _bhBasicService;
    private readonly IRegisterRepository _registerRepository;
    private readonly ILogger<ShortMessageService> _logger;
    private readonly IShortMessageManagerService _shortMessageManagerService;
    private readonly ISplitTableRepository<PeSendShortMsgRecord> _peSendShortMsgRecRepository;
    private readonly IHttpContextUser _httpContextUser;

    public ShortMessageService(IBHBasicService bhBasicService,
        IRegisterRepository registerRepository,
        ILogger<ShortMessageService> logger,
        IShortMessageManagerService shortMessageManagerService,
        ISplitTableRepository<PeSendShortMsgRecord> peSendShortMsgRecRepository,
        IHttpContextUser httpContextUser)
    {
        _bhBasicService = bhBasicService;
        _registerRepository = registerRepository;
        _logger = logger;
        _shortMessageManagerService = shortMessageManagerService;
        _peSendShortMsgRecRepository = peSendShortMsgRecRepository;
        _httpContextUser = httpContextUser;
    }

    /// <summary>
    /// 批量根据短信内容发送短信
    /// </summary>
    /// <param name="keyRecords">发送短信内容记录</param>
    /// <param name="flag">是否成功</param>
    ///  <param name="msg">消息</param>
    /// <returns>PeSendShortMsgRecords</returns>
    public List<PeSendShortMsgRecord> BatchSendShortMsgByPeShortMsgRecord(List<PeSendShortMsgKeyData> keyRecords, out bool flag, out string msg)
    {
        #region 校验
        List<string> emptyRegNos = new List<string>();
        List<string> notApprovedRegNos = new List<string>();
        var records = new List<PeSendShortMsgRecord>();
        foreach (var item in keyRecords)
        {
            var record = _shortMessageManagerService.GetPeSendShortMsgRecord(item.Id, item.RegNo, out msg);
            if (record.IsNullOrEmpty())
            {
                emptyRegNos.Add(item.RegNo);
                continue;
            }
            else if (record.Status < EnumPeSendShortMsgRecordStatus.Approved)
            {
                notApprovedRegNos.Add(record.RegNo);
                continue;
            }
            records.Add(record);
        }
        if (!emptyRegNos.IsNullOrEmpty() || !notApprovedRegNos.IsNullOrEmpty())
        {
            StringBuilder sb = new StringBuilder();
            sb.Append("验证未通过的体检号，");
            if (!emptyRegNos.IsNullOrEmpty())
                sb.AppendFormat("记录缺失：{0}，请添加！", string.Join("、", emptyRegNos.Distinct()));

            if (!notApprovedRegNos.IsNullOrEmpty())
                sb.AppendFormat("未审核：{0}，请前往审核！", string.Join("、", notApprovedRegNos.Distinct()));

            msg = sb.ToString();
            flag = false;
            return null;
        }
        #endregion

        #region 发送短信
        List<string> sendFailRegNos = new List<string>();
        var updateRecords = new List<PeSendShortMsgRecord>();
        foreach (var record in records)
        {
            try
            {
                var reg = _registerRepository.ReadRegister(record.RegNo).First();
                if (reg.IsNullOrEmpty()) continue;

                var sendShortMsgData = new SendShortMsgData(record.PhoneNumber, record.ShortMsgContent, reg.NameRespect);
                var sendStatus = SendShortMsg(sendShortMsgData, out msg);
                if (!sendStatus)
                {
                    sendFailRegNos.Add(record.RegNo);
                    continue;
                }
            }
            catch
            {
                sendFailRegNos.Add(record.RegNo);
                continue;
            }
            record.SetSentTime();
            record.SetUpdateValue(_httpContextUser.UserId, _httpContextUser.UserName);
            updateRecords.Add(record);
        }
        if (!updateRecords.IsNullOrEmpty())
            _peSendShortMsgRecRepository.SplitTableUpdate(updateRecords);

        flag = true;
        if (sendFailRegNos.IsNullOrEmpty())
            msg = ResxCommon.Success;
        else if (sendFailRegNos.Count == records.Count)
        {
            msg = ResxCommon.Fail;
            flag = false;
            updateRecords = null;
        }
        else
        {
            msg = string.Format("部分未发送成功的体检号：{0}", string.Join("、", sendFailRegNos.Distinct()));
        }
        return updateRecords;
        #endregion
    }
    #region 发送短信

    /// <summary>
    /// 发送短信
    /// </summary>
    /// <param name="data">数据</param>
    /// <param name="msg">消息</param>
    /// <returns>是否成功</returns>
    public bool SendShortMsg(SendShortMsgData data, out string msg)
    {
        if (CheckHelper.IsNullOrEmpty(data, nameof(data), out msg))
            return false;

        var reqMsg = new BHApiOpssReqMsg<SendShortMsgApiParam>(SendShortMsgApiParam.METHOD_CODE);
        reqMsg.MethodParam.SetValue(data.PhoneNumber, data.Content);
        var res = _bhBasicService.PostOpss(reqMsg);
        if (!res.IsSuccess)
            _logger.LogError("发送短信失败：{0}", res.ReturnMsg);

        msg = res.IsSuccess ? "发送成功！" : $"发送短信失败：{res.ReturnMsg}" ;
        return res.IsSuccess;
    }

    /// <summary>
    /// 发送短信模板编号为MB01的短信内容
    /// </summary>
    /// <param name="regNo">体检号</param>
    /// <param name="msg">消息</param>
    /// <returns>bool</returns>
    public bool SendShortMsgMB01(string regNo, out string msg)
    {
        if (!CheckHelper.VerifyRegNo(regNo, out msg))
            return false;

        var msgTempl = _shortMessageManagerService.GetShortMsgTemplate(ResxExternal.SysShortMsgTemplate_MB01, out msg);
        if (msgTempl.IsNullOrEmpty())
        {
            msg = $"未找到编号为{ResxExternal.SysShortMsgTemplate_MB01}的短信模板！";
            return false;
        }

        var reg = _registerRepository.ReadRegister(regNo).First();
        if (reg.IsNullOrEmpty())
        {
            msg = ResxCommon.NotExitPatient;
            return false;
        }

        var data = new SendShortMsgData(reg.Tel, msgTempl.Content, reg.NameRespect);
        return SendShortMsg(data, out msg);
    }

    #endregion
}
