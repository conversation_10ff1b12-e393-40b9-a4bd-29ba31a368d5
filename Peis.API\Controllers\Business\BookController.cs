﻿using Microsoft.AspNetCore.Mvc;
using Peis.Model.DTO;
using Peis.Model.DTO.Register;
using Peis.Service.IService;
using System;
using System.Collections.Generic;

namespace Peis.API.Controllers.Business
{
    /// <summary>
    /// 预约
    /// </summary>
    [Route("api/[controller]")]
    [ApiController]
    public class BookController : BaseApiController
    {
        private readonly IBookService _bookService;
        private readonly IRegisterNewService _registerService;

        public BookController(IBookService bookService, IRegisterNewService registerService)
        {
            _bookService = bookService;
            _registerService = registerService;
        }

        /// <summary>
        /// 获取候选套餐列表（套餐、绑定的组合、互斥的组合）
        /// </summary>
        /// <returns></returns>
        [ProducesResponseType(typeof(CandidateCluster[]), 200)]
        [HttpPost("ReadCandidateCluster")]
        public IActionResult ReadCandidateCluster()
        {
            result.ReturnData = _registerService.ReadCandidateCluster();
            return Ok(result);
        }

        /// <summary>
        /// 获取候选组合列表（组合、绑定的组合、互斥的组合）
        /// </summary>
        /// <returns></returns>
        [ProducesResponseType(typeof(CandidateComb[]), 200)]
        [HttpPost("ReadCandidateComb")]
        public IActionResult ReadCandidateComb()
        {
            result.ReturnData = _registerService.ReadCandidateComb();
            return Ok(result);
        }

        /// <summary>
        /// 获取预约个人信息列表
        /// </summary>
        /// <param name="isCompanyCheck">是否团检</param>
        /// <response code="200">执行成功</response>
        /// <returns></returns>
        [HttpPost("ReadPatientList")]
        [ProducesResponseType(typeof(List<PeBookPatientInfo>), 200)]
        public IActionResult ReadPatientList(bool isCompanyCheck)
        {
            try
            {
                result.ReturnData = _bookService.ReadPatientList(isCompanyCheck);

                var msg = string.Empty;
            }
            catch (Exception e)
            {
                result.Success = false;
                result.ReturnMsg = e.Message;
            }

            return Ok(result);
        }

        /// <summary>
        /// 按预约号获取预约的信息
        /// </summary>
        /// <param name="bookNo">预约号</param>
        /// <response code="200">执行成功</response>
        /// <returns></returns>
        [HttpPost("ReadPeBook")]
        [ProducesResponseType(typeof(Book), 200)]
        public IActionResult ReadPeBook(string bookNo)
        {
            try
            {
                result.ReturnData = _bookService.Read(bookNo);
            }
            catch (Exception e)
            {
                result.Success = false;
                result.ReturnMsg = e.Message;
            }

            return Ok(result);
        }

        /// <summary>
        /// SaveBook/Person 保存个人预约
        /// SaveBook/Company 保存单位预约
        /// </summary>
        /// <param name="personOrCompany">个人或团体</param>
        /// <param name="book">预约信息</param>
        /// <returns></returns>
        /// <response code="200">新建时返回体检号，修改时返回true/false</response>
        [HttpPost("SaveBook/{personOrCompany}")]
        public IActionResult SaveBook([FromRoute] string personOrCompany, [FromBody] Book book)
        {
            var regNo = string.Empty;
            var errMsg = string.Empty;

            switch (personOrCompany.ToLower())
            {
                case "person":
                    if (string.IsNullOrWhiteSpace(book.BookPatient.RegNo))
                        result.Success = _bookService.NewBook(false, book, ref regNo, ref errMsg);
                    else
                        result.Success = _bookService.AlterBook(false, book, ref errMsg);
                    break;
                case "company":
                    if (string.IsNullOrWhiteSpace(book.BookPatient.RegNo))
                        result.Success = _bookService.NewBook(true, book, ref regNo, ref errMsg);
                    else
                        result.Success = _bookService.AlterBook(true, book, ref errMsg);
                    break;
                default:
                    return BadRequest();
            }

            result.ReturnData = regNo;
            result.ReturnMsg = errMsg;
            return Ok(result);
        }

        /// <summary>
        /// 删除预约信息
        /// </summary>
        /// <param name="peBook">预约信息（个人信息、套餐组合信息）</param>
        /// <response code="200">执行成功</response>
        /// <returns></returns>
        [HttpPost("DeleteBook")]
        [ProducesResponseType(typeof(string), 200)]
        public IActionResult DeleteBook([FromBody] Book peBook)
        {
            try
            {
                string msg = string.Empty;

                result.Success = _bookService.DeleteBook(peBook, ref msg);

                result.ReturnMsg = msg;
            }
            catch (Exception e)
            {
                result.Success = false;
                result.ReturnMsg = e.Message;
            }

            return Ok(result);
        }
    }
}
