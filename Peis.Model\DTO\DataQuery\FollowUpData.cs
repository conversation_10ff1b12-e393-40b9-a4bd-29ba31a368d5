﻿using Peis.Model.Other.PeEnum;
using System;

namespace Peis.Model.DTO.DataQuery
{
    /// <summary>
    /// 随访登记数据
    /// </summary>
    public class FollowUpData
    {
        /// <summary>
        /// 随访ID
        /// </summary>
        public long FollowUpId {  get; set; }

        /// <summary>
        /// 体检号
        /// </summary>
        public string RegNo { get; set; }

        /// <summary>
        /// 姓名
        /// </summary>
        public string Name { get; set; }

        /// <summary>
        /// 性别
        /// </summary>
        public Sex Sex { get; set; }

        /// <summary>
        /// 年龄
        /// </summary>
        public int Age { get; set; }

        /// <summary>
        /// 电话
        /// </summary>
        public string Tel { get; set; }

        /// <summary>
        /// 体检日期
        /// </summary>
        public DateTime? ActiveTime { get; set; }

        /// <summary>
        /// 审核日期
        /// </summary>
        public DateTime? AuditTime { get; set; }

        /// <summary>
        /// 个人/单位
        /// </summary>
        public string PeType { get; set; }

        /// <summary>
        /// 单位/地址
        /// </summary>
        public string Address { get; set; }

        /// <summary>
        /// 记录日期
        /// </summary>
        public DateTime CreateTime { get; set; }

        /// <summary>
        /// 诊断
        /// </summary>
        public string Diagnosis { get; set; }

        /// <summary>
        /// 发现的问题
        /// </summary>
        public string FindProblems { get; set; }

        /// <summary>
        /// 发现问题的科室
        /// </summary>
        public string FindProblemsDept { get; set; }

        /// <summary>
        /// 建议
        /// </summary>
        public string Suggestion { get; set; }

        /// <summary>
        /// 主检医生
        /// </summary>
        public string CheckDoctorCode { get; set; }

        /// <summary>
        /// 记录人
        /// </summary>
        public string Recorder { get; set; }

        /// <summary>
        /// 通知人
        /// </summary>
        public string Notifier { get; set; }

        /// <summary>
        /// 被通知人
        /// </summary>
        public string NotifiedPerson { get; set; }

        /// <summary>
        /// 后续随访情况
        /// </summary>
        public string AfterFollowUp { get; set; }

        /// <summary>
        /// 二次随访员 
        /// </summary>        
        public string SecondaryNotifier { get; set; }

        /// <summary>
        /// 二次随访情况
        /// </summary>        
        public string SecondaryAfterFollowUp { get; set; }

        /// <summary>
        /// 删除时间
        /// </summary>
        public DateTime DeleteTime { get; set; }
        /// <summary>
        /// 是否需要后续随访
        /// </summary>
        public bool NeedFinallyNotify { get; set; }
        /// <summary>
        /// 后续随访员 
        /// </summary>        
        public string FinallyNotifier { get; set; }

        /// <summary>
        /// 后续随访情况
        /// </summary>        
        public string FinallyAfterFollowUp { get; set; }
        /// <summary>
        /// 随访来源
        /// </summary>
        public FollowUpSource Source { get; set; }
        /// <summary>
        /// 随访时间
        /// </summary>        
        public DateTime? AfterFollowUpTime { get; set; }
        /// <summary>
        /// 二次随访时间
        /// </summary>        
        public DateTime? SecondaryAfterFollowUpTime { get; set; }
    }
}
