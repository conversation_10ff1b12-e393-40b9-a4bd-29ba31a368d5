﻿using Peis.Quartz.UI.BaseService;
using Peis.External.Platform.Service.Service.IService.His;

namespace Peis.External.Platform.Service.Service.ServiceImpl.Job;

/// <summary>
/// 作业：HIS建档、主索引申请单同步信息
/// </summary>
public class SyncHisOrderJob : IJobService
{
    private readonly IHisBillService _hisBillService;

    public SyncHisOrderJob(IHisBillService hisBillService)
    {
        _hisBillService = hisBillService;
    }

    public string ExecuteService(string parameter)
    {
        _hisBillService.SyncOrderAuto();
        return "定时任务已执行成功!";
    }
}
