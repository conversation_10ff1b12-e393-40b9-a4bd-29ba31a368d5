﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Peis.Model.Other.PeEnum;
using Peis.Service.IService;
using Peis.Utility.CustomAttribute;
using System;
using System.Threading.Tasks;

namespace Peis.API.Controllers.Business
{
    /// <summary>
    /// 打印文件服务
    /// </summary>
    [Route("api/[controller]")]
    [ApiController]
    [AllowAnonymous]
    public class PrintFileController : BaseApiController
    {
        private readonly IPrintFileService _printFileService;

        public PrintFileController(IPrintFileService printFileService)
        {
            _printFileService = printFileService;
        }

        /// <summary>
        /// 获取指引单Pdf文件
        /// </summary>
        /// <param name="regNo">体检号</param>
        /// <param name="updatePrintStatus"></param>
        /// <param name="reportCode"></param>
        /// <returns>文件流</returns>
        [HttpGet("GuidancePdf/{regNo}")]
        public async Task<IActionResult> GetGuidancePdf([Regex(VerifyRegNo = true, ErrorMessage = "体检号格式错误！")] string regNo,
            [FromQuery] bool updatePrintStatus, [FromQuery] string reportCode)
        {
            var stream = await _printFileService.GetPrintPdfStream(regNo, PrintFileType.Guidance, updatePrintStatus, reportCode);
            if (stream is null) return NoContent();

            return File(stream, "application/pdf",
                $"{regNo}_guidance.pdf");
        }

        /// <summary>
        /// 获取体检标签Pdf文件
        /// </summary>
        /// <param name="regNo">体检号</param>
        /// <returns>文件流</returns>
        [HttpGet("PeLabelPdf/{regNo}")]
        public async Task<IActionResult> GetPeLabelPdf([Regex(VerifyRegNo = true, ErrorMessage = "体检号格式错误！")] string regNo)
        {
            var stream = await _printFileService.GetPrintPdfStream(regNo, PrintFileType.PeLabel);
            if (stream is null) return NoContent();

            return File(stream, "application/pdf", $"{regNo}_pelabel.pdf");
        }

        /// <summary>
        /// 获取检验非采血条码Pdf文件
        /// </summary>
        /// <param name="regNo">体检号</param>
        /// <returns>文件流</returns>
        [HttpGet("NonBloodBarcodePdf/{regNo}")]
        public async Task<IActionResult> GetNonBloodBarcodePdf([Regex(VerifyRegNo = true, ErrorMessage = "体检号格式错误！")] string regNo)
        {
            var stream = await _printFileService.GetNonBloodBarcodePdfStream(regNo);
            if (stream is null) return NoContent();

            return File(stream, "application/pdf", $"{regNo}_nonbloodbarcode.pdf");
        }

        /// <summary>
        /// 获取可打印列表
        /// </summary>
        /// <param name="regNo"></param>
        /// <param name="printType"></param>
        /// <returns></returns>
        [HttpPost("QueryPrintFileList/{regNo}")]
        public IActionResult QueryPrintFileList([Regex(VerifyRegNo = true, ErrorMessage = "体检号格式错误！")][FromRoute] string regNo, [FromBody] string[] printType)
        {
            result.ReturnData = _printFileService.QueryPrintFileList(regNo, printType);
            return Ok(result);
        }

        /// <summary>
        /// 获取条码打印pdf文件
        /// </summary>
        /// <param name="regNo"></param>
        /// <param name="barcodeType"></param>
        /// <param name="updatePrintStatus"></param>
        /// <param name="reportCode"></param>
        /// <param name="sampleNo"></param>
        /// <returns></returns>
        [HttpGet("BarcodePdf/{barcodeType}/{regNo}")]
        public async Task<IActionResult> BarcodePdf([Regex(VerifyRegNo = true, ErrorMessage = "体检号格式错误！")][FromRoute] string regNo,
            [FromRoute] string barcodeType, [FromQuery] bool updatePrintStatus, [FromQuery] string reportCode, [FromQuery] string sampleNo)
        {
            var printType = barcodeType.ToEnum<PrintFileType>();
            if (printType != PrintFileType.Barcode && printType != PrintFileType.BloodBarcode && printType != PrintFileType.NonBloodBarcode)
            {
                return BadRequest("参数错误！");
            }
            var stream = await _printFileService.GetPrintPdfStream(regNo, printType, updatePrintStatus, reportCode, sampleNo);
            if (stream is null) return NoContent();

            return File(stream, "application/pdf",
                $"{regNo}_{barcodeType}{sampleNo ?? string.Empty}.pdf");
        }

        /// <summary>
        /// 获取胃镜申请单打印pdf文件
        /// </summary>
        /// <param name="regNo"></param>
        /// <returns></returns>
        [HttpGet("GastroscopePdf/{regNo}")]
        public async Task<IActionResult> GastroscopePdf([Regex(VerifyRegNo = true, ErrorMessage = "体检号格式错误！")][FromRoute] string regNo)
        {
            var stream = await _printFileService.GetPrintPdfStream(regNo, PrintFileType.Gastroscope);
            if (stream is null) return NoContent();

            return File(stream, "application/pdf",
                $"{regNo}_gastroscope.pdf");
        }

        /// <summary>
        /// 获取肠镜申请单打印pdf文件
        /// </summary>
        /// <param name="regNo"></param>
        /// <returns></returns>
        [HttpGet("ColonoscopyPdf/{regNo}")]
        public async Task<IActionResult> ColonoscopyPdf([Regex(VerifyRegNo = true, ErrorMessage = "体检号格式错误！")][FromRoute] string regNo)
        {
            var stream = await _printFileService.GetPrintPdfStream(regNo, PrintFileType.Colonoscopy);
            if (stream is null) return NoContent();

            return File(stream, "application/pdf",
                $"{regNo}_colonoscopy.pdf");
        }

        /// <summary>
        /// 获取报告打印pdf文件
        /// </summary>
        /// <param name="regNo"></param>
        /// <param name="updatePrintStatus"></param>
        /// <param name="reportCode"></param>
        /// <param name="isOccupation"></param>
        /// <returns></returns>
        [HttpGet("ReportPdf/{regNo}")]
        public async Task<IActionResult> ReportPdf([Regex(VerifyRegNo = true, ErrorMessage = "体检号格式错误！")][FromRoute] string regNo,
            [FromQuery] bool updatePrintStatus, [FromQuery] string reportCode, [FromQuery] bool isOccupation)
        {
            var stream = await _printFileService.GetPrintPdfStream(regNo, PrintFileType.Report, updatePrintStatus, reportCode, isOccupation: isOccupation);
            if (stream is null) return NoContent();

            return File(stream, "application/pdf",
                $"{regNo}_report.pdf");
        }

        /// <summary>
        /// 获取麻醉材料费pdf文件
        /// </summary>
        /// <param name="regNo"></param>
        /// <returns></returns>
        [HttpGet("AnesthesiaCostPdf/{regNo}")]
        public async Task<IActionResult> AnesthesiaCostPdf([Regex(VerifyRegNo = true, ErrorMessage = "体检号格式错误！")][FromRoute] string regNo)
        {
            var stream = await _printFileService.GetPrintPdfStream(regNo, PrintFileType.AnesthesiaCost);
            if (stream is null) return NoContent();

            return File(stream, "application/pdf",
                $"{regNo}_report.pdf");
        }

        /// <summary>
        /// 更新打印状态及时间
        /// </summary>
        /// <param name="regNo"></param>
        /// <param name="printType"></param>
        /// <param name="isOccupation"></param>
        /// <returns></returns>
        [HttpPost("UpdatePrintStatus")]
        public IActionResult UpdatePrintStatus([Regex(VerifyRegNo = true, ErrorMessage = "体检号格式错误！")][FromQuery] string regNo,
            [FromQuery] string printType, [FromQuery] bool isOccupation)
        {
            result.Success = _printFileService.UpdatePrintStatus(printType, regNo, "", isOccupation);
            return Ok(result);
        }
    }
}
