﻿namespace Peis.Model.DataBaseEntity.External
{
    /// <summary>
    /// 医疗指导
    /// </summary>
    public class MedicalAdvice
    {
        /// <summary>
        /// 体检号
        /// </summary>        
        [SugarColumn(IsNullable = false, IsPrimaryKey = true, Length = 12)]
        public string RegNo { get; set; }

        /// <summary>
        /// 专科就诊建议
        /// </summary>
        [SugarColumn(IsNullable = true, ColumnDataType = StaticConfig.CodeFirst_BigString)]
        public string SpecialistVisit { get; set; }

        /// <summary>
        /// 定期复查建议
        /// </summary>
        [SugarColumn(IsNullable = true, ColumnDataType = StaticConfig.CodeFirst_BigString)]
        public string RegularReview { get; set; }
    }
}
