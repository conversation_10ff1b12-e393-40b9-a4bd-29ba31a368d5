﻿using Peis.Model.Other.PeEnum;

namespace Peis.Model.DataBaseEntity;

///<summary>
///疾病信息表
///</summary>
[SugarTable("CodeDisease")]
public class CodeDisease
{
    /// <summary>
    /// 疾病代码
    /// </summary>        
    [SugarColumn(IsNullable = false, IsPrimaryKey = true, Length = 8)]
    public string DiseaseCode { get; set; }

    /// <summary>
    /// 疾病名称
    /// </summary>        
    [SugarColumn(IsNullable = false, Length = 255)]
    public string DiseaseName { get; set; }

    /// <summary>
    /// 项目分类代码
    /// </summary>        
    [SugarColumn(IsNullable = true, Length = 6)]
    public string ClsCode { get; set; }

    /// <summary>
    /// 疾病科普
    /// </summary>        
    [SugarColumn(IsNullable = true, ColumnDataType = StaticConfig.CodeFirst_BigString)]
    public string Presentation { get; set; }

    /// <summary>
    /// 建议内容
    /// </summary>        
    [SugarColumn(IsNullable = true, ColumnDataType = StaticConfig.CodeFirst_BigString)]
    public string SuggestContent { get; set; }

    /// <summary>
    /// 疾病等级：1是A类，2是B类，3-5是普通，轻度的
    /// </summary>        
    [SugarColumn(IsNullable = true)]
    public DiseaseGrade? DiseaseGrade { get; set; }

    #region Ext
    /// <summary>
    /// 疾病词条
    /// </summary>
    [SugarColumn(IsIgnore = true)]
    public List<CodeDiseaseEntry> CodeDiseaseEntry { get; set; }

    /// <summary>
    /// 项目分类名称
    /// </summary>        
    [SugarColumn(IsIgnore = true)]
    public string ClsName { get; set; }

    /// <summary>
    /// 创建来源：0-默认，1-自定义疾病
    /// </summary>        
    [SugarColumn(IsIgnore = true)]
    public int CreatedFrom { get; set; }

    /// <summary>
    /// 重大阳性代码
    /// </summary>        
    [SugarColumn(IsIgnore = true)]
    public string PositiveCode { get; set; }

    /// <summary>
    /// 重大阳性名称
    /// </summary>        
    [SugarColumn(IsIgnore = true)]
    public string PositiveName { get; set; }
    #endregion
}