﻿using SqlSugar;

namespace Peis.Model.DataBaseEntity
{
    ///<summary>
    ///疾病词条表
    ///</summary>
    [SugarTable("CodeDiseaseEntry")]
    public class CodeDiseaseEntry
    {
        /// <summary>
        /// 疾病码
        /// </summary>        
        [SugarColumn(IsNullable = false, IsPrimaryKey = true, Length = 8)]
        public string DiseaseCode { get; set; }

        /// <summary>
        /// 疾病词条
        /// </summary>        
        [SugarColumn(IsNullable = false, IsPrimaryKey = true, Length = 255)]
        public string EntryText { get; set; }
    }
}