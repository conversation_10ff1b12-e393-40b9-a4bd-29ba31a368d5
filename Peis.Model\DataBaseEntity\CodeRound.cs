﻿using SqlSugar;

namespace Peis.Model.DataBaseEntity
{
    ///<summary>
    ///凑整规则信息（收费类代码）
    ///</summary>
    [SugarTable("CodeRound")]
    public class CodeRound
    {
        /// <summary>
        /// 规则代码
        /// </summary>        
        [SugarColumn(IsNullable = false, IsPrimaryKey = true, Length = 10)]
        public string RoundCode { get; set; }

        /// <summary>
        /// 规则名称
        /// </summary>        
        [SugarColumn(IsNullable = false, Length = 50)]
        public string RoundName { get; set; }

        /// <summary>
        /// 下限
        /// </summary>        
        [SugarColumn(IsNullable = false, Length = 10, DecimalDigits = 2)]
        public decimal Lower { get; set; }

        /// <summary>
        /// 上限
        /// </summary>        
        [SugarColumn(IsNullable = false, Length = 10, DecimalDigits = 2)]
        public decimal Higher { get; set; }

        /// <summary>
        /// 凑整金额
        /// </summary>        
        [SugarColumn(IsNullable = false, Length = 10, DecimalDigits = 2)]
        public decimal Amount { get; set; }
    }
}