﻿using Peis.Model.DTO.ReportConclusion;
using Peis.Model.Other.Input.ReportConclusion;
using Peis.Model.Other.PeEnum;
using Peis.Repository.IRepository;
using Peis.Service.IService;
using Peis.Service.IService.Helper;
using Peis.Utility.PeUser;
using System.Data;
using System.Text.RegularExpressions;

namespace Peis.Service.Service
{
    public class ReportConclusionService : IReportConclusionService
    {
        private readonly IHttpContextUser _httpContextUser;
        private readonly INoGeneration _noGeneration;
        private readonly IDataTranRepository _dataTranRepository;
        private readonly IRegisterRepository _registerRepository;
        private readonly IReportConclusionRepository _reportConclusionRepository;
        private readonly IRecordRepository _recordRepository;
        private readonly IDataRepository<CodeDisease> _codeDiseaseRepository;
        private readonly IDataRepository<PeRegister> _peRegisterRepository;
        private readonly IDataRepository<PeReportAllocate> _peReportAllocateRepository;
        private readonly IDataRepository<PeReportPriority> _peReportPriorityRepository;
        private readonly IDataRepository<PeReportExtReturnChecked> _peReportExtReturnCheckedRepository;
        private readonly IDataRepository<PeQuestionToDoctor> _questionToDoctorRepository;
        private readonly IDataRepository<CodeNormalSummary> _codeNormalSummaryRepository;

        //分表仓储
        private readonly ISplitTableRepository<PeReportSummary> _peReportSummaryRepository;
        private readonly ISplitTableRepository<PeReportSuggestion> _peReportSuggestionRepository;
        private readonly ISplitTableRepository<PeReportSuggestionDisease> _peReportSuggestionDiseaseRepository;
        private readonly ISplitTableRepository<PeReportConclusion> _peReportConclusionRepository;

        public ReportConclusionService(
            IHttpContextUser httpContextUser,
            INoGeneration noGeneration,
            IDataTranRepository dataTranRepository,
            IRegisterRepository registerRepository,
            IReportConclusionRepository reportConclusionRepository,
            IRecordRepository recordRepository,
            IDataRepository<CodeDisease> codeDiseaseRepository,
            IDataRepository<PeRegister> peRegisertRepository,
            IDataRepository<PeReportAllocate> peReportAllocate,
            IDataRepository<PeReportPriority> peReportPriorityRepository,
            IDataRepository<PeReportExtReturnChecked> peReportExtReturnCheckedRepository,
            IDataRepository<PeQuestionToDoctor> questionToDoctorRepository,
            IDataRepository<CodeNormalSummary> codeNormalSummaryRepository,

            ISplitTableRepository<PeReportSummary> peReportSummaryRepository,
            ISplitTableRepository<PeReportSuggestion> peReportSuggestionRepository,
            ISplitTableRepository<PeReportSuggestionDisease> peReportSuggestionDiseaseRepository,
            ISplitTableRepository<PeReportConclusion> peReportConclusionRepository
            )
        {
            _httpContextUser = httpContextUser;
            _noGeneration = noGeneration;
            _dataTranRepository = dataTranRepository;
            _registerRepository = registerRepository;
            _reportConclusionRepository = reportConclusionRepository;
            _recordRepository = recordRepository;
            _codeDiseaseRepository = codeDiseaseRepository;
            _peRegisterRepository = peRegisertRepository;
            _peReportAllocateRepository = peReportAllocate;
            _peReportPriorityRepository = peReportPriorityRepository;
            _peReportExtReturnCheckedRepository = peReportExtReturnCheckedRepository;
            _questionToDoctorRepository = questionToDoctorRepository;
            _codeNormalSummaryRepository = codeNormalSummaryRepository;

            _peReportSummaryRepository = peReportSummaryRepository;
            _peReportSuggestionRepository = peReportSuggestionRepository;
            _peReportSuggestionDiseaseRepository = peReportSuggestionDiseaseRepository;
            _peReportConclusionRepository = peReportConclusionRepository;
        }

        /// <summary>
        /// 获取建议
        /// </summary>
        /// <param name="diseaseCode"></param>
        /// <returns></returns>
        public string GetSuggestion(string diseaseCode)
        {
            return _codeDiseaseRepository.FindAll(a => a.DiseaseCode == diseaseCode)
                .Select(a => a.SuggestContent)
                .FirstOrDefault();
        }

        /// <summary>
        /// 关键词查询疾病建议
        /// </summary>
        /// <param name="keyword">关键字</param>
        /// <param name="queryType">查询类型:1-疾病名称，2-建议内容，其他-疾病名称、建议内容</param>
        /// <returns>DiseaseSuggestions</returns>
        public List<DiseaseSuggestion> QueryDiseaseSuggestions(string keyword, int queryType)
        {
            //所有疾病建议
            IEnumerable<CodeDisease> codeDiseases;
            if (queryType == 1)
                codeDiseases = _codeDiseaseRepository.FindAll(x => x.DiseaseName.Contains(keyword));
            else if (queryType == 2)
                codeDiseases = _codeDiseaseRepository.FindAll(x => x.SuggestContent.Contains(keyword));
            else
                codeDiseases = _codeDiseaseRepository.FindAll(x => x.DiseaseName.Contains(keyword) || x.SuggestContent.Contains(keyword));

            return codeDiseases
                .Select(a => new DiseaseSuggestion
                {
                    DiseaseCode = a.DiseaseCode,
                    DiseaseName = a.DiseaseName,
                    SuggestContent = a.SuggestContent,
                    SortIndex = a.DiseaseName.Contains(keyword) ? a.DiseaseName.Length : 99999,
                })
                .OrderBy(x => x.SortIndex)
                .Take(100)
                .ToList();
        }

        /// <summary>
        /// 优先分配主检
        /// </summary>
        /// <param name="regNo"></param>
        /// <param name="operatorCode"></param>
        /// <param name="msg"></param>
        /// <returns></returns>
        public bool PriorityAllocation(string regNo, string operatorCode, ref string msg)
        {
            var peReportPriorities = _peReportPriorityRepository.FindAll(a => a.RegNo == regNo);

            if (peReportPriorities.Any())
            {
                msg = $"已分配，不能再优先分配";

                return false;
            }
            else
            {
                PeReportPriority peReportPriorityNew = new()
                {
                    RegNo = regNo,
                    Priority = 0,
                    OperatorCode = operatorCode,
                    OperateTime = DateTime.Now
                };

                _dataTranRepository.ExecTran(() =>
                {
                    _peReportPriorityRepository.Insert(peReportPriorityNew);
                });

                return true;
            }
        }

        #region 获取人员列表
        /// <summary>
        /// 获取主检的人员列表(分配)
        /// </summary>
        /// <param name="queryPatientList"></param>
        /// <returns></returns>
        public List<PatientInfo> GetPatientList4Allocate(QueryPatientList queryPatientList)
        {
            if (Enum.IsDefined(typeof(QueryPatientList.EnumCheckStatus), queryPatientList.CheckStatus))//是否枚举值
            {
                PeStatus peStatus = 0;

                switch (queryPatientList.CheckStatus)
                {
                    case QueryPatientList.EnumCheckStatus.待主检:

                        return GetAllocatedPatients(queryPatientList.OperatorCode);
                    case QueryPatientList.EnumCheckStatus.已主检:
                        peStatus = PeStatus.已总检;
                        break;
                    case QueryPatientList.EnumCheckStatus.已审核:
                        peStatus = PeStatus.已审核;
                        break;
                    default:
                        break;
                }

                var endDate = queryPatientList.EndDate ?? DateTime.Now;
                var beginDate = queryPatientList.BeginDate ?? endDate.AddYears(-3);//三年前

                return _reportConclusionRepository.GetPatientInfo(beginDate, endDate)
                    .Where((a, b, c) => a.PeStatus == peStatus)
                    .WhereIF(peStatus == PeStatus.已总检, (a, b, c) => c.CheckDoctorCode == queryPatientList.OperatorCode)
                    .WhereIF(peStatus == PeStatus.已审核, (a, b, c) => c.AuditDoctorCode == queryPatientList.OperatorCode)
                    .Select((a, b) => new PatientInfo
                    {
                        RegNo = a.RegNo,
                        Name = a.Name,
                        Sex = a.Sex,
                        Age = a.Age,
                        AgeUnit = a.AgeUnit,
                        CompanyName = b.CompanyName,
                        ClusterName = SqlFunc.Subqueryable<PeRegisterCluster>()
                                 .Where(z => z.RegNo == a.RegNo)
                                 .SelectStringJoin(z => z.ClusName, "，"),
                        Tel = a.Tel,
                        CardType = a.CardType,
                        CardNo = a.CardNo,
                        MarryStatus = a.MarryStatus,
                        Address = a.Address,
                        IsCompanyCheck = a.IsCompanyCheck,
                        PeStatus = a.PeStatus,
                        PeCls = a.PeCls,
                        RegisterTime = a.RegisterTime
                    })
                    .ToList();
            }
            else
            {
                throw new Exception("状态不对");
            }
        }

        /// <summary>
        /// 查询已检的人员列表（主检分配）
        /// </summary>
        /// <param name="regNo"></param>
        /// <param name="beginActiveDate"></param>
        /// <param name="endActiveDate"></param>
        /// <param name="patientInfos"></param>
        /// <param name="msg"></param>
        /// <returns></returns>
        public bool QueryCheckedPatientList(string regNo, DateTime? beginActiveDate, DateTime? endActiveDate, ref List<PatientInfo> patientInfos, ref string msg)
        {
            if (string.IsNullOrEmpty(regNo) && beginActiveDate == null && endActiveDate == null)
            {
                msg = $"参数不能全空";

                return false;
            }
            else if (beginActiveDate == null && endActiveDate != null || beginActiveDate != null && endActiveDate == null)
            {
                msg = $"日期错误";

                return false;
            }

            patientInfos = _reportConclusionRepository.GetPatientInfo()
                .Where((a, b) => a.PeStatus == PeStatus.已总检)
                .WhereIF(!SqlFunc.IsNullOrEmpty(regNo), (a, b) => a.RegNo == regNo)
                .WhereIF(!SqlFunc.IsNullOrEmpty(beginActiveDate) && !SqlFunc.IsNullOrEmpty(endActiveDate), (a, b) => SqlFunc.Between(a.ActiveTime, beginActiveDate.Value.Date, endActiveDate.Value.Date))
                .Select((a, b) => new PatientInfo
                {
                    RegNo = a.RegNo,
                    Name = a.Name,
                    Sex = a.Sex,
                    Age = a.Age,
                    AgeUnit = a.AgeUnit,
                    CompanyName = b.CompanyName,
                    ClusterName = SqlFunc.Subqueryable<PeRegisterCluster>()
                                 .Where(z => z.RegNo == a.RegNo)
                                 .SelectStringJoin(z => z.ClusName, "，"),
                    Tel = a.Tel,
                    CardType = a.CardType,
                    CardNo = a.CardNo,
                    MarryStatus = a.MarryStatus,
                    Address = a.Address,
                    IsCompanyCheck = a.IsCompanyCheck,
                    PeStatus = a.PeStatus,
                    PeCls = a.PeCls,
                    RegisterTime = a.RegisterTime
                })
                .ToList();

            return true;
        }

        /// <summary>
        /// 获取已分配的体检者（待检）
        /// </summary>
        /// <param name="operatorCode"></param>
        /// <returns></returns>
        private List<PatientInfo> GetAllocatedPatients(string operatorCode)
        {
            #region 分配的体检者
            var patientInfos = _reportConclusionRepository.GetAllocatedPatients()
                .Where((a, b, c) => a.CheckDoctorCode == operatorCode && b.PeStatus == PeStatus.已检完)
                .Select((a, b, c) => new PatientInfo
                {
                    RegNo = b.RegNo,
                    Name = b.Name,
                    Sex = b.Sex,
                    Age = b.Age,
                    AgeUnit = b.AgeUnit,
                    CompanyName = c.CompanyName,
                    ClusterName = SqlFunc.Subqueryable<PeRegisterCluster>()
                                 .Where(z => z.RegNo == a.RegNo)
                                 .SelectStringJoin(z => z.ClusName, "，"),
                    Tel = b.Tel,
                    CardType = b.CardType,
                    CardNo = b.CardNo,
                    MarryStatus = b.MarryStatus,
                    Address = b.Address,
                    IsCompanyCheck = b.IsCompanyCheck,
                    PeStatus = b.PeStatus,
                    PeCls = b.PeCls,
                    RegisterTime = b.RegisterTime
                })
                .ToList();
            #endregion

            if (!patientInfos.Any())//没有分配，现在分配
            {
                var datetime = DateTime.Now.AddYears(-1);//一年前的才显示

                var patients2Allocate = _reportConclusionRepository.GetPatients2Allocate()
                    .Where((a, b, c, d) => a.ActiveTime > datetime && c.OperatorCode == operatorCode)
                    .OrderBy((a, b, c, d) => SqlFunc.IF(d.RegNo == null).Return(9999)
                                                    .End(d.Priority))
                    .OrderBy((a, b, c, d) => SqlFunc.IF(a.PeCls == PeCls.入职).Return(0)
                                                    .End(1))
                    .OrderBy((a, b, c, d) => a.ActiveTime)
                    .OrderByDescending((a, b, c, d) => a.IsVIP)
                    //.OrderBy((a, b, c, d) => a.ActiveTime)
                    .Select((a, b) => new PatientInfo
                    {
                        RegNo = a.RegNo,
                        Name = a.Name,
                        Sex = a.Sex,
                        Age = a.Age,
                        AgeUnit = a.AgeUnit,
                        CompanyName = b.CompanyName,
                        ClusterName = SqlFunc.Subqueryable<PeRegisterCluster>()
                                 .Where(z => z.RegNo == a.RegNo)
                                 .SelectStringJoin(z => z.ClusName, "，"),
                        Tel = a.Tel,
                        CardType = a.CardType,
                        CardNo = a.CardNo,
                        MarryStatus = a.MarryStatus,
                        Address = a.Address,
                        IsCompanyCheck = a.IsCompanyCheck,
                        PeStatus = a.PeStatus,
                        PeCls = a.PeCls,
                        RegisterTime = a.RegisterTime
                    })
                    .ToList();

                if (patients2Allocate.Any())
                {
                    var patientInfo = patients2Allocate.First();//取第一个分配

                    patientInfos = new List<PatientInfo> { patientInfo };

                    var peReportAllocateNew = new PeReportAllocate
                    {
                        RegNo = patientInfo.RegNo,
                        CheckDoctorCode = operatorCode,
                        AllocateTime = DateTime.Now
                    };

                    _dataTranRepository.ExecTran(() =>
                    {
                        _peReportAllocateRepository.Insert(peReportAllocateNew);
                    });
                }
            }

            return patientInfos;
        }

        /// <summary>
        /// 获取主检的人员列表(不分配)
        /// </summary>
        /// <param name="queryPatientList"></param>
        /// <returns></returns>
        public List<PatientInfo> GetPatientList4NotAllocate(QueryPatientList queryPatientList)
        {
            if (!Enum.IsDefined(typeof(QueryPatientList.EnumCheckStatus), queryPatientList.CheckStatus))//是否枚举值
                throw new Exception("状态不对");

            PeStatus peStatus = 0;
            switch (queryPatientList.CheckStatus)
            {
                case QueryPatientList.EnumCheckStatus.待主检:
                    peStatus = PeStatus.已检完;
                    break;
                case QueryPatientList.EnumCheckStatus.已主检:
                    peStatus = PeStatus.已总检;
                    break;
                case QueryPatientList.EnumCheckStatus.已审核:
                    peStatus = PeStatus.已审核;
                    break;
                default:
                    break;
            }
            // 如果query.KeyWord是体检号则优先查
            var regNoFlag = queryPatientList.KeyWord != null ? queryPatientList.KeyWord.Length == 12 && Regex.IsMatch(queryPatientList.KeyWord, @"\d{12}") : false;

            var endDate = queryPatientList.EndDate ?? DateTime.Now;
            endDate = endDate.Date.Add(new TimeSpan(23, 59, 59));
            var beginDate = queryPatientList.BeginDate ?? endDate.AddDays(-7);//未选时间则一周前

            var peReportPriority = _peReportPriorityRepository.FindAll();

            return _reportConclusionRepository.GetPatientInfo(beginDate, endDate)
                .Where((a, b, c, d, e) => a.PeStatus == peStatus)
                .Where(a => SqlFunc.Between(a.ActiveTime, beginDate, endDate))
                .WhereIF(!string.IsNullOrEmpty(queryPatientList.KeyWord) && regNoFlag, reg => reg.RegNo == queryPatientList.KeyWord)
                .WhereIF(!string.IsNullOrEmpty(queryPatientList.KeyWord) && !regNoFlag, reg => reg.Name.Contains(queryPatientList.KeyWord))
                .Select((a, b, c, d, e) => new PatientInfo
                {
                    RegNo = a.RegNo,
                    Name = a.Name,
                    Sex = a.Sex,
                    Age = a.Age,
                    AgeUnit = a.AgeUnit,
                    CompanyName = b.CompanyName,
                    ClusterName = SqlFunc.Subqueryable<PeRegisterCluster>()
                             .Where(z => z.RegNo == a.RegNo)
                             .SelectStringJoin(z => z.ClusName, "，"),
                    Tel = a.Tel,
                    CardType = a.CardType,
                    CardNo = a.CardNo,
                    MarryStatus = a.MarryStatus,
                    Address = a.Address,
                    IsCompanyCheck = a.IsCompanyCheck,
                    PeStatus = a.PeStatus,
                    PeCls = a.PeCls,
                    AllocatedDoctorCode = d.CheckDoctorCode,
                    IsPriority = !SqlFunc.IsNullOrEmpty(e.RegNo),
                    CheckDoctorCode = c.CheckDoctorCode,
                    CheckDoctorName = c.CheckDoctorName,
                    RegisterTime = a.RegisterTime
                })
                .ToList()
                .OrderByDescending(x => x.IsPriority)
                .ToList();
        }
        #endregion

        #region 获取报告结论
        private PatientInfo GetPatientInfo(string regNo)
        {
            return _reportConclusionRepository.GetPatientInfo()
                .Where(a => a.RegNo == regNo)
                .Where((a, b) => a.PeStatus == PeStatus.已检完 || a.PeStatus == PeStatus.已总检 || a.PeStatus == PeStatus.已审核)
                .Select((a, b) => new PatientInfo
                {
                    RegNo = a.RegNo,
                    Address = a.Address,
                    Age = a.Age,
                    AgeUnit = a.AgeUnit,
                    CardNo = a.CardNo,
                    CardType = a.CardType,
                    CompanyName = b.CompanyName,
                    ClusterName = SqlFunc.Subqueryable<PeRegisterCluster>()
                                 .Where(z => z.RegNo == a.RegNo)
                                 .SelectStringJoin(z => z.ClusName, "，"),
                    IsCompanyCheck = a.IsCompanyCheck,
                    MarryStatus = a.MarryStatus,
                    Name = a.Name,
                    Sex = a.Sex,
                    Tel = a.Tel,
                    PeStatus = a.PeStatus,
                    PeCls = a.PeCls,
                    RegisterTime = a.RegisterTime
                })
                .First();
        }

        #region 生成综述建议
        /// <summary>
        /// 生成综述
        /// </summary>
        /// <param name="regNo"></param>
        /// <returns></returns>
        private Summary GenerateSummary(string regNo)
        {
            var summary = new Summary();

            #region 获取组合小结
            var normalSummarys = _codeNormalSummaryRepository.FindAll().Select(x => x.Result).ToArray();
            var combTabs = _recordRepository.ReadCombRecord(regNo)
                .Where((regComb, recordComb, recordCombTag) => !SqlFunc.IsNullOrEmpty(recordCombTag.CombTag))
                .Where((regComb, recordComb, recordCombTag) => !SqlFunc.ContainsArray(normalSummarys, recordCombTag.CombTag))
                .Select((regComb, recordComb, recordCombTag) => new
                {
                    RecCombId = recordComb.Id,
                    regComb.CombCode,
                    regComb.CombName,
                    regComb.CombSortIndex,
                    regComb.ClsSortIndex,
                    regComb.CheckCls,
                    recordCombTag.CombTag
                })
                .ToList();

            summary.SumCombs = combTabs
                .GroupBy(a => new
                {
                    a.RecCombId,
                    a.CombCode,
                    a.CombName,
                    a.CombSortIndex,
                    a.ClsSortIndex,
                    a.CheckCls,
                })
                .Select(a => new SumComb
                {
                    CombCode = a.Key.CombCode,
                    CombName = a.Key.CombName,
                    CombSortIndex = a.Key.CombSortIndex,
                    ClsSortIndex = a.Key.ClsSortIndex,
                    CheckCls = a.Key.CheckCls,
                    SumTags = a.Select(b => new SumTag
                    {
                        Id = _noGeneration.NextSnowflakeId(),
                        Tag = b.CombTag
                    })
                    .ToList()
                })
                .ToList();
            #endregion

            #region 判断小结内容：现在，所有结果正常时，小结没有“未见异常”，暂时判断有没有值
            if (summary.SumCombs.Any())
            {
                summary.EmptySumTag = false;
                summary.EmptySumTagContent = "";
                summary.SumCombs = BaseCombClsSort.OrderBy(summary.SumCombs).ToList();
            }
            else
            {
                summary.EmptySumTag = true;
                summary.EmptySumTagContent = "所检项目未见明显异常";//暂时这样赋值
            }
            #endregion

            return summary;
        }

        /// <summary>
        /// 生成建议
        /// </summary>
        /// <param name="summary"></param>
        /// <returns></returns>
        private List<Suggestion> GenerateSuggestions(Summary summary)
        {
            var suggestions = new List<Suggestion>();

            if (!summary.EmptySumTag)
            {
                var sumTags = summary.SumCombs.SelectMany(a => a.SumTags).ToList();

                var tagIdDiseaseCodes = GetTagIdDiseaseCode(sumTags);//小结标签与疾病代码的对应

                #region 合并相同建议
                var suggestDiseases = _reportConclusionRepository.GetDiseaseAndEntry()
                    .Select(a => new
                    {
                        a.DiseaseCode,
                        a.DiseaseName,
                        a.SuggestContent
                    })
                    .ToList()
                    .Join(tagIdDiseaseCodes, a => a.DiseaseCode, b => b.DiseaseCode, (a, b) => new
                    {
                        a.DiseaseCode,
                        a.DiseaseName,
                        a.SuggestContent,
                        SummTagId = b.SumTagId
                    });

                suggestions = suggestDiseases
                    .GroupBy(a => new
                    {
                        a.SuggestContent
                    })
                    .Select((a, index) => new Suggestion
                    {
                        Id = _noGeneration.NextSnowflakeId(),
                        SugContent = a.Key.SuggestContent,
                        DeaTags = a.Select(b => new
                        {
                            b.DiseaseCode,
                            b.DiseaseName,
                            b.SummTagId
                        })
                        .GroupBy(a => new
                        {
                            a.DiseaseCode,
                            a.DiseaseName
                        })
                        .Select(a => new DiseaseTag
                        {
                            Id = _noGeneration.NextSnowflakeId(),
                            DiseaseCode = a.Key.DiseaseCode,
                            Tag = a.Key.DiseaseName,
                            BindSummTagIds = a.Select(b => b.SummTagId).ToList()
                        })
                        .ToList()
                    })
                    .ToList();
                #endregion

                #region 排序：根据组合排建议&小结
                int index = 0;
                foreach (SumComb sumComb in summary.SumCombs)
                {
                    foreach (Suggestion suggestion in suggestions)
                    {
                        bool isContain = false;
                        foreach (var item in suggestion.DeaTags)
                        {
                            isContain = sumComb.SumTags
                                .Select(x => x.Tag)
                                .Any(x => x.Contains(item.Tag));
                        }

                        if (isContain)
                            suggestion.SortIndex = ++index;
                    }
                }
                suggestions = suggestions.OrderBy(x => x.SortIndex).ToList();
                #endregion
            }

            return suggestions;
        }

        /// <summary>
        /// 生成综述建议
        /// </summary>
        /// <param name="regNo"></param>
        /// <returns></returns>
        public SummarySuggestions GenerateSummarySuggestions(string regNo)
        {
            SummarySuggestions summarySuggestions = new()
            {
                Summary = GenerateSummary(regNo)
            };

            summarySuggestions.Suggestions = GenerateSuggestions(summarySuggestions.Summary);

            return summarySuggestions;
        }
        #endregion

        /// <summary>
        /// 获取小结标签与疾病代码的对应
        /// </summary>
        /// <param name="sumTags"></param>
        /// <returns></returns>
        private List<SumTagIdDiseaseCode> GetTagIdDiseaseCode(List<SumTag> sumTags)
        {
            var diseaseAndEntries = _reportConclusionRepository.GetDiseaseAndEntry()
               .Select((a, b) => new
               {
                   a.DiseaseCode,
                   a.DiseaseName,
                   a.SuggestContent,
                   b.EntryText,
               })
               .ToList();

            var sumTagIdDiseaseCodes = diseaseAndEntries
                .SelectMany(disease => sumTags
                    .Where(tag => tag.Tag.Contains(disease.DiseaseName) ||
                                  !string.IsNullOrEmpty(disease.EntryText) && tag.Tag.Contains(disease.EntryText))
                    .Select(tag => new SumTagIdDiseaseCode
                    {
                        SumTagId = tag.Id,
                        DiseaseCode = disease.DiseaseCode
                    })
                )
                .Distinct()
                .ToList();

            return sumTagIdDiseaseCodes;
        }

        #region 从表数据组成综述建议
        /// <summary>
        /// 组成综述
        /// </summary>
        /// <param name="regNo"></param>
        /// <param name="registerTime"></param>
        /// <returns></returns>
        private Summary ComposeSummary(string regNo, DateTime registerTime)
        {
            var summary = new Summary
            {
                SumCombs = ComposeSumComb(regNo, registerTime)
            };

            if (summary.SumCombs.Any())
            {
                summary.EmptySumTag = false;
                summary.EmptySumTagContent = "";
            }
            else
            {
                summary.EmptySumTag = true;
                summary.EmptySumTagContent = _peReportConclusionRepository
                    .FindAll(registerTime, a => a.RegNo == regNo)
                    .Select(a => a.Suggestion)
                    .FirstOrDefault();
            }

            return summary;
        }
        /// <summary>
        /// 组成综述的小结标签信息
        /// </summary>
        /// <param name="regNo"></param>
        /// <param name="registerTime"></param>
        /// <returns></returns>
        private List<SumComb> ComposeSumComb(string regNo, DateTime registerTime)
        {
            var peReportSummaries = _peReportSummaryRepository.FindAll(registerTime, a => a.RegNo == regNo);

            var sumCombs = peReportSummaries
                .GroupBy(a => new
                {
                    a.CombCode,
                    a.CombName,
                    a.SortIndex
                })
                .Select(a => new SumComb
                {
                    CombCode = a.Key.CombCode,
                    CombName = a.Key.CombName,
                    SortIndex = a.Key.SortIndex,
                    IsChange = false,
                    SumTags = a.Select(b => new SumTag
                    {
                        Id = b.Id,
                        Tag = b.SummTag
                    })
                    .ToList()
                })
                .OrderBy(x => x.SortIndex)
                .ToList();

            return sumCombs;
        }

        /// <summary>
        /// 组成建议信息
        /// </summary>
        /// <param name="regNo"></param>
        /// <param name="registerTime"></param>
        /// <returns></returns>
        private List<Suggestion> ComposeSuggestion(string regNo, DateTime registerTime)
        {
            var suggestions = new List<Suggestion>();

            var peReportSuggestions = _peReportSuggestionRepository
                .FindAll(registerTime, a => a.RegNo == regNo);

            if (peReportSuggestions.Any())
            {
                var peReportSuggestionDiseases = _peReportSuggestionDiseaseRepository
                    .FindAll(registerTime, a => a.RegNo == regNo);

                foreach (var item in peReportSuggestions)
                {
                    var suggestionNew = new Suggestion
                    {
                        Id = item.Id,
                        SugContent = item.Suggestion,
                        SortIndex = item.SortIndex,
                        DeaTags = peReportSuggestionDiseases
                            .Where(a => a.SuggId == item.Id)
                            .Select(a => new DiseaseTag
                            {
                                Id = a.Id,
                                DiseaseCode = a.DiseaseCode,
                                Tag = a.DiseaseTag,
                                BindSummTagIds = string.IsNullOrEmpty(a.BindSummTagIds) ?
                                    new() : a.BindSummTagIds.Split(';')
                                    .Select(a => Convert.ToInt64(a))
                                    .ToList()
                            })
                            .ToList()
                    };

                    suggestions.Add(suggestionNew);
                }

            }

            return suggestions;
        }
        #endregion

        public bool GetReportConclusion(string regNo, CheckAudit checkAudit, ref ReportConclusion reportConclusion, ref string msg)
        {
            #region PeQuestionToDoctor 医生问题存在未解锁数据的就不能查询
            if (_questionToDoctorRepository.Any(x => x.RegNo == regNo && x.UnlockTime == null))
            {
                msg = "当前体检号存在未解锁的咨询记录!";
                return false;
            }
            #endregion

            #region 个人信息
            reportConclusion.PatientInfo = GetPatientInfo(regNo);

            if (reportConclusion.PatientInfo == null)
            {
                msg = "没有相关状态的记录";
                return false;
            }
            else if (checkAudit == CheckAudit.审核 && reportConclusion.PatientInfo.PeStatus == PeStatus.已检完)
            {
                msg = $"体检号：{regNo}，状态：{reportConclusion.PatientInfo.PeStatus}，未主检，不可操作";
                return false;
            }
            #endregion

            if (reportConclusion.PatientInfo.PeStatus == PeStatus.已检完
                || reportConclusion.PatientInfo.PeStatus == PeStatus.已总检
                || reportConclusion.PatientInfo.PeStatus == PeStatus.已审核)
            {
                #region 结论信息
                var peReportConclusion = _peReportConclusionRepository
                    .FindAll(reportConclusion.PatientInfo.RegisterTime, a => a.RegNo == regNo)
                    .FirstOrDefault();

                if (peReportConclusion == null)//生成综述建议
                {
                    #region 综述
                    reportConclusion.Summary = GenerateSummary(regNo);
                    #endregion

                    #region 建议
                    reportConclusion.Suggestions = GenerateSuggestions(reportConclusion.Summary);
                    #endregion
                }
                else//从数据库获取数据组装
                {
                    #region 综述
                    reportConclusion.Summary = ComposeSummary(regNo, reportConclusion.PatientInfo.RegisterTime);
                    #endregion

                    #region 建议
                    reportConclusion.Suggestions = ComposeSuggestion(regNo, reportConclusion.PatientInfo.RegisterTime);
                    #endregion

                    reportConclusion.Conclusion = peReportConclusion.Conclusion;
                    reportConclusion.CheckDoctorCode = peReportConclusion.CheckDoctorCode;
                    reportConclusion.CheckDoctorName = peReportConclusion.CheckDoctorName;
                    reportConclusion.CheckTime = peReportConclusion.CheckTime;
                    reportConclusion.AuditDoctorCode = peReportConclusion.AuditDoctorCode;
                    reportConclusion.AuditDoctorName = peReportConclusion.AuditDoctorName;
                    reportConclusion.AuditTime = peReportConclusion.AuditTime;
                }
                #endregion
            }
            else
            {
                msg = $"不可以操作的状态：{reportConclusion.PatientInfo.PeStatus}";
            }

            return true;
        }

        /// <summary>
        /// 仅读取报告结论
        /// </summary>
        /// <param name="regNo"></param>
        /// <returns></returns>
        public ReportConclusion ReadonlyReportConclusion(string regNo)
        {
            var jm = new ReportConclusion
            {
                PatientInfo = GetPatientInfo(regNo)
            };

            if (jm.PatientInfo == null)
                return null;

            var repConQuery = _peReportConclusionRepository
                .First(jm.PatientInfo.RegisterTime, a => a.RegNo == regNo);

            if (repConQuery != null)
            {
                jm.Summary = ComposeSummary(regNo, jm.PatientInfo.RegisterTime);
                jm.Suggestions = ComposeSuggestion(regNo, jm.PatientInfo.RegisterTime);
                jm.Conclusion = repConQuery.Conclusion;
                jm.CheckDoctorCode = repConQuery.CheckDoctorCode;
                jm.CheckDoctorName = repConQuery.CheckDoctorName;
                jm.CheckTime = repConQuery.CheckTime;
                jm.AuditDoctorCode = repConQuery.AuditDoctorCode;
                jm.AuditDoctorName = repConQuery.AuditDoctorName;
                jm.AuditTime = repConQuery.AuditTime;
            }

            return jm;
        }
        #endregion

        #region 保存报告结论
        public bool SaveReportConclusion(ReportConclusion reportConclusion, ref string msg)
        {
            var regInfo = _registerRepository.ReadRegister(reportConclusion.PatientInfo.RegNo).First();
            if (regInfo.PeStatus != PeStatus.已检完 && regInfo.PeStatus != PeStatus.已总检)
            {
                msg = $"体检号：{regInfo.RegNo}，当前状态：{regInfo.PeStatus}，不可以操作";
                return false;
            }

            // 防止前端缓存，所有此参数设置可空
            var isMainCheck = reportConclusion.PatientInfo.IsMainCheck ?? true;
            if (!isMainCheck && regInfo.PeStatus == PeStatus.已总检)
            {
                msg = $"体检号：{regInfo.RegNo}，当前状态：{regInfo.PeStatus}，不可以操作";
                return false;
            }

            if (string.IsNullOrEmpty(reportConclusion.CheckDoctorCode))
            {
                msg = "主检医生不能为空!";
                return false;
            }

            #region 综述
            var oldNewSummTagIds = new Dictionary<long, long>();

            var emptySumTag = false;
            var summaryText = "";//综述文本

            var peReportSummaries = GetSummary4Save(regInfo.RegNo, regInfo.RegisterTime, reportConclusion.Summary, ref oldNewSummTagIds, ref emptySumTag, ref summaryText);
            #endregion

            #region 建议
            var oldNewSuggIds = new Dictionary<long, long>();
            var peReportSuggestions = GetSuggestion4Save(regInfo.RegNo, regInfo.RegisterTime, reportConclusion.Suggestions, ref oldNewSuggIds);
            #endregion

            #region 引出建议的疾病
            var suggestionText = "";//建议文本

            var peReportSuggestionDiseases = GetSuggestionDisease4Save(regInfo.RegNo, regInfo.RegisterTime, reportConclusion.Suggestions, oldNewSuggIds, oldNewSummTagIds, ref suggestionText);
            #endregion

            #region 结论
            bool isNewFlag = false;
            var peReportConclusion = _reportConclusionRepository.GetReportConclusion(regInfo.RegNo).First();
            if (peReportConclusion == null)
            {
                isNewFlag = true;
                peReportConclusion = new();
                peReportConclusion.CheckDoctorCode = reportConclusion.CheckDoctorCode;
                peReportConclusion.CheckDoctorName = reportConclusion.CheckDoctorName;
            }

            if (string.IsNullOrEmpty(peReportConclusion.CheckDoctorCode))
            {
                peReportConclusion.CheckDoctorCode = reportConclusion.CheckDoctorCode;
                peReportConclusion.CheckDoctorName = reportConclusion.CheckDoctorName;
            }

            peReportConclusion.RegNo = regInfo.RegNo;
            peReportConclusion.EmptySumTag = emptySumTag;
            peReportConclusion.Summary = summaryText;
            peReportConclusion.Suggestion = suggestionText;
            peReportConclusion.Conclusion = reportConclusion.Conclusion;
            peReportConclusion.CheckTime = DateTime.Now;
            peReportConclusion.RegisterTime = regInfo.RegisterTime;
            #endregion

            _dataTranRepository.ExecTran(() =>
            {
                #region 综述
                //删除原记录
                _peReportSummaryRepository.SplitTableDelete(regInfo.RegisterTime, a => a.RegNo == regInfo.RegNo);
                //保存新记录
                _peReportSummaryRepository.SplitTableInsert(peReportSummaries);
                #endregion

                #region 建议
                //删除原记录
                _peReportSuggestionRepository.SplitTableDelete(regInfo.RegisterTime, a => a.RegNo == regInfo.RegNo);

                if (peReportSuggestions.Count > 0)
                    //保存新记录
                    _peReportSuggestionRepository.SplitTableInsert(peReportSuggestions);
                #endregion

                #region 引出建议的疾病
                //删除原记录
                _peReportSuggestionDiseaseRepository.SplitTableDelete(regInfo.RegisterTime, a => a.RegNo == regInfo.RegNo);

                //保存新记录
                _peReportSuggestionDiseaseRepository.SplitTableInsert(peReportSuggestionDiseases);
                #endregion

                // 保存结论
                if (isNewFlag)
                    _peReportConclusionRepository.SplitTableInsert(peReportConclusion);
                else
                    _peReportConclusionRepository.SplitTableUpdate(peReportConclusion);

                if (isMainCheck)
                {
                    regInfo.PeStatus = PeStatus.已总检;
                    _peRegisterRepository.Update(regInfo);
                }
            });

            return true;
        }

        #region 返回综述，用于保存
        private List<PeReportSummary> GetSummary4Save(string regNo, DateTime registerTime, Summary summary, ref Dictionary<long, long> oldNewSummTagIds, ref bool emptySumTag, ref string summaryText)
        {
            var peReportSummaries = new List<PeReportSummary>();

            var sumCombs = summary.SumCombs;
            if (!sumCombs.Any())
            {
                emptySumTag = true;
                summaryText = summary.EmptySumTagContent;
                return peReportSummaries;
            }

            var newLine = Environment.NewLine;//  /r/n
            int index = 0;
            foreach (var sumComb in sumCombs)
            {
                var summTagText = "";//小结标签文本
                foreach (var item in sumComb.SumTags)
                {
                    // item.id 为0才重新生成
                    if (item.Id == 0)
                        item.Id = _noGeneration.NextSnowflakeId();

                    var peReportSummaryNew = new PeReportSummary
                    {
                        Id = item.Id,
                        RegNo = regNo,
                        SummTag = item.Tag,
                        CombCode = sumComb.CombCode,
                        CombName = sumComb.CombName,
                        RegisterTime = registerTime,
                        SortIndex = index,
                    };

                    peReportSummaries.Add(peReportSummaryNew);
                    oldNewSummTagIds.Add(item.Id, item.Id);
                    summTagText += $"{item.Tag};";
                }

                summTagText = summTagText.Substring(0, summTagText.Length - 1);
                summaryText += $"★  {sumComb.CombName}{newLine}{summTagText}{newLine}{newLine}";
                ++index;
            }

            summaryText = summaryText.Substring(0, summaryText.Length - 2);//去掉最后的换行符

            return peReportSummaries;
        }
        #endregion

        #region 返回建议用于保存
        private List<PeReportSuggestion> GetSuggestion4Save(string regNo, DateTime registerTime, List<Suggestion> suggestions, ref Dictionary<long, long> oldNewSuggIds)
        {
            var peReportSuggestions = new List<PeReportSuggestion>();

            foreach (var suggestion in suggestions)
            {
                var suggId = _noGeneration.NextSnowflakeId();

                var peReportSuggestionNew = new PeReportSuggestion
                {
                    Id = suggId,
                    RegNo = regNo,
                    Suggestion = suggestion.SugContent,
                    SortIndex = suggestion.SortIndex,
                    RegisterTime = registerTime
                };

                peReportSuggestions.Add(peReportSuggestionNew);

                oldNewSuggIds.Add(suggestion.Id, suggId);
            }

            return peReportSuggestions;
        }
        #endregion

        #region 返回引出建议的疾病，用于保存
        private List<PeReportSuggestionDisease> GetSuggestionDisease4Save(string regNo, DateTime registerTime, List<Suggestion> suggestions, Dictionary<long, long> oldNewSuggIds, Dictionary<long, long> oldNewSummTagIds, ref string suggestionText)
        {
            var peReportSuggestionDiseases = new List<PeReportSuggestionDisease>();

            var newLine = Environment.NewLine;

            if (suggestions.Any())
            {
                foreach (var suggestion in suggestions)
                {
                    var suggId = oldNewSuggIds
                        .Where(a => a.Key == suggestion.Id)
                        .Select(a => a.Value)
                        .First();

                    var diseaseText = "";//疾病列表

                    foreach (var deaTag in suggestion.DeaTags)
                    {
                        var summTagIds = oldNewSummTagIds
                                .Join(deaTag.BindSummTagIds, a => a.Key, b => b, (a, b) => a.Value)
                                .ToList();

                        var peReportSuggestionDiseaseNew = new PeReportSuggestionDisease
                        {
                            Id = _noGeneration.NextSnowflakeId(),
                            SuggId = suggId,
                            DiseaseCode = deaTag.DiseaseCode,
                            DiseaseTag = deaTag.Tag,
                            BindSummTagIds = string.Join(";", summTagIds),
                            RegNo = regNo,
                            RegisterTime = registerTime
                        };

                        peReportSuggestionDiseases.Add(peReportSuggestionDiseaseNew);

                        diseaseText += $"{deaTag.Tag};";
                    }

                    diseaseText = diseaseText.Substring(0, diseaseText.Length - 1);

                    suggestionText += $"★  {diseaseText}{newLine}{suggestion.SugContent}{newLine}{newLine}";
                }

                suggestionText = suggestionText.Substring(0, suggestionText.Length - 2);//去掉最后的换行符
            }
            else
            {
                suggestionText = "";
            }

            return peReportSuggestionDiseases;
        }
        #endregion
        #endregion

        #region 取消报告结论
        public bool CancelReportConclusion(string regNo, ref string msg)
        {
            #region 判断状态
            var patientInfo = GetPatientInfo(regNo);

            if (patientInfo.PeStatus != PeStatus.已总检)
            {
                msg = $"当前状态：{patientInfo.PeStatus}，不可以操作";

                return false;
            }
            #endregion

            #region 处理数据
            //修改状态
            var peRegister = UpdateStatus(regNo, PeStatus.已检完);

            //修改主检医生
            var peReportConclusion = _peReportConclusionRepository
                .FindAll(patientInfo.RegisterTime, a => a.RegNo == regNo)
                .FirstOrDefault();

            if (peReportConclusion != null)
            {
                peReportConclusion.CheckDoctorCode = "";
                peReportConclusion.CheckDoctorName = "";
            }
            #endregion

            #region 保存数据
            _peRegisterRepository.Update(peRegister);//修改状态

            _peReportConclusionRepository.SplitTableUpdate(peReportConclusion);//修改主检医生
            #endregion

            return true;
        }
        #endregion

        /// <summary>
        /// 手动生成正常建议
        /// </summary>
        /// <returns></returns>
        public List<Suggestion> AddNormalSuggestions()
        {
            var suggestion = new Suggestion
            {
                Id = _noGeneration.NextSnowflakeId(),
                SortIndex = 1,
                SugContent = "",
                DeaTags = new List<DiseaseTag>
                {
                    new DiseaseTag
                    {
                        Id = _noGeneration.NextSnowflakeId(),
                        DiseaseCode = "正常建议",
                        Tag = "正常建议：均衡饮食，注意休息，适当运动。",
                        BindSummTagIds = new List<long>()
                    }
                }
            };

            return new List<Suggestion> { suggestion };
        }

        #region 审核/取消
        /// <summary>
        /// 审核
        /// </summary>
        /// <param name="auditInfo"></param>
        /// <param name="msg"></param>

        public bool AuditReportConclusion(AuditInfo auditInfo, ref string msg)
        {
            #region 判断状态
            var patientInfo = GetPatientInfo(auditInfo.RegNo);

            if (patientInfo.PeStatus != PeStatus.已总检)
            {
                msg = $"当前状态：{patientInfo.PeStatus}，不可以操作";

                return false;
            }
            #endregion

            #region 判断权限

            #endregion

            #region 处理数据
            //修改状态
            var peRegister = UpdateStatus(auditInfo.RegNo, PeStatus.已审核);

            //修改医生
            var peReportConclusion = _peReportConclusionRepository
                .FindAll(patientInfo.RegisterTime, a => a.RegNo == auditInfo.RegNo)
                .FirstOrDefault();

            if (peReportConclusion != null)
            {
                peReportConclusion.AuditDoctorCode = auditInfo.AuditDoctorCode;
                peReportConclusion.AuditDoctorName = auditInfo.AuditDoctorName;
                peReportConclusion.AuditTime = DateTime.Now;
            }
            #endregion

            #region 保存数据
            _peRegisterRepository.Update(peRegister);//修改状态

            _peReportConclusionRepository.SplitTableUpdate(peReportConclusion);//修改医生
            #endregion

            return true;
        }

        /// <summary>
        /// (新)审核
        /// </summary>
        /// <param name="reportConclusion"></param>
        /// <param name="msg"></param>

        public bool NewAuditReportConclusion(ReportConclusion reportConclusion, ref string msg)
        {
            var regInfo = _registerRepository.ReadRegister(reportConclusion.PatientInfo.RegNo).First();
            if (regInfo.PeStatus != PeStatus.已总检)
            {
                msg = $"体检号：{regInfo.RegNo}，当前状态：{regInfo.PeStatus}，不可以操作";
                return false;
            }

            if (string.IsNullOrEmpty(reportConclusion.AuditDoctorCode))
            {
                msg = "审核医生不能为空!";
                return false;
            }

            #region 综述
            var oldNewSummTagIds = new Dictionary<long, long>();

            var emptySumTag = false;
            var summaryText = "";//综述文本

            var peReportSummaries = GetSummary4Save(regInfo.RegNo, regInfo.RegisterTime, reportConclusion.Summary, ref oldNewSummTagIds, ref emptySumTag, ref summaryText);
            #endregion

            #region 建议
            var oldNewSuggIds = new Dictionary<long, long>();
            var peReportSuggestions = GetSuggestion4Save(regInfo.RegNo, regInfo.RegisterTime, reportConclusion.Suggestions, ref oldNewSuggIds);
            #endregion

            #region 引出建议的疾病
            var suggestionText = "";//建议文本
            var peReportSuggestionDiseases = GetSuggestionDisease4Save(regInfo.RegNo, regInfo.RegisterTime, reportConclusion.Suggestions, oldNewSuggIds, oldNewSummTagIds, ref suggestionText);
            #endregion

            #region 结论
            if (string.IsNullOrEmpty(reportConclusion.AuditDoctorCode) || string.IsNullOrEmpty(reportConclusion.AuditDoctorName))
            {
                msg = "审核医生不能为空!";
                return false;
            }

            var peReportConclusion = _reportConclusionRepository.GetReportConclusion(regInfo.RegNo).First();
            if (peReportConclusion == null)
            {
                msg = "主检结论为空,数据异常!";
                return false;
            }

            peReportConclusion.AuditDoctorCode = reportConclusion.AuditDoctorCode;
            peReportConclusion.AuditDoctorName = reportConclusion.AuditDoctorName;
            peReportConclusion.AuditTime = DateTime.Now;
            peReportConclusion.EmptySumTag = emptySumTag;
            peReportConclusion.Summary = summaryText;
            peReportConclusion.Suggestion = suggestionText;
            peReportConclusion.Conclusion = reportConclusion.Conclusion;
            #endregion

            _dataTranRepository.ExecTran(() =>
            {
                #region 综述
                //删除原记录
                _peReportSummaryRepository.SplitTableDelete(regInfo.RegisterTime, a => a.RegNo == regInfo.RegNo);
                //保存新记录
                _peReportSummaryRepository.SplitTableInsert(peReportSummaries);
                #endregion

                #region 建议
                //删除原记录
                _peReportSuggestionRepository.SplitTableDelete(regInfo.RegisterTime, a => a.RegNo == regInfo.RegNo);

                if (peReportSuggestions.Count > 0)
                    //保存新记录
                    _peReportSuggestionRepository.SplitTableInsert(peReportSuggestions);
                #endregion

                #region 引出建议的疾病
                //删除原记录
                _peReportSuggestionDiseaseRepository.SplitTableDelete(regInfo.RegisterTime, a => a.RegNo == regInfo.RegNo);

                //保存新记录
                _peReportSuggestionDiseaseRepository.SplitTableInsert(peReportSuggestionDiseases);
                #endregion

                _peReportConclusionRepository.SplitTableUpdate(peReportConclusion);
                regInfo.PeStatus = PeStatus.已审核;
                _peRegisterRepository.Update(regInfo);
            });

            return true;
        }

        /// <summary>
        /// 取消审核
        /// </summary>
        /// <param name="auditInfo"></param>
        /// <param name="msg"></param>
        public bool CancelAuditReportConclusion(AuditInfo auditInfo, ref string msg)
        {
            #region 判断状态
            var patientInfo = GetPatientInfo(auditInfo.RegNo);

            if (patientInfo.PeStatus != PeStatus.已审核)
            {
                msg = $"当前状态：{patientInfo.PeStatus}，不可以操作";

                return false;
            }
            #endregion

            #region 处理数据
            //修改状态
            var peRegister = UpdateStatus(auditInfo.RegNo, PeStatus.已总检);

            //修改医生
            var peReportConclusion = _peReportConclusionRepository
                .FindAll(patientInfo.RegisterTime, a => a.RegNo == auditInfo.RegNo)
                .FirstOrDefault();

            if (peReportConclusion != null)
            {
                #region 判断权限
                if (peReportConclusion.AuditDoctorCode != auditInfo.AuditDoctorCode)
                {
                    msg = $"审核医生：{peReportConclusion.AuditDoctorName}，其他医生不可以操作";

                    return false;
                }
                #endregion

                peReportConclusion.AuditDoctorCode = "";
                peReportConclusion.AuditDoctorName = "";
                peReportConclusion.AuditTime = null;
            }
            #endregion

            #region 保存数据
            _peRegisterRepository.Update(peRegister);//修改状态

            _peReportConclusionRepository.SplitTableUpdate(peReportConclusion);//修改医生
            #endregion

            return true;
        }
        #endregion

        #region 获取已录入结果的组合，用于综述
        /// <summary>
        /// 获取已录入结果的组合，用于综述
        /// </summary>
        /// <param name="regNo"></param>
        /// <param name="msg"></param>
        /// <returns></returns>
        public List<ReportComb> GetRecordCombs(string regNo, ref string msg)
        {
            var list = _recordRepository.ReadCombsInfo(regNo)
                .Where((regComb, recComb) => !SqlFunc.IsNullOrEmpty(recComb.CombResult))
                .Select((regComb, recComb) => new ReportComb
                {
                    CombCode = recComb.CombCode,
                    CombName = regComb.CombName,
                    CombSortIndex = regComb.CombSortIndex,
                    ClsSortIndex = regComb.ClsSortIndex,
                    CheckCls = regComb.CheckCls,
                    DoctorName = recComb.DoctorName,
                    ExamTime = recComb.ExamTime,
                })
                .ToList();

            list = BaseCombClsSort.OrderBy(collection: list).ToList();
            return list;
        }
        #endregion

        #region 编辑小结标签
        public SumTagEditBack EditSumTag(SumTagEdit sumTagEdit)
        {
            var sumTagEditBack = new SumTagEditBack();//返回

            if (!Enum.IsDefined(typeof(SumTagEdit.TagEditMode), sumTagEdit.EditMode))
                throw new Exception("操作模式不正确");

            switch (sumTagEdit.EditMode)
            {
                case SumTagEdit.TagEditMode.新增:
                    var sumTagId = _noGeneration.NextSnowflakeId(); // 新增标签的id
                    sumTagEdit.SumTag.Id = sumTagId;
                    sumTagEditBack.SumTagId = sumTagId;
                    break;

                case SumTagEdit.TagEditMode.修改:
                case SumTagEdit.TagEditMode.删除:
                    var suggestionsNew = new List<Suggestion>();

                    foreach (var suggestion in sumTagEdit.Suggestions)
                    {
                        foreach (var diseaseTag in suggestion.DeaTags)
                        {
                            // 疾病是否绑定小结标签id
                            if (diseaseTag.BindSummTagIds.Contains(sumTagEdit.SumTag.Id))
                            {
                                if (diseaseTag.BindSummTagIds.Count > 1) // 绑定并且还有其它id
                                {
                                    diseaseTag.BindSummTagIds.Remove(sumTagEdit.SumTag.Id);
                                    suggestionsNew.Add(suggestion);
                                }
                            }
                            else
                            {
                                suggestionsNew.Add(suggestion);
                            }
                        }
                    }

                    sumTagEdit.Suggestions = suggestionsNew; // 新建议
                    break;
            }

            if (sumTagEdit.EditMode == SumTagEdit.TagEditMode.新增 || sumTagEdit.EditMode == SumTagEdit.TagEditMode.修改)
            {
                //对应的疾病代码
                var tagIdDiseaseCodes = GetTagIdDiseaseCode(new List<SumTag> { sumTagEdit.SumTag });
                //TODO:生成综述还在优化中
                //if (tagIdDiseaseCodes.Count == 0)
                //    return sumTagEditBack;

                //疾病信息
                var diseases = _reportConclusionRepository.GetDiseaseAndEntry()
                    .Select(a => new
                    {
                        a.DiseaseCode,
                        a.DiseaseName,
                        a.SuggestContent
                    })
                    .Distinct()
                    .ToList();

                //相关的建议
                var suggestDiseases = diseases
                        .Join(tagIdDiseaseCodes, a => a.DiseaseCode, b => b.DiseaseCode, (a, b) => new
                        {
                            a.DiseaseCode,
                            a.DiseaseName,
                            a.SuggestContent,
                            SummTagId = b.SumTagId
                        })
                        .ToList();

                //合并建议
                foreach (var item in suggestDiseases)
                {
                    var suggestions = sumTagEdit.Suggestions.Where(a => a.SugContent == item.SuggestContent);

                    //相同建议，合并
                    if (!suggestions.Any())
                    {
                        sumTagEdit.Suggestions.Add(new Suggestion
                        {
                            Id = _noGeneration.NextSnowflakeId(),
                            SortIndex = sumTagEdit.Suggestions.Count + 1,
                            SugContent = item.SuggestContent,
                            DeaTags = new List<DiseaseTag>
                                {
                                    new DiseaseTag{
                                        Id = _noGeneration.NextSnowflakeId(),
                                        DiseaseCode= item.DiseaseCode,
                                        Tag = item.DiseaseName,
                                        BindSummTagIds = new List<long>(){item.SummTagId}
                                    }
                                }
                        });
                        break;
                    }

                    var deaTags = suggestions.First().DeaTags
                            .Where(a => a.DiseaseCode == item.DiseaseCode)
                            .ToList();

                    if (!deaTags.Any())//新疾病，新增疾病标签
                    {
                        deaTags.Add(new DiseaseTag
                        {
                            Id = item.SummTagId,
                            DiseaseCode = item.DiseaseCode,
                            Tag = item.DiseaseName,
                            BindSummTagIds = new List<long>() { item.SummTagId }
                        });
                        break;
                    }

                    //已有疾病，只绑定id
                    var bindSummTagIds = deaTags.First().BindSummTagIds;

                    if (bindSummTagIds.Any(a => a == item.SummTagId))
                        bindSummTagIds.Add(item.SummTagId);
                }
            }

            sumTagEditBack.Suggestions = sumTagEdit.Suggestions;

            return sumTagEditBack;
        }
        #endregion

        #region 修改状态
        private PeRegister UpdateStatus(string regNo, PeStatus peStatus)
        {
            var peRegister = _reportConclusionRepository.GetPatientInfo()
                .Where(a => a.RegNo == regNo)
                .Select(a => a)
                .First();

            if (peRegister != null)
            {
                peRegister.PeStatus = peStatus;
            }

            return peRegister;
        }
        #endregion

        /// <summary>
        /// 主检(不分配)----查询
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        public CheckPerson[] ReadCheckPerson(CheckPersonQuery query)
        {
            ISugarQueryable<PeRegister> sugarQueryable = null;
            if (!string.IsNullOrEmpty(query.RegNo))
            {
                sugarQueryable = _registerRepository.ReadRegister(query.RegNo)
                                 .Where(x => x.PeStatus == PeStatus.已检完 || x.PeStatus == PeStatus.已总检 ||
                                        x.PeStatus == PeStatus.已审核);
            }
            else
            {
                sugarQueryable = _registerRepository.ReadRegister()
                        .WhereIF(!string.IsNullOrEmpty(query.Name), x => x.Name.Contains(query.Name))
                        .WhereIF(!string.IsNullOrEmpty(query.CompanyCode), x => x.CompanyCode == query.CompanyCode)
                        .WhereIF((int)query.PeCls != -1, x => x.PeCls == query.PeCls)
                        .WhereIF((int)query.PeStatus == -1, x => x.PeStatus == PeStatus.已检完 || x.PeStatus == PeStatus.已总检 || x.PeStatus == PeStatus.已审核)
                        .WhereIF((int)query.PeStatus != -1, x => x.PeStatus == query.PeStatus)
                        .Where(x => SqlFunc.Between(x.ActiveTime.Value.Date, query.BeginTime, query.EndTime));
            }

            return sugarQueryable.Select(x => new CheckPerson
            {
                RegNo = x.RegNo,
                Name = x.Name,
                Sex = x.Sex,
                Age = x.Age,
                MarryStatus = x.MarryStatus,
                CardNo = x.CardNo,
                Tel = x.Tel,
                PeCls = x.PeCls,
                ActiveTime = x.ActiveTime,
                IsVIP = x.IsVIP,
                PeStatus = x.PeStatus,
                CompanyName = SqlFunc.Subqueryable<CodeCompany>().Where(y => y.CompanyCode == x.CompanyCode).Select(y => y.CompanyName)
            }).ToArray();
        }

        /// <summary>
        /// 撤回主检
        /// </summary>
        /// <param name="returnChecked"></param>
        /// <param name="msg"></param>
        /// <returns></returns>
        public bool ReturnChecked(ReturnChecked returnChecked, ref string msg)
        {
            #region 判断状态
            var patientInfo = GetPatientInfo(returnChecked.RegNo);

            if (patientInfo.PeStatus != PeStatus.已总检)
            {
                msg = $"当前状态：{patientInfo.PeStatus}，不可以操作";

                return false;
            }

            if (string.IsNullOrEmpty(returnChecked.QuestionContent))
            {
                msg = $"咨询内容不能为空";

                return false;
            }
            #endregion

            #region 判断记录
            var peReportExtReturnCheckeds = _peReportExtReturnCheckedRepository
                .FindAll(a => a.RegNo == returnChecked.RegNo);

            if (peReportExtReturnCheckeds.Any())
            {
                if (peReportExtReturnCheckeds.First().ReplyTime == null)
                {
                    msg = $"已在{peReportExtReturnCheckeds.First().QuestionerTime}，向主检医生提出过咨询，医生未回复";

                    return false;
                }
            }
            #endregion

            #region 处理数据
            //新的记录
            PeReportExtReturnChecked peReportExtReturnCheckedNew = new()
            {
                RegNo = returnChecked.RegNo,
                QuestionerCode = returnChecked.QuestionerCode,
                QuestionerName = returnChecked.QuestionerName,
                QuestionContent = returnChecked.QuestionContent,
                QuestionerTime = DateTime.Now,
                ReplyerCode = returnChecked.ReplyerCode,
                ReplyerName = returnChecked?.ReplyerName,
            };
            #endregion

            #region 保存数据
            _peReportExtReturnCheckedRepository.Insert(peReportExtReturnCheckedNew);
            #endregion

            return true;
        }

        /// <summary>
        /// 获取撤回主检记录（主检回复）
        /// </summary>
        /// <param name="operatorCode">审核医生</param>
        /// <returns></returns>
        public List<PeReportExtReturnChecked> GetReturnChecked(string operatorCode)
        {
            return _peReportExtReturnCheckedRepository
                .FindAll(a => a.QuestionerCode == operatorCode)
                .OrderBy(a => a.ReplyContent)
                .ThenBy(a => a.QuestionerTime)
                .ToList();
        }

        /// <summary>
        /// 获取撤回主检的信息
        /// </summary>
        /// <param name="replyerCode"></param>
        /// <param name="peReportExtReturnCheckeds"></param>
        /// <param name="msg"></param>
        /// <returns></returns>
        public bool GetNotReplyReturn(string replyerCode, ref List<PeReportExtReturnChecked> peReportExtReturnCheckeds, ref string msg)
        {
            peReportExtReturnCheckeds = _peReportExtReturnCheckedRepository
                .FindAll(a => a.ReplyerCode == replyerCode && a.ReplyTime == null)
                .ToList();

            return true;
        }

        /// <summary>
        /// 答复撤回主检
        /// </summary>
        /// <param name="peReportExtReturnCheckeds"></param>
        /// <param name="msg"></param>
        /// <returns></returns>
        public bool ReplyReturn(List<PeReportExtReturnChecked> peReportExtReturnCheckeds, ref string msg)
        {
            #region 校验数据
            if (!peReportExtReturnCheckeds.Any())
            {
                msg = $"没有提供任何信息";

                return false;
            }

            var replyTime = DateTime.Now;

            List<PeRegister> peRegisters = new();

            foreach (var item in peReportExtReturnCheckeds)
            {
                if (string.IsNullOrEmpty(item.ReplyContent))
                {
                    msg = $"答复内容不能为空";

                    return false;
                }

                item.ReplyTime = replyTime;

                peRegisters.Add(UpdateStatus(item.RegNo, PeStatus.已检完));
            }
            #endregion

            #region 提交数据
            _dataTranRepository.ExecTran(() =>
            {
                _peReportExtReturnCheckedRepository.Update(peReportExtReturnCheckeds);

                _peRegisterRepository.Update(peRegisters);
            });
            #endregion

            return true;
        }
    }
}
