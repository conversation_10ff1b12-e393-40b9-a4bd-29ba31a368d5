﻿using Peis.Utility.Helper;

namespace Peis.Model.DTO.Disease
{
    public class ReferenceRange
    {
        public double Lower { get; set; }

        public double Upper { get; set; }

        public ReferenceRange(double lower, double upper)
        {
            Lower = lower;
            Upper = upper;
        }

        public static bool TryParse(string lower, string upper, out ReferenceRange range)
        {
            range = null;

            if (lower.TryParseDouble(out var lowerValue) && upper.TryParseDouble(out var upperValue))
            {
                if (upperValue >= lowerValue)
                {
                    range = new ReferenceRange(lowerValue, upperValue);
                    return true;
                }
            }

            return false;
        }

        public bool Contains(double value)
        {
            return value >= Lower && value <= Upper;
        }

        public int CompareTo(double value)
        {
            if (value < Lower)
                return -1;
            else if (value > Upper)
                return 1;
            else
                return 0;
        }
    }
}
