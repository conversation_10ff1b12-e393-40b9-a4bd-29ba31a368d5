﻿namespace Peis.Model.DataBaseEntity.Occupation;

/// <summary>
/// 体检职业病危害因素表
/// </summary>
[SugarTable("PeRegisterOccupationHazard")]
public class PeRegisterOccupationHazard
{
    /// <summary>
    /// 体检号
    /// </summary>        
    [SugarColumn(IsNullable = false, IsPrimaryKey = true, Length = 12)]
    public string RegNo { get; set; }

    /// <summary>
    /// 危害因素代码
    /// </summary>        
    [SugarColumn(IsNullable = false, IsPrimaryKey = true, Length = 10)]
    public string HazardousCode { get; set; }

    /// <summary>
    /// 危害因素名称
    /// </summary>        
    [SugarColumn(IsNullable = false, Length = 100)]
    public string HazardousName { get; set; }

    /// <summary>
    /// 开始接害日期
    /// </summary>        
    [SugarColumn(IsNullable = false)]
    public DateTime StartDateOfHazards { get; set; }

    /// <summary>
    /// 接害工龄年
    /// </summary>        
    [SugarColumn(IsNullable = false)]
    public int YearsOfHazards { get; set; }

    /// <summary>
    /// 接害工龄月
    /// </summary>        
    [SugarColumn(IsNullable = false)]
    public int MonthsOfHazards { get; set; }

    /// <summary>
    /// 结论代码
    /// </summary>        
    [SugarColumn(IsNullable = true, Length = 5)]
    public string ConclusionCode { get; set; }

    /// <summary>
    /// 结论名称
    /// </summary>        
    [SugarColumn(IsNullable = true, Length = 255)]
    public string ConclusionName { get; set; }

    /// <summary>
    /// 职业病代码
    /// </summary>        
    [SugarColumn(IsNullable = true, Length = 5)]
    public string OccupationDiseaseCode { get; set; }

    /// <summary>
    /// 职业病名称
    /// </summary>        
    [SugarColumn(IsNullable = true, Length = 50)]
    public string OccupationDiseaseName { get; set; }

    /// <summary>
    /// 职业禁忌证代码
    /// </summary>        
    [SugarColumn(IsNullable = true, Length = 5)]
    public string ContraindicationCode { get; set; }

    /// <summary>
    /// 职业禁忌证名称
    /// </summary>        
    [SugarColumn(IsNullable = true, Length = 200)]
    public string ContraindicationName { get; set; }

    /// <summary>
    /// 其他疾病或异常内容
    /// </summary>        
    [SugarColumn(IsNullable = true, Length = 255)]
    public string OtherDiseaseOrContent { get; set; }

    #region Ext
    /// <summary>
    /// 危害因素名称集合转字符内容
    /// </summary>
    /// <param name="hazardousNames">危害因素名称集合</param>
    /// <returns></returns>
    public static string ToHazardousNames(IEnumerable<string> hazardousNames) => string.Join("、",hazardousNames);
    #endregion
}
