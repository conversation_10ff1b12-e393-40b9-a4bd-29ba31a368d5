﻿using AutoMapper;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Peis.Model.DataBaseEntity;
using Peis.Model.DTO.DiseaseMgt;
using Peis.Model.DTO.External.DataMaintenances;
using Peis.Model.DTO.ReportConclusionNew;
using Peis.Model.Other.Input.DiseaseMgt;
using Peis.Repository.Repository.TransactionAttribute;
using Peis.Service.IService;
using Peis.Service.IService.Helper;
using System.Collections.Generic;

namespace Peis.API.Controllers.DataMaintenance;

/// <summary>
/// 疾病管理
/// </summary>
[Route("api/[controller]")]
[ApiController]
public class DiseaseMgtController : BaseApiController
{
    private readonly IDiseaseMgtService _diseaseMgtService;
    private readonly INoGeneration _noGeneration;
    private readonly IMapper _mapper;

    public DiseaseMgtController(IDiseaseMgtService diseaseMgtService,
        INoGeneration noGeneration,
        IMapper mapper)
    {
        _diseaseMgtService = diseaseMgtService;
        _noGeneration = noGeneration;
        _mapper = mapper;
    }

    #region 获取科室包含的疾病
    /// <summary>
    /// 获取科室包含的疾病
    /// </summary>
    /// <returns></returns>
    [HttpPost("GetDiseaseInDept")]
    [ProducesResponseType(typeof(List<DiseaseInDept>), 200)]
    public IActionResult GetDiseaseInDept()
    {
        result.ReturnData = _diseaseMgtService.GetDiseaseInDept();
        return Ok(result);
    }
    #endregion

    #region 疾病信息
    /// <summary>
    /// /CU_CodeDisease/Create 新增疾病信息
    /// /CU_CodeDisease/Update 更新疾病信息
    /// </summary>
    /// <param name="type">操作</param>
    /// <param name="codeDisease">疾病信息</param>
    /// <returns></returns>
    [HttpPost("CU_CodeDisease/{type}")]
    public IActionResult CU_CodeDisease([FromRoute] string type, [FromBody] CodeDisease codeDisease)
    {
        string msg = string.Empty;

        switch (type.ToLower())
        {
            case "create":
                codeDisease.DiseaseCode = _noGeneration.NextDiseaseNo()[0];
                result.Success = _diseaseMgtService.CreateCodeDisease(codeDisease, ref msg);
                break;
            case "update":
                result.Success = _diseaseMgtService.UpdateCodeDisease(codeDisease, ref msg);
                break;
            default:
                return new BadRequestResult();
        }
        result.ReturnMsg = msg;
        return Ok(result);
    }

    /// <summary>
    /// /RD_CodeDisease/Read   获取疾病信息
    /// /RD_CodeDisease/Delete 删除疾病信息
    /// </summary>
    /// <param name="type">操作</param>
    /// <param name="diseaseCode">疾病代码{"diseaseCode":"123"}</param>
    /// <returns></returns>
    [HttpPost("RD_CodeDisease/{type}")]
    public IActionResult RD_CodeDisease([FromRoute] string type, [FromQuery] string diseaseCode)
    {
        string msg = string.Empty;

        switch (type.ToLower())
        {
            case "read":
                result.ReturnData = _diseaseMgtService.ReadCodeDisease(diseaseCode);
                break;
            case "delete":
                result.Success = _diseaseMgtService.DeleteCodeDisease(diseaseCode, ref msg);
                break;
            default:
                return new BadRequestResult();
        }
        return Ok(result);
    }

    /// <summary>
    /// 获取疾病集合
    /// </summary>
    /// <param name="query">查询内容</param>
    /// <returns></returns>
    [HttpPost("GetCodeDiseases")]
    [ProducesResponseType(typeof(List<CodeDisease>), StatusCodes.Status200OK)]
    public IActionResult GetCodeDiseases(CodeDiseaseQuery query)
    {
        int totalNumber = 0;
        int totalPage = 0;
        result.ReturnData = _diseaseMgtService.GetCodeDiseases(query, ref totalNumber, ref totalPage);
        result.TotalNumber = totalNumber;
        result.TotalPage = totalPage;

        return Ok(result);
    }

    /// <summary>
    /// 批量疾病词条转所属疾病词条
    /// </summary>
    /// <param name="codeDiseaseEntries">词条内容集合</param>
    /// <returns></returns>
    [HttpPost("BatchCodeDiseasesToEntries")]
    [ProducesResponseType(typeof(bool), StatusCodes.Status200OK)]
    [UnitOfWork]
    public IActionResult BatchCodeDiseasesToEntries(List<CodeDiseaseEntryDto> codeDiseaseEntries)
    {
        result.Success = _diseaseMgtService.BatchCodeDiseasesToEntries(codeDiseaseEntries, out var msg);
        result.ReturnMsg = msg;

        return Ok(result);
    }

    /// <summary>
    /// 汇总统计自定义疾病
    /// </summary>
    /// <param name="query">查询</param>
    /// <returns></returns>
    [HttpPost("GetCustomDiseaseSummaries")]
    [ProducesResponseType(typeof(List<DiseaseSumDto>), StatusCodes.Status200OK)]
    public IActionResult GetCustomDiseaseSummaries(DiseaseSumQuery query)
    {
        result.ReturnData = _diseaseMgtService.GetCustomDiseaseSummaries(query);

        return Ok(result);
    }

    /// <summary>
    /// 获取自定义疾病建议
    /// </summary>
    /// <param name="query">查询</param>
    /// <returns></returns>
    [HttpPost("GetCustomDiseaseSuggestions")]
    [ProducesResponseType(typeof(List<DiseaseSuggDto>), StatusCodes.Status200OK)]
    public IActionResult GetCustomDiseaseSuggestions(DiseaseSuggQuery query)
    {
        result.ReturnData = _diseaseMgtService.GetCustomDiseaseSuggestions(query);

        return Ok(result);
    }

    /// <summary>
    /// 设置报告自定义疾病code默认处理
    /// </summary>
    /// <param name="query">查询</param>
    /// <returns></returns>
    [HttpPost("SetCustomDiseaseCodeDefault")]
    [ProducesResponseType(typeof(bool), StatusCodes.Status200OK)]
    [UnitOfWork]
    public IActionResult SetCustomDiseaseCodeDefault(DiseaseSumQuery query)
    {
        result.ReturnData = _diseaseMgtService.SetCustomDiseaseCodeDefault(query);

        return Ok(result);
    }

    /// <summary>
    /// 通过关键词获取疾病信息
    /// </summary>
    /// <param name="keyword">查询条件(疾病名称，疾病词条，疾病建议)</param>
    /// <returns></returns>
    [HttpPost("GetDiseaseInfo")]
    [ProducesResponseType(typeof(Disease[]), StatusCodes.Status200OK)]
    public IActionResult GetDiseaseInfo([FromQuery] string keyword)
    {
        result.ReturnData = _diseaseMgtService.GetDiseaseInfo(keyword);
        return Ok(result);
    }

    /// <summary>
    /// 根据疾病名获取疾病信息
    /// </summary>
    /// <param name="diseaseName">疾病名</param>
    /// <returns></returns>
    [HttpPost("GetDiseasesByName")]
    [ProducesResponseType(typeof(Disease[]), StatusCodes.Status200OK)]
    public IActionResult GetDiseasesByName([FromQuery] string diseaseName)
    {
        result.ReturnData = _diseaseMgtService.GetDiseasesByName(diseaseName);
        return Ok(result);
    }
    #endregion

    #region 疾病审核条件包含的项目

    #region 疾病逻辑值
    /// <summary>
    /// /CU_CodeDiseaseCriteria/Create 新增疾病逻辑值
    /// /CU_CodeDiseaseCriteria/Update 更新疾病逻辑值
    /// </summary>
    /// <param name="type">操作</param>
    /// <param name="deaCriteria">疾病逻辑值</param>
    /// <returns></returns>
    [HttpPost("CU_CodeDiseaseCriteria/{type}")]
    public IActionResult CU_CodeDiseaseCriteria([FromRoute] string type, [FromBody] CodeDiseaseCriteria deaCriteria)
    {
        string msg = string.Empty;

        switch (type.ToLower())
        {
            case "create":
                result.Success = _diseaseMgtService.CreateCodeDiseaseCriteria(deaCriteria, ref msg);
                break;
            case "update":
                result.Success = _diseaseMgtService.UpdateCodeDiseaseCriteria(deaCriteria, ref msg);
                break;
            default:
                return new BadRequestResult();
        }
        result.ReturnMsg = msg;
        return Ok(result);
    }

    /// <summary>
    /// ReadCodeDiseaseCriteriaItem 获取疾病逻辑值
    /// </summary>
    /// <param name="diseaseCode">疾病代码{"diseaseCode":"123"}</param>
    /// <returns></returns>
    [HttpPost("ReadCodeDiseaseCriteria")]
    [ProducesResponseType(typeof(CodeDiseaseCriteria), 200)]
    public IActionResult ReadCodeDiseaseCriteria([FromQuery] string diseaseCode)
    {
        result.ReturnData = _diseaseMgtService.ReadCodeDiseaseCriteria(diseaseCode);
        return Ok(result);
    }
    #endregion

    /// <summary>
    /// CreateCodeDiseaseCriteriaItem 新增疾病审核条件包含的项目
    /// </summary>
    /// <param name="deaCriteriaItem">疾病审核条件的项目</param>
    /// <returns></returns>
    [HttpPost("CreateCodeDiseaseCriteriaItem")]
    public IActionResult CreateCodeDiseaseCriteriaItem([FromBody] CodeDiseaseCriteriaItem deaCriteriaItem)
    {
        string msg = string.Empty;
        result.Success = _diseaseMgtService.CreateCodeDiseaseCriteriaItem(deaCriteriaItem, ref msg);
        result.ReturnMsg = msg;
        return Ok(result);
    }

    /// <summary>
    /// UpdateCodeDiseaseCriteriaItem 更新疾病审核条件包含的项目
    /// </summary>
    /// <param name="diseaseCriteriaItem">疾病审核条件的项目</param>
    /// <returns></returns>
    [HttpPost("UpdateCodeDiseaseCriteriaItem")]
    public IActionResult UpdateCodeDiseaseCriteriaItem([FromBody] UpdateDiseaseCriteriaItem diseaseCriteriaItem)
    {
        string msg = string.Empty;
        result.Success = _diseaseMgtService.UpdateCodeDiseaseCriteriaItem(diseaseCriteriaItem, ref msg);
        result.ReturnMsg = msg;
        return Ok(result);
    }

    /// <summary>
    /// DeleteCodeDiseaseCriteriaItem 删除疾病审核条件包含的项目
    /// </summary>
    /// <param name="items">项目集合</param>
    /// <returns></returns>
    [HttpPost("DeleteCodeDiseaseCriteriaItem")]
    public IActionResult DeleteCodeDiseaseCriteriaItem([FromBody] List<CodeDiseaseCriteriaItem> items)
    {
        string msg = string.Empty;
        result.Success = _diseaseMgtService.DeleteCodeDiseaseCriteriaItem(items, ref msg);
        result.ReturnMsg = msg;
        return Ok(result);
    }

    /// <summary>
    /// ReadCodeDiseaseCriteriaItem 获取疾病审核条件包含的项目
    /// </summary>
    /// <param name="diseaseCode">疾病代码{"diseaseCode":"123"}</param>
    /// <returns></returns>
    [HttpPost("ReadCodeDiseaseCriteriaItem")]
    [ProducesResponseType(typeof(DiseaseCriteriaItemInfo[]), 200)]
    public IActionResult ReadCodeDiseaseCriteriaItem([FromQuery] string diseaseCode)
    {
        var criteriaItem = _diseaseMgtService.ReadCodeDiseaseCriteriaItem(diseaseCode);//疾病项目
        var criteria = _diseaseMgtService.ReadCodeDiseaseCriteria(diseaseCode);//逻辑值
        result.ReturnData = new { criteriaItem, criteria };
        return Ok(result);
    }
    #endregion

    #region 疾病判断计算式
    /// <summary>
    /// 疾病判断计算式列表(UI左边的接口)
    /// </summary>
    /// <returns></returns>
    [HttpPost("ReadDiseaseExpressionList")]
    public IActionResult ReadDiseaseExpressionList()
    {
        result.ReturnData = _diseaseMgtService.ReadDiseaseExpressionList();
        return Ok(result);
    }

    /// <summary>
    /// /CU_CodeDiseaseExpression/Create 新增疾病判断计算式
    /// /CU_CodeDiseaseExpression/Update 更新疾病判断计算式
    /// </summary>
    /// <param name="type">操作</param>
    /// <param name="codeDiseaseExpression">疾病判断计算式</param>
    /// <returns></returns>
    [HttpPost("CU_CodeDiseaseExpression/{type}")]
    public IActionResult CU_CodeDiseaseExpression([FromRoute] string type, [FromBody] CodeDiseaseExpression codeDiseaseExpression)
    {
        string msg = string.Empty;

        switch (type.ToLower())
        {
            case "create":
                result.Success = _diseaseMgtService.CreateCodeDiseaseExpression(codeDiseaseExpression, ref msg);
                break;
            case "update":
                result.Success = _diseaseMgtService.UpdateCodeDiseaseExpression(codeDiseaseExpression, ref msg);
                break;
            default:
                return new BadRequestResult();
        }
        result.ReturnMsg = msg;
        return Ok(result);
    }

    /// <summary>
    /// /RD_CodeDiseaseExpression/Read   获取疾病判断计算式
    /// /RD_CodeDiseaseExpression/Delete 删除疾病判断计算式
    /// </summary>
    /// <param name="type">操作</param>
    /// <param name="expCode">公式代码{"expCode":"123"}</param>
    /// <returns></returns>
    [HttpPost("RD_CodeDiseaseExpression/{type}")]
    [ProducesResponseType(typeof(CodeDiseaseExpression), 200)]
    public IActionResult RD_CodeDiseaseExpression([FromRoute] string type, [FromQuery] string expCode)
    {
        string msg = string.Empty;

        switch (type.ToLower())
        {
            case "read":
                result.ReturnData = _diseaseMgtService.ReadCodeDiseaseExpression(expCode);
                break;
            case "delete":
                result.Success = _diseaseMgtService.DeleteCodeDiseaseExpression(expCode, ref msg);
                break;
            default:
                return new BadRequestResult();
        }
        return Ok(result);
    }
    #endregion

    #region 疾病包含关系对应 MapDiseaseDisease
    /// <summary>
    /// 获取有疾病对应关系的数据
    /// </summary>
    /// <returns></returns>
    [HttpPost("ReadMapDiseaseDisease")]
    public IActionResult ReadMapDiseaseDisease()
    {
        result.ReturnData = _diseaseMgtService.QueryMapDiseaseDisease();
        return Ok(result);
    }

    /// <summary>
    /// 删除父疾病
    /// </summary>
    /// <param name="deaCodes">["0002","0003"]</param>
    /// <returns></returns>
    [HttpPost("DeleteParentDisease")]
    public IActionResult DeleteParentDisease([FromBody] string[] deaCodes)
    {
        result.ReturnData = _diseaseMgtService.DeleteParentDisease(deaCodes);
        return Ok(result);
    }

    /// <summary>
    /// 获取科室疾病用于疾病包含关系中的父疾病
    /// </summary>
    /// <returns></returns>
    [HttpPost("GetParentDeptDisease")]
    [ProducesResponseType(typeof(List<ClsDisease>), 200)]
    public IActionResult GetParentDeptDisease()
    {
        result.ReturnData = _diseaseMgtService.GetParentDeptDiseases();
        return Ok(result);
    }

    /// <summary>
    /// 获取科室疾病用于疾病包含关系中的子疾病
    /// </summary>
    /// <param name="diseaseCode">父疾病代码</param>
    /// <returns></returns>
    [HttpPost("GetChildDeptDisease")]
    [ProducesResponseType(typeof(List<ClsDisease>), 200)]
    public IActionResult GetChildDeptDisease(string diseaseCode)
    {
        result.ReturnData = _diseaseMgtService.GetChildDeptDiseases(diseaseCode);
        return Ok(result);
    }

    /// <summary>
    /// 获取疾病包含关系对应
    /// </summary>
    /// <param name="diseaseCode">疾病代码</param>
    /// <returns></returns>
    [HttpPost("Query_MapDiseaseDisease")]
    [ProducesResponseType(typeof(List<ClsDisease>), 200)]
    public IActionResult Query_MapDiseaseDisease([FromQuery] string diseaseCode)
    {
        result.ReturnData = _diseaseMgtService.QueryMapDiseaseDisease(diseaseCode);
        return Ok(result);
    }

    /// <summary>
    /// /CD_MapDiseaseDisease/Create 新增疾病包含关系对应
    /// /CD_MapDiseaseDisease/Delete 删除疾病包含关系对应
    /// </summary>
    /// <param name="type">操作</param>
    /// <param name="mapMapDiseaseDiseases">疾病包含关系对应</param>
    /// <returns></returns>
    [HttpPost("CD_MapDiseaseDisease/{type}")]
    public IActionResult CD_MapDiseaseDisease([FromRoute] string type, [FromBody] List<MapDiseaseDisease> mapMapDiseaseDiseases)
    {
        string msg = string.Empty;

        switch (type.ToLower())
        {
            case "create":
                result.Success = _diseaseMgtService.CreateMapDiseaseDisease(mapMapDiseaseDiseases, ref msg);
                break;
            case "delete":
                result.Success = _diseaseMgtService.DeleteMapDiseaseDisease(mapMapDiseaseDiseases, ref msg);
                break;
            default:
                return new BadRequestResult();
        }

        result.ReturnMsg = msg;
        return Ok(result);
    }
    #endregion

    #region 疾病分类信息 CodeDiseaseCls
    /// <summary>
    /// /CU_CodeDiseaseCls/Create 新增疾病分类信息
    /// /CU_CodeDiseaseCls/Update 更新疾病分类信息
    /// </summary>
    /// <param name="type">操作</param>
    /// <param name="codeDiseaseCls">疾病分类信息</param>
    /// <returns></returns>
    [HttpPost("CU_CodeDiseaseCls/{type}")]
    public IActionResult CU_CodeDiseaseCls([FromRoute] string type, [FromBody] CodeDiseaseCls codeDiseaseCls)
    {
        string msg = string.Empty;

        switch (type.ToLower())
        {
            case "create":
                result.Success = _diseaseMgtService.CreateCodeDiseaseCls(codeDiseaseCls, ref msg);
                break;
            case "update":
                result.Success = _diseaseMgtService.UpdateCodeDiseaseCls(codeDiseaseCls, ref msg);
                break;
            default:
                return new BadRequestResult();
        }
        result.ReturnMsg = msg;
        return Ok(result);
    }

    /// <summary>
    /// /RD_CodeDiseaseCls/Read   获取疾病分类信息
    /// /RD_CodeDiseaseCls/Delete 删除疾病分类信息
    /// </summary>
    /// <param name="type">操作</param>
    /// <param name="diseaseClsCode">疾病分类代码</param>
    /// <returns></returns>
    [HttpPost("RD_CodeDiseaseCls/{type}")]
    public IActionResult RD_CodeDiseaseCls([FromRoute] string type, [FromBody] string[] diseaseClsCode)
    {
        string msg = string.Empty;
        switch (type.ToLower())
        {
            case "read":
                result.ReturnData = _diseaseMgtService.ReadCodeDiseaseCls();
                break;
            case "delete":
                result.Success = _diseaseMgtService.DeleteCodeDiseaseCls(diseaseClsCode, ref msg);
                break;
            default:
                return new BadRequestResult();
        }
        return Ok(result);
    }

    /// <summary>
    /// 保存疾病分类对应组合内容
    /// </summary>
    /// <param name="data">数据信息</param>
    /// <returns></returns>
    [HttpPost("BatchSaveMapDiseaseClsComb")]
    [ProducesResponseType(typeof(bool), StatusCodes.Status200OK)]
    [UnitOfWork]
    public IActionResult BatchSaveMapDiseaseClsComb([FromBody] List<MapDiseaseClsCombDto> data)
    {
        result.Success = _diseaseMgtService.BatchSaveMapDiseaseClsComb(_mapper.Map<List<MapDiseaseClsComb>>(data));
        return Ok(result);
    }

    /// <summary>
    /// 删除疾病分类对应组合内容
    /// </summary>
    /// <param name="diseaseClsCode">疾病分类代码</param>
    /// <param name="combCodes">组合代码集合</param>
    /// <returns></returns>
    [HttpPost("BatchRemoveMapDiseaseClsComb/{diseaseClsCode}")]
    [ProducesResponseType(typeof(bool), StatusCodes.Status200OK)]
    public IActionResult BatchRemoveMapDiseaseClsComb(string diseaseClsCode, [FromBody] string[] combCodes)
    {
        result.Success = _diseaseMgtService.BatchRemoveMapDiseaseClsComb(diseaseClsCode, combCodes);
        result.ReturnMsg = result.Success ? "删除成功" : "删除失败";
        return Ok(result);
    }

    /// <summary>
    /// 查询疾病分类对应组合内容
    /// </summary>
    /// <param name="query">查询条件</param>
    /// <returns></returns>
    [HttpPost("GetMapDiseaseClsCombs")]
    [ProducesResponseType(typeof(List<MapDiseaseClsCombDto>), StatusCodes.Status200OK)]
    public IActionResult GetMapDiseaseClsCombs([FromBody] MapDiseaseClsCombQuery query)
    {
        var list = _diseaseMgtService.GetMapDiseaseClsCombs(query);
        result.ReturnData = _mapper.Map<List<MapDiseaseClsCombDto>>(list);
        result.TotalNumber = query.TotalNumber;
        result.TotalPage = query.TotalPage;
        return Ok(result);
    }

    /// <summary>
    /// 获取带有统计疾病数的组合信息
    /// </summary>
    /// <param name="query">查询条件</param>
    /// <returns></returns>
    [HttpPost("GetCombWithDiseCounts")]
    [ProducesResponseType(typeof(List<MatchDiseaseClsCombDto>), StatusCodes.Status200OK)]
    public IActionResult GetCombWithDiseCounts([FromBody] MatchDiseaseClsCombQuery query)
    {
        result.ReturnData = _diseaseMgtService.GetCombWithDiseCounts(query);
        result.TotalNumber = query.TotalNumber;
        result.TotalPage = query.TotalPage;
        return Ok(result);
    }

    #endregion

    #region 疾病分类对应疾病(用于统计报表) MapDiseaseClsDisease
    /// <summary>
    /// 获取科室疾病用于疾病分类对应
    /// </summary>
    /// <param name="diseaseClsCode">疾病分类代码</param>
    /// <returns></returns>
    [HttpPost("GetDeptDisease4DiseaseCls")]
    [ProducesResponseType(typeof(List<ClsDisease>), 200)]
    public IActionResult GetDeptDisease4DiseaseCls(string diseaseClsCode)
    {
        result.ReturnData = _diseaseMgtService.GetDeptDisease4DiseaseCls(diseaseClsCode);
        return Ok(result);
    }

    /// <summary>
    /// 获取疾病分类对应疾病
    /// </summary>
    /// <param name="diseaseClsCode">疾病分类代码</param>
    /// <returns></returns>
    [HttpPost("Query_MapDiseaseClsDisease")]
    [ProducesResponseType(typeof(List<ClsDisease>), 200)]
    public IActionResult Query_MapDiseaseClsDisease([FromQuery] string diseaseClsCode)
    {
        result.ReturnData = _diseaseMgtService.QueryMapDiseaseClsDisease(diseaseClsCode);
        return Ok(result);
    }

    /// <summary>
    /// /CD_MapDiseaseClsDisease/Create 新增疾病分类对应疾病
    /// /CD_MapDiseaseClsDisease/Delete 删除疾病分类对应疾病
    /// </summary>
    /// <param name="type">操作</param>
    /// <param name="mapMapDiseaseClsDiseases">疾病分类对应疾病</param>
    /// <returns></returns>
    [HttpPost("CD_MapDiseaseClsDisease/{type}")]
    public IActionResult CD_MapDiseaseClsDisease([FromRoute] string type, [FromBody] List<MapDiseaseClsDisease> mapMapDiseaseClsDiseases)
    {
        string msg = string.Empty;
        //mapMapDiseaseClsDiseases.BatchUpdate(x=>x.HospCode=_httpContextUser.HospCode);
        switch (type.ToLower())
        {
            case "create":
                result.Success = _diseaseMgtService.CreateMapDiseaseClsDisease(mapMapDiseaseClsDiseases, ref msg);
                break;
            case "delete":
                result.Success = _diseaseMgtService.DeleteMapDiseaseClsDisease(mapMapDiseaseClsDiseases, ref msg);
                break;
            default:
                return new BadRequestResult();
        }

        result.ReturnMsg = msg;
        return Ok(result);
    }
    #endregion

    #region 疾病-重大阳性
    /// <summary>
    /// 保存疾病-重大阳性对应关系
    /// </summary>
    /// <param name="data"></param>
    /// <returns></returns>
    [HttpPost("BatchSaveMapDiseaseMajorPositive")]
    [ProducesResponseType(typeof(bool), 200)]
    [UnitOfWork]
    public IActionResult BatchSaveMapDiseaseMajorPositive(List<MapDiseaseMajorPositive> data)
    {
        result.ReturnData = _diseaseMgtService.BatchSaveMapDiseaseMajorPositive(data);

        return Ok(result);
    }

    /// <summary>
    /// 疾病-重大阳性对应关系列表查询
    /// </summary>
    /// <param name="query"></param>
    /// <returns></returns>
    [HttpPost("GetMapDiseaseMajorPositives")]
    [ProducesResponseType(typeof(List<MapDiseaseMajorPositiveDto>), 200)]
    public IActionResult GetMapDiseaseMajorPositives(MapDiseaseMajorPositiveQuery query)
    {
        int totalNumber = 0;
        int totalPage = 0;
        result.ReturnData = _diseaseMgtService.GetMapDiseaseMajorPositives(query, ref totalNumber, ref totalPage);
        result.TotalNumber = totalNumber;
        result.TotalPage = totalPage;
        return Ok(result);
    }

    /// <summary>
    /// 删除疾病-重大阳性对应关系
    /// </summary>
    /// <param name="data"></param>
    /// <returns></returns>
    [HttpPost("BatchRemoveMapKeywordMajorPositive")]
    [ProducesResponseType(typeof(bool), 200)]
    [UnitOfWork]
    public IActionResult BatchRemoveMapKeywordMajorPositive(List<MapDiseaseMajorPositive> data)
    {
        result.ReturnData = _diseaseMgtService.BatchRemoveMapKeywordMajorPositive(data);

        return Ok(result);
    }

    /// <summary>
    /// 新增疾病统计年龄段
    /// </summary>
    /// <param name="codeDiseaseStatAgeRange"></param>
    /// <returns></returns>
    [HttpPost("CreateCodeDiseaseStatAgeRange")]
    [ProducesResponseType(typeof(bool), 200)]
    public IActionResult CreateCodeDiseaseStatAgeRange(CodeDiseaseStatAgeRange codeDiseaseStatAgeRange)
    {
        result.Success = _diseaseMgtService.CreateCodeDiseaseStatAgeRange(codeDiseaseStatAgeRange);
        return Ok(result);
    }

    /// <summary>
    /// 更新疾病统计年龄段
    /// </summary>
    /// <param name="codeDiseaseStatAgeRange"></param>
    /// <returns></returns>
    [HttpPost("UpdateCodeDiseaseStatAgeRange")]
    [ProducesResponseType(typeof(bool), 200)]
    public IActionResult UpdateCodeDiseaseStatAgeRange(CodeDiseaseStatAgeRange codeDiseaseStatAgeRange)
    {
        result.Success = _diseaseMgtService.UpdateCodeDiseaseStatAgeRange(codeDiseaseStatAgeRange);
        return Ok(result);
    }
    /// <summary>
    /// 删除疾病统计年龄段
    /// </summary>
    /// <param name="codeDiseaseStatAgeRanges"></param>
    /// <returns></returns>
    [HttpPost("DeleteCodeDiseaseStatAgeRange")]
    [ProducesResponseType(typeof(bool), 200)]
    public IActionResult DeleteCodeDiseaseStatAgeRange(List<CodeDiseaseStatAgeRange> codeDiseaseStatAgeRanges)
    {
        result.Success = _diseaseMgtService.DeleteCodeDiseaseStatAgeRange(codeDiseaseStatAgeRanges);
        return Ok(result);
    }

    /// <summary>
    /// 获取疾病统计年龄段
    /// </summary>
    /// <returns></returns>
    /// [HttpPost("ReadCodeDiseaseStatAgeRange")]
    [HttpPost("ReadCodeDiseaseStatAgeRange")]
    [ProducesResponseType(typeof(List<CodeDiseaseStatAgeRange>), 200)]
    public IActionResult ReadCodeDiseaseStatAgeRange()
    {
        result.Success = true;
        result.ReturnData = _diseaseMgtService.ReadCodeDiseaseStatAgeRange();
        return Ok(result);
    }
    #endregion
}