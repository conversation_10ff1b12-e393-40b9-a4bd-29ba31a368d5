﻿global using Microsoft.AspNetCore.Mvc;
global using Microsoft.Extensions.DependencyInjection;
global using Peis.External.Hardware.Service.Models;
global using Peis.Model;
global using Peis.Model.DataBaseEntity;
global using Peis.Repository;
global using Peis.Service;
global using Peis.Service.IService;
global using Peis.Utility.CustomAttribute;
global using Peis.Utility.DTO;
global using Peis.Utility.Helper;
global using System.Diagnostics.CodeAnalysis;
global using System.Reflection;
global using System.Text.Json;
global using System.Text.Json.Serialization;
global using Volo.Abp;
global using Volo.Abp.Modularity;
