﻿using Peis.Model.DataBaseEntity.External;

namespace Peis.External.Platform.Service.Repository.IRepository
{
    public interface IShenShanRegisterRepository
    {
        /// <summary>
        /// 获取订单的组合
        /// </summary>
        /// <param name="regNo">体检号</param>
        /// <param name="regCombIds">登记组合Id</param>
        /// <returns></returns> 
        ISugarQueryable<PeRegister, PeRegisterComb, CodeItemComb, MapCodeItemCombHisOrderItem> ReadRegCombs(string regNo, params long[] regCombIds);

        /// <summary>
        /// 体检检验申请查询
        /// </summary>
        /// <param name="regNo"></param>
        /// <returns></returns>
        ISugarQueryable<PeRegister, ExtHisApplyComb, PeSample> QueryLisApplyInfo(string regNo);

        /// <summary>
        /// 体检检查申请查询
        /// </summary>
        /// <param name="regNo"></param>
        /// <returns></returns>
        ISugarQueryable<PeRegister, ExtHisApplyComb> QueryExamApplyInfo(string regNo);

        /// <summary>
        /// 获取组合列表
        /// </summary>
        /// <param name="regNo"></param>
        /// <returns></returns>
        ISugarQueryable<PeRecordComb> GetPeRecordComb(string regNo);

        /// <summary>
        /// 获取诊疗卡号
        /// </summary>
        /// <param name="regNo"></param>
        /// <returns></returns>
        string GetHisCard(string regNo);

        /// <summary>
        /// 获取支付金额
        /// </summary>
        /// <param name="regNo"></param>
        /// <returns></returns>
        decimal GetHisPrice(string regNo);

        /// <summary>
        /// 获取采血机条码信息
        /// </summary>
        /// <param name="regNo"></param>
        /// <returns></returns>
        ISugarQueryable<PeRegister, PeRegisterComb, PeSample, CodeBarcodeType, CodeSample> ReadSampleDetailWithSpecimenInfo(string regNo);

        /// <summary>
        /// 获取条码列表
        /// </summary>
        /// <param name="regNo"></param>
        /// <returns></returns>
        ISugarQueryable<PeSample> GetSampleInfo(string regNo);

        /// <summary>
        /// 获取收费日结信息
        /// </summary>
        /// <returns></returns>
        ISugarQueryable<ExtHisOrderIndex, ExtHisBill> ReadHisBillList();

        /// <summary>
        /// 获取个人收费日结组合数据
        /// </summary>
        /// <param name="beginTime"></param>
        /// <param name="endTime"></param>
        /// <returns></returns>
        ISugarQueryable<ExtHisOrderIndex, ExtHisBill,ExtHisBillComb, PeRegisterCluster> GetPersonalSettlementComb(DateTime beginTime, DateTime endTime);

        ISugarQueryable<ExtHisOrderIndex, PeRegister> GetPlaformNoticeQueue();
    }
}
