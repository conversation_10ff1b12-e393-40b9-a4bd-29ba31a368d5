﻿using Microsoft.AspNetCore.Mvc;
using Peis.Service.IService.ExternalSystem;

namespace Peis.External.Platform.Service.Controllers
{
    /// <summary>
    /// 仪器接口
    /// </summary>
    [Route("api/<PERSON><PERSON>/[controller]")]
    [ApiController]
    public class MachineController : BaseApiController
    {
        private readonly IExternalSystemMachineService _machineService;

        public MachineController(IExternalSystemMachineService machineService)
        {
            _machineService = machineService;
        }

        [HttpPost("GetSampleDataToMachine")]
        public IActionResult GetSampleDataToMachine([FromQuery] string regNo)
        {
            string msg;
            var sampleData = _machineService.GetSampleInfoToTxt(regNo, out msg);
            var data = new { RegNo = regNo, SampleData = sampleData };
            result.ReturnData = data.ToJson();
            result.ReturnMsg = msg;
            return Ok(result);
        }
    }
}
