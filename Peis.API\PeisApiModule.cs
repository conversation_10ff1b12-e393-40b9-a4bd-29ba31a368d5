﻿using Peis.Quartz.UI;
using Peis.Quartz.UI.CustomAttributes;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.HttpOverrides;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Microsoft.OpenApi.Models;
using Peis.External.Hardware.Service;
using Peis.External.Platform.Service;
using Peis.Model;
using Peis.Repository.Repository.Filter;
using Peis.Service;
using Peis.Utility.CustomAttribute;
using Peis.Utility.DTO;
using Peis.Utility.Helper;
using Peis.Utility.JsonConverter;
using Peis.Utility.Middleware;
using Peis.Utility.PeIM.Extensions;
using Peis.Utility.PeUser;
using Peis.Utility.SqlSugarExtension;
using Serilog;
using SqlSugar;
using Swashbuckle.AspNetCore.Filters;
using Swashbuckle.AspNetCore.SwaggerGen;
using System;
using System.IO;
using System.Linq;
using System.Reflection;
using System.Threading.Tasks;
using Volo.Abp;
using Volo.Abp.AspNetCore;
using Volo.Abp.Autofac;
using Volo.Abp.Modularity;
using Peis.Repository.Cache;

namespace Peis.API
{
    [DependsOn(typeof(AbpAspNetCoreModule))]
    [DependsOn(typeof(AbpAutofacModule))]
    [DependsOn(typeof(PeisServiceModule))]
    [DependsOnPeis(typeof(PeisExternalPlatformModule))]
    [DependsOnPeis(typeof(PeisExternalHardwareModule))]
    [DependsOnPeisQuartzUI(typeof(QuartzUIModule), DbType = DbType.SqlServer, ConnectionStringKey = "ConnectionString:System")]
    public class PeisApiModule : AbpModule
    {
        public override void ConfigureServices(ServiceConfigurationContext context)
        {
            var services = context.Services;

            services.Configure<ForwardedHeadersOptions>(options =>
            {
                // 信任所有代理地址
                options.KnownNetworks.Clear();
                options.KnownProxies.Clear();

                options.ForwardedHeaders = ForwardedHeaders.XForwardedFor | ForwardedHeaders.XForwardedProto;
            });

            //缓存
            services.AddMemoryCache();
            //使用session
            services.AddSession();

            #region 配置认证服务jwt
            //配置认证服务
            services.AddAuthentication(JwtBearerDefaults.AuthenticationScheme)
            .AddJwtBearer(opt =>
            {
                opt.TokenValidationParameters = JwtHelper.TokenValidationParameters;
                opt.Events = new JwtBearerEvents
                {
                    OnMessageReceived = context =>
                    {
                        var accessToken = context.Request.Query["access_token"];
                        if (!string.IsNullOrEmpty(accessToken) && context.HttpContext.Request.Path == "/socket")
                        {
                            context.Token = accessToken;
                        }
                        return Task.CompletedTask;
                    }
                };
            });
            #endregion

            #region 配置跨域
            services.AddCors(options =>
                    options.AddPolicy("Cors",
                        cpBuilder =>
                        cpBuilder.SetIsOriginAllowed(t => true)
                        .AllowAnyMethod()
                        .AllowAnyHeader()
                        .AllowCredentials()
                    )
                );
            #endregion

            #region 配置swagger操作文档
            services.AddSwaggerGen(c =>
            {
                Log.Information("AssemblyName:{Name}", Assembly.GetEntryAssembly().GetName().Name);
                //非必要不要用,会把完整路径暴露出去
                //c.CustomSchemaIds(x => x.FullName);
                c.SwaggerDoc("v1", new OpenApiInfo { Title = "Peis API", Version = "v1" });
                var entryXmlPath = Path.Combine(AppContext.BaseDirectory, $"{Assembly.GetEntryAssembly().GetName().Name}.xml");
                if (File.Exists(entryXmlPath))
                    c.IncludeXmlComments(entryXmlPath, true);

                var modelXmlPath = Path.Combine(AppContext.BaseDirectory, $"Peis.Model.xml");
                if (File.Exists(modelXmlPath))
                    c.IncludeXmlComments(modelXmlPath, true);

                // 开启加权小锁
                c.OperationFilter<AddResponseHeadersFilter>();
                c.OperationFilter<AppendAuthorizeToSummaryOperationFilter>();

                // 在header中添加token，传递到后台
                c.OperationFilter<SecurityRequirementsOperationFilter>();

                // 必须是 oauth2
                c.AddSecurityDefinition("oauth2", new OpenApiSecurityScheme
                {
                    Description = "JWT授权(数据将在请求头中进行传输) 直接在下框中输入Bearer {token}（注意两者之间是一个空格）\"",
                    Name        = "Authorization",//jwt默认的参数名称
                    In          = ParameterLocation.Header,//jwt默认存放Authorization信息的位置(请求头中)
                    Type        = SecuritySchemeType.ApiKey
                });

                // 指定分组加载显示文档内容
                c.DocInclusionPredicate((docName, apiDesc) =>
                {
                    if (docName.Equals("v1", StringComparison.OrdinalIgnoreCase))
                        return apiDesc.GroupName.IsNullOrEmpty() || apiDesc.GroupName.Equals("v1", StringComparison.OrdinalIgnoreCase);
                    else if(docName.Equals(apiDesc.GroupName, StringComparison.OrdinalIgnoreCase))
                        return true;
                    else return false;
                });
            });
            #endregion

            #region HttpClientFactory
            services.AddHttpClient();
            services.AddTransient<IHttpClientHelper, HttpClientHelper>();
            #endregion

            #region 配置数据库
            StaticConfig.SplitTableGetTablesFunc = SplitTableInfoCache.GetTabls;
            services.AddScoped<ISqlSugarClient>(sp =>
            {
                var _log = sp.GetRequiredService<ILogger<ISqlSugarClient>>();
                var _db  = new SqlSugarClient(new ConnectionConfig()
                {
                    ConnectionString      = Appsettings.GetSectionValue("ConnectionString:Entities"),
                    DbType                = DbType.SqlServer,
                    IsAutoCloseConnection = true,//开启自动释放模式
                    InitKeyType           = InitKeyType.Attribute,//从特性读取主键和自增列信息
                    MoreSettings          = new ConnMoreSettings()
                    {
                        IsWithNoLockQuery         = true, // sqlserver-查询启用nolock
                        DisableWithNoLockWithTran = true // sqlserver-事务内，禁用nolock
                    }
                });

                _db.CurrentConnectionConfig.ConfigureExternalServices = new() { SplitTableService = new yyyySplitTableService() };

                //_db.Aop.OnLogExecuting = (sql, pars) =>
                //{
                //    var parsStr = pars.Length > 0 ? _db.Utilities.SerializeObject(pars.ToDictionary(it => it.ParameterName, it => it.Value)) : "-";
                //    _log.LogDebug("SQL Executing {SQL} {Param}", sql, parsStr);
                //};
                _db.Aop.OnLogExecuted = (sql, pars) =>
                {
                    var parsStr  = pars.Length > 0 ? _db.Utilities.SerializeObject(pars.ToDictionary(it => it.ParameterName, it => it.Value)) : "-";
                    var execTime = _db.Ado.SqlExecutionTime.TotalMilliseconds;
                    _log.LogDebug("SQL Executed {SQL} {Param} {ExecTime}ms", sql, parsStr, execTime);
                };

                return _db;
            });
            #endregion

            services.AddSimpleCache();

            services.AddControllersWithViews(opt =>
            {
                // 注册过滤器
                opt.Filters.Add(typeof(UnitOfWorkFilter));
            })
            .AddJsonOptions(options =>
            {
                #region JsonConverter
                options.JsonSerializerOptions.Converters.Add(new DateTimeConverter());
                options.JsonSerializerOptions.Converters.Add(new DateTimeNullableConverter());
                options.JsonSerializerOptions.Converters.Add(new DateTimeOffsetConverter());
                options.JsonSerializerOptions.Converters.Add(new DateTimeOffsetNullableConverter());
                options.JsonSerializerOptions.Converters.Add(new IntNullableConverter());
                options.JsonSerializerOptions.Converters.Add(new LongConverter());
                options.JsonSerializerOptions.Converters.Add(new LongNullableConverter());
                #endregion
            });
            services.AddScoped<ShieldApiResourceFilter>();
            services.AddPeIM();
            services.AddScoped<IHttpContextUser, HttpContextUser>();

            #region 模型绑定特性验证，自定义返回格式
            services.Configure<ApiBehaviorOptions>(options =>
            {
                options.InvalidModelStateResponseFactory = actionContext =>
                {
                    var convertErr = "The JSON value could not be converted to"; // 临时处理转换问题
                    var errors = actionContext.ModelState
                    .Where(e => e.Value.Errors.Any())
                    .Select(e => $"{e.Key.RemovePreFix("$.")}：{string.Join("、", e.Value.Errors.Select(x => x.ErrorMessage.Contains(convertErr, StringComparison.OrdinalIgnoreCase) ? "数据格式不正确！" : x.ErrorMessage))}");
                    // 属性：错误信息
                    var str = "数据验证不通过，请检查如下内容：<br/> " + string.Join("<br/>", errors);
                    var result = new ResponsResult { Success = false, ReturnMsg = str };
                    return new BadRequestObjectResult(result);
                };
            }); 
            #endregion
        }

        public override void OnApplicationInitialization(ApplicationInitializationContext context)
        {
            var app = context.GetApplicationBuilder();
            var env = context.GetEnvironment();

            // 处理转发的请求后获取原始客户端的IP地址和协议信息（如Nginx）
            app.UseForwardedHeaders();

            if (env.IsDevelopment())
                app.UseDeveloperExceptionPage();

            // Https重定向
            //app.UseHttpsRedirection();

            // 静态文件
            app.UseStaticFiles(new StaticFileOptions
            {
                OnPrepareResponse = c => c.Context.Response.Headers.Add("Access-Control-Allow-Origin", "*")
            });

            // 客户端缓存安全策略
            app.UseCookiePolicy();

            // Swagger
            if (Appsettings.GetSectionBooleanValue("AppSettings:SwaggerIsEnabled"))
            {
                app.UseSwagger();
                app.UseSwaggerUI(option =>
                {
                    option.RoutePrefix = "swagger";
                    option.DocumentTitle = "sparktodo API";

                    #region 动态配置 SwaggerEndpoint 多个分组内容
                    SwaggerGenOptions swaggerGenOptions;
                    using (var scope = app.ApplicationServices.CreateScope())
                    {
                        swaggerGenOptions = scope.ServiceProvider.GetRequiredService<IOptionsSnapshot<SwaggerGenOptions>>().Value;
                    }
                    foreach (var item in swaggerGenOptions.SwaggerGeneratorOptions.SwaggerDocs.Reverse())
                    {
                        option.SwaggerEndpoint($"/swagger/{item.Key}/swagger.json", $"{item.Value.Title} {item.Value.Version}");
                    } 
                    #endregion
                });
            }

            app.UseRouting();

            // 跨域
            app.UseCors("Cors");

            // 认证授权
            app.UseAuthentication();
            app.UseAuthorization();

            // 会话
            app.UseSession();

            // 服务端缓存
            app.UseResponseCaching();

            // 解密请求
            if (Appsettings.GetSectionBooleanValue("Middleware:UseRequestDecrypt"))
                app.UseRequestDecryptMiddleware(opt =>
                {
                    opt.AddFilteredPath("/api");
                    opt.AddIgnoredPath("/api/DaoJian"); // 导检不加密请求
                });

            // Serilog日志上下文
            app.UseRequestLogContextMiddleware();

            // 请求响应日志
            if (Appsettings.GetSectionBooleanValue("Middleware:UseReqResLogger"))
                app.UseReqResLoggerMiddleware("/api");

            // 异常处理
            if (Appsettings.GetSectionBooleanValue("Middleware:UseExceptionHandling"))
                app.UseExceptionHandlingMiddleware();

            // 注册socket
            app.UesPeIM("/socket");

            app.UseEndpoints(endpoints =>
            {
                endpoints.MapControllers();
                endpoints.MapControllerRoute(
                    name: "default",
                    pattern: "{controller=Home}/{action=Index}/{id?}");
            });
        }

        /// <summary>
        /// 应用初始完后回调
        /// </summary>
        /// <param name="context"></param>
        public override void OnPostApplicationInitialization(ApplicationInitializationContext context)
        {
            base.OnPostApplicationInitialization(context);

            // 注册Aspose产品，需要新增到此处使用例子，否则破解不生效
            Task.Run(() =>
            {
                HookManager.ShowHookDetails(false);
                HookManager.StartHook();
                var word = new Aspose.Words.Document(); word = null;
            });
        }

        public override void OnApplicationShutdown(ApplicationShutdownContext context)
        {
            var _logger = context.ServiceProvider.GetService<ILogger<PeisApiModule>>();
            _logger.LogInformation("OnApplicationShutdown Start.");
            base.OnApplicationShutdown(context);
            // iis程序池回收后，应用也被回收，导致quartz任务被释放，无法定时执行（进程&内存被释放了）
            // 回收后，执行一次调用请求，触发应用启动
            var _httpClient = context.ServiceProvider.GetService<IHttpClientHelper>();
            var urls = Appsettings.GetSectionArraryValue("AppSettings:RunUrls");
            foreach (var item in urls)
            {
                var url = $"{item}/api/Test/Ping";
                url = url.Replace("*", "localhost");
                _httpClient.GetAsync(url);
                _logger.LogInformation($"try request {url}");
            }

            _logger.LogInformation("OnApplicationShutdown Finish.");
        }
    }
}
