﻿namespace Peis.Utility.Cache;

/*
public class CacheTool
{
    //protected MemoryCacheAdapter _cache;

    //public CacheTool(MemoryCacheAdapter cache)
    //{
    //    _cache = cache;
    //}

    //public T Get<T>(string key)
    //{
    //    return _cache.Get<T>(key);
    //}

    //public object GetValue(string key)
    //{
    //    return _cache.Get<object>(key);
    //}

    protected AppCache _cache;

    public CacheTool(AppCache cache)
    {
        _cache = cache;
    }

    public CacheData GetData(string key)
    {
        return _cache.Get(key);
    }

    public object GetValue(string key)
    {
        var data = GetData(key);
        return data?.Value;
    }
}
*/