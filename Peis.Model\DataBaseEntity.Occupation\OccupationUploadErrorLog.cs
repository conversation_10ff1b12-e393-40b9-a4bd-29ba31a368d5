﻿using System.Text.Json.Serialization;

namespace Peis.Model.DataBaseEntity.Occupation
{
    /// <summary>
    /// 职业病上传错误信息
    /// </summary>
    [SugarTable]
    public class OccupationUploadErrorLog
    {
        /// <summary>
        /// 体检号
        /// </summary>
        [SugarColumn(Length =12,IsNullable =false, IsPrimaryKey = true)]
        public string RegNo { get; set; }

        /// <summary>
        /// 上传时间
        /// </summary>
        [SugarColumn(IsNullable = false)]
        public DateTime UploadTime { get; set; }

        /// <summary>
        /// 错误信息
        /// </summary>
        [JsonIgnore]
        [SugarColumn(IsJson = true ,ColumnDataType = StaticConfig.CodeFirst_BigString,IsNullable =false)]
        public string[] Errors { get; set; }
        /// <summary>
        /// 错误信息（前端显示用）
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public string ErrorString => string.Join("\n", Errors);
    }
}
