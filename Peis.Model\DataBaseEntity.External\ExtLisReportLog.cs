﻿namespace Peis.Model.DataBaseEntity.External
{
    [SugarTable("ExtLisReportLog", "第三方检验报告日志表")]
    [SugarIndex("index_RegNo_ExtLisReportLog", nameof(RegNo), OrderByType.Asc)]
    public class ExtLisReportLog
    {
        /// <summary>
        /// 条码号
        /// </summary>
        [SugarColumn(IsNullable = false, IsPrimaryKey = true, Length = 20)]
        public string SampleNo { get; set; }

        /// <summary>
        /// 体检号
        /// </summary>
        [SugarColumn(IsNullable = false, Length = 12)]
        public string RegNo { get; set; }

        /// <summary>
        /// 组合名
        /// </summary>
        [SugarColumn(IsNullable = false, Length = 255)]
        public string CombName { get; set; }

        /// <summary>
        /// 报文
        /// </summary>
        [SugarColumn(IsNullable = false, ColumnDataType = StaticConfig.CodeFirst_BigString)]
        public string ReportXML { get; set; }

        /// <summary>
        /// 成功标识
        /// </summary>
        [SugarColumn(IsNullable = false)]
        public bool Success { get; set; }

        /// <summary>
        /// 错误消息 
        /// </summary>
        [SugarColumn(IsNullable = true, ColumnDataType = StaticConfig.CodeFirst_BigString)]
        //[SugarColumn(IsIgnore = true)]
        public string ErrMsg { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        [SugarColumn(IsNullable = false)]
        public DateTime CreateTime { get; set; }

        /// <summary>
        /// 更新时间
        /// </summary>
        [SugarColumn(IsNullable = false)]
        public DateTime LastTime { get; set; }
    }
}
