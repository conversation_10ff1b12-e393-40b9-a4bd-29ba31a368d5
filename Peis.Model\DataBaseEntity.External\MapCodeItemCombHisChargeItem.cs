﻿using Peis.Model.DTO.External.DataMaintenances;

namespace Peis.Model.DataBaseEntity.External;

///<summary>
/// 基础数据：体检组合与His收费项目/药品关系中间表
///</summary>
[SugarTable(nameof(MapCodeItemCombHisChargeItem), TableDescription = "体检组合与His收费/药品项目关系中间表")]
[SugarIndex("index_MapHisChargeItem_CombNameHisItemIdName", nameof(CombName), OrderByType.Asc, nameof(HisChargeItemId), OrderByType.Asc, nameof(HisChargeItemCNName), OrderByType.Asc)]
public class MapCodeItemCombHisChargeItem
{
    /// <summary>
    /// 组合代码
    /// </summary>        
    [SugarColumn(IsPrimaryKey = true, Length = 8)]
    public string CombCode { get; set; }

    /// <summary>
    /// His-收费/药品项目代码
    /// </summary>        
    [SugarColumn(IsPrimaryKey = true, Length = 64)]
    public string HisChargeItemCode { get; set; }

    /// <summary>
    /// 类型：0-检查，1-药品
    /// </summary>
    [SugarColumn(IsPrimaryKey = true)]
    public EnumCombHisItemType ItemType { get; set; }

    /// <summary>
    /// 组合名称
    /// </summary>        
    [SugarColumn(IsNullable = true, ColumnDataType = "nvarchar(256)")]
    public string CombName { get; set; }

    /// <summary>
    /// His-收费/药品项目ID
    /// </summary>        
    [SugarColumn(Length = 64)]
    public string HisChargeItemId { get; set; }

    /// <summary>
    /// His-收费/药品项目中文名称
    /// </summary>        
    [SugarColumn(IsNullable = true, ColumnDataType = "nvarchar(256)")]
    public string HisChargeItemCNName { get; set; }

    /// <summary>
    /// His-收费项目价格
    /// </summary>        
    [SugarColumn(Length = 10, DecimalDigits = 2)]
    public decimal HisChargeItemPrice { get; set; }

    /// <summary>
    /// His项目/药品数量
    /// </summary>        
    [SugarColumn(Length = 10, DecimalDigits = 2)]
    public decimal HisChargeItemCount { get; set; }

    /// <summary>
    /// His项目/药品折扣
    /// </summary>        
    [SugarColumn(IsNullable = false, Length = 8, DecimalDigits = 2)]
    public decimal HisChargeItemDiscount { get; set; }

    /// <summary>
    /// 创建人Code
    /// </summary>        
    [SugarColumn(IsNullable = false, Length = 10, IsOnlyIgnoreUpdate = true)]
    public string CreatorCode { get; set; }

    /// <summary>
    /// 创建人名称
    /// </summary>        
    [SugarColumn(IsNullable = false, Length = 50, IsOnlyIgnoreUpdate = true)]
    public string CreatorName { get; set; }

    /// <summary>
    /// 创建时间
    /// </summary> 
    [SugarColumn(IsNullable = false, IsOnlyIgnoreUpdate = true)]
    public DateTime CreatedTime { get; set; }

    /// <summary>
    /// 修改人Code
    /// </summary>        
    [SugarColumn(IsNullable = true, Length = 10)]
    public string UpdatedCode { get; set; }

    /// <summary>
    /// 修改人名称
    /// </summary>        
    [SugarColumn(IsNullable = true, Length = 50)]
    public string UpdatedName { get; set; }

    /// <summary>
    /// 修改时间
    /// </summary>        
    [SugarColumn(IsNullable = true)]
    public DateTime? UpdatedTime { get; set; }

    /// <summary>
    /// 备注
    /// </summary>        
    [SugarColumn(IsNullable = true, ColumnDataType = "nvarchar(200)")]
    public string Remark { get; set; }

    /// <summary>
    /// 是否医嘱项目 0-否，1-是
    /// </summary>        
    [SugarColumn(IsNullable = false)]
    public bool IsOrder { get; set; }

    #region Ext
    public void SetCreate([NotNull] string creatorCode, [NotNull] string creatorName)
    {
        CreatedTime = DateTime.Now;
        CreatorCode = creatorCode;
        CreatorName = creatorName;
    }

    public void SetUpdate([NotNull] string updatedCode, [NotNull] string updatedName)
    {
        UpdatedTime = DateTime.Now;
        UpdatedCode = updatedCode;
        UpdatedName = updatedName;
    }

    public void SetCodeItemComb([NotNull] CodeItemComb comb)
    {
        CombCode = comb.CombCode;
        CombName = comb.CombName;
    }

    public void SetChargeItem([NotNull] CodeHisChargeItem chargeItem)
    {
        HisChargeItemId = chargeItem.ChargeItemId;
        HisChargeItemCode = chargeItem.ChargeItemCode;
        HisChargeItemCNName = chargeItem.ChageItemCNName;
    }

    public void SetDrugItem([NotNull] CodeHisDrugItem drugItem)
    {
        HisChargeItemId = drugItem.DrugId;
        HisChargeItemCode = drugItem.DrugCode;
        HisChargeItemCNName = drugItem.DrugCNName;
    }
    #endregion
}