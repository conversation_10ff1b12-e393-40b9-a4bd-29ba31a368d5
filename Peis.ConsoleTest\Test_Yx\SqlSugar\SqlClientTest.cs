﻿using Peis.Utility.Helper;
using Peis.Utility.SqlSugarExtension;
using SqlSugar;
using System;
using System.Linq;

namespace Peis.ConsoleTest.Test_Yx.SqlSugar
{
    internal class SqlClientTest
    {
        public static SqlSugarScope DB()
        {
            var _db = new SqlSugarScope(new ConnectionConfig()
            {
                ConnectionString = Appsettings.GetSectionValue("ConnectionString:Entities"),
                DbType = DbType.SqlServer,
                IsAutoCloseConnection = true,                  //开启自动释放模式
                InitKeyType = InitKeyType.Attribute, //从特性读取主键和自增列信息
            });

            _db.CurrentConnectionConfig.ConfigureExternalServices = new ConfigureExternalServices()
            {
                SplitTableService = new yyyySplitTableService()
            };

            //Print sql
            _db.Aop.OnLogExecuting = (sql, pars) =>
            {
                Console.WriteLine(sql + "\r\n" + _db.Utilities.SerializeObject(pars.ToDictionary(it => it.ParameterName, it => it.Value)));
                Console.WriteLine();
            };

            return _db;
        }
    }
}
