﻿using SqlSugar;

namespace Peis.Model.DataBaseEntity
{
    ///<summary>
    ///标本信息表
    ///</summary>
    [SugarTable("CodeSample")]
    public class CodeSample
    {
        /// <summary>
        /// 标本代码
        /// </summary>        
        [SugarColumn(IsNullable = false, IsPrimaryKey = true, Length = 10)]
        public string SampCode { get; set; }

        /// <summary>
        /// 标本名称
        /// </summary>        
        [SugarColumn(IsNullable = false, Length = 20)]
        public string SampName { get; set; }
    }
}