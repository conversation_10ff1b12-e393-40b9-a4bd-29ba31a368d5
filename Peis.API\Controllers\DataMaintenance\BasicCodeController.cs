﻿using AutoMapper;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Peis.Model.DataBaseEntity;
using Peis.Model.DTO;
using Peis.Model.DTO.BasicCode;
using Peis.Model.Other.Input;
using Peis.Model.Other.PeEnum;
using Peis.Repository.Repository.TransactionAttribute;
using Peis.Service.IService;
using Peis.Service.IService.Helper;
using Peis.Utility.PeUser;
using System.Collections.Generic;

namespace Peis.API.Controllers.DataMaintenance
{
    /// <summary>
    /// 基础代码维护
    /// </summary>
    [Route("api/[controller]")]
    [ApiController]
    public class BasicCodeController : BaseApiController
    {
        private readonly IBasicCodeService _basicCodeService;
        private readonly IHttpContextUser _httpContextUser;
        private readonly INoGeneration _noGeneration;
        private readonly IMapper _mapper;

        public BasicCodeController(
            IBasicCodeService basicCodeService,
            IHttpContextUser httpContextUser,
            INoGeneration noGeneration,
            IMapper mapper)
        {
            _basicCodeService = basicCodeService;
            _httpContextUser = httpContextUser;
            _noGeneration = noGeneration;
            _mapper = mapper;
        }

        #region 公用类代码

        #region 科室信息 CodeDepartment
        /// <summary>
        /// /CU_CodeDepartment/Create 新增科室信息
        /// /CU_CodeDepartment/Update 更新科室信息
        /// </summary>
        /// <param name="type"></param>
        /// <param name="codeDepart"></param>
        /// <returns></returns>
        [HttpPost("CU_CodeDepartment/{type}")]
        [UnitOfWork]
        public IActionResult CU_CodeDepartment([FromRoute] string type, [FromBody] CodeDepartment codeDepart)
        {
            string msg = string.Empty;
            codeDepart.HospCode = _httpContextUser.HospCode;

            switch (type.ToLower())
            {
                case "create":
                    codeDepart.DeptCode = _noGeneration.NextDeptNo(_httpContextUser.HospCode)[0];
                    result.Success = _basicCodeService.CreateCodeDepartment(codeDepart);
                    break;
                case "update":
                    result.Success = _basicCodeService.UpdateCodeDepartment(codeDepart);
                    break;
                default:
                    return new BadRequestResult();
            }
            result.ReturnMsg = msg;

            return Ok(result);
        }

        /// <summary>
        /// /RD_CodeDepartment/Read   获取科室信息
        /// /RD_CodeDepartment/Delete 删除科室信息
        /// </summary>
        /// <param name="type"></param>
        /// <param name="deptCode"></param>
        /// <returns></returns>
        [HttpPost("RD_CodeDepartment/{type}")]
        public IActionResult RD_CodeDepartment([FromRoute] string type, [FromBody] string[] deptCode)
        {
            string msg = string.Empty;

            switch (type.ToLower())
            {
                case "read":
                    result.ReturnData = _basicCodeService.ReadCodeDepartment();
                    break;
                case "delete":
                    result.Success = _basicCodeService.DeleteCodeDepartment(deptCode);
                    break;
                default:
                    return new BadRequestResult();
            }

            return Ok(result);
        }

        /// <summary>
        /// 根据院区获取科室信息
        /// </summary>
        /// <param name="hospCode">["A","B"]</param>
        /// <returns></returns>
        [HttpPost("ReadCodeDepartmentByHosp")]
        public IActionResult ReadCodeDepartmentByHosp([FromBody] string[] hospCode)
        {
            result.ReturnData = _basicCodeService.ReadCodeDepartmentByHosp(hospCode);
            return Ok(result);
        }
        #endregion

        #region 科室常用词(医生工作站常用词) CodeDeptWord
        /// <summary>
        /// CreateCodeDeptWord 新增科室常用词
        /// </summary>
        /// <param name="deptWord"></param>
        /// <returns></returns>
        [HttpPost("CreateCodeDeptWord")]
        [UnitOfWork]
        public IActionResult CreateCodeDeptWord([FromBody] CodeDeptWord deptWord)
        {
            string msg = string.Empty;
            //deptWord.HospCode = _httpContextUser.HospCode;
            result.Success = _basicCodeService.CreateCodeDeptWord(deptWord);
            result.ReturnMsg = msg;

            return Ok(result);
        }

        /// <summary>
        /// DeleteCodeDeptWord 删除科室常用词
        /// </summary>
        /// <param name="deptWord"></param>
        /// <returns></returns>
        [HttpPost("DeleteCodeDeptWord")]
        [UnitOfWork]
        public IActionResult DeleteCodeDeptWord([FromBody] List<CodeDeptWord> deptWord)
        {
            string msg = string.Empty;
            result.Success = _basicCodeService.DeleteCodeDeptWord(deptWord);
            result.ReturnMsg = msg;

            return Ok(result);
        }

        /// <summary>
        /// ReadCodeDeptWord  获取科室常用词
        /// </summary>
        /// <param name="deptCode">科室编码</param>
        /// <returns></returns>
        [HttpPost("ReadCodeDeptWord")]
        public IActionResult ReadCodeDeptWord([FromQuery] string deptCode)
        {
            string msg = string.Empty;
            result.ReturnData = _basicCodeService.ReadCodeDeptWord(deptCode);

            return Ok(result);
        }
        #endregion

        #region 采集地点信息 CodeGatherPlace
        /// <summary>
        /// /CU_CodeGatherPlace/Create 新增采集地点信息
        /// /CU_CodeGatherPlace/Update 更新采集地点信息
        /// </summary>
        /// <param name="type"></param>
        /// <param name="codePlace"></param>
        /// <returns></returns>
        [HttpPost("CU_CodeGatherPlace/{type}")]
        [UnitOfWork]
        public IActionResult CU_CodeGatherPlace([FromRoute] string type, [FromBody] CodeGatherPlace codePlace)
        {
            string msg = string.Empty;
            codePlace.HospCode = _httpContextUser.HospCode;

            switch (type.ToLower())
            {
                case "create":
                    codePlace.PlaceCode = _noGeneration.NextGatherPlaceNo(_httpContextUser.HospCode)[0];
                    result.Success = _basicCodeService.CreateCodeGatherPlace(codePlace);
                    break;
                case "update":
                    result.Success = _basicCodeService.UpdateCodeGatherPlace(codePlace);
                    break;
                default:
                    return new BadRequestResult();
            }
            result.ReturnMsg = msg;

            return Ok(result);
        }

        /// <summary>
        /// /RD_CodeGatherPlace/Read   获取采集地点信息
        /// /RD_CodeGatherPlace/Delete 删除采集地点信息
        /// </summary>
        /// <param name="type"></param>
        /// <param name="placeCode"></param>
        /// <returns></returns>
        [HttpPost("RD_CodeGatherPlace/{type}")]
        public IActionResult RD_CodeGatherPlace([FromRoute] string type, [FromBody] string[] placeCode)
        {
            string msg = string.Empty;

            switch (type.ToLower())
            {
                case "read":
                    result.ReturnData = _basicCodeService.ReadCodeGatherPlace();
                    break;
                case "delete":
                    result.Success = _basicCodeService.DeleteCodeGatherPlace(placeCode);
                    break;
                default:
                    return new BadRequestResult();
            }

            return Ok(result);
        }
        #endregion

        #region 职业工种信息 CodeJob
        /// <summary>
        /// /CU_CodeJob/Create 新增职业工种信息
        /// /CU_CodeJob/Update 更新职业工种信息
        /// </summary>
        /// <param name="type"></param>
        /// <param name="codeJob"></param>
        /// <returns></returns>
        [HttpPost("CU_CodeJob/{type}")]
        [UnitOfWork]
        public IActionResult CU_CodeJob([FromRoute] string type, [FromBody] CodeJob codeJob)
        {
            string msg = string.Empty;

            switch (type.ToLower())
            {
                case "create":
                    result.Success = _basicCodeService.CreateCodeJob(codeJob);
                    break;
                case "update":
                    result.Success = _basicCodeService.UpdateCodeJob(codeJob);
                    break;
                default:
                    return new BadRequestResult();
            }
            result.ReturnMsg = msg;

            return Ok(result);
        }

        /// <summary>
        /// /RD_CodeJob/Read   获取职业工种信息
        /// /RD_CodeJob/Delete 删除职业工种信息
        /// </summary>
        /// <param name="type"></param>
        /// <param name="jobCode"></param>
        /// <returns></returns>
        [HttpPost("RD_CodeJob/{type}")]
        public IActionResult RD_CodeJob([FromRoute] string type, [FromBody] string[] jobCode)
        {
            string msg = string.Empty;

            switch (type.ToLower())
            {
                case "read":
                    result.ReturnData = _basicCodeService.ReadCodeJob();
                    break;
                case "delete":
                    result.Success = _basicCodeService.DeleteCodeJob(jobCode);
                    break;
                default:
                    return new BadRequestResult();
            }

            return Ok(result);
        }
        #endregion

        #region 民族信息 CodeNation
        /// <summary>
        /// /CU_CodeNation/Create 新增民族信息
        /// /CU_CodeNation/Update 更新民族信息
        /// </summary>
        /// <param name="type"></param>
        /// <param name="codeNation"></param>
        /// <returns></returns>
        [HttpPost("CU_CodeNation/{type}")]
        [UnitOfWork]
        public IActionResult CU_CodeNation([FromRoute] string type, [FromBody] CodeNation codeNation)
        {
            string msg = string.Empty;

            switch (type.ToLower())
            {
                case "create":
                    result.Success = _basicCodeService.CreateCodeNation(codeNation);
                    break;
                case "update":
                    result.Success = _basicCodeService.UpdateCodeNation(codeNation);
                    break;
                default:
                    return new BadRequestResult();
            }
            result.ReturnMsg = msg;

            return Ok(result);


        }

        /// <summary>
        /// /RD_CodeNation/Read   获取民族信息
        /// /RD_CodeNation/Delete 删除民族信息
        /// </summary>
        /// <param name="type"></param>
        /// <param name="natCode"></param>
        /// <returns></returns>
        [HttpPost("RD_CodeNation/{type}")]
        public IActionResult RD_CodeNation([FromRoute] string type, [FromBody] string[] natCode)
        {
            string msg = string.Empty;

            switch (type.ToLower())
            {
                case "read":
                    result.ReturnData = _basicCodeService.ReadCodeNation();
                    break;
                case "delete":
                    result.Success = _basicCodeService.DeleteCodeNation(natCode);
                    break;
                default:
                    return new BadRequestResult();
            }

            return Ok(result);
        }
        #endregion

        #region 籍贯信息 CodeNativePlace
        /// <summary>
        /// /CU_CodeNativePlace/Create 新增籍贯信息
        /// /CU_CodeNativePlace/Update 更新籍贯信息
        /// </summary>
        /// <param name="type"></param>
        /// <param name="codeNativePlace"></param>
        /// <returns></returns>
        [HttpPost("CU_CodeNativePlace/{type}")]
        [UnitOfWork]
        public IActionResult CU_CodeNativePlace([FromRoute] string type, [FromBody] CodeNativePlace codeNativePlace)
        {
            string msg = string.Empty;

            switch (type.ToLower())
            {
                case "create":
                    result.Success = _basicCodeService.CreateCodeNativePlace(codeNativePlace);
                    break;
                case "update":
                    result.Success = _basicCodeService.UpdateCodeNativePlace(codeNativePlace);
                    break;
                default:
                    return new BadRequestResult();
            }
            result.ReturnMsg = msg;

            return Ok(result);
        }

        /// <summary>
        /// /RD_CodeNativePlace/Read   获取籍贯信息
        /// /RD_CodeNativePlace/Delete 删除籍贯信息
        /// </summary>
        /// <param name="type"></param>
        /// <param name="natCode"></param>
        /// <returns></returns>
        [HttpPost("RD_CodeNativePlace/{type}")]
        public IActionResult RD_CodeNativePlace([FromRoute] string type, [FromBody] string[] natCode)
        {
            string msg = string.Empty;

            switch (type.ToLower())
            {
                case "read":
                    result.ReturnData = _basicCodeService.ReadCodeNativePlace();
                    break;
                case "delete":
                    result.Success = _basicCodeService.DeleteCodeNativePlace(natCode);
                    break;
                default:
                    return new BadRequestResult();
            }

            return Ok(result);
        }
        #endregion

        #region 标本信息 CodeSample
        /// <summary>
        /// /CU_CodeSample/Create 新增标本信息
        /// /CU_CodeSample/Update 更新标本信息
        /// </summary>
        /// <param name="type"></param>
        /// <param name="codeSample"></param>
        /// <returns></returns>
        [HttpPost("CU_CodeSample/{type}")]
        [UnitOfWork]
        public IActionResult CU_CodeSample([FromRoute] string type, [FromBody] CodeSample codeSample)
        {
            string msg = string.Empty;

            switch (type.ToLower())
            {
                case "create":
                    result.Success = _basicCodeService.CreateCodeSample(codeSample);
                    break;
                case "update":
                    result.Success = _basicCodeService.UpdateCodeSample(codeSample);
                    break;
                default:
                    return new BadRequestResult();
            }
            result.ReturnMsg = msg;

            return Ok(result);

        }

        /// <summary>
        /// /RD_CodeSample/Read   获取标本信息
        /// /RD_CodeSample/Delete 删除标本信息
        /// </summary>
        /// <param name="type"></param>
        /// <param name="sampCode"></param>
        /// <returns></returns>
        [HttpPost("RD_CodeSample/{type}")]
        public IActionResult RD_CodeSample([FromRoute] string type, [FromBody] string[] sampCode)
        {
            string msg = string.Empty;

            switch (type.ToLower())
            {
                case "read":
                    result.ReturnData = _basicCodeService.ReadCodeSample();
                    break;
                case "delete":
                    result.Success = _basicCodeService.DeleteCodeSample(sampCode);
                    break;
                default:
                    return new BadRequestResult();
            }

            return Ok(result);
        }
        #endregion

        #region 院区代码信息 CodeHospital
        /// <summary>
        /// /CU_CodeHospital/Create 新增院区代码信息
        /// /CU_CodeHospital/Update 更新院区代码信息
        /// </summary>
        /// <param name="type"></param>
        /// <param name="codeHospital"></param>
        /// <returns></returns>
        [HttpPost("CU_CodeHospital/{type}")]
        [UnitOfWork]
        public IActionResult CU_CodeHospital([FromRoute] string type, [FromBody] CodeHospital codeHospital)
        {
            string msg = string.Empty;

            switch (type.ToLower())
            {
                case "create":
                    result.Success = _basicCodeService.CreateCodeHospital(codeHospital);
                    break;
                case "update":
                    result.Success = _basicCodeService.UpdateCodeHospital(codeHospital);
                    break;
                default:
                    return new BadRequestResult();
            }
            result.ReturnMsg = msg;

            return Ok(result);

        }

        /// <summary>
        /// /RD_CodeHospital/Read   获取院区代码信息
        /// /RD_CodeHospital/Delete 删除院区代码信息
        /// </summary>
        /// <param name="type"></param>
        /// <param name="hospCode"></param>
        /// <returns></returns>
        [HttpPost("RD_CodeHospital/{type}")]
        public IActionResult RD_CodeHospital([FromRoute] string type, [FromBody] string[] hospCode)
        {
            string msg = string.Empty;

            switch (type.ToLower())
            {
                case "read":
                    result.ReturnData = _basicCodeService.ReadCodeHospital();
                    break;
                case "delete":
                    result.Success = _basicCodeService.DeleteCodeHospital(hospCode);
                    break;
                default:
                    return new BadRequestResult();
            }

            return Ok(result);
        }
        #endregion

        #region 随访处理结果模板

        /// <summary>
        /// 保存随访处理结果模板内容
        /// </summary>
        /// <param name="data">随访处理结果模板内容</param>
        /// <returns></returns>
        [HttpPost("SaveCodeFollowUpResultTemplate")]
        [ProducesResponseType(typeof(CodeFollowUpResultTemplateDto), 200)]
        public IActionResult SaveCodeFollowUpResultTemplate(CodeFollowUpResultTemplateDto data)
        {
            result.ReturnData = _basicCodeService.SaveCodeFollowUpResultTemplate(_mapper.Map<CodeFollowUpResultTemplate>(data));

            return Ok(result);
        }

        /// <summary>
        /// 获取随访处理结果模板内容
        /// </summary>
        /// <param name="id">记录id</param>
        /// <returns></returns>
        [HttpGet("GetCodeFollowUpResultTemplate/{id}")]
        [ProducesResponseType(typeof(CodeFollowUpResultTemplateDto), 200)]
        public IActionResult GetCodeFollowUpResultTemplate(long id)
        {
            result.ReturnData = _basicCodeService.GetCodeFollowUpResultTemplate(id);

            return Ok(result);
        }

        /// <summary>
        /// 获取随访处理结果模板内容
        /// </summary>
        /// <param name="query">查询实体</param>
        /// <returns></returns>
        [HttpPost("GetCodeFollowUpResultTemplates")]
        [ProducesResponseType(typeof(List<CodeFollowUpResultTemplateDto>), 200)]
        public IActionResult GetCodeFollowUpResultTemplates(CodeFollowUpResultTemplateQuery query)
        {
            int totalNumber = 0;
            int totalPage = 0;
            var list = _basicCodeService.GetCodeFollowUpResultTemplates(query, ref totalNumber, ref totalPage);
            result.ReturnData = _mapper.Map<List<CodeFollowUpResultTemplateDto>>(list);
            result.TotalNumber = totalNumber;
            result.TotalPage = totalPage;

            return Ok(result);
        }

        /// <summary>
        /// 删除随访处理结果模板
        /// </summary>
        /// <param name="id">记录id</param>
        /// <returns></returns>
        [HttpDelete("DeleteCodeFollowUpResultTemplate")]
        [ProducesResponseType(typeof(bool), 200)]
        public IActionResult DeleteCodeFollowUpResultTemplate([FromBody]long[] id)
        {
            result.Success = _basicCodeService.DeleteCodeFollowUpResultTemplate(id);
            result.ReturnData = result.Success;
            return Ok(result);
        }

        #endregion

        #endregion

        #region 体检类代码

        #region 项目分类 CodeItemCls
        /// <summary>
        /// /CU_CodeItemCls/Create 新增项目分类信息
        /// /CU_CodeItemCls/Update 更新项目分类信息
        /// </summary>
        /// <param name="type"></param>
        /// <param name="codeItemCls"></param>
        /// <returns></returns>
        [HttpPost("CU_CodeItemCls/{type}")]
        [UnitOfWork]
        public IActionResult CU_CodeItemCls([FromRoute] string type, [FromBody] CodeItemCls codeItemCls)
        {
            string msg = string.Empty;

            switch (type.ToLower())
            {
                case "create":
                    result.Success = _basicCodeService.CreateCodeItemCls(codeItemCls);
                    break;
                case "update":
                    result.Success = _basicCodeService.UpdateCodeItemCls(codeItemCls);
                    break;
                default:
                    return new BadRequestResult();
            }
            result.ReturnMsg = msg;

            return Ok(result);



        }

        /// <summary>
        /// /RD_CodeItemCls/Read   获取项目分类信息
        /// /RD_CodeItemCls/Delete 删除项目分类信息
        /// </summary>
        /// <param name="type"></param>
        /// <param name="clsCodes"></param>
        /// <returns></returns>
        [HttpPost("RD_CodeItemCls/{type}")]
        public IActionResult RD_CodeItemCls([FromRoute] string type, [FromBody] string[] clsCodes)
        {
            string msg = string.Empty;

            switch (type.ToLower())
            {
                case "read":
                    result.ReturnData = _basicCodeService.ReadCodeItemCls();
                    break;
                case "delete":
                    result.Success = _basicCodeService.DeleteCodeItemCls(clsCodes);
                    break;
                default:
                    return new BadRequestResult();
            }

            return Ok(result);
        }
        #endregion

        #region 条码分类 CodeBarcodeType
        /// <summary>
        /// /CU_CodeBarcodeType/Create 新增条码分类信息
        /// /CU_CodeBarcodeType/Update 更新条码分类信息
        /// </summary>
        /// <param name="type"></param>
        /// <param name="codeBarcodeType"></param>
        /// <returns></returns>
        [HttpPost("CU_CodeBarcodeType/{type}")]
        [UnitOfWork]
        public IActionResult CU_CodeBarcodeType([FromRoute] string type, [FromBody] CodeBarcodeType codeBarcodeType)
        {
            string msg = string.Empty;

            switch (type.ToLower())
            {
                case "create":
                    result.Success = _basicCodeService.CreateCodeBarcodeType(codeBarcodeType);
                    break;
                case "update":
                    result.Success = _basicCodeService.UpdateCodeBarcodeType(codeBarcodeType);
                    break;
                default:
                    return new BadRequestResult();
            }
            result.ReturnMsg = msg;

            return Ok(result);
        }

        /// <summary>
        /// /RD_CodeBarcodeType/Read   获取条码分类信息
        /// /RD_CodeBarcodeType/Delete 删除条码分类信息
        /// </summary>
        /// <param name="type"></param>
        /// <param name="barcodes"></param>
        /// <returns></returns>
        [HttpPost("RD_CodeBarcodeType/{type}")]
        public IActionResult RD_CodeBarcodeType([FromRoute] string type, [FromBody] string[] barcodes)
        {
            string msg = string.Empty;

            switch (type.ToLower())
            {
                case "read":
                    result.ReturnData = _basicCodeService.ReadCodeBarcodeType();
                    break;
                case "delete":
                    result.Success = _basicCodeService.DeleteCodeBarcodeType(barcodes);
                    break;
                default:
                    return new BadRequestResult();
            }

            return Ok(result);
        }
        #endregion

        #region 参考范围分类 CodeBoundType
        /// <summary>
        /// /CU_CodeBoundType/Create 新增参考范围分类信息
        /// /CU_CodeBoundType/Update 更新参考范围分类信息
        /// </summary>
        /// <param name="type"></param>
        /// <param name="codeBoundType"></param>
        /// <returns></returns>
        [HttpPost("CU_CodeBoundType/{type}")]
        [UnitOfWork]
        public IActionResult CU_CodeBoundType([FromRoute] string type, [FromBody] CodeBoundType codeBoundType)
        {
            string msg = string.Empty;

            switch (type.ToLower())
            {
                case "create":
                    result.Success = _basicCodeService.CreateCodeBoundType(codeBoundType);
                    break;
                case "update":
                    result.Success = _basicCodeService.UpdateCodeBoundType(codeBoundType);
                    break;
                default:
                    return new BadRequestResult();
            }
            result.ReturnMsg = msg;

            return Ok(result);
        }

        /// <summary>
        /// /RD_CodeBoundType/Read   获取参考范围分类信息
        /// /RD_CodeBoundType/Delete 删除参考范围分类信息
        /// </summary>
        /// <param name="type"></param>
        /// <param name="boundCodes"></param>
        /// <returns></returns>
        [HttpPost("RD_CodeBoundType/{type}")]
        public IActionResult RD_CodeBoundType([FromRoute] string type, [FromBody] string[] boundCodes)
        {
            string msg = string.Empty;

            switch (type.ToLower())
            {
                case "read":
                    result.ReturnData = _basicCodeService.ReadCodeBoundType();
                    break;
                case "delete":
                    result.Success = _basicCodeService.DeleteCodeBoundType(boundCodes);
                    break;
                default:
                    return new BadRequestResult();
            }

            return Ok(result);
        }
        #endregion

        #region 档案项目信息 CodeArchiveItem
        /// <summary>
        /// /CU_CodeArchiveItem/Create 新增档案项目信息
        /// /CU_CodeArchiveItem/Update 更新档案项目信息
        /// </summary>
        /// <param name="type"></param>
        /// <param name="codeArchiveItem"></param>
        /// <returns></returns>
        [HttpPost("CU_CodeArchiveItem/{type}")]
        [UnitOfWork]
        public IActionResult CU_CodeArchiveItem([FromRoute] string type, [FromBody] CodeArchiveItem codeArchiveItem)
        {
            string msg = string.Empty;

            switch (type.ToLower())
            {
                case "create":
                    result.Success = _basicCodeService.CreateCodeArchiveItem(codeArchiveItem);
                    break;
                case "update":
                    result.Success = _basicCodeService.UpdateCodeArchiveItem(codeArchiveItem);
                    break;
                default:
                    return new BadRequestResult();
            }
            result.ReturnMsg = msg;

            return Ok(result);
        }

        /// <summary>
        /// /RD_CodeArchiveItem/Read   获取档案项目信息
        /// /RD_CodeArchiveItem/Delete 删除档案项目信息
        /// </summary>
        /// <param name="type"></param>
        /// <param name="itemCodes"></param>
        /// <returns></returns>
        [HttpPost("RD_CodeArchiveItem/{type}")]
        public IActionResult RD_CodeArchiveItem([FromRoute] string type, [FromBody] string[] itemCodes)
        {
            string msg = string.Empty;

            switch (type.ToLower())
            {
                case "read":
                    result.ReturnData = _basicCodeService.ReadCodeArchiveItem();
                    break;
                case "delete":
                    result.Success = _basicCodeService.DeleteCodeArchiveItem(itemCodes);
                    break;
                default:
                    return new BadRequestResult();
            }

            return Ok(result);
        }
        #endregion

        #region 互斥组合设置 CodeMutexComb
        /// <summary>
        /// 获取互斥代码列表
        /// </summary>
        /// <returns></returns>
        [ProducesResponseType(typeof(MutexCodeInfo[]), 200)]
        [HttpPost("ReadMutexCode")]
        public IActionResult ReadMutexCode()
        {
            result.ReturnData = _basicCodeService.ReadMutexCode();
            return Ok(result);
        }

        /// <summary>
        /// 删除互斥代码
        /// </summary>
        /// <param name="mutexCode">互斥代码</param>
        /// <returns></returns>
        /// <response code="200">返回成功或失败</response>
        [HttpPost("DeletMutexCode")]
        public IActionResult DeletMutexCode([FromBody] string[] mutexCode)
        {
            _basicCodeService.DeletMutexCode(mutexCode);
            return Ok(result);
        }

        /// <summary>
        /// 获取互斥组合明细
        /// </summary>
        /// <param name="mutexCode">互斥代码</param>
        /// <returns></returns>
        [ProducesResponseType(typeof(CodeMutexComb[]), 200)]
        [HttpPost("ReadMutexCodeMapComb")]
        public IActionResult ReadMutexCodeMapComb([FromQuery] string mutexCode)
        {
            result.ReturnData = _basicCodeService.ReadCodeMutexComb(mutexCode);
            return Ok(result);
        }

        /// <summary>
        /// MutexCodeMapComb/Create 新增互斥组合明细
        /// MutexCodeMapComb/Delete 删除互斥组合明细
        /// </summary>
        /// <param name="type"></param>
        /// <param name="codeMutexComb"></param>
        /// <returns></returns>
        /// <response code="200">返回成功或失败</response>
        [HttpPost("MutexCodeMapComb/{type}")]
        public IActionResult MutexCodeMapComb([FromRoute] string type, [FromBody] List<CodeMutexComb> codeMutexComb)
        {
            string msg = string.Empty;
            switch (type.ToLower())
            {
                case "create":
                    result.Success = _basicCodeService.CreateCodeMutexComb(codeMutexComb);
                    break;
                case "delete":
                    result.Success = _basicCodeService.DeleteCodeMutexComb(codeMutexComb);
                    break;
                default:
                    return new BadRequestResult();
            }

            result.ReturnMsg = msg;
            return Ok(result);
        }

        /// <summary>
        /// 批量保存互斥组合明细
        /// </summary>
        /// <param name="mutexCode">互斥码</param>
        /// <param name="codeMutexCombs">组合明细</param>
        /// <returns></returns>
        [HttpPost("BatchSaveMutexCodeMapComb/{mutexCode}")]
        [UnitOfWork]
        public IActionResult BatchSaveMutexCodeMapComb(string mutexCode, [FromBody] List<CodeMutexComb> codeMutexCombs)
        {
            result.Success = _basicCodeService.BatchSaveMutexCodeMapComb(mutexCode, codeMutexCombs);
            return Ok(result);
        }
        #endregion

        #region 体检结论模板设置 CodeConclusionTemplate
        /// <summary>
        /// /CU_CodeConclusionTemplate/Create 新增体检结论模板设置
        /// /CU_CodeConclusionTemplate/Update 更新体检结论模板设置
        /// </summary>
        /// <param name="type"></param>
        /// <param name="codeConclusionTemplate"></param>
        /// <returns></returns>
        [HttpPost("CU_CodeConclusionTemplate/{type}")]
        [UnitOfWork]
        public IActionResult CU_CodeConclusionTemplate([FromRoute] string type, [FromBody] CodeConclusionTemplate codeConclusionTemplate)
        {
            string msg = string.Empty;

            switch (type.ToLower())
            {
                case "create":
                    result.Success = _basicCodeService.CreateCodeConclusionTemplate(codeConclusionTemplate);
                    break;
                case "update":
                    result.Success = _basicCodeService.UpdateCodeConclusionTemplate(codeConclusionTemplate);
                    break;
                default:
                    return new BadRequestResult();
            }
            result.ReturnMsg = msg;

            return Ok(result);
        }

        /// <summary>
        /// /RD_CodeConclusionTemplate/Read   获取体检结论模板设置
        /// /RD_CodeConclusionTemplate/Delete 删除体检结论模板设置
        /// </summary>
        /// <param name="type"></param>
        /// <param name="tempCodes"></param>
        /// <returns></returns>
        [HttpPost("RD_CodeConclusionTemplate/{type}")]
        public IActionResult RD_CodeConclusionTemplate([FromRoute] string type, [FromBody] string[] tempCodes)
        {
            string msg = string.Empty;

            switch (type.ToLower())
            {
                case "read":
                    result.ReturnData = _basicCodeService.ReadCodeConclusionTemplate();
                    break;
                case "delete":
                    result.Success = _basicCodeService.DeleteCodeConclusionTemplate(tempCodes);
                    break;
                default:
                    return new BadRequestResult();
            }

            return Ok(result);
        }
        #endregion

        #region 正常小结模板设置 CodeNormalResult
        /// <summary>
        /// /CU_CodeNormalResult/Create 新增正常小结模板
        /// /CU_CodeNormalResult/Update 更新正常小结模板
        /// </summary>
        /// <param name="type"></param>
        /// <param name="codeNormalResult"></param>
        /// <returns></returns>
        [HttpPost("CU_CodeNormalResult/{type}")]
        [UnitOfWork]
        public IActionResult CU_CodeNormalResult([FromRoute] string type, [FromBody] CodeNormalSummary codeNormalResult)
        {
            string msg = string.Empty;

            switch (type.ToLower())
            {
                case "create":
                    result.Success = _basicCodeService.CreateCodeNormalResult(codeNormalResult);
                    break;
                case "update":
                    result.Success = _basicCodeService.UpdateCodeNormalResult(codeNormalResult);
                    break;
                default:
                    return new BadRequestResult();
            }
            result.ReturnMsg = msg;

            return Ok(result);
        }

        /// <summary>
        /// /RD_CodeNormalResult/Read   获取正常小结模板
        /// /RD_CodeNormalResult/Delete 删除正常小结模板
        /// </summary>
        /// <param name="type"></param>
        /// <param name="resultCodes"></param>
        /// <returns></returns>
        [HttpPost("RD_CodeNormalResult/{type}")]
        public IActionResult RD_CodeNormalResult([FromRoute] string type, [FromBody] string[] resultCodes)
        {
            string msg = string.Empty;

            switch (type.ToLower())
            {
                case "read":
                    result.ReturnData = _basicCodeService.ReadCodeNormalResult();
                    break;
                case "delete":
                    result.Success = _basicCodeService.DeleteCodeNormalResult(resultCodes);
                    break;
                default:
                    return new BadRequestResult();
            }

            return Ok(result);
        }
        #endregion

        #region 体检项目信息 CodeItem
        /// <summary>
        /// /CU_CodeItem/Create 新增体检项目信息
        /// /CU_CodeItem/Update 更新体检项目信息
        /// </summary>
        /// <param name="type"></param>
        /// <param name="codeItem"></param>
        /// <returns></returns>
        [HttpPost("CU_CodeItem/{type}")]
        [UnitOfWork]
        public IActionResult CU_CodeItem([FromRoute] string type, [FromBody] CodeItem codeItem)
        {
            string msg = string.Empty;

            switch (type.ToLower())
            {
                case "create":
                    codeItem.ItemCode = _noGeneration.NextItemNo()[0];
                    result.Success = _basicCodeService.CreateCodeItem(codeItem);
                    break;
                case "update":
                    result.Success = _basicCodeService.UpdateCodeItem(codeItem);
                    break;
                default:
                    return new BadRequestResult();
            }
            result.ReturnMsg = msg;

            return Ok(result);
        }

        /// <summary>
        /// /PageQuery_CodeItem 分页查询体检项目信息
        /// </summary>
        /// <param name="clsCode"></param>
        /// <param name="pageNumber"></param>
        /// <param name="pageSize"></param>
        /// <returns></returns>
        [HttpPost("PageQuery_CodeItem")]
        public IActionResult PageQuery_CodeItem([FromQuery] string clsCode, [FromQuery] int pageNumber, [FromQuery] int pageSize)
        {
            int totalNumber = 0;
            int totalPage = 0;

            result.ReturnData = _basicCodeService.ReadCodeItem(clsCode, pageNumber, pageSize, ref totalNumber, ref totalPage);

            result.TotalNumber = totalNumber;
            result.TotalPage = totalPage;


            return Ok(result);
        }

        /// <summary>
        /// /Delete_CodeItem 删除体检项目信息
        /// </summary>
        /// <param name="itemCodes"></param>
        /// <returns></returns>
        [HttpPost("Delete_CodeItem")]
        public IActionResult Delete_CodeItem([FromBody] string[] itemCodes)
        {
            string msg = string.Empty;
            var flag = _basicCodeService.DeleteCodeItem(itemCodes);
            if (!flag)
            {
                result.Success = false;
                result.ReturnMsg = msg;
                return Ok(result);
            }

            return Ok(result);
        }
        #endregion

        #region 体检组合信息 CodeItemComb
        /// <summary>
        /// /CU_CodeItemComb/Create 新增体检组合信息
        /// /CU_CodeItemComb/Update 更新体检组合信息Comb
        /// </summary>
        /// <param name="type"></param>
        /// <param name="codeItemComb"></param>
        /// <returns></returns>
        [HttpPost("CU_CodeItemComb/{type}")]
        [UnitOfWork]
        public IActionResult CU_CodeItemComb([FromRoute] string type, [FromBody] CodeItemComb codeItemComb)
        {
            string msg = string.Empty;
            codeItemComb.HospCode = _httpContextUser.HospCode;
            switch (codeItemComb.CheckCls)
            {
                // 默认置空
                case CheckCls.一般检查:
                case CheckCls.医生检查:
                    codeItemComb.BwName = string.Empty;
                    codeItemComb.BwDlName = string.Empty;
                    codeItemComb.BarCodeType = string.Empty;
                    codeItemComb.IsMergeTube = false;
                    break;
                case CheckCls.功能检查:
                    codeItemComb.BarCodeType = string.Empty;
                    codeItemComb.IsMergeTube = false;
                    break;
                case CheckCls.检验检查:
                    codeItemComb.BwName = string.Empty;
                    codeItemComb.BwDlName = string.Empty;
                    break;
            }

            switch (type.ToLower())
            {
                case "create":
                    codeItemComb.CombCode = _noGeneration.NextCombNo(_httpContextUser.HospCode)[0];
                    result.Success = _basicCodeService.CreateCodeItemComb(codeItemComb);
                    break;
                case "update":
                    result.Success = _basicCodeService.UpdateCodeItemComb(codeItemComb);
                    break;
                default:
                    return new BadRequestResult();
            }
            result.ReturnMsg = msg;

            return Ok(result);

        }

        /// <summary>
        /// /PageQuery_CodeItemComb 分页查询体检组合信息
        /// </summary>
        /// <param name="clsCode"></param>
        /// <param name="pageNumber"></param>
        /// <param name="pageSize"></param>
        /// <returns></returns>
        [HttpPost("PageQuery_CodeItemComb")]
        [ProducesResponseType(typeof(CodeItemComb[]), 200)]
        public IActionResult PageQuery_CodeItemComb([FromQuery] string clsCode, [FromQuery] int pageNumber, [FromQuery] int pageSize)
        {
            int totalNumber = 0;
            int totalPage = 0;

            result.ReturnData = _basicCodeService.ReadCodeItemComb(clsCode, pageNumber, pageSize, ref totalNumber, ref totalPage);

            result.TotalNumber = totalNumber;
            result.TotalPage = totalPage;


            return Ok(result);
        }

        /// <summary>
        /// /Delete_CodeItemComb 删除体检组合信息
        /// </summary>
        /// <param name="itemCodes"></param>
        /// <returns></returns>
        [HttpPost("Delete_CodeItemComb")]
        [UnitOfWork]
        public IActionResult Delete_CodeItemComb([FromBody] string[] itemCodes)
        {

            string msg = string.Empty;
            var flag = _basicCodeService.DeleteCodeItemComb(itemCodes);
            if (!flag)
            {
                result.Success = false;
                result.ReturnMsg = msg;
                return Ok(result);
            }

            return Ok(result);
        }
        #endregion

        #region 体检普通套餐信息 CodeCluster
        /// <summary>
        /// /CU_CodeCluster/Create 新增体检普通套餐信息
        /// /CU_CodeCluster/Update 更新体检普通套餐信息
        /// </summary>
        /// <param name="type"></param>
        /// <param name="codeCluster"></param>
        /// <returns></returns>
        [HttpPost("CU_CodeCluster/{type}")]
        [UnitOfWork]
        public IActionResult CU_CodeCluster([FromRoute] string type, [FromBody] CodeCluster codeCluster)
        {
            string msg = string.Empty;
            codeCluster.HospCode = _httpContextUser.HospCode;
            codeCluster.IsOccupation = false;
            switch (type.ToLower())
            {
                case "create":
                    codeCluster.ClusCode = _noGeneration.NextClusterNo(_httpContextUser.HospCode)[0];
                    result.Success = _basicCodeService.CreateCodeCluster(codeCluster);
                    break;
                case "update":
                    result.Success = _basicCodeService.UpdateCodeCluster(codeCluster);
                    break;
                default:
                    return new BadRequestResult();
            }
            result.ReturnMsg = msg;

            return Ok(result);
        }

        /// <summary>
        /// /RD_CodeCluster/Read   获取体检普通套餐信息
        /// /RD_CodeCluster/Delete 删除体检普通套餐信息
        /// </summary>
        /// <param name="type"></param>
        /// <param name="clusCodes"></param>
        /// <returns></returns>
        [HttpPost("RD_CodeCluster/{type}")]
        [UnitOfWork]
        public IActionResult RD_CodeCluster([FromRoute] string type, [FromBody] string[] clusCodes)
        {
            string msg = string.Empty;

            switch (type.ToLower())
            {
                case "read":
                    result.ReturnData = _basicCodeService.ReadCodeCluster(false);
                    break;
                case "delete":
                    result.Success = _basicCodeService.DeleteCodeCluster(clusCodes);
                    break;
                default:
                    return new BadRequestResult();
            }

            return Ok(result);
        }

        /// <summary>
        /// 获取套餐及其明细内容
        /// </summary>
        /// <returns></returns>
        [HttpPost("GetCodeClusterWithDetails")]
        [ProducesResponseType(typeof(List<CodeCluster>), StatusCodes.Status200OK)]
        public IActionResult GetCodeClusterWithDetails()
        {
            result.ReturnData = _basicCodeService.GetCodeClusterWithDetails(false);
            return Ok(result);
        }
        #endregion

        #endregion

        #region 收费类代码

        #region 费用分类信息（收费类代码） CodeFeeCls
        /// <summary>
        /// CU_CodeFeeCls/Create 新增费用分类信息（收费类代码）
        /// CU_CodeFeeCls/Update 更新费用分类信息（收费类代码）
        /// </summary>
        /// <param name="type"></param>
        /// <param name="codeFeeCls"></param>
        /// <returns></returns>
        [HttpPost("CU_CodeFeeCls/{type}")]
        [UnitOfWork]
        public IActionResult CU_CodeFeeCls([FromRoute] string type, [FromBody] CodeFeeCls codeFeeCls)
        {
            string msg = string.Empty;

            switch (type.ToLower())
            {
                case "create":
                    result.Success = _basicCodeService.CreateCodeFeeCls(codeFeeCls);
                    break;
                case "update":
                    result.Success = _basicCodeService.UpdateCodeFeeCls(codeFeeCls);
                    break;
                default:
                    return new BadRequestResult();
            }
            result.ReturnMsg = msg;

            return Ok(result);
        }

        /// <summary>
        /// /RD_CodeFeeCls/Read   获取费用分类信息（收费类代码）
        /// /RD_CodeFeeCls/Delete 删除费用分类信息（收费类代码）
        /// </summary>
        /// <param name="type"></param>
        /// <param name="clsCodes"></param>
        /// <returns></returns>
        [HttpPost("RD_CodeFeeCls/{type}")]
        public IActionResult RD_CodeFeeCls([FromRoute] string type, [FromBody] string[] clsCodes)
        {
            string msg = string.Empty;

            switch (type.ToLower())
            {
                case "read":
                    result.ReturnData = _basicCodeService.ReadCodeFeeCls();
                    break;
                case "delete":
                    result.Success = _basicCodeService.DeleteCodeFeeCls(clsCodes);
                    break;
                default:
                    return new BadRequestResult();
            }

            return Ok(result);
        }
        #endregion

        #region 费用分类分组信息(收费类代码) CodeFeeClsGroup
        /// <summary>
        /// CU_CodeFeeClsGroup/Create 新增费用分类分组信息(收费类代码)
        /// CU_CodeFeeClsGroup/Update 更新费用分类分组信息(收费类代码)
        /// </summary>
        /// <param name="type"></param>
        /// <param name="codeFeeClsGroup"></param>
        /// <returns></returns>
        [HttpPost("CU_CodeFeeClsGroup/{type}")]
        [UnitOfWork]
        public IActionResult CU_CodeFeeClsGroup([FromRoute] string type, [FromBody] CodeFeeClsGroup codeFeeClsGroup)
        {
            string msg = string.Empty;

            switch (type.ToLower())
            {
                case "create":
                    result.Success = _basicCodeService.CreateCodeFeeClsGroup(codeFeeClsGroup);
                    break;
                case "update":
                    result.Success = _basicCodeService.UpdateCodeFeeClsGroup(codeFeeClsGroup);
                    break;
                default:
                    return new BadRequestResult();
            }
            result.ReturnMsg = msg;

            return Ok(result);

        }

        /// <summary>
        /// /RD_CodeFeeClsGroup/Read   获取费用分类分组信息(收费类代码)
        /// /RD_CodeFeeClsGroup/Delete 删除费用分类分组信息(收费类代码)
        /// </summary>
        /// <param name="type"></param>
        /// <param name="groupCodes"></param>
        /// <returns></returns>
        [HttpPost("RD_CodeFeeClsGroup/{type}")]
        public IActionResult RD_CodeFeeClsGroup([FromRoute] string type, [FromBody] string[] groupCodes)
        {
            string msg = string.Empty;

            switch (type.ToLower())
            {
                case "read":
                    result.ReturnData = _basicCodeService.ReadCodeFeeClsGroup();
                    break;
                case "delete":
                    result.Success = _basicCodeService.DeleteCodeFeeClsGroup(groupCodes);
                    break;
                default:
                    return new BadRequestResult();
            }

            return Ok(result);
        }
        #endregion

        #region 结账模式（收费类代码） CodeFee
        /// <summary>
        /// CU_CodeFee/Create 新增结账模式（收费类代码）
        /// CU_CodeFee/Update 更新结账模式（收费类代码）
        /// </summary>
        /// <param name="type"></param>
        /// <param name="codePaymentMode"></param>
        /// <returns></returns>
        [HttpPost("CU_CodeFee/{type}")]
        [UnitOfWork]
        public IActionResult CU_CodeFee([FromRoute] string type, [FromBody] CodePaymentMode codePaymentMode)
        {
            string msg = string.Empty;

            switch (type.ToLower())
            {
                case "create":
                    result.Success = _basicCodeService.CreateCodePaymentMode(codePaymentMode);
                    break;
                case "update":
                    result.Success = _basicCodeService.UpdateCodePaymentMode(codePaymentMode);
                    break;
                default:
                    return new BadRequestResult();
            }
            result.ReturnMsg = msg;

            return Ok(result);
        }

        /// <summary>
        /// /RD_CodeFee/Read   获取结账模式（收费类代码）
        /// /RD_CodeFee/Delete 删除结账模式（收费类代码）
        /// </summary>
        /// <param name="type"></param>
        /// <param name="payModeCodes"></param>
        /// <returns></returns>
        [HttpPost("RD_CodeFee/{type}")]
        public IActionResult RD_CodeFee([FromRoute] string type, [FromBody] string[] payModeCodes)
        {
            string msg = string.Empty;

            switch (type.ToLower())
            {
                case "read":
                    result.ReturnData = _basicCodeService.ReadCodePaymentMode();
                    break;
                case "delete":
                    result.Success = _basicCodeService.DeleteCodePaymentMode(payModeCodes);
                    break;
                default:
                    return new BadRequestResult();
            }

            return Ok(result);
        }
        #endregion

        #region 结账类型信息（收费类代码） CodePayment
        /// <summary>
        /// CU_CodePayment/Create 新增结账类型信息（收费类代码）
        /// CU_CodePayment/Update 更新结账类型信息（收费类代码）
        /// </summary>
        /// <param name="type"></param>
        /// <param name="codePayment"></param>
        /// <returns></returns>
        [HttpPost("CU_CodePayment/{type}")]
        [UnitOfWork]
        public IActionResult CU_CodePayment([FromRoute] string type, [FromBody] CodePayment codePayment)
        {
            string msg = string.Empty;

            switch (type.ToLower())
            {
                case "create":
                    result.Success = _basicCodeService.CreateCodePayment(codePayment);
                    break;
                case "update":
                    result.Success = _basicCodeService.UpdateCodePayment(codePayment);
                    break;
                default:
                    return new BadRequestResult();
            }
            result.ReturnMsg = msg;

            return Ok(result);
        }

        /// <summary>
        /// /RD_CodePayment/Read   获取结账类型信息（收费类代码）
        /// /RD_CodePayment/Delete 删除结账类型信息（收费类代码）
        /// </summary>
        /// <param name="type"></param>
        /// <param name="payCodes"></param>
        /// <returns></returns>
        [HttpPost("RD_CodePayment/{type}")]
        public IActionResult RD_CodePayment([FromRoute] string type, [FromBody] string[] payCodes)
        {
            string msg = string.Empty;

            switch (type.ToLower())
            {
                case "read":
                    result.ReturnData = _basicCodeService.ReadCodePayment();
                    break;
                case "delete":
                    result.Success = _basicCodeService.DeleteCodePayment(payCodes);
                    break;
                default:
                    return new BadRequestResult();
            }

            return Ok(result);
        }
        #endregion

        #region 凑整规则信息（收费类代码） CodeRound
        /// <summary>
        /// CU_CodeRound/Create 新增凑整规则信息（收费类代码）
        /// CU_CodeRound/Update 更新凑整规则信息（收费类代码）
        /// </summary>
        /// <param name="type"></param>
        /// <param name="codeRound"></param>
        /// <returns></returns>
        [HttpPost("CU_CodeRound/{type}")]
        [UnitOfWork]
        public IActionResult CU_CodeRound([FromRoute] string type, [FromBody] CodeRound codeRound)
        {
            string msg = string.Empty;

            switch (type.ToLower())
            {
                case "create":
                    result.Success = _basicCodeService.CreateCodeRound(codeRound);
                    break;
                case "update":
                    result.Success = _basicCodeService.UpdateCodeRound(codeRound);
                    break;
                default:
                    return new BadRequestResult();
            }
            result.ReturnMsg = msg;

            return Ok(result);
        }

        /// <summary>
        /// /RD_CodeRound/Read   获取凑整规则信息（收费类代码）
        /// /RD_CodeRound/Delete 删除凑整规则信息（收费类代码）
        /// </summary>
        /// <param name="type"></param>
        /// <param name="roundCodes"></param>
        /// <returns></returns>
        [HttpPost("RD_CodeRound/{type}")]
        public IActionResult RD_CodeRound([FromRoute] string type, [FromBody] string[] roundCodes)
        {
            string msg = string.Empty;

            switch (type.ToLower())
            {
                case "read":
                    result.ReturnData = _basicCodeService.ReadCodeRound();
                    break;
                case "delete":
                    result.Success = _basicCodeService.DeleteCodeRound(roundCodes);
                    break;
                default:
                    return new BadRequestResult();
            }

            return Ok(result);
        }
        #endregion

        #endregion

        #region 体质信息（中医管理） CodeConstitution
        /// <summary>
        /// CUD_CodeConstitution/Create 新增体质信息（中医管理）
        /// CUD_CodeConstitution/Update 更新体质信息（中医管理）
        /// CUD_CodeConstitution/Delete 删除体质信息（中医管理）
        /// </summary>
        /// <param name="type"></param>
        /// <param name="codeConstitution"></param>
        /// <returns></returns>
        [HttpPost("CUD_CodeConstitution/{type}")]
        [UnitOfWork]
        public IActionResult CUD_CodeConstitution([FromRoute] string type, [FromBody] CodeConstitution codeConstitution)
        {
            string msg = string.Empty;

            switch (type.ToLower())
            {
                case "create":
                    result.Success = _basicCodeService.CreateCodeConstitution(codeConstitution);
                    break;
                case "update":
                    result.Success = _basicCodeService.UpdateCodeConstitution(codeConstitution);
                    break;
                case "delete":
                    result.Success = _basicCodeService.DeleteCodeConstitution(codeConstitution);
                    break;
                default:
                    return new BadRequestResult();
            }
            result.ReturnMsg = msg;

            return Ok(result);
        }

        /// <summary>
        /// R_CodeConstitution 获取体质信息（中医管理）
        /// </summary>
        /// <returns></returns>
        [HttpPost("R_CodeConstitution")]
        public IActionResult R_CodeConstitution()
        {
            result.ReturnData = _basicCodeService.ReadCodeConstitution();

            return Ok(result);
        }
        #endregion

        #region 参考范围
        /// <summary>
        /// /CU_CodeItemBound/Create 新增参考范围
        /// /CU_CodeItemBound/Update 更新参考范围
        /// </summary>
        /// <param name="type"></param>
        /// <param name="codeItemBound"></param>
        /// <returns></returns>
        [HttpPost("CU_CodeItemBound/{type}")]
        [UnitOfWork]
        public IActionResult CU_CodeItemBound([FromRoute] string type, [FromBody] CodeItemBound codeItemBound)
        {
            string msg = string.Empty;

            switch (type.ToLower())
            {
                case "create":
                    result.Success = _basicCodeService.CreateCodeItemBound(codeItemBound);
                    break;
                case "update":
                    result.Success = _basicCodeService.UpdateCodeItemBound(codeItemBound);
                    break;
                default:
                    return new BadRequestResult();
            }
            result.ReturnMsg = msg;

            return Ok(result);
        }

        /// <summary>
        /// Read_CodeItemBound   获取参考范围
        /// </summary>
        /// <param name="ItemCode">项目编码</param>
        /// <returns></returns>
        [HttpPost("Read_CodeItemBound")]
        public IActionResult Read_CodeItemBound([FromQuery] string ItemCode)
        {
            result.ReturnData = _basicCodeService.ReadCodeItemBound(ItemCode);
            return Ok(result);
        }

        /// <summary>
        /// Delete_CodeItemBound 删除参考范围
        /// </summary>
        /// <param name="codeItemBound"></param>
        /// <returns></returns>
        [HttpPost("Delete_CodeItemBound")]
        public IActionResult Delete_CodeItemBound([FromBody] List<CodeItemBound> codeItemBound)
        {
            string msg = string.Empty;
            result.Success = _basicCodeService.DeleteCodeItemBound(codeItemBound);
            result.ReturnMsg = msg;

            return Ok(result);
        }
        #endregion

        #region 项目结果
        /// <summary>
        /// /CU_CodeItemResult/Create 新增项目结果
        /// /CU_CodeItemResult/Update 更新项目结果
        /// </summary>
        /// <param name="type"></param>
        /// <param name="codeItemResult"></param>
        /// <returns></returns>
        [HttpPost("CU_CodeItemResult/{type}")]
        [UnitOfWork]
        public IActionResult CU_CodeItemResult([FromRoute] string type, [FromBody] CodeItemResult codeItemResult)
        {
            string msg = string.Empty;

            switch (type.ToLower())
            {
                case "create":
                    result.Success = _basicCodeService.CreateCodeItemResult(codeItemResult);
                    break;
                case "update":
                    result.Success = _basicCodeService.UpdateCodeItemResult(codeItemResult);
                    break;
                default:
                    return new BadRequestResult();
            }
            result.ReturnMsg = msg;

            return Ok(result);
        }

        /// <summary>
        /// Read_CodeItemResult  获取项目结果
        /// </summary>
        /// <param name="ItemCode"></param>
        /// <returns></returns>
        [HttpPost("Read_CodeItemResult")]
        public IActionResult Read_CodeItemResult([FromQuery] string ItemCode)
        {
            string msg = string.Empty;
            result.ReturnData = _basicCodeService.ReadCodeItemResult(ItemCode);


            return Ok(result);
        }

        /// <summary>
        /// Delete_CodeItemResult 删除项目结果
        /// </summary>
        /// <param name="codeItemResult"></param>
        /// <returns></returns>
        [HttpPost("Delete_CodeItemResult")]
        [UnitOfWork]
        public IActionResult Delete_CodeItemResult([FromBody] List<CodeItemResult> codeItemResult)
        {
            string msg = string.Empty;

            result.Success = _basicCodeService.DeleteCodeItemResult(codeItemResult);
            result.ReturnMsg = msg;
            return Ok(result);
        }
        #endregion

        #region 项目根据项目分类分组(参考范围/项目结果)
        /// <summary>
        /// 项目根据项目分类分组
        /// </summary>
        /// <returns></returns>
        [HttpPost("ItemGroupByItemCls")]
        public IActionResult ItemGroupByItemCls()
        {
            result.ReturnData = _basicCodeService.ItemGroupByItemCls();


            return Ok(result);
        }
        #endregion

        #region 项目结果禁止规则
        /// <summary>
        /// 获取项目结果禁止规则
        /// </summary>
        /// <param name="itemCode">项目编码</param>
        /// <returns></returns>
        [HttpPost("Read_ItemBanResult")]
        public IActionResult Read_ItemBanResult([FromQuery] string itemCode)
        {
            result.ReturnData = _basicCodeService.ReadItemBanResult(itemCode);


            return Ok(result);
        }

        /// <summary>
        /// /CUD_CodeItemBanResult/Create 新增项目结果禁止规则
        /// /CUD_CodeItemBanResult/Delete 删除项目结果禁止规则
        /// /CUD_CodeItemBanResult/Update 修改项目结果禁止规则
        /// </summary>
        /// <param name="type"></param>
        /// <param name="codeItemBanResults"></param>
        /// <returns></returns>
        [HttpPost("CUD_CodeItemBanResult/{type}")]
        public IActionResult CUD_ItemBanResult([FromRoute] string type, [FromBody] List<CodeItemBanResult> codeItemBanResults)
        {
            string msg = string.Empty;

            switch (type.ToLower())
            {
                case "create":
                    result.Success = _basicCodeService.CreateItemBanResult(codeItemBanResults);
                    break;
                case "delete":
                    result.Success = _basicCodeService.DeleteItemBanResult(codeItemBanResults);
                    break;
                case "update":
                    result.Success = _basicCodeService.UpdateItemBanResult(codeItemBanResults);
                    break;
                default:
                    return new BadRequestResult();
            }

            result.ReturnMsg = msg;
            return Ok(result);
        }
        #endregion

        #region 科研分析模板
        /// <summary>
        /// /CU_ResearchAnalysisTemplate/Create 新增科研分析模板
        /// /CU_ResearchAnalysisTemplate/Update 更新科研分析模板
        /// </summary>
        /// <param name="type"></param>
        /// <param name="temp"></param>
        /// <returns></returns>
        [HttpPost("CU_ResearchAnalysisTemplate/{type}")]
        [UnitOfWork]
        public IActionResult CU_ResearchAnalysisTemplate([FromRoute] string type, [FromBody] CodeResearchAnalysisTemplate temp)
        {
            string msg = string.Empty;
            temp.HospCode = _httpContextUser.HospCode;
            switch (type.ToLower())
            {
                case "create":
                    result.Success = _basicCodeService.CreateCreateResearchAnalysisTemplate(temp);
                    break;
                case "update":
                    result.Success = _basicCodeService.UpdateCreateResearchAnalysisTemplate(temp);
                    break;
                default:
                    return new BadRequestResult();
            }
            result.ReturnMsg = msg;

            return Ok(result);
        }

        /// <summary>
        /// DeleteResearchAnalysisTemplate 删除科研分析模板
        /// </summary>
        /// <param name="tempCodes">模板代码["01","02"]</param>
        /// <returns></returns>
        [HttpPost("DeleteResearchAnalysisTemplate")]
        [UnitOfWork]
        public IActionResult DeleteResearchAnalysisTemplate([FromBody] string[] tempCodes)
        {
            string msg = string.Empty;
            result.Success = _basicCodeService.DeleteResearchAnalysisTemplate(tempCodes);
            return Ok(result);
        }

        /// <summary>
        /// ReadResearchAnalysisTemplate 获取科研分析模板
        /// </summary>
        /// <param name="temp"></param>
        /// <returns></returns>
        [HttpPost("ReadResearchAnalysisTemplate")]
        public IActionResult ReadResearchAnalysisTemplate([FromBody] ResearchTemplate temp)
        {
            result.ReturnData = _basicCodeService.ReadResearchAnalysisTemplate(temp.Creator);
            return Ok(result);
        }
        #endregion
    }
}
