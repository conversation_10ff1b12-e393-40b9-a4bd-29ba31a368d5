﻿using SqlSugar;

namespace Peis.Model.DataBaseEntity
{
    ///<summary>
    ///角色功能权限
    ///</summary>
    [SugarTable("SysRoleFunc")]
    public class SysRoleFunc
    {
        /// <summary>
        /// 角色代码
        /// </summary>        
        [SugarColumn(IsNullable = false, IsPrimaryKey = true, Length = 10)]
        public string RoleCode { get; set; }

        /// <summary>
        /// 菜单代码
        /// </summary>        
        [SugarColumn(IsNullable = false, IsPrimaryKey = true, Length = 50)]
        public string MenuCode { get; set; }

        /// <summary>
        /// 功能代码
        /// </summary>        
        [SugarColumn(IsNullable = false, IsPrimaryKey = true, Length = 50)]
        public string FuncCode { get; set; }
    }
}