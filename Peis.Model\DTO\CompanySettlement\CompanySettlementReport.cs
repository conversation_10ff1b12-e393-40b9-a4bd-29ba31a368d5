﻿namespace Peis.Model.DTO.CompanySettlement
{
    /// <summary>
    /// 结算费用报表
    /// </summary>
    public class CompanySettlementReport
    {
        /// <summary>
        /// 数量合计
        /// </summary>
        public int Count { set; get; }
        /// <summary>
        /// 金额合计
        /// </summary>
        public decimal Price { set; get; }
        /// <summary>
        /// 明细列表
        /// </summary>
        public List<CompanySettlementReportDetail> list { set; get; }
    }
    /// <summary>
    /// 结算费用明细报表
    /// </summary>
    public class CompanySettlementReportDetail
    {
        /// <summary>
        /// 结算序列号
        /// </summary>
        public string BillSeqNo { set; get; }
        /// <summary>
        /// 单位代码
        /// </summary>
        public string CompanyCode { set; get; }
        /// <summary>
        /// 单位名称
        /// </summary>
        public string CompanyName { set; get; }
        /// <summary>
        /// 组合代码
        /// </summary>
        public string CombCode { set; get; }
        /// <summary>
        /// 组合名称
        /// </summary>
        public string CombName { set; get; }
        /// <summary>
        /// 金额
        /// </summary>
        public decimal Price { set; get; }
        /// <summary>
        /// 折扣
        /// </summary>
        public decimal Discount { set; get; }
        /// <summary>
        /// 数量
        /// </summary>
        public int Count { set; get; }
        /// <summary>
        /// 合计金额
        /// </summary>
        public decimal SumPrice { set; get; }
        /// <summary>
        /// 发票号
        /// </summary>
        public string InvoinceNo { set; get; }
        /// <summary>
        /// 结算时间
        /// </summary>
        public DateTime? ChargeTime { set; get; }
    }
}
