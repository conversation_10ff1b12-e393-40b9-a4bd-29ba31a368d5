﻿namespace Peis.Model.DTO.CompanySettlement
{
    /// <summary>
    /// 
    /// </summary>
    public class CombPriceWithBindPrice
    {
        /// <summary>
        /// Id
        /// </summary>
        public long Id { set; get; }
        /// <summary>
        /// 组合代码
        /// </summary>
        public string CombCode { set; get; }
        /// <summary>
        /// 组合名称
        /// </summary>
        public string CombName { set; get; }
        /// <summary>
        /// 项目分类
        /// </summary>
        public string ClsCode { set; get; }
        /// <summary>
        /// 金额
        /// </summary>
        public decimal Price { set; get; }
        /// <summary>
        /// 原始金额
        /// </summary>
        public decimal OriginalPrice { set; get; }
        /// <summary>
        /// 折扣
        /// </summary>
        public decimal Discount { set; get; }
        /// <summary>
        /// 执行科室
        /// </summary>
        public string ExamDeptCode { set; get; }
        [SugarColumn(IsJson = true)]
        public string[] Befrom { get; set; }

        public string RegNo { set; get; }
    }
}
