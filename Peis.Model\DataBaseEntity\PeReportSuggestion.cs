namespace Peis.Model.DataBaseEntity
{
    ///<summary>
    ///体检建议表
    ///</summary>
    [SplitTable(SplitType.Year)]
    [SugarTable("PeReportSuggestion_{yyyy}", "体检建议表")]
    [SugarIndex("index_RegNo_", nameof(RegNo), OrderByType.Asc)]
    public class PeReportSuggestion
    {
        /// <summary>
        /// 建议id
        /// </summary>        
        [SugarColumn(IsNullable = false, IsPrimaryKey = true)]
        public long Id { get; set; }

        /// <summary>
        /// 体检号
        /// </summary>        
        [SugarColumn(IsNullable = false, Length = 12)]
        public string RegNo { get; set; }

        /// <summary>
        /// 疾病建议
        /// </summary>        
        [SugarColumn(IsNullable = false, ColumnDataType = StaticConfig.CodeFirst_BigString)]
        public string Suggestion { get; set; }

        /// <summary>
        /// 建议顺序Id
        /// </summary>        
        [SugarColumn(IsNullable = false)]
        public int SortIndex { get; set; }

        /// <summary>
        /// 体检登记时间（分表依据）
        /// </summary>        
        [SplitField]
        [SugarColumn(IsNullable = false)]
        public DateTime RegisterTime { get; set; }

        /// <summary>
        /// 是否职业病
        /// </summary>
        [SugarColumn(IsNullable = false)]
        public bool IsOccupaiton { get; set; }
    }
}