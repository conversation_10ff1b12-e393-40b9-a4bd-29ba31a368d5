﻿namespace Peis.External.Platform.Service.Service.IService.Report
{
    public interface IExamReportService
    {
        /// <summary>
        /// 检查报告回传
        /// </summary>
        /// <param name="xml"></param>
        /// <param name="respData"></param>
        /// <returns></returns>
        void AcceptReport(string xml, ref string respData);

        /// <summary>
        /// 检查危急值回传
        /// </summary>
        /// <param name="xml"></param>
        /// <param name="respData"></param>
        /// <returns></returns>
        void AcceptExamCritical(string xml, ref string respData);

        /// <summary>
        /// 重新接收结果
        /// </summary>
        /// <param name="applyNo">申请单编号</param>
        void ReacceptReport(string applyNo);
    }
}
