﻿using SqlSugar;
using System;

namespace Peis.Model.DataBaseEntity
{
    ///<summary>
    ///标本打包表
    ///</summary>
    [SugarTable("PeSamplePackage")]
    public class PeSamplePackage
    {
        /// <summary>
        /// 打包号
        /// </summary>        
        [SugarColumn(IsNullable = false, IsPrimaryKey = true, Length = 11)]
        public string PackageNo { get; set; }

        /// <summary>
        /// 打包时间
        /// </summary>        
        [SugarColumn(IsNullable = false)]
        public DateTime PackageTime { get; set; }

        /// <summary>
        /// 打包人
        /// </summary>        
        [SugarColumn(IsNullable = false, Length = 10)]
        public string Packer { get; set; }

        /// <summary>
        /// 包运送标识
        /// </summary>        
        [SugarColumn(IsNullable = false)]
        public bool HasTransport { get; set; }

        /// <summary>
        /// 包运送时间
        /// </summary>        
        [SugarColumn(IsNullable = true)]
        public DateTime? TransportTime { get; set; }

        /// <summary>
        /// 包到达标识
        /// </summary>        
        [SugarColumn(IsNullable = false)]
        public bool IsArrive { get; set; }

        /// <summary>
        /// 包到达时间
        /// </summary>        
        [SugarColumn(IsNullable = true)]
        public DateTime? ArriveTime { get; set; }
    }
}