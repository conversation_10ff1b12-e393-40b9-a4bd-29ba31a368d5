# 请参阅 https://aka.ms/customizecontainer 以了解如何自定义调试容器，以及 Visual Studio 如何使用此 Dockerfile 生成映像以更快地进行调试。

# 此阶段用于在快速模式(默认为调试配置)下从 VS 运行时
FROM mcr.microsoft.com/dotnet/aspnet:6.0 AS base
WORKDIR /app
EXPOSE 80


# 此阶段用于生成服务项目
FROM mcr.microsoft.com/dotnet/sdk:6.0 AS build
ARG BUILD_CONFIGURATION=Release
WORKDIR /src
COPY ["Peis.API/Peis.API.csproj", "Peis.API/"]
COPY ["Peis.External.Hardware.Service/Peis.External.Hardware.Service.csproj", "Peis.External.Hardware.Service/"]
COPY ["Peis.Model/Peis.Model.csproj", "Peis.Model/"]
COPY ["Peis.Utility/Peis.Utility.csproj", "Peis.Utility/"]
COPY ["Peis.Repository/Peis.Repository.csproj", "Peis.Repository/"]
COPY ["Peis.Service/Peis.Service.csproj", "Peis.Service/"]
COPY ["Peis.Quartz.UI/Peis.Quartz.UI.csproj", "Peis.Quartz.UI/"]
COPY ["Peis.External.Platform.Service/Peis.External.Platform.Service.csproj", "Peis.External.Platform.Service/"]
COPY ["Peis.External.Wx.Service/Peis.External.Wx.Service.csproj", "Peis.External.Wx.Service/"]
RUN dotnet restore "./Peis.API/Peis.API.csproj"
COPY . .
WORKDIR "/src/Peis.API"
RUN dotnet build "./Peis.API.csproj" -c $BUILD_CONFIGURATION -o /app/build

# 此阶段用于发布要复制到最终阶段的服务项目
FROM build AS publish
ARG BUILD_CONFIGURATION=Release
RUN dotnet publish "./Peis.API.csproj" -c $BUILD_CONFIGURATION -o /app/publish /p:UseAppHost=false

# 此阶段在生产中使用，或在常规模式下从 VS 运行时使用(在不使用调试配置时为默认值)
FROM base AS final
WORKDIR /app
COPY --from=publish /app/publish .
ENTRYPOINT ["dotnet", "Peis.API.dll"]