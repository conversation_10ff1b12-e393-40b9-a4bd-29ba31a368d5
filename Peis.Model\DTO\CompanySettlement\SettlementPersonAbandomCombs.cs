﻿using Peis.Model.Other.PeEnum;

namespace Peis.Model.DTO.CompanySettlement
{
    public class SettlementPersonAbandomCombs
    {
        /// <summary>
        /// 体检号
        /// </summary>
        public string RegNo { get; set; }
        /// <summary>
        /// 姓名
        /// </summary>
        public string Name { get; set; }
        /// <summary>
        /// 年龄
        /// </summary>
        public int Age { get; set; }
        /// <summary>
        /// 性别
        /// </summary>
        public Sex Sex { get; set; }
        /// <summary>
        /// 弃检组合
        /// </summary>
        public string AbandonCombs { get; set; }
        /// <summary>
        /// 弃检金额
        /// </summary>
        public decimal AbandonPrice { get; set; }
    }
}
