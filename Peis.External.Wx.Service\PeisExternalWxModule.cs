﻿using Autofac;
using Microsoft.Extensions.DependencyInjection;
using Peis.Repository;
using Peis.Service;
using System.Reflection;
using System.Runtime.CompilerServices;
using Volo.Abp;
using Volo.Abp.Modularity;

namespace Peis.External.Wx.Service;

[DependsOn(typeof(PeisRepositoryModule))]
[DependsOn(typeof(PeisServiceModule))]
public class PeisExternalWxModule : AbpModule
{
    public PeisExternalWxModule()
    {
    }

    public override void ConfigureServices(ServiceConfigurationContext context)
    {
        var services = context.Services;
        var builder = services.GetContainerBuilder();

        // 注册自定义服务到Autofac容器中
        var baseTypes = Assembly.GetExecutingAssembly().GetTypes()
                                .Where(x => x.IsClass && !x.IsDefined(typeof(CompilerGeneratedAttribute), false))
                                .ToArray();
        builder.RegisterTypes(baseTypes).AsImplementedInterfaces().InstancePerLifetimeScope();
    }

    public override void OnApplicationInitialization(ApplicationInitializationContext context)
    {

    }
}
