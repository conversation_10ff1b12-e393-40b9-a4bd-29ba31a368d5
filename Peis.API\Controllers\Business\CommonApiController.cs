﻿using Aspose.Words.Fields;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Peis.Model.DTO;
using Peis.Model.DTO.CriticalValue;
using Peis.Service.IService;
using Peis.Service.Service;
using Peis.Utility.CustomAttribute;
using Peis.Utility.Flee;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Reflection;
using System.Threading.Tasks;

namespace Peis.API.Controllers.Business
{
    /// <summary>
    /// 公用API
    /// </summary>
    [Route("api/[controller]")]
    [ApiController]
    public class CommonApiController : BaseApiController
    {
        public readonly ICommonService _commonService;
        private readonly ICriticalValueService _criticalValueService;

        public CommonApiController(
            ICommonService commonService,
            ICriticalValueService criticalValueService)
        {
            _commonService = commonService;
            _criticalValueService = criticalValueService;
        }

        /// <summary>
        /// 上传团体导入文件
        /// </summary>
        /// <param name="files">文件</param>
        /// <returns></returns>
        [HttpPost("UploadTeamImport")]
        [AllowAnonymous]
        public IActionResult UploadTeamImport([FromForm] IFormFileCollection files)
        {
            string msg = string.Empty;
            result.Success = _commonService.UploadTeamImport(files, ref msg);
            result.ReturnMsg = msg;

            return Ok(result);
        }

        /// <summary>
        /// 上传体检人相片
        /// </summary>
        /// <param name="regNo">体检号</param>
        /// <param name="files">文件</param>
        /// <returns></returns>
        [HttpPost("UploadPhoto")]
        [AllowAnonymous]
        public IActionResult UploadPhoto([FromQuery] string regNo, [FromForm] IFormFileCollection files)
        {
            string msg = string.Empty;
            result.Success = _commonService.UploadPhoto(regNo, files, ref msg);
            result.ReturnMsg = msg;

            return Ok(result);
        }

        /// <summary>
        /// 上传体检人相片(新) 不带体检号
        /// </summary>
        /// <param name="files"></param>
        /// <returns></returns>
        [HttpPost("UploadPhotoNew")]
        [AllowAnonymous]
        public IActionResult UploadPhotoNew([FromForm] IFormFileCollection files)
        {
            result.ReturnData = _commonService.UploadPhoto(files);
            result.Success = true;
            return Ok(result);
        }

        /// <summary>
        /// 上传听力测试图
        /// </summary>
        /// <param name="regNo">体检号</param>
        /// <param name="files">文件</param>
        /// <returns></returns>
        [HttpPost("UploadAuditoryPicture")]
        [AllowAnonymous]
        public IActionResult UploadAuditoryPicture([FromQuery] string regNo, [FromForm] IFormFileCollection files)
        {
            string msg = string.Empty;
            result.Success = _commonService.UploadAuditoryPicture(regNo, files, ref msg);
            result.ReturnMsg = msg;

            return Ok(result);
        }
        /// <summary>
        /// 上传签名图片
        /// </summary>
        /// <param name="operatorCode"></param>
        /// <param name="files"></param>
        /// <returns></returns>
        [HttpPost("UploadSignature")]
        [AllowAnonymous]
        public IActionResult UploadSignature([FromQuery]string operatorCode, [FromForm] IFormFileCollection files)
        {
            result.Success = _commonService.UploadSignature(operatorCode, files);
            return Ok(result);
        }
        /// <summary>
        /// 上传检查图文报告
        /// </summary>
        /// <param name="regNo"></param>
        /// <param name="regCombId"></param>
        /// <param name="files"></param>
        /// <returns></returns>
        [HttpPost("UploadReportGraphFile")]
        [AllowAnonymous]
        public IActionResult UploadReportGraphFile([FromQuery] string regNo, [FromQuery] long regCombId, [FromForm] IFormFileCollection files)
        {
            result.Success = _commonService.UploadReportGraphFile(regNo,regCombId, files,out var fileUrl);
            result.ReturnData = fileUrl;
            return Ok(result);
        }
        /// <summary>
        /// 获取用户最近的消息
        /// </summary>
        /// <param name="operCode">用户代码</param>
        /// <param name="lastDays">最近的天数</param>
        /// <returns></returns>
        [HttpPost("GetUserRecentMssage")]
        public IActionResult GetUserRecentMssage(string operCode, int lastDays)
        {
            result.ReturnData = _commonService.GetUserRecentMssage(operCode, lastDays);
            return Ok(result);
        }

        /// <summary>
        /// 获取所有未处理的危急值
        /// </summary>
        /// <returns></returns>
        [ProducesResponseType(typeof(ImportantAbnormalMsg[]), 200)]
        [HttpPost("GetAllUntreatedCriticalValue")]
        public IActionResult GetAllUntreatedCriticalValue()
        {
            result.ReturnData = _criticalValueService.GetAllUntreatedCriticalValue();
            return Ok(result);
        }

        /// <summary>
        /// 运算符及函数列表
        /// </summary>
        /// <returns></returns>
        [HttpPost("OperationAndFunction")]
        public IActionResult OperationAndFunction()
        {
            #region 运算符
            List<object> operList = new()
            {
                new { code = "+", name = "加" },
                new { code = "-", name = "减" },
                new { code = "*", name = "乘" },
                new { code = "/", name = "除" },
                new { code = "(", name = "左括号" },
                new { code = ")", name = "右括号" },
                new { code = "and", name = "逻辑与" },
                new { code = "or", name = "逻辑或" },
                new { code = ">", name = "大于" },
                new { code = "<", name = "小于" }
            };
            #endregion

            #region 函数列表
            List<object> funcList = new List<object>
            {
                new { code = "Cos(x)", name = "余弦" },
                new { code = "decimal.Parse(x)", name = "复合类型转换" }
            };

            // 遍历类中的所有方法
            foreach (MethodInfo method in typeof(CustomFunction).GetMethods(BindingFlags.Public | BindingFlags.Static))
            {
                // 获取方法名
                string methodName = method.Name;

                // 获取方法的描述信息（即注释）
                string methodDesc = method.GetCustomAttribute<System.ComponentModel.DescriptionAttribute>()?.Description ?? "无描述信息";

                // 获取方法的参数信息
                string parameters = string.Join(", ", method.GetParameters().Select(p => $"{p.ParameterType.Name} {p.Name}"));

                funcList.Add(new { methodName = $"{methodName}({parameters})", methodDesc });
            }
            #endregion

            result.ReturnData = new { operList, funcList };
            return Ok(result);
        }

        /// <summary>
        /// 校验计算公式(疾病计算公式/重大阳性计算公式)
        /// </summary>
        /// <param name="expression"></param>
        /// <returns></returns>
        [HttpPost("VerifyExpression")]
        public IActionResult VerifyExpression([FromBody] VerifyExpression expression)
        {
            result.Success = _commonService.VerifyDiseaseExpression(expression);
            return Ok(result);
        }

        /// <summary>
        /// 获取切割的pdf文件
        /// </summary>
        /// <param name="pages"></param>
        /// <param name="files"></param>
        /// <returns></returns>
        [HttpPost("GetSplitPdfByPages")]
        [AllowAnonymous]
        public IActionResult GetSplitPdfByPages([Regex(VerifyIntArray = true, ErrorMessage = "页码数组格式错误！")][FromQuery] string pages, [FromForm] IFormFileCollection files)
        {
            var stream = _commonService.GetSplitPdfByPages(pages, files);
            if (stream is null) return NoContent();
            return File(stream, "application/pdf",
                "splitPdf.pdf");
        }
    }
}
