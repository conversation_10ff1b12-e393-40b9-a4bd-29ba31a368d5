﻿using Microsoft.AspNetCore.Mvc;
using Peis.Model.DTO.WorkloadStatistics;
using Peis.Service.IService;
using System;

namespace Peis.API.Controllers.Statistics
{
    /// <summary>
    /// 工作量统计
    /// </summary>
    [Route("api/[controller]")]
    [ApiController]
    public class WorkloadStatisticsController : BaseApiController
    {
        private readonly IWorkloadStatisticsService _workloadStatisticsService;

        public WorkloadStatisticsController(IWorkloadStatisticsService workloadStatisticsService)
        {
            _workloadStatisticsService = workloadStatisticsService;
        }

        /// <summary>
        /// 获取基础工作量统计(图表)
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        [HttpPost("GetBasicStatistical")]
        [ProducesResponseType(typeof(BasicStatistical), 200)]
        public IActionResult GetBasicStatistical(StatisticalQuery query)
        {
            BasicStatistical data = new();
            string msg = string.Empty;
            result.Success = _workloadStatisticsService.GetBasicStatistical(query, ref data, ref msg);
            result.ReturnMsg = msg;
            result.ReturnData = data;
            return Ok(result);
        }

        /// <summary>
        /// 获取科室压力统计
        /// </summary>
        /// <param name="query">查询条件</param>
        /// <returns></returns>
        [HttpPost("GetDeptPressure")]
        [ProducesResponseType(typeof(DeptPressure), 200)]
        public IActionResult GetDeptPressure(StatisticalQuery query)
        {
            DeptPressure deptPressure = new();
            var msg = string.Empty;
            result.Success = _workloadStatisticsService.GetDeptPressure(query, ref deptPressure, ref msg);
            result.ReturnMsg = msg;
            result.ReturnData = deptPressure;
            return Ok(result);
        }

        /// <summary>
        /// 获取科室工作量
        /// </summary>
        /// <param name="query">查询条件</param>
        /// <returns></returns>
        [HttpPost("GetDeptWorkload")]
        [ProducesResponseType(typeof(DeptWorkload), 200)]
        public IActionResult GetDeptWorkload(StatisticalQuery query)
        {
            DeptWorkload deptWorkload = new();
            var msg = string.Empty;
            result.Success = _workloadStatisticsService.GetDeptWorkload(query, ref deptWorkload, ref msg);
            result.ReturnMsg = msg;
            result.ReturnData = deptWorkload;
            return Ok(result);
        }

        /// <summary>
        /// 获取医生工作量
        /// </summary>
        /// <param name="query">查询条件</param>
        /// <returns></returns>
        [HttpPost("GetDoctorWorkload")]
        [ProducesResponseType(typeof(DoctorWorkload), 200)]
        public IActionResult GetDoctorWorkload(StatisticalQuery query)
        {
            DoctorWorkload doctorWorkload = new();
            var msg = string.Empty;
            result.Success = _workloadStatisticsService.GetDoctorWorkload(query, ref doctorWorkload, ref msg);
            result.ReturnMsg = msg;
            result.ReturnData = doctorWorkload;
            return Ok(result);
        }

        /// <summary>
        /// 获取登记员工作量
        /// </summary>
        /// <param name="query">查询条件</param>
        /// <returns></returns>
        [HttpPost("GetRegistrarWorkload")]
        [ProducesResponseType(typeof(RegistrarWorkload), 200)]
        public IActionResult GetRegistrarWorkload(StatisticalQuery query)
        {
            RegistrarWorkload registrarWorkload = new();
            var msg = string.Empty;
            result.Success = _workloadStatisticsService.GetRegistrarWorkload(query, ref registrarWorkload, ref msg);
            result.ReturnMsg = msg;
            result.ReturnData = registrarWorkload;
            return Ok(result);
        }

        /// <summary>
        /// 获取开单医生工作量
        /// </summary>
        /// <param name="query">查询条件</param>
        /// <returns></returns>
        [HttpPost("GetApplicantWorkload")]
        [ProducesResponseType(typeof(ApplicantWorkload), 200)]
        public IActionResult GetApplicantWorkload(StatisticalQuery query)
        {
            ApplicantWorkload applicantWorkload = new();
            var msg = string.Empty;
            result.Success = _workloadStatisticsService.GetApplicantWorkload(query, ref applicantWorkload, ref msg);
            result.ReturnMsg = msg;
            result.ReturnData = applicantWorkload;
            return Ok(result);
        }

        /// <summary>
        /// 获取采集员工作量
        /// </summary>
        /// <param name="query">查询条件</param>
        /// <returns></returns>
        [HttpPost("GetGatherOperatorWorkload")]
        [ProducesResponseType(typeof(GatherOperatorWorkload), 200)]
        public IActionResult GetGatherOperatorWorkload(StatisticalQuery query)
        {
            GatherOperatorWorkload gatherOperatorWorkload = new();
            var msg = string.Empty;
            result.Success = _workloadStatisticsService.GetGatherOperatorWorkload(query, ref gatherOperatorWorkload, ref msg);
            result.ReturnMsg = msg;
            result.ReturnData = gatherOperatorWorkload;
            return Ok(result);
        }

        /// <summary>
        /// 加项工作量报表
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        [HttpPost("AddItemWorkloadReport")]
        [ProducesResponseType(typeof(AddItemAndActivateReport), 200)]
        public IActionResult AddItemWorkloadReport([FromBody] StatisticalQuery query)
        {
            AddItemAndActivateReport workloadReport = new();
            var msg = string.Empty;
            result.Success = _workloadStatisticsService.AddItemWorkloadReport(query, ref workloadReport, ref msg);
            result.ReturnMsg = msg;
            result.ReturnData = workloadReport;
            return Ok(result);
        }

        /// <summary>
        /// 激活工作量报表
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        [HttpPost("ActivateWorkloadReport")]
        [ProducesResponseType(typeof(AddItemAndActivateReport), 200)]
        public IActionResult ActivateWorkloadReport([FromBody] StatisticalQuery query)
        {
            AddItemAndActivateReport workloadReport = new();
            var msg = string.Empty;
            result.Success = _workloadStatisticsService.ActivateWorkloadReport(query, ref workloadReport, ref msg);
            result.ReturnMsg = msg;
            result.ReturnData = workloadReport;
            return Ok(result);
        }

        /// <summary>
        /// 录入员工作量报表
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        [HttpPost("InputDoctorWorkloadReport")]
        [ProducesResponseType(typeof(InputDoctorReport), 200)]
        public IActionResult InputDoctorWorkloadReport([FromBody] StatisticalQuery query)
        {
            InputDoctorReport workloadReport = new();
            var msg = string.Empty;
            result.Success = _workloadStatisticsService.InputDoctorWorkloadReport(query, ref workloadReport, ref msg);
            result.ReturnMsg = msg;
            result.ReturnData = workloadReport;
            return Ok(result);
        }

        /// <summary>
        /// 主检员工作量报表(汇总)
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        [HttpPost("CheckDoctorWorkloadReport")]
        [ProducesResponseType(typeof(CheckAndAuditReport), 200)]
        public IActionResult CheckDoctorWorkloadReport([FromBody] StatisticalQuery query)
        {
            CheckAndAuditReport workloadReport = new();
            var msg = string.Empty;
            result.Success = _workloadStatisticsService.CheckDoctorWorkloadReport(query, ref workloadReport, ref msg);
            result.ReturnMsg = msg;
            result.ReturnData = workloadReport;
            return Ok(result);
        }

        /// <summary>
        /// 主检员工作量报表(明细)
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        [HttpPost("CheckDoctorWorkloadDetailReport")]
        [ProducesResponseType(typeof(CheckAndAuditDetailReport[]), 200)]
        public IActionResult CheckDoctorWorkloadDetailReport([FromBody] StatisticalQuery query)
        {
            var workloadReport = Array.Empty<CheckAndAuditDetailReport>();
            var msg = string.Empty;
            result.Success = _workloadStatisticsService.CheckDoctorWorkloadDetailReport(query, ref workloadReport, ref msg);
            result.ReturnMsg = msg;
            result.ReturnData = workloadReport;
            return Ok(result);
        }

        /// <summary>
        /// 审核员工作量报表
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        [HttpPost("AuditDoctorWorkloadReport")]
        [ProducesResponseType(typeof(CheckAndAuditReport), 200)]
        public IActionResult AuditDoctorWorkloadReport([FromBody] StatisticalQuery query)
        {
            CheckAndAuditReport workloadReport = new();
            var msg = string.Empty;
            result.Success = _workloadStatisticsService.AuditDoctorWorkloadReport(query, ref workloadReport, ref msg);
            result.ReturnMsg = msg;
            result.ReturnData = workloadReport;
            return Ok(result);
        }
    }
}