﻿namespace Peis.Model.DataBaseEntity;

///<summary>
///互斥组合设置（同属一个互斥代码中的组合相斥）
///</summary>
[SugarTable("CodeMutexComb")]
public class CodeMutexComb
{
    /// <summary>
    /// 互斥代码（同属一个互斥代码中的组合相斥）
    /// </summary>        
    [SugarColumn(IsNullable = false, IsPrimaryKey = true, Length = 4)]
    public string MutexCode { get; set; }

    /// <summary>
    /// 互斥名称
    /// </summary>        
    [SugarColumn(IsNullable = false, Length = 20)]
    public string MutexName { get; set; }

    /// <summary>
    /// 组合代码
    /// </summary>        
    [SugarColumn(IsNullable = false, IsPrimaryKey = true, Length = 8)]
    public string CombCode { get; set; }

    /// <summary>
    /// 组合名称
    /// </summary>        
    [SugarColumn(IsNullable = false, Length = 200)]
    public string CombName { get; set; }

    /// <summary>
    /// 是否为主组合（用于互斥时，保留主组合）：1-是，0-否
    /// </summary>        
    [SugarColumn(IsNullable = false)]
    public bool IsPrimary { get; set; }
}