﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Peis.Model.DTO;
using Peis.Model.DTO.Sample;
using Peis.Model.Other.Input;
using Peis.Service.IService;
using System;
using System.Collections.Generic;

namespace Peis.API.Controllers.Business
{
    /// <summary>
    /// 标本采集打包服务
    /// </summary>
    [Route("api/[controller]")]
    [ApiController]
    public class SampleController : BaseApiController
    {
        private readonly ISampleService _sampleService;

        public SampleController(ISampleService sampleService)
        {
            _sampleService = sampleService;
        }

        /// <summary>
        /// 试管类别列表
        /// </summary>
        /// <returns></returns>
        [HttpPost("TestTubeCategoryList")]
        [ProducesResponseType(typeof(TestTubeCategory[]), 200)]
        public IActionResult TestTubeCategoryList()
        {
            result.ReturnData = _sampleService.TestTubeCategoryList();
            return Ok(result);
        }

        /// <summary>
        /// 读取标本数据
        /// </summary>
        /// <param name="filters">采集配管查询</param>
        /// <returns></returns>
        [HttpPost("ReadSampleData")]
        [ProducesResponseType(typeof(List<SampleData>), 200)]
        public IActionResult ReadSampleData([FromBody] SampleMultipleFilters filters)
        {
            result.ReturnData = _sampleService.ReadSampleData(filters);
            return Ok(result);
        }

        /// <summary>
        /// 标本配管详情
        /// </summary>
        /// <param name="sampDataQuery"></param>
        /// <returns></returns>
        [HttpPost("ReadSampleDetail")]
        [ProducesResponseType(typeof(SampleDetails[]), 200)]
        public IActionResult ReadSampleDetail([FromBody] List<SampDataQuery> sampDataQuery)
        {
            result.ReturnData = _sampleService.ReadSampleDetail(sampDataQuery);
            return Ok(result);
        }

        /// <summary>
        /// 标本条码详情
        /// </summary>
        /// <param name="sampleNos">条码号数组：["0156489965","0256456536"]</param>
        /// <returns></returns>
        [HttpPost("ReadSampleBarcodeDetails")]
        [ProducesResponseType(typeof(SampleBarcodeDetails[]), 200)]
        public IActionResult ReadSampleBarcodeDetails([FromBody] string[] sampleNos)
        {
            result.ReturnData = _sampleService.ReadSampleBarcodeDetail(sampleNos);
            return Ok(result);
        }

        /// <summary>
        /// 创建标本条码号
        /// </summary>
        /// <returns></returns>
        [HttpPost("CreateSampleNo")]
        public IActionResult CreateSampleNo()
        {
            result.ReturnData = _sampleService.CreateSampleNo();
            return Ok(result);
        }

        /// <summary>
        /// 标本绑定条码(预制条码配管)
        /// </summary>
        /// <param name="sampleBarCode"></param>
        /// <returns></returns>
        [HttpPost("PrefabricatedBarcode")]
        public IActionResult PrefabricatedBarcode([FromBody] SampleBarCode sampleBarCode)
        {
            string msg = string.Empty;

            var flag = _sampleService.SampleBindBarCode(sampleBarCode, ref msg);
            if (!flag)
            {
                result.Success = false;
                result.ReturnMsg = msg;
            }

            return Ok(result);
        }

        /// <summary>
        /// 标本绑定条码(系统生成条码配管)
        /// </summary>
        /// <param name="sampleBarCode">标本绑定条码</param>
        /// <returns></returns>
        [HttpPost("GeneratBarcodeBySystem")]
        public IActionResult GeneratBarcodeBySystem([FromBody] GeneratBarcode sampleBarCode)
        {
            string sampleNo = string.Empty;//返回给前端用于打印
            string msg = string.Empty;

            var flag = _sampleService.SampleBindBarCode(sampleBarCode, ref sampleNo, ref msg);
            if (!flag)
            {
                result.Success = false;
                result.ReturnMsg = msg;
            }
            result.ReturnData = sampleNo;
            return Ok(result);
        }

        /// <summary>
        /// 取消标本条码(取消配管)
        /// </summary>
        /// <param name="sampleNos">条码号数组["0100252522","0253645685"]</param>
        /// <returns></returns>
        [HttpPost("CancelSampleBarCode")]
        public IActionResult CancelSampleBarCode([FromBody] string[] sampleNos)
        {
            string msg = string.Empty;

            var flag = _sampleService.CancelSampleBarCode(sampleNos, ref msg);
            if (!flag)
            {
                result.Success = false;
                result.ReturnMsg = msg;
            }

            return Ok(result);
        }

        /// <summary>
        /// 标本运送：按条件查询未打包标本信息
        /// </summary>
        /// <param name="sampleUnpackedQuery">未打包标本查询</param>
        /// <returns></returns>
        [HttpPost("ReadUnPackedSample")]
        [ProducesResponseType(typeof(UnPackedSample[]), 200)]
        public IActionResult ReadUnPackedSample([FromBody] SampleUnpackedQuery sampleUnpackedQuery)
        {
            result.ReturnData = _sampleService.ReadUnPackedSample(sampleUnpackedQuery);
            return Ok(result);
        }

        /// <summary>
        /// 标本运送：条码号获取未打包标本信息
        /// </summary>
        /// <param name="sampleNo">条码号</param>
        /// <returns></returns>
        [HttpPost("ReadUnPackedSampleBySampleNo")]
        [ProducesResponseType(typeof(UnPackedSample), 200)]
        public IActionResult ReadUnPackedSampleBySampleNo([FromQuery] string sampleNo)
        {
            result.ReturnData = _sampleService.ReadUnPackedSample(sampleNo);
            return Ok(result);
        }

        /// <summary>
        /// 标本运送：打包运送
        /// </summary>
        /// <param name="samplePackTransport">标本打包运送</param>
        /// <returns></returns>
        [HttpPost("PackTransportSample")]
        public IActionResult PackTransportSample([FromBody] SamplePackTransport samplePackTransport)
        {
            var msg = string.Empty;

            result.ReturnData = _sampleService.PackTransportSample(samplePackTransport, ref msg);
            result.ReturnMsg = msg;
            return Ok(result);
        }

        /// <summary>
        /// 标本运送：条码号获取取条码号所在包
        /// </summary>
        /// <param name="sampleNo">条码号</param>
        /// <returns></returns>
        [HttpPost("ReadPackageBySampleNo")]
        [ProducesResponseType(typeof(SamplePackage), 200)]
        public IActionResult ReadPackageBySampleNo([FromQuery] string sampleNo)
        {
            result.ReturnData = _sampleService.ReadPackageBySampleNo(sampleNo);
            return Ok(result);
        }

        /// <summary>
        /// 标本运送：获取标本包列表
        /// </summary>
        /// <param name="beginPackageTime">开始打包时间</param>
        /// <param name="endPackageTime">结束打包时间</param>
        /// <param name="sampleCode">标本分类编码</param>
        /// <returns></returns>
        [HttpPost("ReadSamplePackage")]
        [ProducesResponseType(typeof(SamplePackage[]), 200)]
        public IActionResult ReadSamplePackage([FromQuery] DateTime beginPackageTime, [FromQuery] DateTime endPackageTime, [FromQuery] string sampleCode)
        {
            result.ReturnData = _sampleService.ReadSamplePackage(beginPackageTime, endPackageTime, sampleCode);
            return Ok(result);
        }

        /// <summary>
        /// 标本运送：获取包标本编码与打包时间
        /// </summary>
        /// <param name="packageNo">包号</param>
        /// <returns></returns>
        [HttpPost("ReadSamplePackageTime")]
        [ProducesResponseType(typeof(SamplePackageTime), 200)]
        public IActionResult ReadSamplePackageTime([FromQuery] string packageNo)
        {
            result.ReturnData = _sampleService.ReadSamplePackageTime(packageNo);
            return Ok(result);
        }

        /// <summary>
        /// 标本运送：删除包信息
        /// </summary>
        /// <param name="packageNo">包号</param>
        /// <returns></returns>
        [HttpPost("DeletePackage")]
        public IActionResult DeletePackage([FromQuery] string packageNo)
        {
            var msg = string.Empty;

            result.Success = _sampleService.DeletePackage(packageNo, ref msg);
            result.ReturnMsg = msg;
            return Ok(result);
        }

        /// <summary>
        /// 标本运送：根据包号获取标本列表
        /// </summary>
        /// <param name="packageNo">包号</param>
        /// <returns></returns>
        [HttpPost("ReadSampleByPackageNo")]
        [ProducesResponseType(typeof(PackageData[]), 200)]
        public IActionResult ReadSampleByPackageNo([FromQuery] string packageNo)
        {
            result.ReturnData = _sampleService.ReadDataByPackageNo(packageNo);
            return Ok(result);
        }

        /// <summary>
        /// 标本运送：删除包标本
        /// </summary>
        /// <param name="sampleNo">条码号</param>
        /// <returns></returns>
        [HttpPost("DeletePackageSample")]
        public IActionResult DeletePackageSample([FromQuery] string sampleNo)
        {
            var msg = string.Empty;

            result.Success = _sampleService.DeletePackageSample(sampleNo, ref msg);
            result.ReturnMsg = msg;
            return Ok(result);
        }

        /// <summary>
        /// 标本打印时间更新
        /// </summary>
        /// <param name="sampleNo">条码号</param>
        /// <returns></returns>
        [HttpPost("PrintSample")]
        public IActionResult PrintSample([FromQuery] string sampleNo)
        {
            var msg = string.Empty;
            result.Success = _sampleService.PrintSample(sampleNo, ref msg);
            result.ReturnMsg = msg;
            return Ok(result);
        }

        /// <summary>
        /// 标本采集状态更新
        /// </summary>
        /// <param name="sampleNo">条码号</param>
        /// <returns></returns>
        [HttpPost("GatherSample")]
        public IActionResult GatherSample([FromQuery] string sampleNo)
        {
            var msg = string.Empty;
            object info = null;
            result.Success = _sampleService.GatherSample(sampleNo, ref info, ref msg);
            result.ReturnData = info;
            result.ReturnMsg = msg;
            return Ok(result);
        }

        /// <summary>
        /// 取消标本采集
        /// </summary>
        /// <param name="sampleNo"></param>
        /// <returns></returns>
        [HttpPost("CancelGatherSample")]
        public IActionResult CancelGatherSample([FromQuery] string sampleNo)
        {
            var msg = string.Empty;
            object info = null;
            result.Success = _sampleService.CancelGatherSample(sampleNo, ref msg);
            result.ReturnData = info;
            result.ReturnMsg = msg;
            return Ok(result);
        }

        /// <summary>
        /// 获取标本采集列表
        /// </summary>
        /// <param name="filters"></param>
        /// <returns></returns>
        [HttpPost("ReadGatherSampleData")]
        [ProducesResponseType(typeof(List<SampleData>), 200)]
        public IActionResult ReadGatherSampleData([FromBody] SampleMultipleFilters filters)
        {
            result.ReturnData = _sampleService.ReadGatherSampleData(filters);
            return Ok(result);
        }
    }
}
