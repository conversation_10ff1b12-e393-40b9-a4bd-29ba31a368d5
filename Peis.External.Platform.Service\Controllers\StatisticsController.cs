﻿using Microsoft.AspNetCore.Mvc;
using Peis.External.Platform.Service.Service.IService.Statistics;
using Peis.Model.DTO.External.His;
using Peis.Model.DTO.FinancialStatistics;
using Peis.Model.DTO.PackageStatisticsReport;
using Peis.Service.IService.ExternalSystem;

namespace Peis.External.Platform.Service.Controllers
{
    /// <summary>
    /// 深汕报表控制器
    /// </summary>
    [Route("api/ShenShan/[controller]")]
    [ApiController]
    public class StatisticsController : BaseApiController
    {
        private readonly IStatisticsService _statisticsService;
        public StatisticsController(IStatisticsService statisticsService)
        {
            _statisticsService = statisticsService;
        }


        /// <summary>
        /// 获取收费信息列表
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        [HttpPost("HisBillListQuery")]
        [ProducesResponseType(typeof(PersonnelFeeList), 200)]
        public IActionResult HisBillListQuery([FromBody] HisBillListQuery query)
        {
            PersonnelFeeList data = new();
            var msg = string.Empty;
            result.Success = _statisticsService.HisBillListQuery(query, ref data, ref msg);
            result.ReturnData = data;
            result.ReturnMsg = msg;
            return Ok(result);
        }

        /// <summary>
        /// 获取个人收费日结组合统计报表
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        [HttpPost("GetPersonalSettlementCombReport")]
        [ProducesResponseType(typeof(PersonalSettlementCombReportDto), 200)]
        public IActionResult GetPersonalSettlementCombReport([FromBody] PersonalSettlementCombReportQuery query)
        {
            PersonalSettlementCombReportDto d = _statisticsService.GetPersonalSettlementCombReport(query, out bool flag, out string msg);
            result.Success = flag;
            result.ReturnData = d;
            result.ReturnMsg = msg;
            return Ok(result);
        }
    }
}
