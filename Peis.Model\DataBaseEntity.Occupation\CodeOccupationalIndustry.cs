﻿namespace Peis.Model.DataBaseEntity.Occupation
{
    /// <summary>
    /// 行业分类
    /// </summary>
    [SugarTable]
    public class CodeOccupationalIndustry
    {
        /// <summary>
        /// 行业分类代码
        /// </summary>
        [SugarColumn(Length =10,IsPrimaryKey =true)]
        public string IndustryCode { get; set; }
        /// <summary>
        /// 行业分类名称
        /// </summary>
        [SugarColumn(Length =255,IsNullable =false)]
        public string IndustryName { get; set; }
    }
}
