﻿using Microsoft.AspNetCore.Mvc;
using Peis.Model.DTO.SystemParameters;
using Peis.Service.IService;

namespace Peis.API.Controllers.PermissionSetting
{
    /// <summary>
    /// 系统参数
    /// </summary>
    [Route("api/[controller]")]
    [ApiController]
    public class SystemParamController : BaseApiController
    {
        private readonly ISystemParameterService _systemParameterService;

        public SystemParamController(ISystemParameterService systemParameterService)
        {
            _systemParameterService = systemParameterService;
        }

        /// <summary>
        /// 获取系统参数
        /// </summary>
        /// <returns></returns>
        [ProducesResponseType(typeof(SystemParamGroup[]), 200)]
        [HttpPost("ReadSystemParameterList")]
        public IActionResult ReadSystemParameterList()
        {
            result.ReturnData = _systemParameterService.ReadParams();
            return Ok(result);
        }

        /// <summary>
        /// 更新系统参数
        /// </summary>
        /// <returns></returns>
        [HttpPost("UpdateSystemParameter")]
        public IActionResult SaveSystemParameter(SystemParameterUpdate newParam)
        {
            _systemParameterService.SaveParamValue(newParam);
            return Ok(result);
        }
    }
}
