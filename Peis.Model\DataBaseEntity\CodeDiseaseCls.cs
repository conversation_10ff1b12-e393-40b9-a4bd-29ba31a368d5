﻿namespace Peis.Model.DataBaseEntity;

///<summary>
///疾病分类信息
///</summary>
[SugarTable("CodeDiseaseCls")]
public class CodeDiseaseCls
{
    /// <summary>
    /// 疾病分类代码
    /// </summary>        
    [SugarColumn(IsNullable = false, IsPrimaryKey = true, Length = 6)]
    public string DiseaseClsCode { get; set; }

    /// <summary>
    /// 疾病分类名称
    /// </summary>        
    [SugarColumn(IsNullable = false, Length = 255)]
    [Required(ErrorMessage = "疾病分类名称不能为空")]
    [MaxLength(255, ErrorMessage = "疾病分类名称长度不能超过255")]
    public string DiseaseClsName { get; set; }

    /// <summary>
    /// 建议/健康教育和对策-用于出报告内容
    /// </summary>        
    [SugarColumn(IsNullable = true, ColumnDataType = StaticConfig.CodeFirst_BigString)]
    public string Suggestion { get; set; }

    /// <summary>
    /// 科普-用于出报告内容
    /// </summary>        
    [SugarColumn(IsNullable = true, ColumnDataType = StaticConfig.CodeFirst_BigString)]
    public string PopularScience { get; set; }

    /// <summary>
    /// 显示顺序
    /// </summary>        
    [SugarColumn(IsNullable = false)]
    public int SortIndex { get; set; }

    /// <summary>
    /// 是否参与异常分析报告前几排行
    /// </summary>        
    [SugarColumn(IsNullable = false)]
    public bool IsEnableTop { get; set; }
}