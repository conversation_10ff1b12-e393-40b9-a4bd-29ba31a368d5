﻿using Peis.Model.Other.PeEnum;
using SqlSugar;

namespace Peis.Model.DataBaseEntity
{
    ///<summary>
    ///费用分类分组与费用分类对应信息（收费类代码）
    ///</summary>
    [SugarTable("MapFeeClsGroupFeeCls")]
    public class MapFeeClsGroupFeeCls
    {
        /// <summary>
        /// 费用分类分组代码
        /// </summary>        
        [SugarColumn(IsNullable = false, IsPrimaryKey = true, Length = 10)]
        public string FeeClsGroupCode { get; set; }

        /// <summary>
        /// 费用分类分组名称
        /// </summary>        
        [SugarColumn(IsNullable = false, Length = 20)]
        public string FeeClsGroupName { get; set; }

        /// <summary>
        /// 费用分类分组类型
        /// </summary>        
        [SugarColumn(IsNullable = false)]
        public FeeClsGroupType FeeClsGroupType { get; set; }

        /// <summary>
        /// 费用分类代码
        /// </summary>        
        [SugarColumn(IsNullable = false, IsPrimaryKey = true, Length = 10)]
        public string FeeClsCode { get; set; }

        /// <summary>
        /// 费用分类名称
        /// </summary>        
        [SugarColumn(IsNullable = false, Length = 40)]
        public string FeeClsName { get; set; }
    }
}