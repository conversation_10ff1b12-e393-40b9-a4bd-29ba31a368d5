﻿namespace Peis.Model.DataBaseEntity
{
    /// <summary>
    /// 报表数据模型
    /// </summary>
    [SugarTable("CodeReportDataModel")]
    public class CodeReportDataModel
    {
        /// <summary>
        /// 数据模型代码
        /// </summary>
        [SugarColumn(IsPrimaryKey = true, Length = 4)]
        public string ModelCode { get; set; }

        /// <summary>
        /// 数据模型名称
        /// </summary>
        [SugarColumn(IsNullable = false, Length = 255)]
        public string ModelName { get; set; }
    }
}
