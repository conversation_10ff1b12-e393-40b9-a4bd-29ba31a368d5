﻿using MediatR;
using Peis.Model.DataBaseEntity.Occupation;
using Peis.Model.DTO;
using Peis.Model.DTO.Register;
using Peis.Model.DTO.Sample;
using Peis.Model.Other.Input;
using Peis.Model.Other.PeEnum;
using Peis.Repository.IRepository;
using Peis.Service.IService;
using Peis.Service.IService.ExternalSystem;
using Peis.Service.IService.Helper;
using Peis.Service.IService.Occupation.BasicCode;
using Peis.Service.Service.MediatR;
using Peis.Utility.PeIM.Core;
using Peis.Utility.PeUser;
using System.Text;
using System.Text.RegularExpressions;

namespace Peis.Service.Service;

/// <summary>
/// 单位服务
/// </summary>
public class CompanyService : ICompanyService
{
    private readonly IPeimClients _peimClients;
    private readonly IHttpContextUser _httpContextUser;
    private readonly ICompanyRepository _companyRepository;
    private readonly IRegisterRepository _registerRepository;
    private readonly IClusterCombRepository _clusterCombRepository;
    private readonly IDataTranRepository _dataTranRepository;
    private readonly IDataRepository<CodeCompany> _codeCompanyRepository;
    private readonly IDataRepository<CodeCompanyTimes> _codeCompanyTimesRepository;
    private readonly IDataRepository<CodeCompanyCluster> _codeCompanyClusterRepositoty;
    private readonly IDataRepository<CodeCompanyCls> _codeCompanyClsRepository;
    private readonly IDataRepository<CodeCompanyDepartment> _codeCompanyDepartmentRepository;
    private readonly IDataRepository<MapCompanyClusterComb> _mapCompanyClusterCombRepository;
    private readonly IDataRepository<MapCompanyClusterCombTestTube> _mapCompanyClusterCombTestTubeRepository;
    private readonly IDataRepository<PeRegisterOccupation> _peRegisterOccupationRepository;
    private readonly IDataRepository<PeRegisterOccupationHazard> _peRegisterOccupationHazardRepository;
    private readonly IDataRepository<MapCompanyPersonCombChargeItem> _mapCompanyPersonCombChargeItemRepository;
    private readonly IDataRepository<CodeCluster> _codeClusterRepository;
    private readonly IDataRepository<CodeMutexComb> _codeMutexCombRepository;

    //团体导入名单
    private readonly IDataRepository<PeRegister> _peRegisterRepository;
    private readonly IDataRepository<PeRegisterCluster> _peRegisterClusterRepository;
    private readonly ISplitTableRepository<PeRegisterComb> _peRegisterCombRepository;
    private readonly IDataRepository<CodeJob> _codeJobRepository;
    private readonly IDataRepository<CodeNativePlace> _codeNativePlaceRepository;
    private readonly IDataRepository<SysOperator> _sysOperatorRepository;
    private readonly INoGeneration _noGeneration;
    private readonly ISystemParameterService _systemParameterService;
    private readonly IExternalSystemCompanySettlementService _companySettlementService;
    private readonly ISampleService _sampleService;
    private readonly IExternalSystemOrderService _externalSystemOrderService;
    private readonly IMediator _mediator;
    private readonly IDataRepository<PeSample> _peSampleRepository;
    private readonly IServiceProvider _serviceProvider;
    private readonly IOccupationalBasicCodeService _occupationalBasicCodeService;
    private readonly ICacheRepository _cacheRepository;
    private readonly ILogBusinessNewService _logBusinessService;
    private readonly IMapper _mapper;

    public CompanyService(
        IPeimClients peimClients,
        IHttpContextUser httpContextUser,
        ICompanyRepository companyRepository,
        IRegisterRepository registerRepository,
        IClusterCombRepository clusterCombRepository,
        IDataTranRepository dataTranRepository,
        IDataRepository<CodeCompany> codeCompanyRepository,
        IDataRepository<CodeCompanyTimes> codeCompanyTimesRepository,
        IDataRepository<CodeCompanyCluster> codeCompanyClusterRepositoty,
        IDataRepository<CodeCompanyCls> codeCompanyClsRespository,
        IDataRepository<CodeCompanyDepartment> codeCompanyDepartmentRepository,
        IDataRepository<MapCompanyClusterComb> mapCompanyClusterCombRepository,
        IDataRepository<PeRegister> peRegisterRepository,
        IDataRepository<PeRegisterCluster> peRegisterClusterRepository,
        ISplitTableRepository<PeRegisterComb> peRegisterCombRepository,
        IDataRepository<CodeJob> codeJobRepository,
        IDataRepository<CodeNativePlace> codeNativePlaceRepository,
        IDataRepository<SysOperator> sysOperatorRepository,
        INoGeneration noGeneration,
        ISystemParameterService systemParameterService,
        IExternalSystemCompanySettlementService companySettlementService,
        ISampleService sampleService,
        IDataRepository<MapCompanyClusterCombTestTube> mapCompanyClusterCombTestTubeRepository,
        IExternalSystemOrderService externalSystemOrderService,
        IMediator mediator,
        IDataRepository<PeSample> peSampleRepository,
        IServiceProvider serviceProvider,
        IOccupationalBasicCodeService occupationalBasicCodeService,
        ICacheRepository cacheRepository,
        IDataRepository<PeRegisterOccupation> peRegisterOccupationRepository,
        IDataRepository<PeRegisterOccupationHazard> peRegisterOccupationHazardRepository,
        ILogBusinessNewService logBusinessService,
        IDataRepository<MapCompanyPersonCombChargeItem> mapCompanyPersonCombChargeItemRepository,
        IMapper mapper,
        IDataRepository<CodeCluster> codeClusterRepository,
        IDataRepository<CodeMutexComb> codeMutexCombRepository)
    {
        _peimClients = peimClients;
        _httpContextUser = httpContextUser;
        _companyRepository = companyRepository;
        _registerRepository = registerRepository;
        _clusterCombRepository = clusterCombRepository;
        _dataTranRepository = dataTranRepository;
        _codeCompanyRepository = codeCompanyRepository;
        _codeCompanyTimesRepository = codeCompanyTimesRepository;
        _codeCompanyClusterRepositoty = codeCompanyClusterRepositoty;
        _codeCompanyClsRepository = codeCompanyClsRespository;
        _codeCompanyDepartmentRepository = codeCompanyDepartmentRepository;
        _mapCompanyClusterCombRepository = mapCompanyClusterCombRepository;
        _peRegisterRepository = peRegisterRepository;
        _peRegisterClusterRepository = peRegisterClusterRepository;
        _peRegisterCombRepository = peRegisterCombRepository;
        _codeJobRepository = codeJobRepository;
        _codeNativePlaceRepository = codeNativePlaceRepository;
        _sysOperatorRepository = sysOperatorRepository;
        _noGeneration = noGeneration;
        _systemParameterService = systemParameterService;
        _companySettlementService = companySettlementService;
        _sampleService = sampleService;
        _mapCompanyClusterCombTestTubeRepository = mapCompanyClusterCombTestTubeRepository;
        _externalSystemOrderService = externalSystemOrderService;
        _mediator = mediator;
        _peSampleRepository = peSampleRepository;
        _serviceProvider = serviceProvider;
        _occupationalBasicCodeService = occupationalBasicCodeService;
        _cacheRepository = cacheRepository;
        _peRegisterOccupationRepository = peRegisterOccupationRepository;
        _peRegisterOccupationHazardRepository = peRegisterOccupationHazardRepository;
        _logBusinessService = logBusinessService;
        _mapCompanyPersonCombChargeItemRepository = mapCompanyPersonCombChargeItemRepository;
        _mapper = mapper;
        _codeClusterRepository = codeClusterRepository;
        _codeMutexCombRepository = codeMutexCombRepository;
    }

    #region 单位分类
    /// <summary>
    /// 获取单位分类及其单位的层级信息
    /// </summary>
    /// <returns></returns>
    public List<CompanyLevel> ReadCompanyLevel()
    {
        return _companyRepository.ReadCompanyLevel();
    }

    /// <summary>
    /// 新增单位分类代码
    /// </summary>
    /// <param name="codeCompanyCls"></param>
    /// <param name="msg"></param>
    /// <returns></returns>
    public bool CreateCodeCompanyCls(CodeCompanyCls codeCompanyCls, ref string msg)
    {
        try
        {
            if (_codeCompanyClsRepository.Any(codeCompanyCls))
            {
                msg = $"编码'{codeCompanyCls.CompanyClsCode}'已存在";
                return false;
            }

            _codeCompanyClsRepository.Insert(codeCompanyCls);
            _logBusinessService.BasicCodeNewLog(codeCompanyCls);
        }
        catch (Exception e)
        {
            msg = $"写入单位分类代码异常：{e.Message}";
            return false;
        }

        return true;
    }

    /// <summary>
    /// 更新单位分类代码
    /// </summary>
    /// <param name="codeCompanyCls"></param>
    /// <param name="msg"></param>
    /// <returns></returns>
    public bool UpdateCodeCompanyCls(CodeCompanyCls codeCompanyCls, ref string msg)
    {
        try
        {
            var oldCodeCompanyCls = _codeCompanyClsRepository.FindInSingleKey(codeCompanyCls.CompanyClsCode);
            if (oldCodeCompanyCls == null)
            {
                msg = $"编码'{codeCompanyCls.CompanyClsCode}'不存在";
                return false;
            }

            _codeCompanyClsRepository.Update(codeCompanyCls);
            _logBusinessService.BasicCodeModifyLog(oldCodeCompanyCls, codeCompanyCls);
        }
        catch (Exception e)
        {
            msg = $"更新单位分类代码异常：{e.Message}";
            return false;
        }

        return true;
    }

    /// <summary>
    /// 删除单位分类代码
    /// </summary>
    /// <param name="codeCompanyCls"></param>
    /// <param name="msg"></param>
    /// <returns></returns>
    public bool DeleteCodeCompanyCls(CodeCompanyCls codeCompanyCls, ref string msg)
    {
        try
        {
            _codeCompanyClsRepository.Delete(codeCompanyCls);
            _logBusinessService.BasicCodeDeleteLog(new List<CodeCompanyCls> { codeCompanyCls });
        }
        catch (Exception e)
        {
            msg = $"删除单位分类代码异常：{e.Message}";
            return false;
        }

        return true;
    }

    /// <summary>
    /// 获取单位分类代码
    /// </summary>
    /// <returns></returns>
    public List<CodeCompanyCls> ReadCodeCompanyCls()
    {
        var list = _codeCompanyClsRepository.FindAll().ToList();
        return list;
    }
    #endregion

    #region 单位信息
    /// <summary>
    /// 新增单位信息
    /// </summary>
    /// <param name="company"></param>
    /// <returns></returns>
    public bool CreateCompany(CodeCompany company)
    {
        //同院区不能存在同名称的单位
        if (_codeCompanyRepository.Any(x => x.CompanyName == company.CompanyName))
        {
            throw new BusinessException($"编码:{company.CompanyCode}/名称:{company.CompanyName}的单位已存在");
        }
        if (!string.IsNullOrEmpty(company.CreditCode))
            if (_codeCompanyRepository.Any(x => x.CreditCode == company.CreditCode))
            {
                throw new BusinessException($"社会信用代码为{company.CreditCode}的单位已存在！");
            }
        _codeCompanyRepository.Insert(company);
        _logBusinessService.BasicCodeNewLog(company);
        CacheHelper.Remove("CodeCompanyDictionary");

        return true;
    }

    /// <summary>
    /// 更新单位信息
    /// </summary>
    /// <param name="company"></param>
    /// <returns></returns>
    public bool UpdateCompany(CodeCompany company)
    {
        var oldCompany = _codeCompanyRepository.FindInSingleKey(company.CompanyCode);
        if (oldCompany == null)
        {
            throw new BusinessException($"编码'{company.CompanyCode}'的单位不存在");
        }
        if (!string.IsNullOrEmpty(company.CreditCode))
            if (_codeCompanyRepository.Any(x => x.CreditCode == company.CreditCode))
            {
                throw new BusinessException($"社会信用代码为{company.CreditCode}的单位已存在！");
            }
        _codeCompanyRepository.Update(company);
        _logBusinessService.BasicCodeModifyLog(oldCompany, company);
        CacheHelper.Remove("CodeCompanyDictionary");
        return true;
    }

    /// <summary>
    /// 删除单位信息
    /// </summary>
    /// <param name="company"></param>
    /// <returns></returns>
    public bool DeleteCompany(CodeCompany company)
    {
        //删除此单位的部门
        _codeCompanyDepartmentRepository.Delete(x => x.CompanyCode == company.CompanyCode);

        //删除属于此单位的单位套餐
        _codeCompanyClusterRepositoty.Delete(x => x.CompanyCode == company.CompanyCode);

        //删除属于此单位的体检次数
        _codeCompanyTimesRepository.Delete(x => x.CompanyCode == company.CompanyCode);

        //取消子单位的父级关联
        _codeCompanyRepository.Update(set => set.Parent == "", colu => colu.CompanyCode == company.CompanyCode);

        //删除单位
        _codeCompanyRepository.Delete(company);
        _logBusinessService.BasicCodeDeleteLog(new List<CodeCompany> { company });
        CacheHelper.Remove("CodeCompanyDictionary");

        return true;
    }

    /// <summary>
    /// 获取单位信息
    /// </summary>
    /// <param name="companyQuery"></param>
    /// <returns></returns>
    public CodeCompany[] ReadCompany(CompanyQuery companyQuery)
    {
        ISugarQueryable<CodeCompany> queryable = null;
        //查单位分类
        if (!string.IsNullOrEmpty(companyQuery.CompanyClsCode))
            queryable = _companyRepository.ReadCompanys().Where(x => x.CompanyClsCode == companyQuery.CompanyClsCode);
        //查单位信息
        if (!string.IsNullOrEmpty(companyQuery.CompanyCode))
            queryable = _companyRepository.ReadCompanys().Where(x => x.CompanyCode == companyQuery.CompanyCode);
        //查子公司
        if (!string.IsNullOrEmpty(companyQuery.Parent))
            queryable = _companyRepository.ReadCompanys().Where(x => x.Parent == companyQuery.Parent);

        if (queryable is null)
            return Array.Empty<CodeCompany>();

        var companies = queryable
        .OrderBy(x => x.SortIndex)
        .OrderBy(x => x.CompanyCode)
        .ToArray();
        if (companies.IsNullOrEmpty()) return companies;

        foreach (var item in companies.Where(x => !x.Parent.IsNullOrWhiteSpace()))
        {
            var comp = _companyRepository.ReadCompany(item.Parent).First();
            item.ParentCompanyName = comp?.CompanyName ?? "";
        }
        var relation = _cacheRepository.DictAddressRelation();
        foreach (var item in companies.Where(x => !x.AddressCode.IsNullOrEmpty()))
        {
            if (relation.ContainsKey(item.AddressCode))
                item.AddreassRelation = relation[item.AddressCode];
        }
        return companies;
    }

    /// <summary>
    /// 获取单位信息及单位次数
    /// </summary>
    /// <param name="query">查询条件</param>
    /// <param name="totalNumber">返回总数</param>
    /// <param name="totalPage">返回总页码</param>
    /// <returns>CodeCompanies</returns>
    public List<CodeCompany> GetCompaniesWithTimes(CompanyQuery query, ref int totalNumber, ref int totalPage)
    {
        CheckHelper.NotNullAndEmpty(query, nameof(query));

        return _companyRepository
            .QueryCompaniesWithTimes(null)
            .WhereIF(!query.Keyword.IsNullOrEmpty(), a => a.CompanyName.Contains(query.Keyword) || a.CompanyCode.Contains(query.Keyword))
            .ToMyPageList(query.PageNumber, query.PageSize, ref totalNumber, ref totalPage);
    }
    #endregion

    #region 单位部门信息
    /// <summary>
    /// 新增单位部门信息
    /// </summary>
    /// <param name="codeCompanyDepartment"></param>
    /// <param name="msg"></param>
    /// <returns></returns>
    public bool CreateCodeCompanyDepartment(CodeCompanyDepartment codeCompanyDepartment, ref string msg)
    {
        try
        {
            if (_codeCompanyDepartmentRepository.Any(x => x.CompanyCode == codeCompanyDepartment.CompanyCode && x.DeptName == codeCompanyDepartment.DeptName))
            {
                msg = $"单位'{codeCompanyDepartment.DeptCode}'已存在部门'{codeCompanyDepartment.DeptName}'";
                return false;
            }

            _codeCompanyDepartmentRepository.Insert(codeCompanyDepartment);
        }
        catch (Exception e)
        {
            msg = $"写入单位部门信息异常：{e.Message}";
            return false;
        }

        return true;
    }

    /// <summary>
    /// 更新单位部门信息
    /// </summary>
    /// <param name="codeCompanyDepartment"></param>
    /// <param name="msg"></param>
    /// <returns></returns>
    public bool UpdateCodeCompanyDepartment(CodeCompanyDepartment codeCompanyDepartment, ref string msg)
    {
        try
        {
            if (!_codeCompanyDepartmentRepository.Any(codeCompanyDepartment))
            {
                msg = $"编码'{codeCompanyDepartment.DeptCode}'不存在";
                return false;
            }

            _codeCompanyDepartmentRepository.Update(codeCompanyDepartment);
        }
        catch (Exception e)
        {
            msg = $"更新单位部门信息异常：{e.Message}";
            return false;
        }

        return true;
    }

    /// <summary>
    /// 删除单位部门信息
    /// </summary>
    /// <param name="codeCompanyDepartment"></param>
    /// <param name="msg"></param>
    /// <returns></returns>
    public bool DeleteCodeCompanyDepartment(CodeCompanyDepartment codeCompanyDepartment, ref string msg)
    {
        try
        {
            _codeCompanyDepartmentRepository.Delete(codeCompanyDepartment);
        }
        catch (Exception e)
        {
            msg = $"删除单位部门信息异常：{e.Message}";
            return false;
        }

        return true;
    }

    /// <summary>
    /// 获取单位部门信息
    /// </summary>
    /// <param name="companyCode"></param>
    /// <returns></returns>
    public CodeCompanyDepartment[] ReadCodeCompanyDepartment(string companyCode)
    {
        return _companyRepository.ReadCodeCompanyDepartment(companyCode).ToArray();
    }
    #endregion

    #region 单位次数
    /// <summary>
    /// 新增单位体检次数信息
    /// </summary>
    /// <param name="companyTimes"></param>
    /// <param name="msg"></param>
    /// <returns></returns>
    public bool CreateCompanyTimes(CodeCompanyTimes companyTimes, ref string msg)
    {
        try
        {
            if (_codeCompanyTimesRepository.Any(companyTimes))
            {
                msg = $"编码'{companyTimes.CompanyCode}'的单位次数已存在";
                return false;
            }

            _codeCompanyTimesRepository.Insert(companyTimes);
        }
        catch (Exception e)
        {
            msg = $"写入单位次数信息异常：{e.Message}";
            return false;
        }

        return true;
    }

    /// <summary>
    /// 更新单位体检次数信息
    /// </summary>
    /// <param name="company"></param>
    /// <param name="msg"></param>
    /// <returns></returns>
    public bool UpdateCompanyTimes(CodeCompanyTimes companyTimes, ref string msg)
    {
        try
        {
            if (!_codeCompanyTimesRepository.Any(companyTimes))
            {
                msg = $"编码'{companyTimes.CompanyCode}'的单位次数不存在";
                return false;
            }

            _codeCompanyTimesRepository.Update(companyTimes);
        }
        catch (Exception e)
        {
            msg = $"更新单位次数信息异常：{e.Message}";
            return false;
        }

        return true;
    }

    /// <summary>
    /// 删除单位体检次数信息
    /// </summary>
    /// <param name="companyCode"></param>
    /// <param name="companyTimes"></param>
    /// <param name="msg"></param>
    /// <returns></returns>
    public bool DeleteCompanyTimes(string companyCode, int companyTimes, ref string msg)
    {
        try
        {
            return _codeCompanyTimesRepository.Delete(x => x.CompanyCode == companyCode && x.CompanyTimes == companyTimes);
        }
        catch (Exception e)
        {
            msg = $"删除单位体检次数异常：{e.Message}";
            return false;
        }
    }

    /// <summary>
    /// 获取单位体检次数信息
    /// </summary>
    /// <param name="companyCode"></param>
    /// <param name="companyTimes"></param>
    /// <returns></returns>
    public List<CodeCompanyTimes> ReadCompanyTimes(string companyCode)
    {
        return _companyRepository.ReadCodeCompanyTimes(companyCode)
            .Select(x => new CodeCompanyTimes
            {
                CompanyCode = x.CompanyCode,
                CompanyTimes = x.CompanyTimes,
                BeginDate = x.BeginDate,
                EndDate = x.EndDate,
                OnLineEndDate = x.OnLineEndDate,
                InvoiceHeader = x.InvoiceHeader,
                TaxID = x.TaxID,
                HospContact = x.HospContact,
                CompanyContact = x.CompanyContact,
                OpenBank = x.OpenBank,
                BankAccount = x.BankAccount,
                Tel = x.Tel,
                IsOutside = x.IsOutside,
                Note = x.Note,
            })
            .ToList();
    }

    /// <summary>
    /// 获取体检次数简要信息
    /// </summary>
    /// <param name="companyCode"></param>
    /// <returns></returns>
    public object SimpleCompanyTimes(string companyCode)
    {
        //TODO:返回体检次数的状态 已完成等之类的
        return _companyRepository.ReadCodeCompanyTimes(companyCode)
            .Select(x => new
            {
                x.CompanyCode,
                x.CompanyTimes
            })
            .ToList();
    }
    #endregion

    #region 单位套餐、单位套餐的组合
    /// <summary>
    /// 获取单位套餐概要信息列表
    /// </summary>
    /// <param name="companyCode">单位码</param>
    /// <param name="companyTimes">单位次数</param>
    /// <returns></returns>
    public object ReadCompanyClusSimpleInfos(string companyCode, int companyTimes)
    {
        return _companyRepository.ReadCompanyCluster(companyCode, companyTimes)
                .Select(x => new
                {
                    x.ClusterCode,
                    x.ClusterName,
                    x.Price
                }).ToArray();
    }

    /// <summary>
    /// 获取单位套餐及其明细内容
    /// </summary>
    /// <param name="companyCode">单位码</param>
    /// <param name="companyTimes">单位次数</param>
    /// <returns></returns>
    public List<CodeCompanyCluster> GetCompanyClusWithDetails(string companyCode, int companyTimes)
    {
        var list = _companyRepository.ReadCompanyCluster(companyCode, companyTimes).ToList();
        foreach (var clus in list)
        {
            // 对应组合
            clus.MapCompanyClusterCombs = new();
            var combs = this.GetCompanyCombs(clus.ClusterCode);
            foreach (var comb in combs)
            {
                clus.MapCompanyClusterCombs.Add(new MapCompanyClusterComb
                {
                    ClusterCode = clus.ClusterCode,
                    CombCode = comb.CombCode,
                    CombName = comb.CombName,
                    Price = comb.Price,
                    OriginalPrice = comb.OriginalPrice,
                    Discount = comb.Discount,
                    IsPayBySelf = comb.IsPayBySelf,
                    IsCheckComb = comb.IsCheckComb,
                    Count = comb.Count,
                    BeFrom = comb.BeFrom,
                    Sex = comb.Sex,
                });
            }

            // 职业套餐：危害因素、岗位状态
            if (clus.IsOccupation)
            {
                clus.HazardNames = string.Join("，", _cacheRepository.DictHazardFactor().Where(y => (clus.HazardFactors ?? new()).Contains(y.Key)).Select(y => y.Value.HazardousName));
                clus.JobStatusName = _cacheRepository.DictJobStatus().FirstOrDefault(y => y.Key == clus.JobStatus).Value;
            }
        }

        return list;
    }

    /// <summary>
    /// 复制单位套餐的组合列表
    /// </summary>
    /// <param name="clusterCode"></param>
    /// <returns></returns>
    public object CopyCompanyClusterCombs(bool IsCompany, string clusterCode)
    {
        if (IsCompany)
            return _companyRepository.ReadCompanyClusterBindComb(clusterCode)
                .Select((clusComb, comb) => new CompanyComb
                {
                    CombCode = clusComb.CombCode,
                    CombName = comb.CombName,
                    Price = comb.Price, //默认取最新单价
                    OriginalPrice = comb.Price,
                    Discount = 1,          //默认折扣100%
                    IsPayBySelf = false,       //默认团体项目,
                    IsCheckComb = true,
                    Count = 1,
                })
                .ToArray();

        return _clusterCombRepository.ReadClusterBindComb(false)
            .Where((clusComb, comb) => clusComb.ClusCode == clusterCode)
            .OrderBy((clusComb, comb, cls) => cls.SortIndex)
            .OrderBy((clusComb, comb) => comb.SortIndex)
            .Select((clusComb, comb) => new CompanyComb
            {
                CombCode = clusComb.CombCode,
                CombName = comb.CombName,
                Price = comb.Price, //默认取最新单价
                OriginalPrice = comb.Price,
                Discount = 1,          //默认折扣100%
                IsPayBySelf = false,       //默认团体项目
                IsCheckComb = true,
                Count = 1,
            })
            .ToArray();
    }

    /// <summary>
    /// 获取单位套餐信息及组合列表
    /// </summary>
    /// <param name="clusterCode">套餐码</param>
    /// <returns></returns>
    public CompanyClusterDetail ReadCompanyClusAndCombs(string clusterCode)
    {
        return new CompanyClusterDetail
        {
            CompanyCluster = _companyRepository.ReadCompanyCluster(clusterCode),
            CompanyComb = this.GetCompanyCombs(clusterCode)
        };
    }

    /// <summary>
    /// 获取单位组合列表
    /// </summary>
    /// <param name="clusterCode">套餐码</param>
    /// <returns></returns>
    private CompanyComb[] GetCompanyCombs(string clusterCode)
    {
        var checkComb = _companyRepository.ReadCompanyClusterBindComb(clusterCode, true)
            .Select((clusComb, comb) => new CompanyComb
            {
                ClusterCode = clusComb.ClusterCode,
                CombCode = clusComb.CombCode,
                CombName = SqlFunc.IsNull(comb.CombName, "原组合已失效"),
                Sex = comb.Sex,
                Price = clusComb.Price,
                OriginalPrice = clusComb.OriginalPrice,
                Discount = clusComb.Discount,
                IsPayBySelf = clusComb.IsPayBySelf,
                IsCheckComb = true,
                Count = 1
            })
            .ToArray();
        var tubeComb = _companyRepository.ReadCompanyClusterBindTube(clusterCode)
            .Select((tubeComb, comb) => new CompanyComb
            {
                ClusterCode = tubeComb.ClusterCode,
                CombCode = tubeComb.CombCode,
                CombName = SqlFunc.IsNull(comb.CombName, "原组合已失效"),
                Sex = comb.Sex,
                Price = tubeComb.Price,
                OriginalPrice = tubeComb.OriginalPrice,
                Discount = tubeComb.Discount,
                IsPayBySelf = tubeComb.IsPayBySelf,
                IsCheckComb = false,
                Count = tubeComb.CombCount,
                BeFrom = tubeComb.BeFrom,
            })
            .ToArray();

        return checkComb.Concat(tubeComb).ToArray();
    }

    /// <summary>
    /// 删除单位套餐及组合信息
    /// </summary>
    /// <param name="clusterCode">单位套餐码</param>
    /// <returns></returns>
    public bool DeleteCompanyClusterAndComb(string clusterCode)
    {
        if (string.IsNullOrWhiteSpace(clusterCode))
        {
            throw new BusinessException("参数缺失：删除单位套餐需要指定的单位套餐码");
        }

        //删除单位套餐及组合信息
        _companyRepository.DeleteClusterAndComb(clusterCode);
        _mapCompanyClusterCombTestTubeRepository.Delete(x => x.ClusterCode == clusterCode);
        _logBusinessService.BasicCodeDeleteLog(new List<CodeCompanyCluster> { new CodeCompanyCluster { ClusterCode = clusterCode } });
        CacheHelper.RemoveKeys($"{_httpContextUser.HospCode}-CompanyCluster", $"{_httpContextUser.HospCode}-TestTube");
        return true;
    }

    /// <summary>
    /// 新建单位套餐及单位套餐组合信息
    /// </summary>
    /// <param name="companyClusterDetail">单位套餐及单位套餐组合信息</param>
    /// <returns></returns>
    public bool InsertCompanyClusterAndComb(CompanyClusterDetail companyClusterDetail)
    {
        if (companyClusterDetail.CompanyComb.Length == 0)
        {
            throw new BusinessException("单位套餐的组合列表不能为空");
        }

        companyClusterDetail.CompanyCluster.ClusterCode = _noGeneration.NextCompanyClusterNo(companyClusterDetail.CompanyCluster.CompanyCode);
        companyClusterDetail.CompanyCluster.HospCode = _httpContextUser.HospCode;

        // 检查组合
        var companyCombs = companyClusterDetail.CompanyComb.Where(x => x.IsCheckComb).Select(x => x as MapCompanyClusterComb).ToList();
        companyCombs.ForEach(x => x.ClusterCode = companyClusterDetail.CompanyCluster.ClusterCode);

        // 材料
        var tubes = companyClusterDetail.CompanyComb.Where(x => !x.IsCheckComb)
        .Select(x => new MapCompanyClusterCombTestTube
        {
            ClusterCode = companyClusterDetail.CompanyCluster.ClusterCode,
            CombCode = x.CombCode,
            CombCount = x.Count,
            Discount = x.Discount,
            BeFrom = x.BeFrom,
            IsPayBySelf = x.IsPayBySelf,
            Price = x.Price,
            OriginalPrice = x.OriginalPrice
        }).ToList();


        //写入单位套餐及组合信息
        _companyRepository.InsertClusterAndComb(companyClusterDetail.CompanyCluster, companyCombs);
        _mapCompanyClusterCombTestTubeRepository.Insert(tubes);
        _companySettlementService.SaveCompanyCombChargeItemMap(companyClusterDetail.CompanyCluster.ClusterCode);
        _logBusinessService.BasicCodeNewLog(companyClusterDetail.CompanyCluster);
        CacheHelper.RemoveKeys($"{_httpContextUser.HospCode}-CompanyCluster", $"{_httpContextUser.HospCode}-TestTube");

        return true;
    }

    /// <summary>
    /// 更新单位套餐及单位套餐组合信息
    /// </summary>
    /// <param name="companyClusterDetail">单位套餐及单位套餐组合信息</param>
    /// <param name="msg">错误消息</param>
    /// <returns></returns>
    public bool UpdateCompanyClusterAndComb(CompanyClusterDetail companyClusterDetail)
    {
        var companyCombs = (companyClusterDetail.CompanyComb.Where(x => x.IsCheckComb).ToArray() as MapCompanyClusterComb[]).ToList();

        if (companyCombs.Count == 0)
        {
            throw new BusinessException("单位套餐的组合列表不能为空");
        }

        companyCombs.ForEach(x =>
            x.ClusterCode = companyClusterDetail.CompanyCluster.ClusterCode);
        var tubes = companyClusterDetail.CompanyComb.Where(x => !x.IsCheckComb)
        .Select(x => new MapCompanyClusterCombTestTube
        {
            ClusterCode = companyClusterDetail.CompanyCluster.ClusterCode,
            CombCode = x.CombCode,
            CombCount = x.Count,
            Discount = x.Discount,
            BeFrom = x.BeFrom,
            IsPayBySelf = x.IsPayBySelf,
            Price = x.Price,
            OriginalPrice = x.OriginalPrice,
        }).ToList();
        var oldCluster = _companyRepository.ReadCompanyCluster(companyClusterDetail.CompanyCluster.ClusterCode);
        var oldCombs = _mapCompanyClusterCombRepository.FindAll(x => x.ClusterCode == companyClusterDetail.CompanyCluster.ClusterCode).ToList();
        oldCluster.MapCompanyClusterCombs = oldCombs;
        companyClusterDetail.CompanyCluster.MapCompanyClusterCombs = companyCombs;
        //删除后重新写入单位套餐及组合信息
        _companyRepository.DeleteClusterAndComb(companyClusterDetail.CompanyCluster.ClusterCode);
        _companyRepository.InsertClusterAndComb(companyClusterDetail.CompanyCluster, companyCombs);
        _mapCompanyClusterCombTestTubeRepository.Delete(x => x.ClusterCode == companyClusterDetail.CompanyCluster.ClusterCode);
        _mapCompanyClusterCombTestTubeRepository.Insert(tubes);
        _companySettlementService.SaveCompanyCombChargeItemMap(companyClusterDetail.CompanyCluster.ClusterCode);
        _logBusinessService.BasicCodeModifyLog(oldCluster, companyClusterDetail.CompanyCluster);
        CacheHelper.RemoveKeys($"{_httpContextUser.HospCode}-CompanyCluster", $"{_httpContextUser.HospCode}-TestTube");

        return true;
    }

    /// <summary>
    /// 根据体检次数获取单位套餐(批增加/分配套餐)
    /// </summary>
    /// <param name="companyCode"></param>
    /// <param name="companyTimes"></param>
    /// <returns></returns>
    public CompanyCluster[] GetCompanyCluster(string companyCode, int companyTimes)
    {
        if (string.IsNullOrWhiteSpace(companyCode) || companyTimes == 0)
            return Array.Empty<CompanyCluster>();

        return _companyRepository.ReadCompanyCluster(companyCode, companyTimes)
          .Select(x => new CompanyCluster
          {
              ClusterCode = x.ClusterCode,
              ClusterName = x.ClusterName,
              Price = x.Price
          }).ToArray();
    }

    /// <summary>
    /// 更新团体套餐组合(此套餐下的人登记组合需要修改)
    /// </summary>
    /// <param name="clusterComb"></param>
    /// <param name="msg"></param>
    /// <returns></returns>
    public bool UpdateCompanyCluster(UpdateClusterComb clusterComb, ref string msg)
    {
        // 查询拥有此套餐的人
        var regInfoList = _registerRepository
            .ReadRegister(clusterComb.CompanyCode, clusterComb.CompanyTimes, clusterComb.ClusterCode)
            .Where(x => !x.IsActive)
            .Select(x => new
            {
                x.RegNo,
                x.RegisterTime
            })
            .ToArray();

        // 查询组合的明细
        var combLists = ReadCombDetail();
        if (combLists.Length == 0)
        {
            msg = "套餐的组合不能为空!";
            return false;
        }

        // 人员为空则修改单位套餐的对应
        if (regInfoList.Length == 0)
        {
            _dataTranRepository.ExecTran(() =>
            {
                //删除单位套餐的对应组合
                _mapCompanyClusterCombRepository.Delete(x => x.ClusterCode == clusterComb.ClusterCode);

                //插入单位套餐对应项目表
                var mapCompanyClusterList = combLists.Select(x => new MapCompanyClusterComb
                {
                    ClusterCode = clusterComb.ClusterCode,
                    CombCode = x.CombCode,
                    Price = x.Price,
                    Discount = x.Discount,
                    IsPayBySelf = x.IsPayBySelf
                }).ToList();

                _mapCompanyClusterCombRepository.Insert(mapCompanyClusterList);
            });
            return true;
        }

        var regNoArray = regInfoList.Select(x => x.RegNo).ToArray();

        // 生成登记组合列表
        var peRegisterCombList = regNoArray.AsParallel()
                    .SelectMany(regNo => GeneratePersonConmbList(regNo, combLists))
                    .ToList();

        DateTime startTime = regInfoList.Min(reg => reg.RegisterTime);
        DateTime endTime = regInfoList.Max(reg => reg.RegisterTime);
        _dataTranRepository.ExecTran(() =>
        {
            //对应体检号直接清空登记组合
            _registerRepository.DeleteRegisterComb(startTime, endTime, regNoArray);

            //添加新的组合
            _registerRepository.InsertRegisterComb(peRegisterCombList);

            //删除单位套餐的对应组合
            _mapCompanyClusterCombRepository.Delete(x => x.ClusterCode == clusterComb.ClusterCode);

            //插入单位套餐对应项目表
            var mapCompanyClusterList = combLists.Select(x => new MapCompanyClusterComb
            {
                ClusterCode = clusterComb.ClusterCode,
                CombCode = x.CombCode,
                Price = x.Price,
                Discount = x.Discount,
                IsPayBySelf = x.IsPayBySelf
            }).ToList();

            _mapCompanyClusterCombRepository.Insert(mapCompanyClusterList);
        });
        _mediator.Publish(new SyncPeRegisterOrderHandle.Data(regNoArray));

        return true;

        #region 内部方法
        // 查询同步组合的明细
        PeRegisterComb[] ReadCombDetail()
        {
            return _clusterCombRepository.ReadComb(clusterComb.Combs)
            .Select(comb => new PeRegisterComb
            {
                CombCode = comb.CombCode,
                CombName = comb.CombName,
                ExamDeptCode = comb.ExamDeptCode,
                CheckCls = comb.CheckCls,
                ClsCode = comb.ClsCode,
                ClsName = SqlFunc.Subqueryable<CodeItemCls>()
                                       .Where(x => x.ClsCode == comb.ClsCode)
                                       .Select(x => x.ClsName),
                ReportShow = comb.ReportShow,
                OriginalPrice = comb.Price,
                Price = comb.Price,
                Discount = 1,
                IsPayBySelf = false,
                PayStatus = PayStatus.未收费,
                ApplicantCode = clusterComb.OpratorCode,
                ApplicantName = clusterComb.OpratorName
            }).ToArray();
        }

        // 生成登记组合
        IEnumerable<PeRegisterComb> GeneratePersonConmbList(string regNo, IEnumerable<PeRegisterComb> combTemplate)
        {
            return combTemplate.Select(temp => new PeRegisterComb
            {
                Id = _noGeneration.NextSnowflakeId(),
                RegNo = regNo,
                CombCode = temp.CombCode,
                CombName = temp.CombName,
                ExamDeptCode = temp.ExamDeptCode,
                CheckCls = temp.CheckCls,
                ClsCode = temp.ClsCode,
                ClsName = temp.ClsName,
                ReportShow = temp.ReportShow,
                OriginalPrice = temp.Price,
                Price = temp.Price,
                Discount = temp.Discount,
                IsPayBySelf = temp.IsPayBySelf,
                PayStatus = temp.PayStatus,
                ApplicantCode = temp.ApplicantCode,
                ApplicantName = temp.ApplicantName,
                CreateTime = DateTime.Now,
                RegisterTime = regInfoList.First(x => x.RegNo == regNo).RegisterTime
            });
        }
        #endregion
    }
    #endregion

    #region 团体导入

    /// <summary>
    /// 导入团体登记信息
    /// </summary>
    /// <param name="importRegs"></param>
    /// <param name="msg"></param>
    /// <param name="result"></param>
    /// <returns></returns>
    public bool CompanyImport(List<CompanyImport> importRegs, ref CompanyImportResult result, ref string msg)
    {
        if (importRegs.Count == 0)
        {
            msg = "不能导入空名单";
            return false;
        }

        #region 辅助数据
        var companyCodes = importRegs.Select(sub => sub.CompanyCode).Distinct().ToArray();
        if (companyCodes.Length > 1)
        {
            msg = "名单中不能同时有多个单位！";
            return false;
        }
        var jobNames = importRegs.Select(x => x.JobName).Distinct().ToArray();

        #region 套餐
        //该单位的编码
        var companyCode = companyCodes[0];
        var company = _codeCompanyRepository.First(x => x.CompanyCode == companyCode) ?? throw new BusinessException($"单位代码{companyCode}不存在!");
        //获取套餐信息
        var clusterArray = importRegs.Select(reg => reg.ClusterCode.Trim())
            .Where(x => !string.IsNullOrWhiteSpace(x))
            .Distinct().ToArray();
        var clusterQuery = new Dictionary<string, RegisterClusterDto>();
        if (!clusterArray.IsNullOrEmpty())
        {
            var compClusList = _codeCompanyClusterRepositoty
            .FindAll(x => (SqlFunc.ContainsArray(clusterArray, x.ClusterCode) || SqlFunc.ContainsArray(clusterArray, x.ClusterName)) && x.CompanyCode == company.CompanyCode).ToList();
            foreach (var item in clusterArray)
            {
                // 单位套餐
                var clus = compClusList.FirstOrDefault(x => x.ClusterCode == item || x.ClusterName == item);
                if (clus != null)
                {
                    clusterQuery[item] = _mapper.Map<RegisterClusterDto>(clus);
                    continue;
                }
                // 公共套餐
                var baseClus = _codeClusterRepository.First(x => x.ClusCode == item && x.IsEnabled == true);
                if (baseClus != null)
                {
                    clusterQuery[item] = _mapper.Map<RegisterClusterDto>(baseClus);
                    continue;
                }
            }
            var errorCluster = clusterArray.Where(x => !clusterQuery.ContainsKey(x)).ToArray();
            if (errorCluster.Length > 0)
            {
                msg = "名单中，" + string.Join(",", errorCluster) + "套餐有误，非本单位套餐或者套餐代码不存在！";
                return false;
            }
        }
        #endregion

        //该单位的部门列表
        var _departmentQuery = _codeCompanyDepartmentRepository
            .FindAll(x => x.CompanyCode == companyCode)
            .ToArray();
        var newCompanyDept = importRegs.Select(x => x.CompanyDeptName).Distinct()
            .Where(x => x != string.Empty)
            .Where(x => !_departmentQuery.Any(sub => sub.DeptName == x))
            .Select(x => new CodeCompanyDepartment
            {
                CompanyCode = companyCode,
                DeptCode = _noGeneration.NextCompanyDeptNo()[0],
                DeptName = x,
            }).ToArray();
        if (newCompanyDept.Length > 0)
        {
            _codeCompanyDepartmentRepository.Insert(newCompanyDept);
            _departmentQuery = _departmentQuery.Union(newCompanyDept).ToArray();
        }

        //获取工种对照码
        var _codeJobQuery = _codeJobRepository
            .FindAll(x => SqlFunc.ContainsArray(jobNames, x.JobName))
            .ToArray();

        // 先查询数据库存在的数据
        var cardNoArray = importRegs.Select(x => x.CardNo).ToArray();
        var peRegArray = _registerRepository.ReadRegister(cardNoArray)
                         .Where(x => x.CompanyCode == importRegs[0].CompanyCode
                            && x.CompanyTimes == importRegs[0].CompanyTimes)
                        .ToArray();
        #endregion

        SendProgressMsg(0, "校验数据中...");

        #region 检查错误、去除非内容部分
        foreach (var register in importRegs)
        {
            if (string.IsNullOrEmpty(register.Name) && string.IsNullOrEmpty(register.Sex))
                break;

            if (!CheckErrors(importRegs, register, company, _departmentQuery, peRegArray, result))
                continue;
        }
        #endregion
        result.CorrectList.AddRange(importRegs.Except(result.ErrorList).ToList());
        if (!result.ErrorList.IsNullOrEmpty())
        {
            SendProgressMsg(100, "校验完成");
            return false;
        }

        var has = clusterQuery.Count > 0 || importRegs.Any(x => !string.IsNullOrWhiteSpace(x.Hazards));
        if (has)
            return HasClusterImport(result.CorrectList, clusterQuery, _departmentQuery, _codeJobQuery);
        else
            return NoClusterImport(result.CorrectList, _departmentQuery, _codeJobQuery);
    }

    /// <summary>
    /// 根据单位获取导入的数据
    /// </summary>
    /// <param name="companyInfo"></param>
    /// <param name="totalNumber"></param>
    /// <param name="totalPage"></param>
    /// <returns></returns>
    public List<CompanyRegInfo> GetCompanyRegList(CompanyInfo companyInfo, ref int totalNumber, ref int totalPage)
    {
        var queryable = _companyRepository.GetTeamRegList(companyInfo)
            .LeftJoin<PeRegisterOccupation>((reg, regClust, comp, dept, occ) => occ.RegNo == reg.RegNo)
           .Select((reg, regClust, comp, dept, occ) => new CompanyRegInfo
           {
               DeptName = dept.DeptName,
               RegNo = reg.RegNo,
               Name = reg.Name,
               CardType = reg.CardType,
               CardNo = reg.CardNo,
               Age = reg.Age,
               AgeUnit = reg.AgeUnit,
               Sex = reg.Sex,
               MarryStatus = reg.MarryStatus,
               ClusName = regClust.ClusName,
               IsMain = regClust.IsMain,
               Note = reg.Note,
               Tel = reg.Tel,
               PeStatus = reg.PeStatus,
               JobStatus = occ.JobStatus,
               Hazards = SqlFunc.Subqueryable<PeRegisterOccupationHazard>().Where(x => x.RegNo == reg.RegNo).SelectStringJoin(x => x.HazardousName, ","),
           });

        var regInfos = new List<CompanyRegInfo>();
        if (companyInfo.pageNumber == 0 || companyInfo.pageSize == 0)
            regInfos = queryable.ToList();
        else
            regInfos = queryable.ToPageList(companyInfo.pageNumber, companyInfo.pageSize, ref totalNumber, ref totalPage);

        foreach (var item in regInfos)
            item.AllotStatus = item.IsMain ? "已分配" : "未分配";

        return regInfos;
    }

    /// <summary>
    /// 删除单位导入的数据
    /// </summary>
    /// <param name="regNoArray"></param>
    /// <param name="msg"></param>
    /// <returns></returns>
    public bool DelCompanyRegList(string[] regNoArray, ref string msg)
    {
        if (regNoArray.Length == 0)
        {
            msg = "传入的数据为空!";
            return false;
        }

        // 如果存在已激活的数据则不能删除
        var regList = _registerRepository.ReadRegister()
                      .Where(x => SqlFunc.ContainsArray(regNoArray, x.RegNo))
                      .ToList();

        if (regList.Any(x => x.IsActive))
        {
            msg = "已激活的数据不能删除!";
            return false;
        }

        // 分表依据
        var startTime = regList.Min(x => x.RegisterTime);
        var endTime = regList.Max(x => x.RegisterTime);
        _dataTranRepository.ExecTran(() =>
        {
            _peRegisterCombRepository.SplitTableDelete(startTime, endTime, x => SqlFunc.ContainsArray(regNoArray, x.RegNo));
            _peRegisterClusterRepository.Delete(x => SqlFunc.ContainsArray(regNoArray, x.RegNo));
            _peRegisterRepository.DeleteByIds(regNoArray);
        });

        return true;
    }

    /// <summary>
    /// 分配套餐
    /// </summary>
    /// <param name="clusterArrays"></param>
    /// <param name="msg"></param>
    /// <returns></returns>
    public bool SetCompanyCluster(ClusterArray clusterArrays, ref string msg)
    {
        //查出单位套餐
        var cluster = _codeCompanyClusterRepositoty.First(x => x.CompanyCode == clusterArrays.CompanyCode
                 && x.CompanyTimes == clusterArrays.CompanyTimes && x.ClusterCode == clusterArrays.ClusterCode);
        if (cluster == null)
        {
            msg = "未找到此套餐,无法分配";
            return false;
        }

        //如果是已经分配过套餐的体检号，则过滤掉
        var regNoArray = _registerRepository.ReadRegisterClusters()
            .Where(clus => SqlFunc.ContainsArray(clusterArrays.RegNos, clus.RegNo) && clus.IsMain)
            .Select(clus => clus.RegNo).ToArray();

        //去重/过滤
        var updateArray = clusterArrays.RegNos.Except(regNoArray).ToArray();
        if (updateArray.Length <= 0)
        {
            msg = "选中的数据已存在套餐,无需重复分配";
            return false;
        }

        //组装套餐与登记组合
        var regClusters = SpliceCluster(updateArray, cluster);
        var regCombs = SpliceComb(cluster.ClusterCode, updateArray);
        var regSamples = GetPeSample(regCombs, regClusters);
        var extra = SplicePersonCombChargeItem(cluster.ClusterCode, updateArray);
        var updateRegList = regNoArray.Select(x =>
            new PeRegister
            {
                RegNo = x,
                TotalPrice = cluster.Price
            }
        ).ToList();
        regClusters.ForEach(x => _logBusinessService.RegisterNewLog(x.RegNo, $"团体名单导入：套餐{x.ClusCode}:{x.ClusName}"));

        _dataTranRepository.ExecTran(() =>
        {
            _peRegisterRepository.Update(updateRegList, x => x.TotalPrice);
            _peRegisterCombRepository.SplitTableInsert(regCombs);
            _peRegisterClusterRepository.BulkCopy(regClusters);
            _peRegisterRepository.Update(
                x => new PeRegister { GuidanceType = cluster.GuidanceType, ReportType = cluster.ReportType },
                x => SqlFunc.ContainsArray(updateArray, x.RegNo));
            _peSampleRepository.BulkCopy(regSamples);
            _mapCompanyPersonCombChargeItemRepository.BulkCopy(extra);
            _logBusinessService.SaveLogs();
        });

        return true;
    }

    #region 组装套餐/登记组合
    /// <summary>
    /// 组装套餐
    /// </summary>
    /// <param name="regNos">体检号数组</param>
    /// <param name="cluster">单位套餐</param>
    /// <returns></returns>
    List<PeRegisterCluster> SpliceCluster(string[] regNos, CodeCompanyCluster cluster)
    {
        return regNos.Select(regNo => new PeRegisterCluster
        {
            ClusCode = cluster.ClusterCode,
            ClusName = cluster.ClusterName,
            Price = cluster.Price,
            IsMain = true,
            RegNo = regNo
        }).ToList();
    }

    /// <summary>
    /// 组装登记组合
    /// </summary>
    /// <param name="clusCode">单位套餐编码</param>
    /// <param name="regNos"></param>
    /// <returns></returns>
    List<PeRegisterComb> SpliceComb(string clusCode, string[] regNos)
    {
        var combList = _companyRepository.ReadCompanyClusterCls(clusCode)
            .Select((clusComb, comb, cls) => new PeRegisterComb
            {
                Id = 0,//由于SqlSugar 不支持调用其他方法,只能先查出来，再下方Select中给Id赋值
                CombCode = comb.CombCode,
                CombName = comb.CombName,
                CombSortIndex = comb.SortIndex,
                ExamDeptCode = comb.ExamDeptCode,
                CheckCls = comb.CheckCls,
                ClsCode = comb.ClsCode,
                ClsName = cls.ClsName,
                OriginalPrice = clusComb.OriginalPrice,
                ClsSortIndex = cls.SortIndex,
                Price = clusComb.Price,
                ReportShow = comb.ReportShow,
                Discount = clusComb.Discount,
                IsPayBySelf = clusComb.IsPayBySelf,
            }).ToArray();

        // 查出登记时间赋值，如果填当前时间,跨年查询会有查询bug
        Dictionary<string, PeRegister> regNoDic = _registerRepository.ReadRegister()
                             .Where(reg => SqlFunc.ContainsArray(regNos, reg.RegNo))
                             .ToList()
                             .ToDictionary(x => x.RegNo, x => x);

        var newRegCombs = regNos.SelectMany(regNo =>
        {
            return combList.Select(comb => new PeRegisterComb()
            {
                Id = _noGeneration.NextSnowflakeId(),
                RegNo = regNo,
                CombCode = comb.CombCode,
                CombName = comb.CombName,
                CombSortIndex = comb.CombSortIndex,
                ExamDeptCode = comb.ExamDeptCode,
                CheckCls = comb.CheckCls,
                ClsCode = comb.ClsCode,
                ClsName = comb.ClsName,
                ClsSortIndex = comb.ClsSortIndex,
                OriginalPrice = comb.OriginalPrice,
                Price = comb.Price,
                ReportShow = comb.ReportShow,
                Discount = comb.Discount,
                IsPayBySelf = comb.IsPayBySelf,
                PayStatus = PayStatus.未收费,
                CreateTime = DateTime.Now,
                RegisterTime = regNoDic[regNo].RegisterTime,
                IsOrdinary = regNoDic[regNo].IsOrdinary,
                IsOccupation = regNoDic[regNo].IsOccupation
            });
        }).ToList();
        var tubes = GetRegisterTustTubeCombs(clusCode, newRegCombs);
        newRegCombs.AddRange(tubes);
        return newRegCombs;
    }

    /// <summary>
    /// 组装条码
    /// </summary>
    /// <param name="registerCombs">登记组合</param>
    /// <returns></returns>
    List<PeSample> GetPeSample(List<PeRegisterComb> registerCombs, List<PeRegisterCluster> regClusters)
    {
        var samples = new List<PeSample>();
        foreach (var comb in registerCombs.GroupBy(x => x.RegNo))
        {
            var combs = comb.Select(x => new RegisterNewComb
            {
                Id = x.Id,
                CombCode = x.CombCode,
                CombName = x.CombName,
                Discount = x.Discount,
                DiscountOperCode = x.ApplicantCode,
                DiscountOperName = x.ApplicantName,
                IsPayBySelf = false,
                PayStatus = PayStatus.未收费,
                ApplicantCode = x.ApplicantCode,
                ApplicantName = x.ApplicantName,
                BeFrom = x.BeFrom,
                IsExamComb = true,
                IsGathered = false,
                Price = x.Price,
                OriginalPrice = x.OriginalPrice,
                IsOccupation = false,
                IsOrdinary = true,
                IsSelfSelected = false,
                MutexCombs = null
            }).ToArray();
            var clusterCode = regClusters.Where(x => x.RegNo == comb.Key).First().ClusCode;
            samples.AddRange(_sampleService.GenerateAllSampleNewCompany(comb.Key, combs, clusterCode));
        }
        //另外处理标本序列号，减少更新数据库次数，提高效率
        var barcodeCounts = samples.GroupBy(x => x.BarcodeType).ToDictionary(x => x.Key, x => x.Count());
        var barcodeNos = new Dictionary<string, int[]>();
        foreach (var item in barcodeCounts)
        {
            barcodeNos.Add(item.Key, _noGeneration.NextBarcodeNo(item.Key, item.Value));
        }
        foreach (var item in barcodeNos)
        {
            int i = 0;
            foreach (var sample in samples.Where(x => x.BarcodeType == item.Key))
            {
                sample.BarcodeSN = item.Value[i++];
            }
        }
        return samples;
    }
    #endregion

    /// <summary>
    /// 取消分配套餐
    /// </summary>
    /// <param name="clusterArrays"></param>
    /// <param name="msg"></param>
    /// <returns></returns>
    public bool CancelSetCompanyCluster(ClusterArray clusterArrays, ref string msg)
    {
        if (clusterArrays.RegNos.Length == 0)
        {
            msg = "传入的数据为空!";
            return false;
        }

        // 如果存在已激活的数据则不能删除
        var regList = _registerRepository.ReadRegister()
                      .Where(x => SqlFunc.ContainsArray(clusterArrays.RegNos, x.RegNo))
                      .ToList();

        if (regList.Any(x => x.IsActive))
        {
            msg = "已激活的数据不能取消分配套餐!";
            return false;
        }

        var delPeRegCombs = _registerRepository.ReadRegisterCombs(clusterArrays.RegNos)
                            .Where(x => SqlFunc.ContainsArray(clusterArrays.RegNos, x.RegNo))
                            .ToArray();
        regList.ForEach(x => x.TotalPrice = 0);//取消分配更新价格为0
        _dataTranRepository.ExecTran(() =>
        {
            _peRegisterRepository.Update(regList, x => x.TotalPrice);
            _peRegisterClusterRepository.Delete(x => SqlFunc.ContainsArray(clusterArrays.RegNos, x.RegNo));
            _peRegisterCombRepository.SplitTableDelete(delPeRegCombs);
        });
        return true;
    }
    #endregion

    /// <summary>
    /// 获取子公司
    /// </summary>
    /// <param name="parentCode">父公司代码</param>
    /// <param name="subCompanys">子公司代码列表</param>
    public void GetSubCompany(string parentCode, List<string> subCompanys)
    {
        var companyCodes = _codeCompanyRepository.FindAll(a => a.Parent == parentCode)
            .Select(a => a.CompanyCode);

        foreach (var item in companyCodes)
        {
            subCompanys.Add(item);
            GetSubCompany(item, subCompanys);
        }
    }

    public void SavePersonChargeItem(string companyCode)
    {
        var regNos = _registerRepository.ReadRegister().Where(x => x.CompanyCode == companyCode).Select(x => x.RegNo).ToArray();
        var list = _registerRepository.ReadRegisterClusters().Where(x => SqlFunc.ContainsArray(regNos, x.RegNo)).ToList();
        var maps = new List<MapCompanyPersonCombChargeItem>();
        foreach (var item in list.GroupBy(x => x.ClusCode))
        {
            var subRegNos = item.Select(x => x.RegNo).ToArray();
            var xx = _companySettlementService.GetClusterChargeItemEntry(item.Key);
            maps.AddRange(subRegNos.SelectMany(regNo =>
            {
                return xx.Select(comb => new MapCompanyPersonCombChargeItem()
                {
                    RegNo = regNo,
                    CombCode = comb.CombCode,
                    CombName = comb.CombName,
                    HisChargeItemCode = comb.HisChargeItemCode,
                    ItemType = comb.ItemType,
                    HisChargeItemPrice = comb.HisChargeItemPrice,
                    HisChargeItemDiscount = comb.HisChargeItemDiscount,
                    HisChargeItemCount = comb.HisChargeItemCount,
                    HisChargeItemCNName = comb.HisChargeItemCNName,
                    HisChargeItemId = comb.HisChargeItemId,
                    IsInCluster = true
                });
            }).ToList());
        }
        if (maps.Count > 0)
        {
            _mapCompanyPersonCombChargeItemRepository.BulkCopy(maps);
        }
    }
    public void SaveExtra(string companyCode)
    {
        var regNos = _registerRepository.ReadRegister().Where(x => x.CompanyCode == companyCode).Select(x => x.RegNo).ToArray();
        foreach (var regNo in regNos)
            _companySettlementService.SavePersonExtraChargeCombs(_registerRepository.ReadRegister(regNo).First());
    }

    #region 私有方法
    /// <summary>
    /// 检查导入数据是否有误
    /// </summary>
    /// <param name="register"></param>
    /// <param name="company"></param>
    /// <param name="_departmentQuery"></param>
    /// <param name="result"></param>
    /// <returns></returns>
    private bool CheckErrors(
        List<CompanyImport> importRegs,
        CompanyImport register,
        CodeCompany company,
        CodeCompanyDepartment[] _departmentQuery,
        PeRegister[] peRegArray,
        CompanyImportResult result)
    {
        StringBuilder sb = new StringBuilder();
        // 检查单位编码
        if (company.CompanyCode != register.CompanyCode.Trim())
        {
            sb.Append("单位编码不存在/不能为空;");
        }

        // 检查单位部门
        if (!_departmentQuery.Any(x => x.DeptName == register.CompanyDeptName.Trim()) && !string.IsNullOrEmpty(register.CompanyDeptName))
        {
            sb.Append("单位部门不存在/不能为空;");
        }

        // 检查姓名
        if (string.IsNullOrEmpty(register.Name))
        {
            sb.Append("姓名不能为空;");
        }

        // 检查性别格式
        if (!string.IsNullOrEmpty(register.Sex) && register.Sex != "男" && register.Sex != "女")
        {
            sb.Append("性别格式不正确，应为\"男\"、\"女\"、\"\";");
        }

        //// 检查年龄或出生年月日格式
        if (!string.IsNullOrEmpty(register.Birthday) && !Regex.IsMatch(register.Birthday, @"^\d+$") &&
            !Regex.IsMatch(register.Birthday, @"^\d{4}[.-]([1-9]|[0][1-9]|[1][0-2])[.-]([1-9]|[0][1-9]|[1-2][0-9]|[3][0-1])$"))
        {
            sb.Append("出生年月日格式不正确;");
        }

        // 检查婚姻状况
        if (!string.IsNullOrEmpty(register.MarryStatus))
        {
            if (!Enum.TryParse(register.MarryStatus, out MarryStatus _))
            {
                var values = string.Join("，", Enum.GetNames(typeof(MarryStatus)));
                sb.Append($"婚姻状况为{register.MarryStatus}，应为{values};");
            }
        }

        // 校验证件号码
        if (string.IsNullOrEmpty(register.CardNo))
        {
            sb.Append("证件号码不能为空;");
        }
        else
        {
            if (!Enum.TryParse(register.CardType, out CardType cardType))
            {
                var values = string.Join("，", Enum.GetNames(typeof(CardType)));
                sb.Append($"证件类型为{register.CardType}，应为{values};");
            }

            // 身份证校验
            if (cardType == CardType.居民身份证)
            {
                if (!IDCardHelper.CheckIDCard(register.CardNo, out string message))
                {
                    sb.Append($"{message};");
                }
                else
                {
                    register.Age = IDCardHelper.Age(IDCardHelper.Birthday(register.CardNo));
                }

            }
            else
            {
                if (string.IsNullOrEmpty(register.Birthday))
                {
                    sb.Append("出生年月日格式不正确;");
                }
                else
                {
                    register.Age = IDCardHelper.Age(register.Birthday);
                }
            }
        }

        if (register.IsOccupation && !DateTime.TryParse(register.StartDateOfHazards, out var _))
        {
            sb.Append("开始接害时间格式不正确;");
        }
        if (register.IsOccupation && !_cacheRepository.DictOccupationJob().Any(x => x.Value.Equals(register.JobType)))
        {
            sb.Append("工种不正确;");
        }
        if (register.IsOccupation && !register.TotalYearsOfWork.HasValue)
        {
            sb.Append("总工龄年未填写;");
        }
        if (register.IsOccupation && !register.TotalMonthsOfWork.HasValue)
        {
            sb.Append("总工龄月未填写;");
        }
        if (register.IsOccupation && !string.IsNullOrEmpty(register.JobStatus))
        {
            register.JobStatusCode = _cacheRepository.DictJobStatus().FirstOrDefault(x => x.Value.Equals(register.JobStatus)).Key;
            if (register.JobStatusCode.IsNullOrEmpty())
            {
                sb.Append($"在岗状态“{register.JobStatus}”不正确;");
            }
        }
        if (register.IsOccupation && !register.HazardList.IsNullOrEmpty())
        {
            var illegalHazards = register.HazardList.Where(x => !_cacheRepository.DictHazardFactor().Any(y => y.Value.HazardousName == x));
            if (illegalHazards.Count() > 0)
            {
                sb.Append($"危害因素{string.Join(",", illegalHazards)}不正确;");
            }
        }

        // 是否存在重复数据
        if (register.IsOccupation)
        {
            List<string> errs = new();
            // 导入数据
            errs.AddRange(register.CheckSameJobHazard(importRegs));
            // 历史记录
            errs.AddRange(CheckSameJobHazardByExistData());
            if (!errs.IsNullOrEmpty())
                sb.Append($"不能插入重复的数据（{string.Join("；", errs)}）;");
        }

        if (sb.Length > 0)
        {
            register.errMsg = sb.ToString();
            result.ErrorList.Add(register);
            return false;
        }
        return true;

        // 判断单位当次已登记的岗位状态和危害因素
        List<string> CheckSameJobHazardByExistData()
        {
            List<string> errs = new();
            var regSrcs = peRegArray.Where(x => x.CardNo == register.CardNo);
            foreach (var regSrc in regSrcs)
            {
                // 再次导入的岗位状态和危害因素不相同的记录，允许导入系统
                var regOccupSrc = _peRegisterOccupationRepository.First(x => x.RegNo == regSrc.RegNo);
                if (regOccupSrc.JobStatus == register.JobStatusCode)
                {
                    var hazardSrcs = _peRegisterOccupationHazardRepository.FindAll(x => x.RegNo == regSrc.RegNo).Select(x => x.HazardousName).ToArray();
                    var intersectHazards = hazardSrcs.Intersect(register.HazardList).ToArray();
                    if (!intersectHazards.IsNullOrEmpty())
                    {
                        errs.Add($"{regSrc.RegNo}：在岗状态：{register.JobStatus}，危害因素：{string.Join("/", intersectHazards)}");
                    }
                }
            }

            return errs;
        }
    }

    /// <summary>
    /// 生成档案号和体检号
    /// </summary>
    /// <param name="newRegs">登记记录</param>
    /// <param name="correctList">导入数据</param>
    private void GeneratePatCodeAndRegNo(List<PeRegister> newRegs, List<CompanyImport> correctList)
    {
        // 继承历史档案号（PatCode）
        var lastRegs = _companyRepository.GetLastRegister(newRegs).ToDictionary(x => x.CardNo + x.Name);
        // 生成 RegNo
        string[] regNos = _noGeneration.NextRegNo(newRegs.Count);
        for (int i = 0; i < newRegs.Count; i++)
        {
            var newReg = newRegs[i];
            newReg.RegisterTimes = 0; // 重置
            if (lastRegs.TryGetValue(newReg.CardNo + newReg.Name, out var lastReg))
            {
                newReg.PatCode = lastReg.PatCode;
                newReg.RegisterTimes = NextRegisterTimes(newReg.PatCode, lastReg.RegisterTimes);
            }

            newRegs[i].RegNo = regNos[i];
            correctList[i].RegNo = regNos[i];
        }

        // 生成 PatCode: 根据身份证、姓名，标识档案号继承关系
        var groupByNoPatCardNos = newRegs.FindAll(x => x.PatCode.IsNullOrEmpty())
            .GroupBy(x => new { x.CardNo, x.Name }).ToArray();
        string[] patCodes = _noGeneration.NextPatCode(groupByNoPatCardNos.Length);
        for (int i = 0; i < groupByNoPatCardNos.Length; i++)
        {
            foreach (var regItem in groupByNoPatCardNos[i])
            {
                regItem.PatCode = patCodes[i];
                regItem.RegisterTimes = NextRegisterTimes(regItem.PatCode);
            }
        }

        // 生成体检次数
        int NextRegisterTimes(string patCode, int lastTimes = 0)
        {
            var times = newRegs.FindAll(x => x.PatCode == patCode).Max(x => x.RegisterTimes);
            times = lastTimes > times ? lastTimes : times;

            return ++times;
        }
    }

    /// <summary>
    /// 名单包含套餐或危害因素的导入
    /// </summary>
    private bool HasClusterImport(
        List<CompanyImport> correctList,
        Dictionary<string, RegisterClusterDto> cluster,
        CodeCompanyDepartment[] _departmentQuery,
        CodeJob[] _codeJobQuery)
    {
        var peRegList = CreatePeRegister(_httpContextUser.HospCode, correctList, cluster, _departmentQuery, _codeJobQuery); // 登记信息
        var regClusters = GetPeRegisterCluster(); // 套餐信息
        var (occupations, hazards) = CreateOccupation(); // 职业检信息（职业登记信息&更新登记信息，危害因素）
        var regCombs = GetPeRegisterComb(); // 登记组合
        var regSamples = GetPeSample(regCombs); // 条码
        var extra = GetPersonCombChargeItem(); // 项目明细（客户化调整）
        peRegList.ForEach(x => x.TotalPrice = regCombs.Where(y => y.RegNo == x.RegNo).Sum(z => z.Price));
        regClusters.ForEach(x => _logBusinessService.RegisterNewLog(x.RegNo, $"团体名单导入：套餐{x.ClusCode}:{x.ClusName}"));
        _dataTranRepository.ExecTran(() =>
        {
            _peRegisterRepository.BulkCopy(peRegList);
            if (!regClusters.IsNullOrEmpty())
                _peRegisterClusterRepository.BulkCopy(regClusters);

            _peRegisterCombRepository.SplitTableBulkCopy(regCombs);
            _peSampleRepository.BulkCopy(regSamples);

            if (!occupations.IsNullOrEmpty())
                _peRegisterOccupationRepository.BulkCopy(occupations);

            if (!hazards.IsNullOrEmpty())
                _peRegisterOccupationHazardRepository.BulkCopy(hazards);

            if (!extra.IsNullOrEmpty())
                _mapCompanyPersonCombChargeItemRepository.BulkCopy(extra);

            // 保存日志
            _logBusinessService.SaveLogs();
        });

        SyncExternalSystemOrder(peRegList);
        SendProgressMsg(100, "导入完成");
        return true;

        #region 组装套餐/登记组合
        /// <summary>
        /// 组装套餐
        /// </summary>
        /// <returns></returns>
        List<PeRegisterCluster> GetPeRegisterCluster()
        {
            return correctList.Where(importReg => cluster.ContainsKey(importReg.ClusterCode))
                .Select(importReg => new PeRegisterCluster
                {
                    ClusCode = cluster[importReg.ClusterCode].ClusterCode,
                    ClusName = cluster[importReg.ClusterCode].ClusterName,
                    Price = cluster[importReg.ClusterCode].Price,
                    IsMain = true,
                    RegNo = importReg.RegNo,
                    IsOccupation = importReg.IsOccupation,
                })
                .ToList();
        }

        /// <summary>
        /// 组装登记组合
        /// </summary>
        /// <returns></returns>
        List<PeRegisterComb> GetPeRegisterComb()
        {
            var registerComb = new List<PeRegisterComb>();
            // 根据套餐获取组合
            var data = correctList.Join(cluster, x => x.ClusterCode, y => y.Key, (x, y) => new { ClusterCode = y.Value.ClusterCode, CompanyImport = x })
                .GroupBy(x => x.ClusterCode)
                .ToDictionary(x => x.Key, x => x.Select(y => y.CompanyImport).ToList());
            var correctClusters = data.Select(x => x.Key).ToArray();
            cluster = cluster.Where(x => correctClusters.Contains(x.Key))
                    .ToDictionary(x => x.Key, x => x.Value);

            foreach (var item in cluster)
            {
                var regNos = data[item.Key].Select(x => x.RegNo).ToArray(); // 符合条件的名单
                registerComb.AddRange(SpliceComb(item.Key, regNos));
            }

            if (occupations.IsNullOrEmpty() || hazards.IsNullOrEmpty())
                return registerComb;

            // 职业检：根据危害因素获取组合
            foreach (var gRegNoItem in hazards.GroupBy(x => x.RegNo))
            {
                var occReg = occupations.FirstOrDefault(x => x.RegNo == gRegNoItem.Key);
                if (occReg.IsNullOrEmpty()) continue;

                List<string> combCodes = new();
                foreach (var hazardItem in gRegNoItem)
                {
                    if (!_cacheRepository.DictHazardFactor().ContainsKey(hazardItem.HazardousCode))
                        continue;

                    var hazard = _cacheRepository.DictHazardFactor()[hazardItem.HazardousCode];
                    if (hazard.IsNullOrEmpty() ||
                        hazard.CombsByStatus.IsNullOrEmpty() ||
                        !hazard.CombsByStatus.ContainsKey(occReg.JobStatus))
                        continue;

                    combCodes.AddRange(hazard.CombsByStatus[occReg.JobStatus]);
                }

                HandleMutexComb(combCodes);
                AddRangeComb(occReg.RegNo, combCodes);
            }

            return registerComb;

            // 根据组合互斥关系，保留主组合
            void HandleMutexComb(List<string> combCodes)
            {
                if (combCodes.IsNullOrEmpty())
                    return;
                var removeCombCodes = _codeMutexCombRepository
                    .FindAll(x => combCodes.Contains(x.CombCode) && x.IsPrimary == false)
                    .Select(x => x.CombCode).ToList();
                combCodes.RemoveAll(removeCombCodes);
            }

            // 按体检号添加组合信息
            void AddRangeComb(string regNo, List<string> combCodes)
            {
                if (regNo.IsNullOrEmpty() || combCodes.IsNullOrEmpty()) return;
                foreach (var combCode in combCodes)
                {
                    if (registerComb.Any(x => x.RegNo == regNo && x.CombCode == combCode))
                        continue;

                    if (!_cacheRepository.DictComb().ContainsKey(combCode))
                        continue;

                    var comb = _cacheRepository.DictComb()[combCode].ItemComb;
                    var reg = peRegList.First(x => x.RegNo == regNo);
                    var newPeRegComb = new PeRegisterComb()
                    {
                        Id = _noGeneration.NextSnowflakeId(),
                        RegNo = regNo,
                        CombCode = comb.CombCode,
                        CombName = comb.CombName,
                        CombSortIndex = comb.SortIndex,
                        ExamDeptCode = comb.ExamDeptCode,
                        CheckCls = comb.CheckCls,
                        ClsCode = comb.ClsCode,
                        ClsName = comb.ClsName,
                        ClsSortIndex = comb.ClsSortIndex,
                        OriginalPrice = comb.Price,
                        Price = comb.Price,
                        ReportShow = comb.ReportShow,
                        Discount = 1,
                        IsPayBySelf = false,
                        PayStatus = PayStatus.未收费,
                        CreateTime = DateTime.Now,
                        RegisterTime = DateTime.Now,
                        ApplicantCode = _httpContextUser.UserId,
                        ApplicantName = _httpContextUser.UserName,
                        IsOccupation = reg.IsOccupation,
                        IsOrdinary = reg.IsOrdinary,
                        IsSelfSelected = false,
                    };

                    registerComb.Add(newPeRegComb);
                }
            }
        }

        /// <summary>
        /// 组装登记组合
        /// </summary>
        /// <param name="clusCode">单位套餐编码</param>
        /// <param name="regNos"></param>
        /// <returns></returns>
        List<PeRegisterComb> SpliceComb(string clusCode, string[] regNos)
        {
            // 单位套餐
            var combList = _companyRepository.ReadCompanyClusterCls(clusCode)
                .Select((clusComb, comb, cls) => new PeRegisterComb
                {
                    Id = 0,//由于SqlSugar 不支持调用其他方法,只能先查出来，再下方Select中给Id赋值
                    CombCode = comb.CombCode,
                    CombName = comb.CombName,
                    CombSortIndex = comb.SortIndex,
                    ExamDeptCode = comb.ExamDeptCode,
                    CheckCls = comb.CheckCls,
                    ClsCode = comb.ClsCode,
                    ClsName = cls.ClsName,
                    ClsSortIndex = cls.SortIndex,
                    OriginalPrice = clusComb.OriginalPrice,
                    Price = clusComb.Price,
                    ReportShow = comb.ReportShow,
                    Discount = clusComb.Discount,
                    ApplicantCode = _httpContextUser.UserId,
                    ApplicantName = _httpContextUser.UserName,
                    IsPayBySelf = clusComb.IsPayBySelf,
                }).ToList();

            // 公共套餐
            var baseCombs = GetBaseClusCombs(clusCode);
            combList.AddRange(baseCombs);

            var newRegCombs = regNos.SelectMany(regNo =>
            {
                var reg = peRegList.First(x => x.RegNo == regNo);
                return combList.Select(comb => new PeRegisterComb()
                {
                    Id = _noGeneration.NextSnowflakeId(),
                    RegNo = regNo,
                    CombCode = comb.CombCode,
                    CombName = comb.CombName,
                    CombSortIndex = comb.CombSortIndex,
                    ExamDeptCode = comb.ExamDeptCode,
                    CheckCls = comb.CheckCls,
                    ClsCode = comb.ClsCode,
                    ClsName = comb.ClsName,
                    ClsSortIndex = comb.ClsSortIndex,
                    OriginalPrice = comb.OriginalPrice,
                    Price = comb.Price,
                    ReportShow = comb.ReportShow,
                    Discount = comb.Discount,
                    IsPayBySelf = comb.IsPayBySelf,
                    PayStatus = PayStatus.未收费,
                    CreateTime = DateTime.Now,
                    RegisterTime = DateTime.Now,
                    ApplicantCode = comb.ApplicantCode,
                    ApplicantName = comb.ApplicantName,
                    IsOccupation = reg.IsOccupation,
                    IsOrdinary = reg.IsOrdinary,
                    IsSelfSelected = false,
                });
            }).ToList();
            var tubes = GetRegisterTustTubeCombs(clusCode, newRegCombs);
            newRegCombs.AddRange(tubes);
            return newRegCombs;

            // 获取公共套餐组合内容
            List<PeRegisterComb> GetBaseClusCombs(string clusCode)
            {
                return _companyRepository.ReadClusterCls(clusCode)
                 .Select((clusComb, comb, cls) => new PeRegisterComb
                 {
                     CombCode = comb.CombCode,
                     CombName = comb.CombName,
                     CombSortIndex = comb.SortIndex,
                     ExamDeptCode = comb.ExamDeptCode,
                     CheckCls = comb.CheckCls,
                     ClsCode = comb.ClsCode,
                     ClsName = cls.ClsName,
                     ClsSortIndex = cls.SortIndex,
                     OriginalPrice = clusComb.OriginalPrice,
                     Price = clusComb.Price,
                     ReportShow = comb.ReportShow,
                     Discount = clusComb.Discount,
                     ApplicantCode = _httpContextUser.UserId,
                     ApplicantName = _httpContextUser.UserName,
                     IsPayBySelf = false,
                 }).ToList();
            }
        }
        /// <summary>
        /// 组装条码
        /// </summary>
        /// <param name="registerCombs">登记组合</param>
        /// <returns></returns>
        List<PeSample> GetPeSample(List<PeRegisterComb> registerCombs)
        {
            var samples = new List<PeSample>();
            foreach (var comb in registerCombs.GroupBy(x => x.RegNo))
            {
                var combs = comb.Select(x => new RegisterNewComb
                {
                    Id = x.Id,
                    CombCode = x.CombCode,
                    CombName = x.CombName,
                    Discount = x.Discount,
                    DiscountOperCode = x.ApplicantCode,
                    DiscountOperName = x.ApplicantName,
                    IsPayBySelf = false,
                    PayStatus = PayStatus.未收费,
                    ApplicantCode = x.ApplicantCode,
                    ApplicantName = x.ApplicantName,
                    BeFrom = x.BeFrom,
                    IsExamComb = true,
                    IsGathered = false,
                    Price = x.Price,
                    OriginalPrice = x.OriginalPrice,
                    IsOccupation = false,
                    IsOrdinary = true,
                    IsSelfSelected = false,
                    MutexCombs = null
                }).ToArray();

                var clusterCode = regClusters.FirstOrDefault(x => x.RegNo == comb.Key)?.ClusCode;
                samples.AddRange(_sampleService.GenerateAllSampleNewCompany(comb.Key, combs, clusterCode));
            }
            //另外处理标本序列号，减少更新数据库次数，提高效率
            var barcodeCounts = samples.GroupBy(x => x.BarcodeType).ToDictionary(x => x.Key, x => x.Count());
            var barcodeNos = new Dictionary<string, int[]>();
            foreach (var item in barcodeCounts)
            {
                barcodeNos.Add(item.Key, _noGeneration.NextBarcodeNo(item.Key, item.Value));
            }
            foreach (var item in barcodeNos)
            {
                int i = 0;
                foreach (var sample in samples.Where(x => x.BarcodeType == item.Key))
                {
                    sample.BarcodeSN = item.Value[i++];
                }
            }
            return samples;
        }
        /// <summary>
        /// 生成职业病信息
        /// </summary>
        /// <returns></returns>
        (List<PeRegisterOccupation>, List<PeRegisterOccupationHazard>) CreateOccupation()
        {
            var regOccupations = new List<PeRegisterOccupation>();
            var regHazards = new List<PeRegisterOccupationHazard>();
            var hazardDict = _cacheRepository.DictHazardFactor();
            var jobDict = _cacheRepository.DictOccupationJob();
            foreach (var companyInfo in correctList)
            {
                if (!companyInfo.IsOccupation)
                    continue;

                var regCluster = regClusters.FirstOrDefault(x => x.RegNo == companyInfo.RegNo);
                var register = peRegList.FirstOrDefault(x => x.RegNo == companyInfo.RegNo);
                var occupation = new PeRegisterOccupation
                {
                    RegNo = companyInfo.RegNo,
                    JobId = companyInfo.JobId,
                    JobType = _cacheRepository.DictOccupationJob().First(x => x.Value.Equals(companyInfo.JobType)).Key,
                    JobStatus = string.IsNullOrEmpty(companyInfo.JobStatus) ? cluster[regCluster.ClusCode].JobStatus : _cacheRepository.DictJobStatus().First(x => x.Value == companyInfo.JobStatus).Key,
                    TotalYearsOfWork = companyInfo.TotalYearsOfWork.Value,
                    TotalMonthsOfWork = companyInfo.TotalMonthsOfWork.Value,
                    MonitoringType = 1,
                    ReportType = _systemParameterService.DefaultOccupaitonalReportType,
                    JobName = companyInfo.JobType,
                    Workshop = companyInfo.Workshop?.Trim(),
                    ProtectiveMeasures = companyInfo.ProtectiveMeasures?.Trim(),
                };
                List<string> hazardCodes;
                //危害因素有填写则取填写的，否则取套餐的值
                if (companyInfo.HazardList.IsNullOrEmpty())
                {
                    hazardCodes = regCluster == null ? new() : cluster[regCluster.ClusCode].HazardFactors;
                }
                else
                {
                    hazardCodes = _cacheRepository.DictHazardFactor()
                        .Where(x => companyInfo.HazardList.Contains(x.Value.HazardousName)).Select(x => x.Key).ToList();
                }
                DateTime.TryParse(companyInfo.StartDateOfHazards, out DateTime startDate);
                int yearDifference = DateTime.Now.Year - startDate.Year;
                int monthDifference = DateTime.Now.Month - startDate.Month;
                int daysDifference = DateTime.Now.Day - startDate.Day;
                // 如果天数差值为负数，则调整月份差值
                if (daysDifference < 0)
                {
                    monthDifference--;
                }
                // 如果月份差值为负数，则调整年份和月份差值
                if (monthDifference < 0)
                {
                    yearDifference--;
                    monthDifference += 12;
                }
                var hazard = hazardCodes.Select(x => new PeRegisterOccupationHazard
                {
                    RegNo = companyInfo.RegNo,
                    HazardousCode = x,
                    HazardousName = hazardDict[x].HazardousName,
                    StartDateOfHazards = startDate,
                    YearsOfHazards = yearDifference,
                    MonthsOfHazards = monthDifference
                });
                // 导入只能职业或普通一种，如果是职业套餐直接修改掉登记表信息
                register.IsOccupation = true;
                register.IsOrdinary = false;
                register.PeCls = PeCls.职业病;
                regOccupations.Add(occupation);
                regHazards.AddRange(hazard);
            }
            return (regOccupations, regHazards);
        }

        List<MapCompanyPersonCombChargeItem> GetPersonCombChargeItem()
        {
            var personCombChargeItems = new List<MapCompanyPersonCombChargeItem>();
            var data = correctList.Join(cluster, x => x.ClusterCode, y => y.Key, (x, y) => new { y.Value.ClusterCode, CompanyImport = x })
                .GroupBy(x => x.ClusterCode)
                .ToDictionary(x => x.Key, x => x.Select(y => y.CompanyImport).ToList());

            var correctClusters = data.Select(x => x.Key).ToArray();
            cluster = cluster.Where(x => correctClusters.Contains(x.Key))
                    .ToDictionary(x => x.Key, x => x.Value);

            foreach (var item in cluster)
            {
                var regNos = data[item.Key].Select(x => x.RegNo).ToArray();//符合条件的名单
                personCombChargeItems.AddRange(SplicePersonCombChargeItem(item.Key, regNos));
            }
            return personCombChargeItems;
        }
        #endregion
    }

    /// <summary>
    /// 名单不包含套餐的导入
    /// </summary>
    /// <param name="correctList"></param>
    /// <returns></returns>
    private bool NoClusterImport(
        List<CompanyImport> correctList,
        CodeCompanyDepartment[] _departmentQuery,
        CodeJob[] _codeJobQuery
        )
    {
        var peRegList = CreatePeRegister(_httpContextUser.HospCode,
            correctList,
            new Dictionary<string, RegisterClusterDto>(),
            _departmentQuery,
            _codeJobQuery);
        peRegList.ForEach(x => _logBusinessService.RegisterNewLog(x.RegNo, "团体名单导入（不包含套餐）"));
        _dataTranRepository.ExecTran(() =>
        {
            _peRegisterRepository.BulkCopy(peRegList);
            _logBusinessService.SaveLogs();
        });

        SyncExternalSystemOrder(peRegList);
        SendProgressMsg(100, "导入完成");
        return true;
    }

    /// <summary>
    /// 同步外部系统订单
    /// </summary>
    /// <param name="peRegs"></param>
    void SyncExternalSystemOrder(List<PeRegister> peRegs)
    {
        foreach (var reg in peRegs)
        {
            _externalSystemOrderService.SyncOrder(reg);
        }
    }

    /// <summary>
    /// 根据导入项创建 PeRegister 对象数组
    /// </summary> 
    private List<PeRegister> CreatePeRegister(
        string hospCode,
        List<CompanyImport> correctList,
        Dictionary<string, RegisterClusterDto> cluster,
        CodeCompanyDepartment[] departmentQuery,
        CodeJob[] codeJobQuery)
    {
        var newRegs = new List<PeRegister>();
        foreach (var item in correctList)
        {
            var newReg = new PeRegister
            {
                Name = item.Name,
                Sex = Enum.TryParse(item.Sex, out Sex sex) ? sex : Sex.通用,
                Age = (int)item.Age,
                AgeUnit = AgeUnit.岁,
                CardType = ((int)Enum.Parse(typeof(CardType), item.CardType)).ToString(),
                CardNo = item.CardNo,
                Birthday = DateTime.TryParse(item.Birthday, out DateTime birthday) ? birthday : null,
                Tel = item.Tel,
                MarryStatus = Enum.TryParse(item.MarryStatus, out MarryStatus marryStatus) ? marryStatus : null,
                CompanyCode = item.CompanyCode,
                CompanyTimes = item.CompanyTimes,
                CompanyDeptCode = departmentQuery.FirstOrDefault(x => x.DeptName == item.CompanyDeptName)?.DeptCode,
                JobCode = codeJobQuery.FirstOrDefault(x => x.JobName == item.JobName)?.JobCode,
                IsVIP = item.IsVIP == "是",
                IsConstitution = false,        // 体质辨识标识
                IsActive = false,        // 激活标识
                RegisterTime = DateTime.Now, // 登记时间
                RegisterTimes = 1,
                IsCompanyCheck = true,         // 个人/团体标识（true团体,false个人）
                Note = item.Note,    // 备注
                BookType = BookType.团体导入,
                PeCls = PeCls.健康体检,
                PeStatus = PeStatus.未检查,
                OperatorCode = item.OperatorCode,
                HospCode = hospCode,
                IsOrdinary = true,
                IsOccupation = false,
                PayStatus = PaymentStatus.Unpaid,
                TotalPrice = 0
            };

            if (item.CardType == CardType.居民身份证.ToString() && IDCardHelper.CheckIDCard(item.CardNo, out _))
            {
                newReg.Sex = Enum.Parse<Sex>(IDCardHelper.Sex(item.CardNo));
                newReg.Birthday = DateTime.Parse(IDCardHelper.Birthday(item.CardNo));
                newReg.Age = IDCardHelper.Age((DateTime)newReg.Birthday);
            }

            // 分配报表格式
            if (cluster.TryGetValue(item.ClusterCode, out var companyClust))
            {
                newReg.GuidanceType = companyClust.GuidanceType;
                newReg.ReportType = companyClust.ReportType;
            }
            else
            {
                newReg.GuidanceType = _systemParameterService.DefaultGuidanceType;
                newReg.ReportType = _systemParameterService.DefaultReportType;
            }

            newRegs.Add(newReg);
        }

        // 处理无档案号、无体检号
        GeneratePatCodeAndRegNo(newRegs, correctList);

        SendProgressMsg(76, "创建数据完成...");
        return newRegs;
    }

    /// <summary>
    /// 团体导入进度消息推送
    /// </summary>
    /// <param name="percent"></param>
    /// <param name="message"></param>
    private void SendProgressMsg(int percent, string message)
    {
        var msg = new
        {
            MsgCode = "CompanyImport",
            MsgData = new
            {
                Message = message,
                CurrentProgressPercentage = percent // 当前进度百分比
            }
        };
        _peimClients.User(_httpContextUser.UserId).SendMsg(msg.ToJson()).Wait();//
    }
    #endregion

    /// <summary>
    /// 计算套餐试管、采血费用
    /// </summary>
    /// <param name="combs"></param>
    /// <returns></returns>
    public List<CompanyComb> CalculateTustTubeCombs(CompanyComb[] combs)
    {
        var venousBloodComb = _systemParameterService.VenousBloodComb;
        var newCompanyComb = combs.Where(x => x.IsCheckComb).ToList();
        var oldTubes = combs.Where(x => !x.IsCheckComb).ToList();
        var checkCombCodes = newCompanyComb.Select(x => x.CombCode).ToArray();
        var tubeRelation = _sampleService.CalculateTestTubeBefrom(combs).Where(x => !string.IsNullOrEmpty(x.TubeCombCode)).ToList();
        if (tubeRelation.Count == 0)
        {
            return newCompanyComb;
        }
        #region  试管
        foreach (var item in tubeRelation.GroupBy(x => new { x.TubeCombCode, x.IsPayBySelf }))
        {
            if (!oldTubes.Any(x => x.CombCode == item.Key.TubeCombCode && x.IsPayBySelf == item.Key.IsPayBySelf))
            {
                newCompanyComb.Add(new CompanyComb
                {
                    ClusterCode = "",
                    CombCode = item.Key.TubeCombCode,
                    Count = item.Count(),
                    BeFrom = item.Select(x => x.BeFrom).ToArray().ToJson(),
                    Discount = 1,
                    IsPayBySelf = item.Key.IsPayBySelf,
                    OriginalPrice = item.Max(x => x.Price),
                    Price = item.Max(x => x.Price),
                    CombName = item.Max(x => x.TubeCombName),
                    IsCheckComb = false,
                    Sex = Sex.通用
                });
            }
            else
            {
                var testTube = oldTubes.Where(x => x.CombCode == item.Key.TubeCombCode && x.IsPayBySelf == item.Key.IsPayBySelf).First();
                testTube.Count = item.Count();
                testTube.BeFrom = item.Select(x => x.BeFrom).ToArray().ToJson();
                newCompanyComb.Add(testTube);
            }
        }
        #endregion
        #region 采血 
        var blood = InsertVenousBloodComb(checkCombCodes);
        if (blood != null)
        {
            if (!oldTubes.Any(x => x.CombCode == venousBloodComb))
            {
                newCompanyComb.Add(new CompanyComb
                {
                    ClusterCode = string.Empty,
                    CombCode = blood.TubeCombCode,
                    Count = 1,
                    BeFrom = new string[][] { blood.BeFrom }.ToJson(),
                    Discount = 1,
                    IsPayBySelf = !newCompanyComb.Any(x => !x.IsPayBySelf && !x.IsCheckComb),
                    Price = blood.Price,
                    OriginalPrice = blood.Price,
                    CombName = blood.TubeCombName,
                    IsCheckComb = false,
                    Sex = Sex.通用
                });
            }
            else
            {
                var bloodComb = oldTubes.Where(x => x.CombCode == venousBloodComb).First();
                bloodComb.BeFrom = new string[][] { blood.BeFrom }.ToJson();
                bloodComb.IsPayBySelf = !newCompanyComb.Any(x => !x.IsPayBySelf && !x.IsCheckComb);
                newCompanyComb.Add(bloodComb);
            }
        }
        #endregion            
        return newCompanyComb;
        SampleCombs InsertVenousBloodComb(string[] combs)
        {
            //获取采血收费组合代码
            var bloodComb = _systemParameterService.VenousBloodComb;
            if (string.IsNullOrEmpty(bloodComb))
            {
                return null;
            }
            //获取组合检验试管的绑定关系：血类型，无则删除采血组合
            var combBindTestTubes = _clusterCombRepository.ReadCombBarcodeType(combs)
                .Where((comb, barType) => SqlFunc.Subqueryable<CodeSample>().Where(samp => samp.SampCode == barType.SampCode && samp.SampName.Contains("血")).Any())
                .Select((comb, barType) => comb.CombCode).ToArray();
            if (combBindTestTubes.Length == 0)
            {
                return null;
            }
            string beFromJson = JsonConvert.SerializeObject(combBindTestTubes.ToArray());
            var combInfo = _clusterCombRepository.ReadComb(bloodComb)
                    .Select(x => new
                    {
                        x.CombCode,
                        x.CombName,
                        x.ExamDeptCode,
                        x.CheckCls,
                        x.ClsCode,
                        x.Price,
                    })
                    .First();
            if (combInfo == null) //组合不存在
                return null;
            //采血组合
            var newComb = new SampleCombs
            {
                TubeCombCode = bloodComb,
                TubeCombName = combInfo.CombName,
                BarCode = null,
                Price = combInfo.Price,
                BeFrom = combBindTestTubes
            };
            return newComb;
        }
    }
    /// <summary>
    /// 生成材料费
    /// </summary>
    /// <param name="companyClusterCode"></param>
    /// <param name="list"></param>
    /// <returns></returns>
    private List<PeRegisterComb> GetRegisterTustTubeCombs(string companyClusterCode, List<PeRegisterComb> list)
    {
        if (_systemParameterService.MaterialCost != "计算生成")
            return new();
        List<PeRegisterComb> tubes = new();
        var clusterTubes = _mapCompanyClusterCombTestTubeRepository.FindAll(x => x.ClusterCode == companyClusterCode).ToList();
        var tubeCombInfo = _clusterCombRepository.ReadComb(clusterTubes.Select(x => x.CombCode).ToArray()).Select(x => new
        {
            x.CombCode,
            x.CombName,
            x.ExamDeptCode,
            x.CheckCls,
            x.ClsCode,
            x.Price,
            x.ReportShow,
            x.SortIndex,
            ClsSortIndex = SqlFunc.Subqueryable<CodeItemCls>().Where(cls => cls.ClsCode == x.ClsCode).Select(cls => cls.SortIndex),
        }).ToArray();
        foreach (var item in list.GroupBy(x => x.RegNo))
        {
            var lis = item.ToList();
            foreach (var tube in clusterTubes)
            {
                var combInfo = tubeCombInfo.Where(x => x.CombCode == tube.CombCode).First();
                if (combInfo == null) //试管组合不存在
                    continue;
                foreach (var beFrom in tube.BeFromList)
                {
                    var bloodCombs = lis.Where(x => beFrom.Contains(x.CombCode)).ToArray();
                    if (!bloodCombs.Any())
                        continue;
                    //新试管组合
                    var newTube = new PeRegisterComb
                    {
                        Id = _noGeneration.NextSnowflakeId(),
                        RegNo = item.Key,
                        CombCode = combInfo.CombCode,
                        CombName = combInfo.CombName,
                        ExamDeptCode = combInfo.ExamDeptCode,
                        CheckCls = combInfo.CheckCls,
                        ClsCode = combInfo.ClsCode,
                        ClsName = string.Empty,
                        OriginalPrice = tube.OriginalPrice,
                        Price = tube.Price,
                        ReportShow = combInfo.ReportShow,
                        Discount = tube.Discount,//先取第一个的
                        IsPayBySelf = tube.IsPayBySelf,
                        PayStatus = PayStatus.未收费,
                        BeFrom = bloodCombs.Select(x => x.Id.ToString()).ToArray(),
                        ApplicantCode = item.First().ApplicantCode,
                        ApplicantName = item.First().ApplicantName,
                        CreateTime = DateTime.Now,
                        RegisterTime = item.First().RegisterTime,
                        CombSortIndex = combInfo.SortIndex,
                        ClsSortIndex = combInfo.ClsSortIndex,
                    };
                    tubes.Add(newTube);
                }
            }
        }
        return tubes;
    }
    /// <summary>
    /// 生成收费冗余字典
    /// </summary>
    /// <param name="clusCode"></param>
    /// <param name="regNos"></param>
    /// <returns></returns>
    List<MapCompanyPersonCombChargeItem> SplicePersonCombChargeItem(string clusCode, string[] regNos)
    {
        var combList = _companySettlementService.GetClusterChargeItemEntry(clusCode);

        var personCombChargeItems = regNos.SelectMany(regNo =>
        {
            return combList.Select(comb => new MapCompanyPersonCombChargeItem()
            {
                RegNo = regNo,
                CombCode = comb.CombCode,
                CombName = comb.CombName,
                HisChargeItemCode = comb.HisChargeItemCode,
                ItemType = comb.ItemType,
                HisChargeItemPrice = comb.HisChargeItemPrice,
                HisChargeItemDiscount = comb.HisChargeItemDiscount,
                HisChargeItemCount = comb.HisChargeItemCount,
                HisChargeItemCNName = comb.HisChargeItemCNName,
                HisChargeItemId = comb.HisChargeItemId,
                IsInCluster = true
            });
        }).ToList();
        return personCombChargeItems;
    }

    public object ReadSimpleCompanies(CompanyQuery companyQuery)
    {
        return _companyRepository.ReadCompany()
            .WhereIF(!companyQuery.CompanyClsCode.IsNullOrEmpty(), x => x.CompanyClsCode == companyQuery.CompanyClsCode)
            .WhereIF(!companyQuery.CompanyCode.IsNullOrEmpty(), x => x.CompanyCode == companyQuery.CompanyCode)
            .Select(x => new { x.CompanyCode, x.CompanyName }).ToArray();
    }
}