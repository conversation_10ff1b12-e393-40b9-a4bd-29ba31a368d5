﻿using Peis.External.Platform.Service.Service.IService.Report;
using Peis.External.Platform.Service.Service.IService.WebService;
using Peis.Service.IService.ExternalSystem;

namespace Peis.External.Platform.Service.Service.ServiceImpl.WebService;

/// <summary>
/// 外部统一调用体检WebService接口实现
/// </summary>
public class ExternalWebService : IExternalWebService
{
    private readonly IExternalSystemApplyService _applyService;
    private readonly ILisReportService _lisReportService;
    private readonly IExamReportService _examReportService;
    private readonly IExternalSystemMachineService _machineService;

    public ExternalWebService(
        IExternalSystemApplyService applyService,
        ILisReportService lisReportService,
        IExamReportService examReportService,
        IExternalSystemMachineService machineService)
    {
        _applyService = applyService;
        _lisReportService = lisReportService;
        _examReportService = examReportService;
        _machineService = machineService;
    }

    /// <summary>
    /// 检测用,无具体逻辑
    /// </summary>
    public void Check()
    {
    }

    /// <summary>
    /// 统一请求入口
    /// </summary>
    /// <param name="action">方法名</param>
    /// <param name="reqMsg">入参内容</param>
    /// <returns>string</returns>
    public string Operation(string action, string reqMsg)
    {
        string returnMsg = string.Empty;
        switch (action)
        {
            //检查接口
            case "queryPacsApplyInfoPE":
                _applyService.QueryExamApplyInfoPE(reqMsg, ref returnMsg);
                break;
            case "synchPacsRequisitionStatusPE":
                _applyService.SynchExamRequisitionStatus(reqMsg, ref returnMsg);
                break;
            case "synchPacsReportPE":
                _examReportService.AcceptReport(reqMsg, ref returnMsg);
                break;
            case "synchPacsCriticalPE":
                //_applyService.QueryExamApplyInfoPE(reqMsg, ref retMsg);
                break;
            //检验接口
            case "queryLisApplyInfoPE":
                _applyService.QueryLisApplyInfoPE(reqMsg, ref returnMsg);
                break;
            case "synchLisRequisitionStatusPE":
                _applyService.SynchLisRequisitionStatus(reqMsg, ref returnMsg);
                break;
            case "synchLisReportPE":
                _lisReportService.AcceptReport(reqMsg, ref returnMsg);
                break;
            case "synchLisCriticalPE":
                _lisReportService.AcceptCritical(reqMsg, ref returnMsg);
                break;
            // 自助机接口
            case "queryOrderListOfHATM": // 订单查询
                _machineService.QueryOrderListOfHATM(reqMsg, ref returnMsg);
                break;

            case "queryPrintFileListOfHATM": // 打印文件列表：指引单、非血条码、检查标签
                _machineService.QueryPrintFileListOfHATM(reqMsg, ref returnMsg);
                break;

            case "syncPrintedTimeOfHATM": // 更新打印时间：指引单、非血条码
                _machineService.SyncPrintedTimeOfHATM(reqMsg, ref returnMsg);
                break;
        }
        return returnMsg;
    }

}
