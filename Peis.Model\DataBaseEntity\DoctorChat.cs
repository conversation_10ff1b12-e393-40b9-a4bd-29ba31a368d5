﻿using Peis.Model.Other.PeEnum;
using Peis.Model.TableFilter;
using SqlSugar;
using System;

namespace Peis.Model.DataBaseEntity
{
    ///<summary>
    ///医生会诊表
    ///</summary>
    [SugarTable("DoctorChat")]
    public class DoctorChat: IHospCodeFilter
    {
        /// <summary>
        /// 会诊Id
        /// </summary>        
        [SugarColumn(IsNullable = false, IsPrimaryKey = true)]
        public long ChatId { get; set; }

        /// <summary>
        /// 体检号
        /// </summary>        
        [SugarColumn(IsNullable = false, Length = 12)]
        public string RegNo { get; set; }

        /// <summary>
        /// 医生编码
        /// </summary>        
        [SugarColumn(IsNullable = false, Length = 10)]
        public string DoctorCode { get; set; }

        /// <summary>
        /// 科室编码(发起科室)
        /// </summary>        
        [SugarColumn(IsNullable = false, Length = 6)]
        public string DeptCode { get; set; }

        /// <summary>
        /// 姓名
        /// </summary>        
        [SugarColumn(IsNullable = false, Length = 50)]
        public string Name { get; set; }

        /// <summary>
        /// 性别
        /// </summary>        
        [SugarColumn(IsNullable = false)]
        public Sex Sex { get; set; }

        /// <summary>
        /// 年龄
        /// </summary>        
        [SugarColumn(IsNullable = false)]
        public int Age { get; set; }

        /// <summary>
        /// 发起内容
        /// </summary>        
        [SugarColumn(IsNullable = false, Length = 100)]
        public string Content { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>        
        [SugarColumn(IsNullable = false)]
        public DateTime CreateTime { get; set; }

        /// <summary>
        /// 院区编码
        /// </summary>        
        [SugarColumn(IsNullable = false, Length = 1)]
        public string HospCode { get; set; }
    }
}