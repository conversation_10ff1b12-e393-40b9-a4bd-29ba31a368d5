﻿using SqlSugar;
using System;

namespace Peis.Model.DataBaseEntity
{
    ///<summary>
    ///单位次数信息
    ///</summary>
    [SugarTable("CodeCompanyTimes")]
    public class CodeCompanyTimes
    {
        /// <summary>
        /// 单位代码
        /// </summary>        
        [SugarColumn(IsNullable = false, IsPrimaryKey = true, Length = 6)]
        public string CompanyCode { get; set; }

        /// <summary>
        /// 次数
        /// </summary>        
        [SugarColumn(IsNullable = false, IsPrimaryKey = true)]
        public int CompanyTimes { get; set; }

        /// <summary>
        /// 开始日期
        /// </summary>        
        [SugarColumn(IsNullable = false)]
        public DateTime BeginDate { get; set; }

        /// <summary>
        /// 结束日期
        /// </summary>        
        [SugarColumn(IsNullable = true)]
        public DateTime? EndDate { get; set; }

        /// <summary>
        /// 线上结束日期
        /// </summary>        
        [SugarColumn(IsNullable = true)]
        public DateTime? OnLineEndDate { get; set; }

        /// <summary>
        /// 发票抬头
        /// </summary>        
        [SugarColumn(IsNullable = true, Length = 200)]
        public string InvoiceHeader { get; set; }

        /// <summary>
        /// 税号
        /// </summary>        
        [SugarColumn(IsNullable = true, Length = 50)]
        public string TaxID { get; set; }

        /// <summary>
        /// 院内联系人
        /// </summary>        
        [SugarColumn(IsNullable = true, Length = 10)]
        public string HospContact { get; set; }

        /// <summary>
        /// 单位联系人
        /// </summary>        
        [SugarColumn(IsNullable = true, Length = 50)]
        public string CompanyContact { get; set; }

        /// <summary>
        /// 开户银行
        /// </summary>        
        [SugarColumn(IsNullable = true, Length = 60)]
        public string OpenBank { get; set; }

        /// <summary>
        /// 银行账号
        /// </summary>        
        [SugarColumn(IsNullable = true, Length = 20)]
        public string BankAccount { get; set; }

        /// <summary>
        /// 电话
        /// </summary>        
        [SugarColumn(IsNullable = true, Length = 20)]
        public string Tel { get; set; }

        /// <summary>
        /// 是否外检
        /// </summary>        
        [SugarColumn(IsNullable = false)]
        public bool IsOutside { get; set; }

        /// <summary>
        /// 备注
        /// </summary>        
        [SugarColumn(IsNullable = true, Length = 255)]
        public string Note { get; set; }
    }
}