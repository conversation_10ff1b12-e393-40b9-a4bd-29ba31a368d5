﻿using Peis.Model.Other.PeEnum;
using System;

namespace Peis.Model.DTO.DataQuery
{
    /// <summary>
    /// 重大阳性查询
    /// </summary>
    public class MajorPositiveQuery
    {
        /// <summary>
        /// 开始时间
        /// </summary>
        public DateTime StartTime { get; set; }

        /// <summary>
        /// 结束时间
        /// </summary>
        public DateTime EndTime { get; set; }

        /// <summary>
        /// 单位名称
        /// </summary>
        public string CompanyName { get; set; }

        /// <summary>
        /// 团检标识 个人/单位
        /// </summary>
        public PersonCompany IsCompanyCheck { get; set; }

        /// <summary>
        /// 关键字 (体检号/姓名)
        /// </summary>
        public string KeyWord { get; set; }

    }
}
