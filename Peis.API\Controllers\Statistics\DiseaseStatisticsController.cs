﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Peis.Model.DataBaseEntity;
using Peis.Model.DTO.DiseaseStatistics;
using Peis.Service.IService;
using Peis.Utility.PeUser;
using System.Collections.Generic;

namespace Peis.API.Controllers.Statistics
{
    /// <summary>
    /// 疾病统计报表
    /// </summary>
    [Route("api/[controller]")]
    [ApiController]
    public class DiseaseStatisticsController : BaseApiController
    {
        private readonly IDiseaseStatisticsService _diseaseStatisticsService;
        private readonly IHttpContextUser _httpContextUser;

        public DiseaseStatisticsController(
            IDiseaseStatisticsService diseaseStatisticsService,
            IHttpContextUser httpContextUser)
        {
            _diseaseStatisticsService = diseaseStatisticsService;
            _httpContextUser = httpContextUser;
        }

        /// <summary>
        /// 初始化单位查询模板
        /// </summary>
        /// <param name="companyCode">单位编码</param>
        /// <returns></returns>
        [HttpPost("InitCompanyTemplate")]
        public IActionResult InitCompanyTemplate([FromQuery] string companyCode)
        {
            string msg = string.Empty;
            result.Success = _diseaseStatisticsService.InitCompanyTemplate(companyCode, ref msg);
            result.ReturnMsg = msg;
            return Ok(result);
        }

        /// <summary>
        /// /CD_MapCompanyDiseaseCls/Create 新增单位疾病分类对应疾病
        /// /CD_MapCompanyDiseaseCls/Delete 新增单位疾病分类对应疾病
        /// </summary>
        /// <param name="type">操作</param>
        /// <param name="mapCompanyDiseaseCls">单位疾病分类对应疾病</param>
        /// <returns></returns>
        [HttpPost("CD_MapCompanyDiseaseCls/{type}")]
        public IActionResult CD_MapCompanyDiseaseCls([FromRoute] string type, [FromBody] List<MapCompanyDiseaseCls> mapCompanyDiseaseCls)
        {
            string msg = string.Empty;
            switch (type.ToLower())
            {
                case "create":
                    result.Success = _diseaseStatisticsService.CreateMapCompanyDiseaseCls(mapCompanyDiseaseCls, ref msg);
                    break;
                case "delete":
                    result.Success = _diseaseStatisticsService.DeleteMapCompanyDiseaseCls(mapCompanyDiseaseCls, ref msg);
                    break;
                default:
                    return new BadRequestResult();
            }

            result.ReturnMsg = msg;
            return Ok(result);
        }

        /// <summary>
        /// 获取单位疾病分类对应疾病
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        [HttpPost("Read_MapCompanyDiseaseClsDisease")]
        [ProducesResponseType(typeof(List<CompanyDiseaseClsData>), 200)]
        public IActionResult Read_MapCompanyDiseaseClsDisease([FromBody] CompanyDiseaseClsQuery query)
        {
            string msg = string.Empty;
            List<CompanyDiseaseClsData> data = new();
            result.Success = _diseaseStatisticsService.Read_MapCompanyDiseaseClsDisease(query, ref data, ref msg);
            result.ReturnData = data;
            return Ok(result);
        }

        /// <summary>
        /// 疾病情况统计报表(按大类)
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        [HttpPost("DiseaseStatisticsByDiseaseCls")]
        [ProducesResponseType(typeof(DiseaseClsStatisticReport), 200)]
        public IActionResult DiseaseStatisticsByDiseaseCls([FromBody] DiseaseClsStatisticQuery query)
        {
            string msg = string.Empty;
            DiseaseClsStatisticReport report = new();
            result.Success = _diseaseStatisticsService.DiseaseStatisticsByDiseaseCls(query,ref report, ref msg);
            result.ReturnMsg = msg;
            result.ReturnData = report;
            return Ok(result);
        }

        /// <summary>
        /// 疾病患病率统计报表
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        [HttpPost("DiseaseRateStatistic")]
        [ProducesResponseType(typeof(DiseaseRateStatisticReport), 200)]
        public IActionResult DiseaseRateStatistic([FromBody] DiseaseRateStatisticQuery query)
        {
            string msg = string.Empty;
            result.Success = true;
            result.ReturnMsg = msg;
            result.ReturnData = _diseaseStatisticsService.DiseaseRateStatistic(query);
            return Ok(result);
        }
        /// <summary>
        /// 疾病按年龄分布统计报表
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        [HttpPost("DiseaseRateDistributionByAge")]
        [ProducesResponseType(typeof(DiseaseRateDistributionByAgeReport), 200)]
        public IActionResult DiseaseRateDistributionByAge([FromBody] DiseaseRateStatisticQuery query)
        {
            string msg = string.Empty;
            result.Success = true;
            result.ReturnMsg = msg;
            result.ReturnData = _diseaseStatisticsService.DiseaseRateDistributionByAge(query);
            return Ok(result);
        }
        
    }
}
