﻿namespace Peis.Model.DataBaseEntity
{
    [SugarTable("MapCompanyClusterCombTestTube")]
    public class MapCompanyClusterCombTestTube
    {
        /// <summary>
        /// 套餐代码
        /// </summary>
        [SugarColumn(IsNullable = false, Length = 15, IsPrimaryKey = true)]
        public string ClusterCode { get; set; }
        /// <summary>
        /// 组合代码
        /// </summary>        
        [SugarColumn(IsNullable = false, Length = 8, IsPrimaryKey = true)]
        public string CombCode { get; set; }
        /// <summary>
        /// 原始单价
        /// </summary>        
        [SugarColumn(IsNullable = false, Length = 12, DecimalDigits = 2)]
        public decimal OriginalPrice { get; set; }
        /// <summary>
        /// 单价
        /// </summary>        
        [SugarColumn(IsNullable = false, Length = 12, DecimalDigits = 2)]
        public decimal Price { get; set; }
        /// <summary>
        /// 数量
        /// </summary>
        [SugarColumn(IsNullable = false)]
        public int CombCount { get; set; }
        /// <summary>
        /// 折扣率
        /// </summary>        
        [SugarColumn(IsNullable = false, Length = 0, DecimalDigits = 2)]
        public decimal Discount { get; set; }
        /// <summary>
        /// 是否自费
        /// </summary>        
        [SugarColumn(IsNullable = false, IsPrimaryKey = true)]
        public bool IsPayBySelf { get; set; }
        /// <summary>
        /// 归属关系
        /// </summary>        
        [SugarColumn(IsNullable = true, ColumnDataType = StaticConfig.CodeFirst_BigString)]
        public string BeFrom { get; set; }

        [SugarColumn(IsIgnore = true)]
        public string[][] BeFromList
        {
            get
            {
                if (string.IsNullOrEmpty(BeFrom))
                    return null;
                else
                    return System.Text.Json.JsonSerializer.Deserialize<string[][]>(BeFrom);
            }
        }
    }
}
