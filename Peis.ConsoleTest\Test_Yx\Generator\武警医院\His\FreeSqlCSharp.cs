﻿using SqlSugar;
using System.Collections.Generic;
using System.Linq;

namespace Peis.ConsoleTest.Test_Yx.Generator.武警医院.His
{
    public static class FreeSqlCSharp
    {
        private static SqlSugarScope _db;

        static FreeSqlCSharp()
        {
            _db = new SqlSugarScope(new ConnectionConfig()
            {
                //ConnectionString = "Data Source=192.168.0.254/ORCL;User ID=****;Password=****",
                ConnectionString = "Data Source=192.168.8.29/ORCL;User ID=system;Password=****",
                DbType = DbType.Oracle,
                IsAutoCloseConnection = true
            });
        }

        public static void Invoke()
        {
            string[] tables = new string[]
            {
                //门诊收费
                "PAT_MASTER_INDEX",
                "OUTP_ORDER_DESC_TEMP",
                "OUTP_ORDER_DESC",
                "OUTP_DOCTOR_ASSISTANT",
                "OUTP_BILL_ITEMS_TEMP",
                "OUTP_BILL_ITEMS",
                "CLINIC_ITEM_DICT",
                "CLINIC_VS_CHARGE",
                "CURRENT_PRICE_LIST",

                //门诊申请单Pacs
                "EXAM_APPOINTS",
                "EXAM_ITEMS",
                "EXAM_BILL_ITEMS",

                //门诊申请单Lis
                "LAB_TEST_MASTER",
                "LAB_TEST_ITEMS",
                "TJ_GROUP_INFO",

                //计算Lis附加费
                //"DICT_COMBINE_BAR",
                //"DICT_SAMPLE",
                //"LAB_BLOODVES_BILL_ITEM",
                //"ADMINISTRATION_BILL_DETAIL",
            };

            if (tables.Any())
                Generator(tables).StringNullable().CreateClassFile("D:\\FreeSqlClass");
        }

        static IDbFirst Generator(params string[] objectNames)
        {
            return _db.DbFirst
                    //类
                    .SettingClassTemplate(old =>
                    {
                        return
        @"{using}

namespace {Namespace}
{
    public class {ClassName}
    {
{PropertyName}
    }
}";
                    })
                    //类构造函数
                    .SettingConstructorTemplate(old =>
                    {
                        return string.Empty;
                    })
                     .SettingNamespaceTemplate(old =>
                     {
                         return "using FreeSql.DataAnnotations;";
                     })
                    //属性备注
                    .SettingPropertyDescriptionTemplate(old =>
                    {
                        return string.Empty;
                    })
                    //属性:新重载 完全自定义用配置
                    .SettingPropertyTemplate((columns, temp, type) =>
                    {
                        temp =
        @"           {Column}
           public {PropertyType} {PropertyName} {get;set;}";

                        var columnattribute = "\r\n           [Column({0})]";
                        List<string> attributes = new List<string>();
                        if (columns.IsPrimarykey)
                            attributes.Add("IsPrimary = true");
                        if (columns.IsIdentity)
                            attributes.Add("IsIdentity = true");
                        if (!columns.IsNullable)
                            attributes.Add("IsNullable = false");
                        if (attributes.Count == 0)
                        {
                            columnattribute = "";
                        }
                        return temp.Replace("{PropertyType}", type)
                                    .Replace("{PropertyName}", columns.DbColumnName)
                                    .Replace("{Column}", string.Format(columnattribute, string.Join(", ", attributes)));
                    })
                    .Where(objectNames);
        }
    }
}
