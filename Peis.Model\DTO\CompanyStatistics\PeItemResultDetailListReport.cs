﻿using Peis.Model.Other.PeEnum;

namespace Peis.Model.DTO.CompanyStatistics
{
    public class PeItemResultDetailListReport
    {
        /// <summary>
        /// 表头
        /// </summary>
        public Dictionary<string, string> Header { get; set; }

        /// <summary>
        /// 表数据
        /// </summary>
        public List<Dictionary<string, string>> BodyData { get; set; }

    }

    /// <summary>
    /// 项目
    /// </summary>
    public class PeItemResultDetailListReportItem
    {
        /// <summary>
        /// 体检号
        /// </summary>
        public string RegNo { get; set; }

        /// <summary>
        /// 姓名
        /// </summary>
        public string Name { get; set; }

        /// <summary>
        /// 性别
        /// </summary>
        public Sex? Sex { get; set; }

        /// <summary>
        /// 年龄
        /// </summary>
        public int? Age { get; set; }

        /// <summary>
        /// 身份证号
        /// </summary>
        public string CardNo { get; set; }

        /// <summary>
        /// 工号
        /// </summary>
        public string WorkNo { get; set; }
        /// <summary>
        /// 体检日期
        /// </summary>
        public DateTime ActiveDate { get; set; }

        /// <summary>
        /// 单位名称
        /// </summary>
        public string CompanyName { get; set; }

        /// <summary>
        /// 组合代码
        /// </summary>
        public string CombCode { get; set; }

        /// <summary>
        /// 组合名称
        /// </summary>
        public string CombName { get; set; }

        public string CombResult { get; set; }
    }
}
