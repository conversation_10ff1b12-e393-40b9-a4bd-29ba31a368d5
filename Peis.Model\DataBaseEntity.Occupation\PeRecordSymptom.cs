﻿namespace Peis.Model.DataBaseEntity.Occupation;

/// <summary>
/// 症状询问结果表
/// </summary>
[SugarTable]
public class PeRecordSymptom
{
    /// <summary>
    /// 体检号
    /// </summary>
    [SugarColumn(Length =12,IsPrimaryKey =true)]
    public string RegNo { set; get; }
    /// <summary>
    /// 检查医生姓名
    /// </summary>
    [SugarColumn(Length = 50, IsNullable = false)]
    public string DoctorName { get; set; }

    /// <summary>
    /// 检查时间
    /// </summary>
    [SugarColumn(IsNullable = false)]
    public DateTime ExamTime { get; set; }

    /// <summary>
    /// 症状询问明细
    /// </summary>
    [SugarColumn(IsIgnore = true)]
    [Navigate(NavigateType.OneToMany, nameof(PeRecordSymptom.RegNo))]
    public List<PeRecordSymptomDetail> PeRecordSymptomDetails { get; set; }
}
