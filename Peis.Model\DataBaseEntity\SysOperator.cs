﻿namespace Peis.Model.DataBaseEntity
{
    ///<summary>
    ///用户代码表
    ///</summary>
    [SugarTable("SysOperator")]
    public class SysOperator
    {
        /// <summary>
        /// 代码
        /// </summary>        
        [SugarColumn(IsNullable = false, IsPrimaryKey = true, Length = 10)]
        public string OperatorCode { get; set; }

        /// <summary>
        /// 名称
        /// </summary>        
        [SugarColumn(IsNullable = false, Length = 50)]
        public string Name { get; set; }

        /// <summary>
        /// 密码
        /// </summary>        
        [SugarColumn(IsNullable = false, Length = 50)]
        public string Password { get; set; }

        /// <summary>
        /// 最大折扣价
        /// </summary>        
        [SugarColumn(IsNullable = true, Length = 8, DecimalDigits = 2)]
        public decimal? MaxDiscountPrice { get; set; }

        /// <summary>
        /// 最大抵扣金额
        /// </summary>        
        [SugarColumn(IsNullable = true, Length = 8, DecimalDigits = 2)]
        public decimal? MaxDeductionPrice { get; set; }

        /// <summary>
        /// 最大折扣
        /// </summary>        
        [SugarColumn(IsNullable = true, Length = 8, DecimalDigits = 2)]
        public decimal? MaxDiscount { get; set; }
        /// <summary>
        /// 所属科室编码
        /// </summary>        
        [SugarColumn(IsNullable = false, Length = 6)]
        public string DeptCode { get; set; }

        /// <summary>
        /// 最近访问时间
        /// </summary>        
        [SugarColumn(IsNullable = true)]
        public DateTime? LoginTime { get; set; }

        /// <summary>
        /// 状态
        /// </summary>        
        [SugarColumn(IsNullable = false)]
        public bool IsEnabled { get; set; }

        /// <summary>
        /// 院区编码
        /// </summary>        
        [SugarColumn(ColumnName = "HospCode", IsJson = true,IsNullable = false, Length = 50)]
        public List<string> HospCode { get; set; }

        /// <summary>
        /// 跨科列表
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public List<MapDoctorDept> MapDoctorDepts { get; set; }
        /// <summary>
        /// 签名地址
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public string SignaturePath { get; set; }
    }
}