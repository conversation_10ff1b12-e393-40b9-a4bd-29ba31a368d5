﻿using SqlSugar;

namespace Peis.Model.DataBaseEntity
{
    ///<summary>
    ///重大阳性表达式
    ///</summary>
    [SugarTable("MajorPositiveExpression")]
    public class MajorPositiveExpression
    {
        /// <summary>
        /// 表达式码
        /// </summary>        
        [SugarColumn(IsNullable = false, IsPrimaryKey = true)]
        public long ExpCode { get; set; }

        /// <summary>
        /// 表达式名称
        /// </summary>        
        [SugarColumn(IsNullable = false, Length = 255)]
        public string ExpName { get; set; }

        /// <summary>
        /// 表达式
        /// </summary>        
        [SugarColumn(IsNullable = false, ColumnDataType = StaticConfig.CodeFirst_BigString)]
        public string ExpText { get; set; }

        /// <summary>
        /// 重大阳性代码
        /// </summary>        
        [SugarColumn(IsNullable = false, Length = 8)]
        public string PositiveCode { get; set; }
    }
}