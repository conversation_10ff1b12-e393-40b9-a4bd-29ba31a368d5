﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Peis.Model.DataBaseEntity.External
{
    [SugarTable]
    public class CompanySettlementEntry
    {
        /// <summary>
        /// 当次结算流水号
        /// </summary>
        [SugarColumn(IsNullable = false, IsPrimaryKey = true, Length = 10)]
        public string PE_SEQ_NO { set; get; }
        /// <summary>
        /// 单位代码
        /// </summary>
        [SugarColumn(IsNullable = false, Length = 12)]
        public string PE_NO { set; get; }
        /// <summary>
        /// 证件号
        /// </summary>
        [SugarColumn(IsNullable = false, Length = 20)]
        public string CARD_ID { set; get; }
        /// <summary>
        /// 组合代码
        /// </summary>
        [SugarColumn(IsNullable = false, IsPrimaryKey = true, Length = 10)]
        public string PE_ITEM_CODE { set; get; }
        /// <summary>
        /// 组合名
        /// </summary>
        [SugarColumn(IsNullable = false, Length = 200)]
        public string PE_ITEM_NAME { set; get; }
        /// <summary>
        /// 收费项目代码
        /// </summary>
        [SugarColumn(IsNullable = false, IsPrimaryKey = true, Length = 30)]
        public string HIS_ITEM_CODE { set; get; }
        /// <summary>
        /// 收费项目名称
        /// </summary>
        [SugarColumn(IsNullable = false, Length = 200)]
        public string HIS_ITEM_NAME { set; get; }
        /// <summary>
        /// 体检人次数
        /// </summary>
        [SugarColumn(IsNullable = false)]
        public int PE_TIMES { set; get; }
        /// <summary>
        /// 单价
        /// </summary>
        [SugarColumn(IsNullable = false, Length = 10, DecimalDigits = 2)]
        public decimal UNIT { set; get; }
        /// <summary>
        /// 应收金额
        /// </summary>
        [SugarColumn(IsNullable = false, Length = 12, DecimalDigits = 2)]
        public decimal THEORY_TOTAL_AMOUNT { set; get; }
        /// <summary>
        /// 实收金额
        /// </summary>
        [SugarColumn(IsNullable = false, Length = 12, DecimalDigits = 2)]
        public decimal ACTUAL_TOTAL_AMOUNT { set; get; }
        /// <summary>
        /// 实收数量
        /// </summary>
        [SugarColumn(IsNullable = false)]
        public int QUANTITY { set; get; }
        /// <summary>
        /// 备注 空
        /// </summary>
        [SugarColumn(IsNullable = true, Length = 255)]
        public string REMARK { set; get; }
        /// <summary>
        /// 执行科室
        /// </summary>
        [SugarColumn(IsNullable = false, Length = 1000)]
        public string PERFORMING_DEPT { set; get; }
        /// <summary>
        /// 激活标识 Y
        /// </summary>
        [SugarColumn(IsNullable = true, Length = 1)]
        public string ACTIVE_FLAG { set; get; }
        /// <summary>
        /// 收费标识 Y N
        /// </summary>
        [SugarColumn(IsNullable = true, Length = 1)]
        public string ACCOUNT_FLAG { set; get; }
        /// <summary>
        /// 退费标识 Y N
        /// </summary>
        [SugarColumn(IsNullable = true, Length = 1)]
        public string PAYMENT_INACTIVE_FLAG { set; get; }
        /// <summary>
        /// 发票ID 可空
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public int? INVOICE_ID { set; get; }
        /// <summary>
        /// 发票编号 可空
        /// </summary>
        [SugarColumn(IsNullable = true, Length = 30)]
        public string INVOICE_NO { set; get; }
        /// <summary>
        /// 收费日期
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public DateTime? CHARGE_DATETIME { set; get; }
        /// <summary>
        /// 开单医生工号 HIS工号
        /// </summary>
        [SugarColumn(IsNullable = false, Length = 10)]
        public string PE_DOCTOR_ID { set; get; }
        /// <summary>
        /// 空
        /// </summary>
        [SugarColumn(IsNullable = true, Length = 30)]
        public string scc_code { set; get; }

        /// <summary>
        /// 体检号 计算用
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public string RegNo { set; get; }
        [SugarColumn(IsIgnore = true)]
        public long RegCombId { set; get; }
    }
}
