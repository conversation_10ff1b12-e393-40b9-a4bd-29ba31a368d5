﻿namespace Peis.Model.DataBaseEntity;

///<summary>
/// 疾病分类对应组合项目表
///</summary>
[SugarTable(TableName = nameof(MapDiseaseClsComb), TableDescription = "疾病分类对应组合项目表")]
[SugarIndex($"idx_{nameof(MapDiseaseClsComb)}", nameof(DiseaseClsCode), OrderByType.Asc, nameof(CombCode), OrderByType.Asc)]
public class MapDiseaseClsComb
{
    /// <summary>
    /// 疾病分类代码
    /// </summary>        
    [SugarColumn(IsNullable = false, IsPrimaryKey = true, Length = 20)]
    public string DiseaseClsCode { get; set; }

    /// <summary>
    /// 组合代码
    /// </summary>        
    [SugarColumn(IsNullable = false, IsPrimaryKey = true, Length = 20)]
    public string CombCode { get; set; }

    /// <summary>
    /// 序号
    /// </summary>        
    public int SortIndex { get; set; }

    #region Ext
    /// <summary>
    /// 疾病分类名称
    /// </summary>        
    [SugarColumn(IsIgnore = true)]
    public string DiseaseClsName { get; set; }

    /// <summary>
    /// 组合名称
    /// </summary>        
    [SugarColumn(IsIgnore = true)]
    public string CombName { get; set; }

    /// <summary>
    /// 分类代码
    /// </summary>
    [SugarColumn(IsIgnore = true)]
    public string ClsCode { get; set; }

    /// <summary>
    /// 分类名称
    /// </summary>
    [SugarColumn(IsIgnore = true)]
    public string ClsName { get; set; }
    #endregion
}
