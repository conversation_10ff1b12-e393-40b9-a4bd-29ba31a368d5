﻿using SqlSugar;
using System;

namespace Peis.Model.DataBaseEntity
{
    ///<summary>
    ///标本条码表
    ///</summary>
    [SugarTable("PeSample")]
    public class PeSample
    {
        /// <summary>
        /// 体检号
        /// </summary>        
        [SugarColumn(IsNullable = false, IsPrimaryKey = true, Length = 12)]
        public string RegNo { get; set; }

        /// <summary>
        /// 登记组合Id
        /// </summary>        
        [SugarColumn(IsNullable = false, IsPrimaryKey = true)]
        public long RegCombId { get; set; }

        /// <summary>
        /// 组合码
        /// </summary>        
        [SugarColumn(IsNullable = false, Length = 8)]
        public string CombCode { get; set; }

        /// <summary>
        /// 条码号
        /// </summary>        
        [SugarColumn(IsNullable = false, Length = 20)]
        public string SampleNo { get; set; }

        /// <summary>
        /// 条码类型
        /// </summary>        
        [SugarColumn(IsNullable = false, Length = 4)]
        public string BarcodeType { get; set; }

        /// <summary>
        /// 条码分类序号（每日刷新）
        /// </summary>        
        [SugarColumn(IsNullable = false)]
        public int BarcodeSN { get; set; }

        /// <summary>
        /// 打印时间
        /// </summary>        
        [SugarColumn(IsNullable = true)]
        public DateTime? PrintTime { get; set; }

        /// <summary>
        /// 采集时间
        /// </summary>        
        [SugarColumn(IsNullable = true)]
        public DateTime? GatherTime { get; set; }

        /// <summary>
        /// 采集人
        /// </summary>        
        [SugarColumn(IsNullable = true, Length = 10)]
        public string GatherOperator { get; set; }

        /// <summary>
        /// 包号
        /// </summary>        
        [SugarColumn(IsNullable = true, Length = 11)]
        public string PackageNo { get; set; }

        /// <summary>
        /// 打包时间
        /// </summary>        
        [SugarColumn(IsNullable = true)]
        public DateTime? PackageTime { get; set; }

        /// <summary>
        /// 标本代码
        /// </summary>        
        [SugarColumn(IsNullable = false, Length = 10)]
        public string SampCode { get; set; }
    }
}