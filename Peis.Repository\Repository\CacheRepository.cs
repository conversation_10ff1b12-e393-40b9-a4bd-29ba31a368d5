﻿using Peis.Model.DataBaseEntity;
using Peis.Model.DataBaseEntity.Occupation;
using Peis.Model.DTO.CacheModel;
using Peis.Model.DTO.Register;
using Peis.Model.DTO.ReportConclusionNew;
using Peis.Model.Other.PeEnum;
using Peis.Repository.IRepository;
using Peis.Utility.Helper;
using Peis.Utility.PeUser;
using SqlSugar;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;

namespace Peis.Repository.Repository
{
    public class CacheRepository : ICacheRepository
    {
        private readonly ISqlSugarClient _db;
        private readonly IHttpContextUser _httpContextUser;
        public CacheRepository(ISqlSugarClient db, IHttpContextUser httpContextUser)
        {
            _db = db;
            _httpContextUser = httpContextUser;
        }

        public Dictionary<string, CommonClusterExtendInfo> DictCluster()
        {
            return CacheHelper.GetOrCreate($"{_httpContextUser.HospCode}-CLuster", () =>
            {
                var list = _db.Queryable<CodeCluster>()
                .Where(x => x.IsEnabled == true)
                .Select(x => new CommonClusterExtendInfo
                {
                    CLusCode = x.ClusCode,
                    ClusterName = x.ClusName,
                    Sex = x.Sex,
                    Price = x.Price,
                    IsOccupation = x.IsOccupation,
                    Hazards = x.HazardFactors,
                    JobStatus = x.JobStatus,
                }).ToList();
                var mapCombs = _db.Queryable<MapClusterComb>()
                .InnerJoin<CodeItemComb>((map, comb) => map.CombCode == comb.CombCode)
                .InnerJoin<CodeItemCls>((map, comb, cls) => comb.ClsCode == cls.ClsCode)
                .Where((map, comb) => comb.IsEnabled)
                .OrderBy((map, comb, cls) => cls.SortIndex)
                .OrderBy((map, comb) => comb.SortIndex)
                .Select(map => map)
                .ToList();
                var mutexCombs = _db.Queryable<CodeClusMutexComb>().ToList();
                list.BatchUpdate(x => x.ClusCombs = mapCombs.Where(m => m.ClusCode == x.CLusCode)
                .Select(x => new CandidateClusterBindComb
                {
                    CombCode = x.CombCode,
                    Discount = x.Discount,
                    OriginalPrice = x.OriginalPrice,
                    Price = x.Price,
                    IsPayBySelf = true
                }).ToArray());
                list.BatchUpdate(x => x.MutexCombs = mutexCombs.Where(m => m.ClusCode == x.CLusCode).Select(x => x.CombCode).ToArray());

                return list.ToDictionary(x => x.CLusCode, x => x);
            }, new TimeSpan(2, 0, 0), new TimeSpan(0, 10, 0));
        }

        public Dictionary<string, CombExtendInfo> DictComb()
        {
            return CacheHelper.GetOrCreate($"{_httpContextUser.HospCode}-ItemComb", () =>
            {
                var list = _db.Queryable<CodeItemComb>()
                    .Select(x => new CombExtendInfo { ItemComb = x }).ToList();
                var bindCombs = _db.Queryable<CodeCombComb>().ToList();
                var mutexCombs = _db.Queryable<CodeMutexComb>().ToList();
                var codeItemClss = _db.Queryable<CodeItemCls>().ToList();
                list.BatchUpdate(x =>
                {
                    x.BindCombs = bindCombs.Where(b => b.CombCode == x.ItemComb.CombCode).Select(b => b.AppendCombCode).ToArray();
                    var muterCodes = mutexCombs.Where(m => m.CombCode == x.ItemComb.CombCode).Select(m => m.MutexCode).ToArray();
                    x.MutexCombs = muterCodes.Length > 0 ?
                        mutexCombs
                            .Where(m => muterCodes.Contains(m.MutexCode) && m.CombCode != x.ItemComb.CombCode)
                            .Select(m => m.CombCode).ToArray()
                        : Array.Empty<string>();

                    var cls = codeItemClss.FirstOrDefault(c => c.ClsCode == x.ItemComb.ClsCode);
                    x.ItemComb.ClsName = cls?.ClsName;
                    x.ItemComb.ClsSortIndex = cls?.SortIndex ?? 0;
                });
                return list.ToDictionary(x => x.ItemComb.CombCode, x => x);
            }, new TimeSpan(2, 0, 0), new TimeSpan(0, 10, 0));
        }

        public Dictionary<string, CompanyClusterExtendInfo> DictCompanyCluster()
        {
            return CacheHelper.GetOrCreate($"{_httpContextUser.HospCode}-CompanyCluster", () =>
            {
                var list = _db.Queryable<CodeCompanyCluster>()
                .Select(x => new CompanyClusterExtendInfo
                {
                    CLusCode = x.ClusterCode,
                    ClusterName = x.ClusterName,
                    Sex = x.Sex,
                    Price = x.Price,
                    IsOccupation = x.IsOccupation,
                    Hazards = x.HazardFactors,
                    JobStatus = x.JobStatus,
                }).ToList();
                var mapCombs = _db.Queryable<MapCompanyClusterComb>()
                .InnerJoin<CodeItemComb>((map, comb) => map.CombCode == comb.CombCode)
                .InnerJoin<CodeItemCls>((map, comb, cls) => comb.ClsCode == cls.ClsCode)
                .InnerJoin<CodeCompanyCluster>((map, comb, cls, cluster) => cluster.ClusterCode == map.ClusterCode)
                .Where((map, comb) => comb.IsEnabled)
                .OrderBy((map, comb, cls) => cls.SortIndex)
                .OrderBy((map, comb) => comb.SortIndex)
                .Select(map => map)
                .ToList();
                list.BatchUpdate(x => x.ClusCombs = mapCombs.Where(m => m.ClusterCode == x.CLusCode)
                .Select(x => new CandidateClusterBindComb
                {
                    CombCode = x.CombCode,
                    Discount = x.Discount,
                    OriginalPrice = x.OriginalPrice,
                    Price = x.Price,
                    IsPayBySelf = x.IsPayBySelf,
                }).ToArray());
                return list.ToDictionary(x => x.CLusCode, x => x);
            }, new TimeSpan(2, 0, 0), new TimeSpan(0, 10, 0));
        }

        public Dictionary<string, CodeBarcodeType> DictBarcode()
        {
            return CacheHelper.GetOrCreate("Barcode", () =>
            {
                return _db.Queryable<CodeBarcodeType>().ToList().ToDictionary(x => x.Barcode, x => x);
            }, new TimeSpan(2, 0, 0), new TimeSpan(0, 10, 0));
        }

        public Dictionary<string, HazardFacotrExtendInfo> DictHazardFactor()
        {
            return CacheHelper.GetOrCreate($"HazardFacotr", () =>
            {
                var list = _db.Queryable<CodeOccupationalHazardous>()
                .Select(x => new HazardFacotrExtendInfo
                {
                    HazardousCode = x.HazardousCode,
                    HazardousName = x.HazardousName,
                    IsMainDust = x.IsMainDust,
                    HazardousType = x.HazardousType,
                    IsOtherHazardous = x.IsOtherHazardous
                }).ToList();
                var combs = _db.Queryable<MapOccupationHazardousComb>().ToList();
                foreach (var item in list)
                {
                    item.CombsByStatus = combs.Where(x => x.HazardousCode == item.HazardousCode).GroupBy(x => x.StatusCode).ToDictionary(x => x.Key, x => x.Select(x => x.CombCode).ToArray());
                }
                return list.ToDictionary(x => x.HazardousCode, x => x);
            }, new TimeSpan(2, 0, 0), new TimeSpan(0, 10, 0));
        }

        public Dictionary<string, string> DictOccupationItemCode()
        {
            return CacheHelper.GetOrCreate("MapOccuItemCode", () =>
            {
                return _db.Queryable<MapOccupationalItemPeItem>().ToList().ToDictionary(x => x.PeItemCode, x => x.OccupationalItemCode);
            }, new TimeSpan(2, 0, 0), new TimeSpan(0, 10, 0));
        }

        public Dictionary<string, string> DictOccupationItemUnit()
        {
            return CacheHelper.GetOrCreate("MapOccuItemUnit", () =>
            {
                return _db.Queryable<MapOccupationalItemUnit>().ToList().ToDictionary(x => x.ItemCode, x => x.UnitCode);
            }, new TimeSpan(2, 0, 0), new TimeSpan(0, 10, 0));
        }

        public Dictionary<string, string> DictSymptomMap()
        {
            return CacheHelper.GetOrCreate("MapSymptom", () =>
            {
                return _db.Queryable<MapOccupationalSymptomPeItem>().ToList().ToDictionary(x => x.ItemCode, x => x.SymptomCode);
            }, new TimeSpan(2, 0, 0), new TimeSpan(0, 10, 0));
        }

        public Dictionary<Sex, Dictionary<string, CodeOccupationalItemLimit>> DictItemLimit()
        {
            return CacheHelper.GetOrCreate("OccuItemLimit", () =>
            {
                var dictionary = new Dictionary<Sex, Dictionary<string, CodeOccupationalItemLimit>>();
                var man = _db.Queryable<CodeOccupationalItemLimit>().Where(x => x.Sex == Sex.男).ToList().ToDictionary(x => x.ItemCode, x => x);
                var women = _db.Queryable<CodeOccupationalItemLimit>().Where(x => x.Sex == Sex.女).ToList().ToDictionary(x => x.ItemCode, x => x);
                dictionary[Sex.男] = man;
                dictionary[Sex.女] = women;
                return dictionary;
            }, new TimeSpan(2, 0, 0), new TimeSpan(0, 10, 0));
        }

        public Dictionary<string, List<MapCompanyClusterCombTestTube>> DictCompanyClusterTestTube()
        {
            return CacheHelper.GetOrCreate($"{_httpContextUser.HospCode}-TestTube", () =>
            {
                return _db.Queryable<MapCompanyClusterCombTestTube>()
                .InnerJoin<CodeCompanyCluster>((map, cluster) => map.ClusterCode == cluster.ClusterCode)
                .Select(map => map)
                .ToList()
                .GroupBy(x => x.ClusterCode)
                .ToDictionary(x => x.Key, x => x.ToList());
            }, new TimeSpan(2, 0, 0), new TimeSpan(0, 10, 0));
        }

        public Dictionary<string, string> DictOccupationJob()
        {
            return CacheHelper.GetOrCreate("OccupationJob", () =>
            {
                return _db.Queryable<CodeOccupationalJob>()
                .Select(x => new { x.JobCode, x.JobName })
                .ToArray()
                .ToDictionary(x => x.JobCode, x => x.JobName);
            }, new TimeSpan(2, 0, 0), new TimeSpan(0, 10, 0));
        }

        public Dictionary<string, Disease> DictDisease()
        {
            return CacheHelper.GetOrCreate(
                key: "DiseaseDictionary",
                slidingExpiration: TimeSpan.FromMinutes(1),
                absoluteExpiration: TimeSpan.FromMinutes(10),
                valueFactory: () =>
                {
                    var diseases = _db.Queryable<CodeDisease>().ToArray().ToDictionary(x => x.DiseaseCode, x => new Disease
                    {
                        DiseaseCode = x.DiseaseCode,
                        DiseaseName = x.DiseaseName,
                        SuggestContent = x.SuggestContent,
                        DiseaseEntries = Array.Empty<string>()
                    });

                    var entryGroups = _db.Queryable<CodeDiseaseEntry>().ToArray().GroupBy(x => x.DiseaseCode);
                    foreach (var entryGroup in entryGroups)
                    {
                        if (diseases.TryGetValue(entryGroup.Key, out var dea))
                            dea.DiseaseEntries = entryGroup.Select(x => x.EntryText).ToArray();
                    }

                    return diseases;
                });
        }

        public Dictionary<string, string> DictSymptom()
        {
            return CacheHelper.GetOrCreate("Symptom", () =>
            {
                return _db.Queryable<CodeOccupationalSymptom>().ToList().ToDictionary(x => x.SymptomCode, x => x.SymptomName);
            }, new TimeSpan(2, 0, 0), new TimeSpan(0, 10, 0));
        }

        public Dictionary<Type, PropertyInfo[]> DictMaintainCode()
        {
            return CacheHelper.GetOrCreate("MaintainCode", () =>
            {
                var types = Assembly.GetAssembly(typeof(CodeItem)).GetTypes()
                                .Where(x => x.IsClass && !x.IsSealed && !x.IsAbstract)
                                .Where(x => x.Namespace.StartsWith("Peis.Model.DataBaseEntity")).ToArray();
                return types
                                .ToDictionary(x => x, x => x.GetProperties());
            }, new TimeSpan(2, 0, 0), new TimeSpan(0, 10, 0));
        }

        public Dictionary<string, Dictionary<string, string>> DictAddressRelation()
        {
            return CacheHelper.GetOrCreate("AddressCode", () =>
            {
                var tree = _db.Queryable<CodeOccupationalAddress>().ToTree(x => x.Children, x => x.ParentCode, null);
                var dic = new Dictionary<string, Dictionary<string, string>>();
                foreach (var i in tree)
                {
                    if (!i.Children.IsNullOrEmpty())
                    {
                        var liss = GetChild(i.Children);
                        foreach (var j in liss.Keys)
                        {
                            liss[j].Add(i.AddressCode, i.AddressShortName);
                            dic.Add(j, liss[j].OrderBy(x => x.Key).ToDictionary(x => x.Key, x => x.Value));
                        }
                    }
                }
                return dic;
                Dictionary<string, Dictionary<string, string>> GetChild(List<CodeOccupationalAddress> list)
                {
                    var dic = new Dictionary<string, Dictionary<string, string>>();
                    foreach (var i in list)
                    {
                        if (!i.Children.IsNullOrEmpty())
                        {
                            var liss = GetChild(i.Children);
                            foreach (var j in liss.Keys)
                            {
                                liss[j].Add(i.AddressCode, i.AddressShortName);
                                dic.Add(j, liss[j]);
                            }
                        }
                        else
                        {
                            var dics = new Dictionary<string, string>();
                            dics.Add(i.AddressCode, i.AddressShortName);
                            dic.Add(i.AddressCode, dics);
                        }
                    }
                    return dic;
                }
            }, new TimeSpan(2, 0, 0), new TimeSpan(0, 10, 0));
        }

        /// <summary>
        /// 重大阳性
        /// </summary>
        /// <returns></returns>
        public Dictionary<string, MajorPositive> DictMajorPositive()
        {
            return CacheHelper.GetOrCreate(
                key: "MajorPositiveDictionary",
                slidingExpiration: TimeSpan.FromMinutes(1),
                absoluteExpiration: TimeSpan.FromMinutes(10),
                valueFactory: () =>
                {
                    var majors = _db.Queryable<MajorPositive>().ToArray().ToDictionary(x => x.PositiveCode, x => new MajorPositive
                    {
                        PositiveCode = x.PositiveCode,
                        PositiveName = x.PositiveName,
                        PositiveType = x.PositiveType,
                        CheckType = x.CheckType,
                    });

                    foreach (var item in majors.Values)
                    {
                        var mapDisea = _db.Queryable<MapDiseaseMajorPositive>().First(x => x.PositiveCode == item.PositiveCode);
                        if (mapDisea == null) continue;

                        if (!DictDisease().ContainsKey(mapDisea.DiseaseCode)) continue;

                        var disease = DictDisease()[mapDisea.DiseaseCode];
                        item.DiseaseCode = mapDisea?.DiseaseCode;
                        item.DiseaseName = disease?.DiseaseName;
                        item.SuggestContent = disease?.SuggestContent;
                    }

                    return majors;
                });
        }

        public Dictionary<string, string> DictJobStatus()
        {
            return CacheHelper.GetOrCreate($"JobStatus", () =>
            {
                var list = _db.Queryable<CodeOccupationalPositionStatus>().ToList();
                return list.ToDictionary(x => x.StatusCode, x => x.StatusName);
            }, new TimeSpan(2, 0, 0), new TimeSpan(0, 10, 0));
        }

        /// <summary>
        /// 单位信息
        /// </summary>
        /// <returns></returns>
        public Dictionary<string, CodeCompany> DictCompany()
        {
            return CacheHelper.GetOrCreate($"CodeCompanyDictionary", () =>
            {
                var list = _db.Queryable<CodeCompany>().ToArray();
                return list.ToDictionary(x => x.CompanyCode, x => x);
            }, new TimeSpan(2, 0, 0), new TimeSpan(0, 10, 0));
        }

        /// <summary>
        /// 项目信息
        /// </summary>
        /// <returns></returns>
        public Dictionary<string, CodeItem> DictCodeItem()
        {
            return CacheHelper.GetOrCreate($"CodeItem", () =>
            {
                var list = _db.Queryable<CodeItem>().ToArray();
                return list.ToDictionary(x => x.ItemCode, x => x);
            }, new TimeSpan(2, 0, 0), new TimeSpan(0, 10, 0));
        }

        /// <summary>
        /// 职业病项目信息
        /// </summary>
        /// <returns></returns>
        public Dictionary<string, string> DictCodeOccupationalItem()
        {
            return CacheHelper.GetOrCreate($"CodeOccupationalItem", () =>
            {
                var list = _db.Queryable<CodeOccupationalItem>().ToArray();
                return list.ToDictionary(x => x.ItemCode, x => x.ItemName);
            }, new TimeSpan(2, 0, 0), new TimeSpan(0, 10, 0));
        }

        /// <summary>
        /// 获取岗位字典
        /// </summary>
        /// <returns></returns>
        public Dictionary<string, CodeJob> DictJob()
        {
            return CacheHelper.GetOrCreate($"CodeJob", () =>
            {
                var list = _db.Queryable<CodeJob>().ToArray();
                return list.ToDictionary(x => x.JobCode, x => x);
            }, new TimeSpan(2, 0, 0), new TimeSpan(0, 10, 0));
        }

        /// <summary>
        /// 获取单位部门字典
        /// </summary>
        /// <returns></returns>
        public Dictionary<string, CodeCompanyDepartment> DictCompanyDepartment()
        {
            return CacheHelper.GetOrCreate($"CodeCompanyDepartment", () =>
            {
                var list = _db.Queryable<CodeCompanyDepartment>().ToArray();
                return list.ToDictionary(x => x.DeptCode, x => x);
            }, new TimeSpan(2, 0, 0), new TimeSpan(0, 10, 0));
        }

        /// <summary>
        /// 获取科室字典
        /// </summary>
        /// <returns></returns>
        public Dictionary<string, CodeDepartment> DictDepartment()
        {
            return CacheHelper.GetOrCreate($"CodeDepartment", () =>
            {
                var list = _db.Queryable<CodeDepartment>().ToArray();
                return list.ToDictionary(x => x.DeptCode, x => x);
            }, new TimeSpan(2, 0, 0), new TimeSpan(0, 10, 0));
        }

        /// <summary>
        /// 获取项目分类字典
        /// </summary>
        /// <returns></returns>
        public Dictionary<string, CodeItemCls> DictItemCls()
        {
            return CacheHelper.GetOrCreate($"CodeItemCls", () =>
            {
                var list = _db.Queryable<CodeItemCls>().ToArray();
                return list.ToDictionary(x => x.ClsCode, x => x);
            }, new TimeSpan(2, 0, 0), new TimeSpan(0, 10, 0));
        }

        /// <summary>
        /// 获取样本字典
        /// </summary>
        /// <returns></returns>
        public Dictionary<string, CodeSample> DictSample()
        {
            return CacheHelper.GetOrCreate($"CodeSample", () =>
            {
                var list = _db.Queryable<CodeSample>().ToArray();
                return list.ToDictionary(x => x.SampCode, x => x);
            }, new TimeSpan(2, 0, 0), new TimeSpan(0, 10, 0));
        }
    }
}