{
    // 应用配置
    "AppSettings": {
        "RunUrls": [
            "http://*:9001"
        ],
        "CorsIPs": [
            "http://localhost:9000"
        ],
        "SwaggerIsEnabled": true,
        "ExternalSystemIsEnabled": false // 是否启用扩展服务（需重启应用）
    },
    // 数据库连接地址
    "ConnectionString": {
        "Entities": "Data Source=*************;Initial Catalog=PeisHub_ShenShan;User ID=sa;Password=*********;", // 体检数据库(公司测试)
        "System": "Data Source=*************;Initial Catalog=Peis_SS_Sys;User ID=sa;Password=*********;" // 系统相关：任务、日志等(公司测试) 
    },
    // 业务配置
    "WebserviceHub": {
        "ServiceCode": "0", // 服务器编码（院内为"0"|院外）生成体检号用
        "FileServiceUrl": "http://*************:9021/", // 静态文件url
        "ReportServiceUrl": "https://peisreport.sschospital.cn/" // 报表服务url
    },
    // 中间件
    "Middleware": {
        "UseRequestDecrypt": false,
        "UseReqResLogger": false,
        "UseExceptionHandling": true
    },
    // Token
    "JwtBearer": {
        "Issurer": "Issurer", // 发行人
        "Audience": "Audience", // 受众人
        "SecretKey": "96c6448684c246f1b35e89eb031c787d", // 密钥32字节
        "Expires": 360000000, // 过期时间（秒）
        "SlidingExpires": 350000000 // 滑动过期时间（秒）（刷新token用）
    },
    // 短信配置
    "ShortMsg": {
        "UseTestEnv": true, // 使用测试环境，发送短信号码为固定测试手机号
        "PhoneNumber": "13669516694", // 固定测试手机号
        "CheckPatCode": true // 是否校验档案卡号
    },
    "Occupation": {
        "UnitCode": true, // 使用测试环境，发送短信号码为固定测试手机号
        "Password": "13669516694", // 固定测试手机号
        "PlatFormUrl": "123456"
    },
    // 外部系统配置
    "ExternalSystem": {
        "SeqUrl": "http://localhost:5341", // Seq服务地址
        "PeisWx": {
            "Url": "http://localhost", // 微信服务地址
        }
    }
}
