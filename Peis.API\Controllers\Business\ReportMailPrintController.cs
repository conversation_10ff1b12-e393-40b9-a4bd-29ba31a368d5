﻿using Microsoft.AspNetCore.Mvc;
using Peis.Model.DTO.ReportMailPrint;
using Peis.Model.Other.Input.ReportMailPrint;
using Peis.Service.IService;

namespace Peis.API.Controllers.Business
{
    /// <summary>
    /// 报告邮寄打印
    /// </summary>
    [Route("api/[controller]")]
    [ApiController]
    public class ReportMailPrintController : BaseApiController
    {
        private readonly IReportMailPrintService _reportMailPrintService;

        public ReportMailPrintController(IReportMailPrintService reportMailPrintService)
        {
            _reportMailPrintService = reportMailPrintService;
        }

        /// <summary>
        /// 更新报告已打印
        /// </summary>
        /// <param name="regNo"></param>
        /// <returns></returns>
        [HttpPost("UpdateReportPrinted")]
        public IActionResult UpdateReportPrinted([FromQuery] string regNo)
        {
            string msg = string.Empty;
            result.Success = _reportMailPrintService.UpdateReportPrinted(regNo, ref msg);
            result.ReturnMsg = msg;
            return Ok(result);
        }

        /// <summary>
        /// 获取报告打印列表
        /// </summary>
        /// <param name="printQuery"></param>
        /// <returns></returns>
        [HttpPost("GetReportPrintList")]
        [ProducesResponseType(typeof(ReportPrintData[]), 200)]
        public IActionResult GetReportPrintList([FromBody] ReportPrintQuery printQuery)
        {
            string msg = string.Empty;
            result.ReturnData = _reportMailPrintService.GetReportPrintList(printQuery, ref msg);
            result.ReturnMsg = msg;
            return Ok(result);
        }

        /// <summary>
        /// 报告批打印导出列表
        /// </summary>
        /// <param name="printQuery"></param>
        /// <returns></returns>
        [HttpPost("GetReportBatchPrintAndExportList")]
        [ProducesResponseType(typeof(ReportBatchPrintData[]), 200)]
        public IActionResult GetReportBatchPrintAndExportList([FromBody] ReportBatchPrintQuery printQuery)
        {
            string msg = string.Empty;
            result.ReturnData = _reportMailPrintService.GetReportBatchPrintAndExportList(printQuery, ref msg);
            result.ReturnMsg = msg;
            return Ok(result);
        }

        /// <summary>
        /// 保存报告领取模式
        /// </summary>
        /// <param name="report"></param>
        /// <returns></returns>
        [HttpPost("SaveReportReceiveMode")]
        public IActionResult SaveReportReceiveMode([FromBody] ReportReceiveMode report)
        {
            string msg = string.Empty;
            result.Success = _reportMailPrintService.SaveReportReceiveMode(report, ref msg);
            result.ReturnMsg = msg;
            return Ok(result);
        }

        /// <summary>
        /// 删除报告领取模式
        /// </summary>
        /// <param name="regNos">["220106098088","220106098089"]</param>
        /// <returns></returns>
        [HttpPost("DeleteReportReceiveMode")]
        public IActionResult DeleteReportReceiveMode([FromBody] string[] regNos)
        {
            result.Success = _reportMailPrintService.DeleteReportReceiveMode(regNos);
            return Ok(result);
        }

        /// <summary>
        /// 获取报告邮寄列表
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        [HttpPost("GetReportMailList")]
        [ProducesResponseType(typeof(ReportMailData[]), 200)]
        public IActionResult GetReportMailList([FromBody] ReportMailedQuery query)
        {
            string msg = string.Empty;
            result.ReturnData = _reportMailPrintService.GetReportMailList(query);
            result.ReturnMsg = msg;
            return Ok(result);
        }

        /// <summary>
        /// 未打印--邮寄报告
        /// </summary>
        /// <param name="report"></param>
        /// <returns></returns>
        [HttpPost("UnPrintSendReport")]
        public IActionResult UnPrintSendReport([FromBody] SendReport report)
        {
            string msg = string.Empty;
            result.Success = _reportMailPrintService.UnPrintSendReport(report, ref msg);
            result.ReturnMsg = msg;
            return Ok(result);
        }

        /// <summary>
        /// 未邮寄--邮寄报告
        /// </summary>
        /// <param name="regNos">["221209000001","221209000002"]</param>
        /// <returns></returns>
        [HttpPost("UnMailedSendReport")]
        public IActionResult UnMailedSendReport([FromBody] string[] regNos)
        {
            string msg = string.Empty;
            result.Success = _reportMailPrintService.UnMailedSendReport(regNos, ref msg);
            result.ReturnMsg = msg;
            return Ok(result);
        }

        /// <summary>
        /// 修改报告邮寄数据
        /// </summary>
        /// <param name="report"></param>
        /// <returns></returns>
        [HttpPost("UpdateReportMail")]
        public IActionResult UpdateReportMail([FromBody] SendReport report)
        {
            string msg = string.Empty;
            result.Success = _reportMailPrintService.UpdateReportMail(report, ref msg);
            result.ReturnMsg = msg;
            return Ok(result);
        }

        /// <summary>
        /// 获取报告打印列表
        /// </summary>
        /// <param name="printQuery"></param>
        /// <returns></returns>
        [HttpPost("NewGetReportPrintList")]
        [ProducesResponseType(typeof(ReportPrintData[]), 200)]
        public IActionResult NewGetReportPrintList([FromBody] NewReportPrintQuery printQuery)
        {
            string msg = string.Empty;
            result.ReturnData = _reportMailPrintService.NewGetReportPrintList(printQuery);
            result.ReturnMsg = msg;
            return Ok(result);
        }
    }
}
