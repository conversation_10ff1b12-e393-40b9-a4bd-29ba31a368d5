﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Peis.Model.DTO.PackageStatisticsReport;
using Peis.Model.Other.Input.PackageStatisticsReport;
using Peis.Service.IService;
using System.Collections.Generic;
using System.Drawing;

namespace Peis.API.Controllers.Statistics
{
    /// <summary>
    /// 人群套餐项目统计
    /// </summary>
    [Route("api/[controller]")]
    [ApiController]
    public class PackageStatisticsController : BaseApiController
    {
        private readonly IPackageStatisticsService _packageService;

        public PackageStatisticsController(IPackageStatisticsService packageService)
        {
            _packageService = packageService;
        }

        /// <summary>
        /// 套餐登记信息统计
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        [HttpPost("PackageInfoReport")]
        [ProducesResponseType(typeof(PackageInfoData), 200)]
        public IActionResult PackageInfoReport([FromBody] PackageInfoQuery query)
        {
            PackageInfoData data = new();
            var msg = string.Empty;
            result.Success = _packageService.PackageInfoReport(query, ref data, ref msg);
            result.ReturnData = data;
            result.ReturnMsg = msg;
            return Ok(result);
        }

        /// <summary>
        /// 体检人员费用列表
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        [HttpPost("PersonnelFeeList")]
        [ProducesResponseType(typeof(List<PersonnelFeeList>), 200)]
        public IActionResult PersonnelFeeList([FromBody] PersonnelFeeListQuery query)
        {
            PersonnelFeeList data = new();
            var msg = string.Empty;
            result.Success = _packageService.PersonnelFeeList(query, ref data, ref msg);
            result.ReturnData = data;
            result.ReturnMsg = msg;
            return Ok(result);
        }

        /// <summary>
        /// 体检项目结果清单
        /// </summary>
        /// <param name="peItemResultListQuery"></param>
        /// <returns></returns>
        [HttpPost("PeItemResultListQuery")]
        [ProducesResponseType(typeof(List<PeItemResultListReport>), 200)]
        public IActionResult PeItemResultListQuery([FromBody] PeItemResultListQuery peItemResultListQuery)
        {
            int totalNumber = 0;
            int totalPage = 0;
            result.Success = true;
            result.ReturnData = _packageService.PeItemResultList(peItemResultListQuery, ref totalNumber, ref totalPage);
            result.TotalNumber = totalNumber;
            result.TotalPage = totalPage;
            return Ok(result);
        }
        /// <summary>
        /// 体检项目结果清单excel导出接口（无分页）
        /// </summary>
        /// <param name="peItemResultListQuery"></param>
        /// <returns></returns>
        [HttpPost("ExportPeItemResultListQuery")]
        [ProducesResponseType(typeof(List<PeItemResultListReport>), 200)]
        [AllowAnonymous]
        public IActionResult ExportPeItemResultListQuery([FromBody] PeItemResultListQuery peItemResultListQuery)
        {
            var stream = _packageService.PeItemResultListExport(peItemResultListQuery);
            if (stream is null) return NoContent();
            return File(stream, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", "体检项目结果清单.xlsx");
        }
    }
}
