﻿using Peis.Model.Other.PeEnum;
using SqlSugar;
using System;

namespace Peis.Model.DataBaseEntity
{
    /// <summary>
    /// 组合记录标签表
    /// </summary>
    [SplitTable(SplitType.Year)]
    [SugarTable("PeRecordCombTag_{yyyy}", "组合记录标签表")]
    [SugarIndex("index_RecCombId_", nameof(RecCombId), OrderByType.Asc)]
    public class PeRecordCombTag
    {
        /// <summary>
        /// 组合记录标签Id
        /// </summary>        
        [SugarColumn(IsNullable = false, IsPrimaryKey = true)]
        public long Id { get; set; }

        /// <summary>
        /// 组合记录Id
        /// </summary>        
        [SugarColumn(IsNullable = false)]
        public long RecCombId { get; set; }

        /// <summary>
        /// 组合代码
        /// </summary>        
        [SugarColumn(IsNullable = false, Length = 8)]
        public string CombCode { get; set; }

        /// <summary>
        /// 小结标签
        /// </summary>        
        [SugarColumn(IsNullable = false, ColumnDataType = StaticConfig.CodeFirst_BigString)]
        public string CombTag { get; set; }

        /// <summary>
        /// 疾病代码
        /// </summary>        
        [SugarColumn(IsNullable = true, Length = 8)]
        public string DiseaseCode { get; set; }

        /// <summary>
        /// 疾病名称
        /// </summary>        
        [SugarColumn(IsNullable = true, Length = 255)]
        public string DiseaseName { get; set; }

        /// <summary>
        /// 自定义标签标识
        /// </summary>        
        [SugarColumn(IsNullable = false)]
        public bool IsCustom { get; set; }

        /// <summary>
        /// 异常类型
        /// </summary>        
        [SugarColumn(IsNullable = false)]
        public AbnormalType AbnormalType { get; set; }

        /// <summary>
        /// 关联的项目标签
        /// </summary>        
        [SugarColumn(IsNullable = false, Length = 250)]
        public string BindItemTags { get; set; }

        /// <summary>
        /// 体检登记时间（分表依据）
        /// </summary>
        [SplitField]
        [SugarColumn(IsNullable = false)]
        public DateTime RegisterTime { get; set; }
    }
}
