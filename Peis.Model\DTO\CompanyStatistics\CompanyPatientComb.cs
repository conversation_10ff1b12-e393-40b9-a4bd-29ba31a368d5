﻿using Peis.Model.Other.PeEnum;
using System;
using System.Collections.Generic;

namespace Peis.Model.DTO.CompanyStatistics
{
    /// <summary>
    /// 单位体检人员组合
    /// </summary>
    public class CompanyPatientComb
    {
        /// <summary>
        /// 组合名称列表
        /// </summary>
        public List<string> CombNames { get; set; }
        /// <summary>
        /// 体检人员组合结果列表
        /// </summary>
        public List<PatientCombResult> PatientCombResults { get; set; } = new List<PatientCombResult>();

        /// <summary>
        /// 体检人员组合结果
        /// </summary>
        public class PatientCombResult
        {
            /// <summary>
            /// 体检号
            /// </summary>
            public string RegNo { get; set; }
            /// <summary>
            /// 姓名
            /// </summary>
            public string Name { get; set; }
            /// <summary>
            /// 证件类型
            /// </summary>
            public string CardType { get; set; }
            /// <summary>
            /// 证件号码
            /// </summary>
            public string CardNo { get; set; }
            /// <summary>
            /// 性别
            /// </summary>
            public Sex? Sex { get; set; }
            /// <summary>
            /// 年龄
            /// </summary>
            public int? Age { get; set; }
            /// <summary>
            /// 年龄单位
            /// </summary>
            public AgeUnit? AgeUnit { get; set; }
            /// <summary>
            /// 体检日期（报到/激活日期）
            /// </summary>
            public DateTime ActiveDate { get; set; }
            /// <summary>
            /// 组合结果列表
            /// </summary>
            public List<string> CombResults { get; set; }
        }
    }
}
