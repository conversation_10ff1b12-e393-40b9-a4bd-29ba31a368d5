﻿using Peis.Model.Other.PeEnum;
using SqlSugar;
using System;

namespace Peis.Model.DataBaseEntity
{
    /// <summary>
    /// 项目记录表
    /// </summary>
    [SplitTable(SplitType.Year)]
    [SugarTable("PeRecordItem_{yyyy}", "项目记录表")]
    [SugarIndex("index_RecCombId_", nameof(RecCombId), OrderByType.Asc)]
    public class PeRecordItem
    {
        /// <summary>
        /// 项目记录Id
        /// </summary>        
        [SugarColumn(IsNullable = false, IsPrimaryKey = true)]
        public long Id { get; set; }

        /// <summary>
        /// 组合记录Id
        /// </summary>        
        [SugarColumn(IsNullable = false)]
        public long RecCombId { get; set; }

        /// <summary>
        /// 组合代码
        /// </summary>        
        [SugarColumn(IsNullable = false, Length = 8)]
        public string CombCode { get; set; }

        /// <summary>
        /// 项目代码
        /// </summary>        
        [SugarColumn(IsNullable = false, Length = 6)]
        public string ItemCode { get; set; }

        /// <summary>
        /// 项目名称
        /// </summary>        
        [SugarColumn(IsNullable = false, Length = 200)]
        public string ItemName { get; set; }

        /// <summary>
        /// 项目显示顺序
        /// </summary>        
        [SugarColumn(IsNullable = false)]
        public int SortIndex { get; set; }

        /// <summary>
        /// 项目结果
        /// </summary>        
        [SugarColumn(IsNullable = false, ColumnDataType = StaticConfig.CodeFirst_BigString)]
        public string ItemResult { get; set; }

        /// <summary>
        /// 项目结果类型
        /// </summary>        
        [SugarColumn(IsNullable = false)]
        public Other.PeEnum.ValueType ResultType { get; set; }

        /// <summary>
        /// 结果单位
        /// </summary>        
        [SugarColumn(IsNullable = true, Length = 10)]
        public string Unit { get; set; }

        /// <summary>
        /// 结果下限
        /// </summary>        
        [SugarColumn(IsNullable = true, ColumnDataType = StaticConfig.CodeFirst_BigString)]// todo:临时使用该字段接收参考范围
        public string LowerLimit { get; set; }

        /// <summary>
        /// 结果上限
        /// </summary>        
        [SugarColumn(IsNullable = true, Length = 10)]
        public string UpperLimit { get; set; }

        /// <summary>
        /// 提示符号
        /// </summary>        
        [SugarColumn(IsNullable = true, Length = 50)]
        public string Hint { get; set; }

        /// <summary>
        /// 项目结果的异常类型
        /// </summary>        
        [SugarColumn(IsNullable = false)]
        public AbnormalType AbnormalType { get; set; }

        /// <summary>
        /// 结果异常标识
        /// </summary>        
        [SugarColumn(IsNullable = false)]
        public bool IsError { get; set; }

        /// <summary>
        /// 参考范围
        /// </summary>
        [SugarColumn(IsNullable = true, ColumnDataType = StaticConfig.CodeFirst_BigString)]
        public string ReferenceRange {  get; set; }

        /// <summary>
        /// 体检登记时间（分表依据）
        /// </summary>        
        [SplitField]
        [SugarColumn(IsNullable = false)]
        public DateTime RegisterTime { get; set; }
    }
}