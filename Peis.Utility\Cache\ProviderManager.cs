﻿using Microsoft.Extensions.Caching.Memory;

namespace Peis.Utility.Cache;

/*
public class ProviderManager
{
    private DataAdapter _dataAdapter;

    public ProviderManager(DataAdapter adapter)
    {
        _dataAdapter = adapter;
    }

    public void Configure(DataAdapter adapter)
    {
        _dataAdapter = adapter;
    }

    public DataAdapter GetDataAdapter()
    {
        return _dataAdapter;
    }

    public AppCache CreateCache()
    {
        var cache = new AppCache();
        _dataAdapter.Fill(cache);
        return cache;
    }

    //public MemoryCacheAdapter CreateCache(IMemoryCache memoryCache)
    //{
    //    var cache = new MemoryCacheAdapter(memoryCache);
    //    _dataAdapter.Fill(cache);
    //    return cache;
    //}
}
*/