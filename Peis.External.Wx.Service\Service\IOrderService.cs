﻿using Peis.External.Wx.Service.Model.DTO;
using Peis.External.Wx.Service.Model.FromBody;
using Peis.Model.DataBaseEntity.External;

namespace Peis.External.Wx.Service.Service
{
    /// <summary>
    /// 微信接口
    /// </summary>
    public interface IOrderService
    {
        #region 个人

        /// <summary>
        /// 获取个人套餐
        /// </summary>
        /// <param name="data"></param>
        /// <param name="personalPackages">返回数据</param>
        /// <param name="msg">信息</param>
        /// <returns></returns>
        bool GetPersonalPackages(ParamData data, ref PersonalPackage[] personalPackages, ref string msg);

        /// <summary>
        /// 根据套餐编码获取套餐的组合
        /// </summary>
        /// <param name="data"></param>
        /// <param name="clusCombs">套餐包含的组合</param>
        /// <param name="msg">信息</param>
        /// <returns></returns>
        bool GetCombsByClusCode(ParamData data, ref ClusterCombs[] clusCombs, ref string msg);

        /// <summary>
        /// 获取所有项目分类下的组合
        /// </summary>
        /// <returns></returns>
        ItemClsComb[] GetItemClsComb();

        #endregion

        #region 团体
        /// <summary>
        /// 团体登录
        /// </summary>
        /// <param name="basicInfo">基本信息</param>
        /// <param name="teamInfo">返回数据</param>
        /// <param name="msg">信息</param>
        /// <returns></returns>
        bool TeamLogin(BasicInfo basicInfo, ref TeamInfo[] teamInfo, ref string msg);

        /// <summary>
        /// 团体登录
        /// </summary>
        /// <param name="request"></param>
        /// <param name="teamInfo"></param>
        /// <param name="msg"></param>
        /// <returns></returns>
        bool TeamLogin(TeamLoginRequest request, ref TeamInfo[] teamInfo, ref string msg);

        /// <summary>
        /// 获取所有单位数据
        /// </summary>
        /// <returns></returns>
        TjLnc[] GetCompanyList();
        #endregion

        #region 同步订单

        /// <summary>
        /// 个人订单数据同步
        /// </summary>
        /// <param name="order"></param>
        /// <param name="regNo"></param>
        /// <param name="msg"></param>
        /// <returns></returns>
        bool SyncPersonOrder(SyncPersonOrder order, ref string regNo, ref string msg);

        /// <summary>
        /// 删除个人记录
        /// </summary>
        /// <param name="regNo"></param>
        /// <returns></returns>
        bool DeletePersonRecord(string regNo);

        /// <summary>
        /// 插入缴费明细
        /// </summary>
        /// <param name="data"></param>
        /// <param name="msg"></param>
        /// <returns></returns>
        bool AddPayInfo(ParamData data, ref string msg);

        /// <summary>
        /// 团体订单同步
        /// </summary>
        /// <param name="order"></param>
        /// <param name="msg"></param>
        /// <returns></returns>
        bool SyncTeamOrder(SyncTeamOrder order, ref string msg);

        /// <summary>
        /// 删除团体订单记录
        /// </summary>
        /// <param name="regNo"></param>
        /// <returns></returns>
        bool DeleteTeamRecord(string regNo);

        /// <summary>
        /// 补充问卷调查
        /// </summary>
        /// <param name="question"></param>
        /// <param name="msg"></param>
        /// <returns></returns>
        bool RegisterQuestionSurvey(WeChatBookQuestion question, ref string msg);
        #endregion

        #region 报告

        /// <summary>
        /// 获取报告列表
        /// </summary>
        /// <param name="bsInfo">入参</param>
        /// <param name="reports">返回数据</param>
        /// <param name="msg">信息</param>
        /// <returns></returns>
        bool GetReportList(BasicInfo bsInfo, ref Report[] reports, ref string msg);

        /// <summary>
        /// 获取报告详情
        /// </summary>
        /// <param name="regNo">体检号</param>
        /// <param name="detail">报告详情</param>
        /// <param name="msg">返回信息</param>
        /// <returns></returns>
        bool GetReportDetail(string regNo, ref ReportDetail detail, ref string msg);

        /// <summary>
        /// 获取报告Pdf地址
        /// </summary>
        /// <param name="regNo">体检号</param>
        /// <param name="url">地址</param>
        /// <returns></returns>
        bool GetReportPdfUrl(string regNo, ref string url);

        /// <summary>
        /// 获取报告影像图文地址
        /// </summary>
        /// <param name="regNo">体检号</param>
        /// <param name="respData">返回数据</param>
        /// <returns></returns>
        bool GetGraphicTextUrl(string regNo, ref string respData);
        #endregion
    }
}
