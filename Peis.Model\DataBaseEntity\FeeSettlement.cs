﻿using Peis.Model.Other.PeEnum;
using Peis.Model.TableFilter;
using SqlSugar;
using System;

namespace Peis.Model.DataBaseEntity
{
    ///<summary>
    ///结算表
    ///</summary>
    [SugarTable("FeeSettlement")]
    public class FeeSettlement: IHospCodeFilter
    {
        /// <summary>
        /// 结算号
        /// </summary>        
        [SugarColumn(IsNullable = false, IsPrimaryKey = true, Length = 10)]
        public string SettlementNo { get; set; }

        /// <summary>
        /// 体检号
        /// </summary>        
        [SugarColumn(IsNullable = false, Length = 12)]
        public string RegNo { get; set; }

        /// <summary>
        /// 结算金额
        /// </summary>        
        [SugarColumn(IsNullable = false, Length = 12, DecimalDigits = 2)]
        public decimal Price { get; set; }

        /// <summary>
        /// 操作员编码
        /// </summary>        
        [SugarColumn(IsNullable = false, Length = 10)]
        public string OperatorCode { get; set; }

        /// <summary>
        /// 结算时间
        /// </summary>        
        [SugarColumn(IsNullable = true)]
        public DateTime? SettlementTime { get; set; }

        /// <summary>
        /// 结算状态（1 支付中 2 收费 3 退款）
        /// </summary>        
        [SugarColumn(IsNullable = false)]
        public SettlementType SettlementType { get; set; }

        /// <summary>
        /// 收费后被退款标识（退款后，原结算的退款标识为true）
        /// </summary>        
        [SugarColumn(IsNullable = false)]
        public bool Refunded { get; set; }

        /// <summary>
        /// 原结算号（退款时关联用）
        /// </summary>        
        [SugarColumn(IsNullable = true, Length = 10)]
        public string OriginalSettlementNo { get; set; }

        /// <summary>
        /// 日结标识
        /// </summary>        
        [SugarColumn(IsNullable = false)]
        public bool HasEndDaySettlement { get; set; }

        /// <summary>
        /// 日结时间
        /// </summary>        
        [SugarColumn(IsNullable = true)]
        public DateTime? EndDaySettlementTime { get; set; }

        /// <summary>
        /// 院区编码
        /// </summary>        
        [SugarColumn(IsNullable = false, Length = 1)]
        public string HospCode { get; set; }
    }
}