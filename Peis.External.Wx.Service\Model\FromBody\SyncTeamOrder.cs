﻿namespace Peis.External.Wx.Service.Model.FromBody
{
    /// <summary>
    /// 微信团体订单同步数据
    /// </summary>
    public class SyncTeamOrder
    {
        /// <summary>
        /// 体检号
        /// </summary>
        public string RegNo { get; set; }

        /// <summary>
        /// 预约号(微信订单号)
        /// </summary>
        public string BookNo { get; set; }

        /// <summary>
        /// 体检日期
        /// </summary>
        public DateTime BeginTime { get; set; }

        /// <summary>
        /// 家族史
        /// </summary>        
        public string? FamilyMedicalHistory { get; set; }

        /// <summary>
        /// 既往病史
        /// </summary>        
        public string? PastMedicalHistory { get; set; }

        /// <summary>
        /// 手术状况
        /// </summary>        
        public string? OperationStatus { get; set; }

        /// <summary>
        /// 吸烟习惯
        /// </summary>        
        public string SmokingHabit { get; set; }

        /// <summary>
        /// 喝酒习惯
        /// </summary>        
        public string? DrinkingHabit { get; set; }

        /// <summary>
        /// 生活习惯
        /// </summary>        
        public string? LivingHabit { get; set; }

        /// <summary>
        /// 现在病况
        /// </summary>        
        public string? CurrentCondition { get; set; }

        /// <summary>
        /// 问卷答案
        /// </summary>        
        public string? QuestionnaireAnswer { get; set; }
    }
}
