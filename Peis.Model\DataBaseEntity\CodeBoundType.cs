﻿using Peis.Model.Other.PeEnum;
using SqlSugar;

namespace Peis.Model.DataBaseEntity
{
    ///<summary>
    ///参考范围类型
    ///</summary>
    [SugarTable("CodeBoundType")]
    public class CodeBoundType
    {
        /// <summary>
        /// 参考范围类型代码
        /// </summary>        
        [SugarColumn(IsNullable = false, IsPrimaryKey = true, Length = 6)]
        public string BoundTypeCode { get; set; }

        /// <summary>
        /// 参考范围类型名称
        /// </summary>        
        [SugarColumn(IsNullable = false, Length = 40)]
        public string BoundTypeName { get; set; }

        /// <summary>
        /// 性别（0 通用 1 男  2 女）
        /// </summary>        
        [SugarColumn(IsNullable = false)]
        public Sex Sex { get; set; }

        /// <summary>
        /// 年龄上限
        /// </summary>        
        [SugarColumn(IsNullable = false, Length = 10)]
        public string UpperAgeLimit { get; set; }

        /// <summary>
        /// 年龄下限
        /// </summary>        
        [SugarColumn(IsNullable = false, Length = 10)]
        public string LowerAgeLimit { get; set; }
    }
}