﻿using Peis.Model.Other.PeEnum;
using SqlSugar;

namespace Peis.Model.DataBaseEntity.External
{
    /// <summary>
    /// 门诊账单组合关联表
    /// </summary>
    [SplitTable(SplitType.Year)]
    [SugarTable("ExtHisBillComb_{yyyy}", "门诊账单组合关联表")]
    [SugarIndex("index_RegNo_", nameof(RegNo), OrderByType.Asc)]
    public class ExtHisBillComb
    {
        /// <summary>
        /// 登记组合Id
        /// </summary>
        [SugarColumn(IsNullable = false, IsPrimaryKey = true, IsIdentity = false)]
        public long RegCombId { get; set; }

        /// <summary>
        /// 体检号
        /// </summary>
        [SugarColumn(IsNullable = false, Length = 12)]
        public string RegNo { get; set; }

        /// <summary>
        /// 姓名
        /// </summary>
        [SugarColumn(IsNullable = false, Length = 50)]
        public string Name { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        [SugarColumn(IsNullable = false, IsPrimaryKey = true)]
        public DateTime CreateTime { get; set; }

        /// <summary>
        /// 组合代码
        /// </summary>
        [SugarColumn(IsNullable = false, Length = 12)]
        public string CombCode { get; set; }

        /// <summary>
        /// 项目名称
        /// </summary>
        [SugarColumn(IsNullable = false, Length = 400)]
        public string CombName { get; set; }

        /// <summary>
        /// 折扣
        /// </summary>        
        [SugarColumn(IsNullable = false, Length = 8, DecimalDigits = 2)]
        public decimal Discount { get; set; }

        /// <summary>
        /// 项目分类
        /// </summary>
        [SugarColumn(IsNullable = false, Length = 6)]
        public string ClsCode { get; set; }

        /// <summary>
        /// 检查科室代码
        /// </summary>
        [SugarColumn(IsNullable = false, Length = 10)]
        public string ExamDeptCode { get; set; }

        /// <summary>
        /// 发票ID
        /// </summary>
        [SugarColumn(IsNullable = true, Length = 20)]
        public string InvoiceId { get; set; }

        /// <summary>
        /// 发票号
        /// </summary>
        [SugarColumn(IsNullable = true, Length = 20)]
        public string InvoiceNo { get; set; }

        /// <summary>
        /// 支付状态
        /// </summary>
        [SugarColumn(IsNullable = false)]
        public PayStatus PayStatus { get; set; }

        /// <summary>
        /// 体检登记时间（分表依据）
        /// </summary>
        [SplitField]
        [SugarColumn(IsNullable = false)]
        public DateTime RegisterTime { get; set; }

        /// <summary>
        /// 单价
        /// </summary>        
        [SugarColumn(IsNullable = false, Length = 12, DecimalDigits = 2)]
        public decimal Price { get; set; }
    }
}
