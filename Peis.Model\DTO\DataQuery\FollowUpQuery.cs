﻿using Peis.Model.Other.PeEnum;
using System;

namespace Peis.Model.DTO.DataQuery
{
    /// <summary>
    /// 随访查询
    /// </summary>
    public class FollowUpQuery
    {
        /// <summary>
        /// 开始时间
        /// </summary>
        public DateTime StartTime { get; set; }

        /// <summary>
        /// 结束时间
        /// </summary>
        public DateTime EndTime { get; set; }

        /// <summary>
        /// 关键字 (体检号/姓名)
        /// </summary>
        public string KeyWord { get; set; }

        /// <summary>
        /// 体检类型(0:所有 1:个人 2:团体)
        /// </summary>
        public int PeType { get; set; }

        /// <summary>
        /// 单位代码
        /// </summary>
        public string CompanyCode { get; set; }
        /// <summary>
        /// 部门代码
        /// </summary>
        public string CompanyDeptCode { get; set; }
        /// <summary>
        /// 是否需要后续随访
        /// </summary>
        public bool? NeedFinallyNotify { get; set; }
        /// <summary>
        /// 随访来源
        /// </summary>
        public FollowUpSource Source { get; set; }
    }
}
