﻿using Peis.Model.Other.PeEnum;

namespace Peis.Model.DTO.DataQuery
{
    public class ExamItemEntryQuery:PageQuery
    {
        //体检号1、档案卡号1、姓名1、性别1、身份证1、单位1、套餐1、分类1、状态1、[体检时间]、[所有|个人|团体]、[全部|一般检查|医生检查|功能检查|检验检查]

        /// <summary>
        /// 体检号
        /// </summary>
        public string RegNo { get; set; }
        /// <summary>
        /// 档案卡号
        /// </summary>
        public string PatCode { get; set; }
        /// <summary>
        /// 姓名
        /// </summary>
        public string Name { get; set; }
        /// <summary>
        /// 性别
        /// </summary>
        public Sex Sex { get; set; }
        /// <summary>
        /// 证件号
        /// </summary>
        public string CardNo { get; set; }
        /// <summary>
        /// 单位代码
        /// </summary>
        public string CompanyCode { get; set; }
        /// <summary>
        /// 套餐代码
        /// </summary>
        public string ClusterCode { get; set; }
        /// <summary>
        /// 体检分类
        /// </summary>
        public PeCls PeCls { get; set; }
        /// <summary>
        /// 体检状态
        /// </summary>
        public PeStatus PeStatus { get; set; }
        /// <summary>
        /// 个人/团体/所有
        /// </summary>
        public PersonCompany PersonCompany { get; set; }
        /// <summary>
        /// 开始时间
        /// </summary>
        public DateTime? BeginTime { get; set; }
        /// <summary>
        /// 结束时间
        /// </summary>
        public DateTime? EndTime { get; set; }
        /// <summary>
        /// 检查类型
        /// </summary>
        public CheckCls CheckCls { get; set; }
        /// <summary>
        /// 单位次数
        /// </summary>
        public int CompanyTimes { get; set; }
    }
}
