﻿using Peis.Model.Other.PeEnum;

namespace Peis.Model.DataBaseEntity.Occupation
{
    /// <summary>
    /// 体检职业病信息表
    /// </summary>
    [SugarTable("PeRegisterOccupation")]
    public class PeRegisterOccupation
    {
        /// <summary>
        /// 体检号
        /// </summary>
        [SugarColumn(IsNullable = false, IsPrimaryKey = true, Length = 12)]
        public string RegNo { get; set; }

        /// <summary>
        /// 工号
        /// </summary>
        [SugarColumn(IsNullable = true, Length = 10)]
        public string JobId { get; set; }

        /// <summary>
        /// 工种
        /// </summary>
        [SugarColumn(IsNullable = false, Length = 40)]
        public string JobType { get; set; }

        /// <summary>
        /// 工种名称（当工种选择其他工种时必填）
        /// </summary>
        [SugarColumn(IsNullable = true, Length = 200)]
        public string JobName { get; set; }

        /// <summary>
        /// 在岗状态
        /// </summary>
        [SugarColumn(IsNullable = false, Length = 10)]
        public string JobStatus { get; set; }

        /// <summary>
        /// 总工龄年
        /// </summary>
        [SugarColumn(IsNullable = false)]
        public int TotalYearsOfWork { get; set; }

        /// <summary>
        /// 总工龄月
        /// </summary>
        [SugarColumn(IsNullable = false)]
        public int TotalMonthsOfWork { get; set; }

        /// <summary>
        /// 监测类型（1 常规监测 2 主动监测）
        /// </summary>
        [SugarColumn(IsNullable = false)]
        public int MonitoringType { get; set; }

        /// <summary>
        /// 体检状态（0：未检查；1：正在检查；2：已检完；3：已总检；4：已审核）
        /// </summary>        
        [SugarColumn(IsNullable = false)]
        public PeStatus PeStatus { get; set; }

        /// <summary>
        /// 报告类型
        /// </summary>
        [SugarColumn(IsNullable = false, Length = 32)]
        public string ReportType { get; set; }

        /// <summary>
        /// 报告打印标识
        /// </summary>        
        [SugarColumn(IsNullable = false)]
        public bool ReportPrinted { get; set; }

        /// <summary>
        /// 报告打印时间
        /// </summary>        
        [SugarColumn(IsNullable = true)]
        public DateTime? ReportPrintedTime { get; set; }

        /// <summary>
        /// 上传标识
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public bool IsUploaded { get; set; }

        /// <summary>
        /// 质控编号（上传成功返回）
        /// </summary>
        [SugarColumn(IsNullable = true, Length = 50)]
        public string ZkCode { get; set; }

        /// <summary>
        /// 疑似职业病告知书编号（日期+报告单总数)
        /// </summary>
        [SugarColumn(IsNullable = true, Length = 50)]
        public string DubiousOccupReportNo { get; set; }

        /// <summary>
        /// 车间
        /// </summary>
        [SugarColumn(IsNullable = true, Length = 512)]
        public string Workshop { get; set; }

        /// <summary>
        /// 防护措施
        /// </summary>
        [SugarColumn(IsNullable = true, Length = 512)]
        public string ProtectiveMeasures { get; set; }
    }
}
