﻿namespace Peis.Model.DTO.CriticalValue
{
    /// <summary>
    /// 危急值项目
    /// </summary>
    public class ImportantAbnormalItem
    {
        /// <summary>
        /// 项目代码
        /// </summary>
        public string ItemCode { get; set; }

        /// <summary>
        /// 项目名称
        /// </summary>
        public string ItemName { get; set; }

        /// <summary>
        /// 项目结果
        /// </summary>
        public string ItemResult { get; set; }

        /// <summary>
        /// 结果下限
        /// </summary>
        public string LowerLimit { get; set; }

        /// <summary>
        /// 结果上限
        /// </summary>
        public string UpperLimit { get; set; }

        /// <summary>
        /// 参考范围
        /// </summary>
        public string  ReferenceRange { get; set; }
    }
}
