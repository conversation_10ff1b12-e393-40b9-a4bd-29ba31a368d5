﻿using Peis.Model.Other.PeEnum;
using Peis.Model.TableFilter;

namespace Peis.Model.DataBaseEntity
{
    ///<summary>
    ///单位套餐信息
    ///</summary>
    [SugarTable("CodeCompanyCluster")]
    public class CodeCompanyCluster: IHospCodeFilter
    {
        /// <summary>
        /// 套餐代码
        /// </summary>        
        [SugarColumn(IsNullable = false, IsPrimaryKey = true, Length = 6)]
        public string ClusterCode { get; set; }

        /// <summary>
        /// 套餐名称
        /// </summary>        
        [SugarColumn(IsNullable = false, Length = 200)]
        public string ClusterName { get; set; }

        /// <summary>
        /// 单位代码
        /// </summary>        
        [SugarColumn(IsNullable = false, Length = 6)]
        public string CompanyCode { get; set; }

        /// <summary>
        /// 单位次数
        /// </summary>        
        [SugarColumn(IsNullable = false)]
        public int CompanyTimes { get; set; }

        /// <summary>
        /// 性别（0通用1男2女）
        /// </summary>        
        [SugarColumn(IsNullable = false)]
        public Sex Sex { get; set; }

        /// <summary>
        /// 婚姻状况（0未知1未婚2已婚3离异4丧偶5）
        /// </summary>        
        [SugarColumn(IsNullable = true)]
        public MarryStatus? MarryStatus { get; set; }

        /// <summary>
        /// 年龄上限
        /// </summary>        
        [SugarColumn(IsNullable = false)]
        public int UpperAgeLimit { get; set; }

        /// <summary>
        /// 年龄下限
        /// </summary>        
        [SugarColumn(IsNullable = false)]
        public int LowerAgeLimit { get; set; }

        /// <summary>
        /// 单价
        /// </summary>        
        [SugarColumn(IsNullable = false)]
        public decimal Price { get; set; }

        /// <summary>
        /// 指引单类型
        /// </summary>        
        [SugarColumn(IsNullable = false, Length = 32)]
        public string GuidanceType { get; set; }

        /// <summary>
        /// 报告类型
        /// </summary>        
        [SugarColumn(IsNullable = false, Length = 32)]
        public string ReportType { get; set; }

        /// <summary>
        /// 加项折扣
        /// </summary>        
        [SugarColumn(IsNullable = true, Length = 12, DecimalDigits = 2)]
        public decimal? AddItemDiscount { get; set; }

        /// <summary>
        /// 是否允许额度内项目可替
        /// </summary>        
        [SugarColumn(IsNullable = false)]
        public bool AllowChangeItem { get; set; }

        /// <summary>
        /// 备注
        /// </summary>        
        [SugarColumn(IsNullable = true, Length = 255)]
        public string Note { get; set; }

        /// <summary>
        /// 院区编码
        /// </summary>        
        [SugarColumn(IsNullable = false, Length = 1)]
        public string HospCode { get; set; }

        /// <summary>
        /// 是否职业
        /// </summary>
        [SugarColumn(IsNullable = false, DefaultValue = "0")]
        public bool IsOccupation { get; set; }

        /// <summary>
        /// 危害因素列表
        /// </summary>
        [SugarColumn(IsNullable = true, IsJson = true, Length = 255)]
        public List<string> HazardFactors { get; set; }

        /// <summary>
        /// 在岗状态
        /// </summary>
        [SugarColumn(IsNullable = true, Length = 10)]
        public string JobStatus { get; set; }

        #region Ext
        /// <summary>
        /// 对应组合
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public List<MapCompanyClusterComb> MapCompanyClusterCombs { get; set; }

        /// <summary>
        /// 危害因素名称
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public string HazardNames { get; set; }

        /// <summary>
        /// 在岗状态名称
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public string JobStatusName { get; set; }
        #endregion
    }
}