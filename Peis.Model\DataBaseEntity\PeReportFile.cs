﻿using Peis.Utility.Helper;
using System.IO;

namespace Peis.Model.DataBaseEntity;

/// <summary>
/// 体检报告文件信息
/// </summary>
[SplitTable(SplitType.Year)]
[SugarTable("PeReportFile_{yyyy}", "体检报告文件信息")]
[SugarIndex("index_PeReportFile_RegNo_RegT", nameof(RegNo), OrderByType.Asc, nameof(RegisterTime), OrderByType.Asc)]
public class PeReportFile
{
    public PeReportFile()
    {

    }

    public PeReportFile(
        [NotNull] long id,
        [NotNull] PeRegister reg,
        [NotNull] string filePath,
        [NotNull] FileInfo file)
    {
        Id = id;
        RegNo = reg.RegNo;
        RegisterTime = reg.RegisterTime;
        ReportType = reg.ReportType;
        HospCode = reg.HospCode;
        FilePath = filePath.Replace(@"\", @"/");
        FileName = Path.GetFileNameWithoutExtension(file.Name);
        FileSize = file.Length;
        FileType = file.Extension;
        IsWithStamp = false;

        SetCreatedTime();
    }

    public void SetCreatedTime()
    {
        CreatedTime = DateTime.Now;
    }

    public void SetUpdatedTime()
    {
        UpdatedTime = DateTime.Now;
    }

    public void Modify([NotNull] string filePath, [NotNull] FileInfo file)
    {
        FilePath = filePath.Replace(@"\", @"/");
        FileName = Path.GetFileNameWithoutExtension(filePath);
        FileSize = file.Length;
        FileType = file.Extension;

        SetUpdatedTime();
    }

    public void SetSyncPEReportTimes()
    {
        ++SyncPEReportTimes;
        SetUpdatedTime();
    }

    public void SetIsWithStamp(bool isWithStamp)
    {
        IsWithStamp = isWithStamp;
    }

    public void SetReportType(string reportType)
    {
        ReportType = reportType;
    }

    /// <summary>
    /// 主键Id
    /// </summary>        
    [SugarColumn(IsNullable = false, IsPrimaryKey = true)]
    public long Id { get; set; }

    /// <summary>
    /// 体检号
    /// </summary>        
    [SugarColumn(IsNullable = false, Length = 12)]
    public string RegNo { get; set; }

    /// <summary>
    /// 体检登记时间（分表依据）
    /// </summary>        
    [SplitField]
    [SugarColumn(IsNullable = false)]
    public DateTime RegisterTime { get; set; }

    /// <summary>
    /// 报告格式
    /// </summary>        
    [SugarColumn(IsNullable = false, Length = 32)]
    public string ReportType { get; set; }

    /// <summary>
    /// 院区编码
    /// </summary>        
    [SugarColumn(IsNullable = false, Length = 1)]
    public string HospCode { get; set; }

    /// <summary>
    /// 文件名称
    /// </summary>        
    [SugarColumn(IsNullable = false, Length = 90)]
    public string FileName { get; set; }

    /// <summary>
    /// 文件路径
    /// </summary>        
    [SugarColumn(IsNullable = false, Length = 256)]
    public string FilePath { get; set; }

    /// <summary>
    /// 文件大小，单位字节
    /// </summary>        
    [SugarColumn(IsNullable = false)]
    public long FileSize { get; set; }

    /// <summary>
    /// 文件格式
    /// </summary>        
    [SugarColumn(IsNullable = false, Length = 30)]
    public string FileType { get; set; }

    /// <summary>
    /// 创建时间
    /// </summary>        
    [SugarColumn(IsNullable = false)]
    public DateTime CreatedTime { get; set; }

    /// <summary>
    /// 修改时间
    /// </summary>        
    [SugarColumn(IsNullable = true)]
    public DateTime? UpdatedTime { get; set; }

    /// <summary>
    /// 同步平台次数
    /// </summary>        
    [SugarColumn(IsNullable = true)]
    public int SyncPEReportTimes { get; set; }

    /// <summary>
    /// 是否带公章
    /// </summary>      
    public bool IsWithStamp { get; set; }

    #region Ext
    /// <summary>
    /// 文件全路径地址
    /// </summary>
    [SugarColumn(IsIgnore = true)]
    public string FileFullPath => FilePath.IsNullOrEmpty() ? "" :
        Path.Combine(Appsettings.GetSectionValue(ResxCommon.ConfigFileServiceUrl), FilePath);
    #endregion
}
