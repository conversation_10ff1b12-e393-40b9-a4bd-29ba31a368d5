﻿using Peis.Model.Other.PeEnum;

namespace Peis.External.Wx.Service.Model.FromBody
{
    /// <summary>
    /// 通用参数
    /// </summary>
    public class ParamData
    {
        /// <summary>
        /// 体检号
        /// </summary>
        public string? RegNo { get; set; }

        /// <summary>
        /// 套餐编码
        /// </summary>
        public string? ClusCode { get; set; }

        /// <summary>
        /// 体检分类
        /// </summary>
        public PeCls PeCls { get; set; }

        /// <summary>
        /// 体检分类
        /// </summary>
        public bool? IsCompanyCheck { get; set; }

        /// <summary>
        /// 院区代码
        /// </summary>
        public string? HospCode { get; set; }
    }
}
