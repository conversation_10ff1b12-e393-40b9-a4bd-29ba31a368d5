﻿using Microsoft.AspNetCore.Mvc;
using Peis.Model.DTO.Notice;
using Peis.Service.IService;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Peis.API.Controllers.DataMaintenance
{
    [Route("api/[controller]")]
    [ApiController]
    public class NoticeController : BaseApiController
    {
        private readonly INoticeService _noticeService;

        public NoticeController(INoticeService noticeService)
        {
            _noticeService = noticeService;
        }

        /// <summary>
        /// 发布公告
        /// </summary>
        /// <param name="noticeContent"></param>
        /// <returns></returns>
        [HttpPost("CreateNotice")]
        public async Task<IActionResult> CreateNotice([FromBody] NewNotice noticeContent)
        {
            result.Success = await _noticeService.CreateNoticeAsync(noticeContent);
            return Ok(result);
        }

        /// <summary>
        /// 获取公告列表
        /// </summary>
        /// <param name="pageNumber"></param>
        /// <param name="pageSize"></param>
        /// <returns></returns>
        [ProducesResponseType(typeof(List<NewNotice>), 200)]
        [HttpPost("ReadNotices")]
        public IActionResult ReadNotices(int pageNumber, int pageSize)
        {
            int totalNumber = 0;
            int totalPage = 0;

            result.ReturnData = _noticeService.ReadNotices(pageNumber, pageSize, ref totalNumber, ref totalPage);
            result.TotalNumber = totalNumber;
            result.TotalPage = totalPage;
            return Ok(result);
        }

        /// <summary>
        /// 公告已读
        /// </summary>
        /// <param name="noticeId"></param>
        /// <param name="operCode"></param>
        /// <returns></returns>
        [HttpPost("UserReadNotice")]
        public IActionResult UserReadNotice(long noticeId, string operCode)
        {
            result.Success = _noticeService.UserReadNotice(noticeId, operCode);
            return Ok(result);
        }
    }
}
