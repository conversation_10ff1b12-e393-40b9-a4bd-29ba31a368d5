﻿namespace Peis.Model.DataBaseEntity
{
    ///<summary>
    ///主检问题单
    ///</summary>
    [SugarTable("PeReportExtReturnChecked")]
    public class PeReportExtReturnChecked
    {
        /// <summary>
        /// Id
        /// </summary>        
        [SugarColumn(IsNullable = false, IsPrimaryKey = true, IsIdentity = true)]
        public int Id { get; set; }

        /// <summary>
        /// 体检号
        /// </summary>        
        [SugarColumn(IsNullable = false, Length = 12)]
        public string RegNo { get; set; }

        /// <summary>
        /// 咨询医生代码
        /// </summary>        
        [SugarColumn(IsNullable = false, Length = 10)]
        public string QuestionerCode { get; set; }

        /// <summary>
        /// 咨询医生名称
        /// </summary>        
        [SugarColumn(IsNullable = false, Length = 50)]
        public string QuestionerName { get; set; }

        /// <summary>
        /// 答复医生代码
        /// </summary>        
        [SugarColumn(IsNullable = false, Length = 10)]
        public string ReplyerCode { get; set; }

        /// <summary>
        /// 答复医生名称
        /// </summary>        
        [SugarColumn(IsNullable = false, Length = 50)]
        public string ReplyerName { get; set; }

        /// <summary>
        /// 咨询内容
        /// </summary>        
        [SugarColumn(IsNullable = false, ColumnDataType = StaticConfig.CodeFirst_BigString)]
        public string QuestionContent { get; set; }

        /// <summary>
        /// 答复内容
        /// </summary>        
        [SugarColumn(IsNullable = true, ColumnDataType = StaticConfig.CodeFirst_BigString)]
        public string ReplyContent { get; set; }

        /// <summary>
        /// 咨询时间
        /// </summary>        
        [SugarColumn(IsNullable = false)]
        public DateTime QuestionerTime { get; set; }

        /// <summary>
        /// 答复时间
        /// </summary>        
        [SugarColumn(IsNullable = true)]
        public DateTime? ReplyTime { get; set; }
        /// <summary>
        /// 是否职业病
        /// </summary>
        [SugarColumn(IsNullable = false, DefaultValue = "0")]
        public bool IsOccupation { get; set; }
    }
}