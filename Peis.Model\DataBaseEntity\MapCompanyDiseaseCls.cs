﻿using SqlSugar;

namespace Peis.Model.DataBaseEntity
{
    ///<summary>
    ///单位疾病分类对应(疾病情况统计报表)
    ///</summary>
    [SugarTable("MapCompanyDiseaseCls")]
    public class MapCompanyDiseaseCls
    {
        /// <summary>
        /// 单位编码
        /// </summary>        
        [SugarColumn(IsNullable = false, Length = 6)]
        public string CompanyCode { get; set; }

        /// <summary>
        /// 疾病分类代码
        /// </summary>        
        [SugarColumn(IsNullable = false, Length = 6)]
        public string DiseaseClsCode { get; set; }

        /// <summary>
        /// 疾病代码
        /// </summary>        
        [SugarColumn(IsNullable = false, Length = 8)]
        public string DiseaseCode { get; set; }
    }
}