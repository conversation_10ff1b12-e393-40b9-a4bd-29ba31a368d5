﻿using Peis.Model.Other.PeEnum;

namespace Peis.Model.DataBaseEntity.Occupation
{
    /// <summary>
    /// 检查项目限定
    /// </summary>
    [SugarTable]
    public class CodeOccupationalItemLimit
    {
        /// <summary>
        /// 组合代码
        /// </summary>
        [SugarColumn(Length =5,IsPrimaryKey = true)]
        public string ItemCode { get; set; }
        /// <summary>
        /// 组合名称
        /// </summary>
        [SugarColumn(Length =50,IsNullable = false)]
        public string ItemName { get; set; }
        /// <summary>
        /// 参考下限
        /// </summary>
        [SugarColumn(IsNullable =true)]
        public int? LowerLimit { get; set; }
        /// <summary>
        /// 参考上限
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public int? UpperLimit { get; set; }
        /// <summary>
        /// 结果最大值
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public int? MaxValue { get; set; }
        /// <summary>
        /// 结果最小值
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public int? MinValue { get; set; }
        /// <summary>
        /// 性别
        /// </summary>
        [SugarColumn(IsPrimaryKey = true)]
        public Sex Sex { get; set; }
    }
}
