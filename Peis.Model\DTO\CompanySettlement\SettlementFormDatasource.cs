﻿namespace Peis.Model.DTO.CompanySettlement
{
    /// <summary>
    /// 费用结算打印单数据源
    /// </summary>
    public class SettlementFormDatasource
    {
        /// <summary>
        /// 结算单位
        /// </summary>
        public string CompanyName { get; set; }
        /// <summary>
        /// 发票抬头
        /// </summary>
        public string InvoiceName { set; get; }
        /// <summary>
        /// 打印日期
        /// </summary>
        public DateTime PrinttTime { set; get; }
        /// <summary>
        /// 结算批次号
        /// </summary>
        public string SeqNo { set; get; }
        /// <summary>
        /// 费用统计日期区间
        /// </summary>
        public string DateScope { set; get; }
        /// <summary>
        /// 结算方式
        /// </summary>
        public string Type { set; get; }
        /// <summary>
        /// 体检人数
        /// </summary>
        public int Count { set; get; }
        /// <summary>
        /// 结算金额
        /// </summary>
        public decimal Price { set; get; }
        /// <summary>
        /// 结算日期
        /// </summary>
        public DateTime ChargeTime { set; get; }
        /// <summary>
        /// 审核日期
        /// </summary>
        public DateTime AuditTime { set; get; }
        /// <summary>
        /// 申请人员:对应BS的经办人
        /// </summary>
        public string CreateOperator { set; get; }
        /// <summary>
        /// 审核人员:对应BS的审核人
        /// </summary>
        public string AuditOperator { set; get; }
    }
}
