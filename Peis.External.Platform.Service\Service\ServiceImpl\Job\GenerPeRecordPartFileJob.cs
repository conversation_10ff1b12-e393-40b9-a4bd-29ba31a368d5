﻿using Peis.Quartz.UI.BaseService;
using Peis.Service.IService;

namespace Peis.External.Platform.Service.Service.ServiceImpl.Job;

/// <summary>
/// 作业：定时生成部位图文件信息
/// </summary>
public class GenerPeRecordPartFileJob : IJobService
{
    readonly IFileReportGraphicTextService _reportGraphicTextService;

    public GenerPeRecordPartFileJob(IFileReportGraphicTextService reportGraphicTextService)
    {
        _reportGraphicTextService = reportGraphicTextService;
    }

    public string ExecuteService(string parameter)
    {
       var query = !parameter.IsNullOrEmpty() ? JsonHelper.ToObject<GenerPeRecordPartFileQuery>(parameter) : new GenerPeRecordPartFileQuery
        {
            IsCover = true
        };

        _reportGraphicTextService.SyncReportGraphicTextFiles(query.RegNo, out var msg, query.StartCreatedTime, query.EndCreatedTime, query.IsCover);
        return msg;
    }

    public class GenerPeRecordPartFileQuery
    {
        /// <summary>
        /// 体检号
        /// </summary>
        public string RegNo { get; set; }

        /// <summary>
        /// 是否覆盖已有的文件
        /// </summary>
        public bool IsCover { get; set; }

        public DateTime? StartCreatedTime { get; set; }

        public DateTime? EndCreatedTime { get; set; }
    }
}
