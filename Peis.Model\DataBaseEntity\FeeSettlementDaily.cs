﻿using Peis.Model.TableFilter;
using SqlSugar;
using System;

namespace Peis.Model.DataBaseEntity
{
    ///<summary>
    ///结算日结表
    ///</summary>
    [SugarTable("FeeSettlementDaily")]
    public class FeeSettlementDaily: IHospCodeFilter
    {
        /// <summary>
        /// 自增id
        /// </summary>        
        [SugarColumn(IsNullable = false, IsPrimaryKey = true, IsIdentity = true)]
        public int Id { get; set; }

        /// <summary>
        /// 日结开始时间
        /// </summary>        
        [SugarColumn(IsNullable = false)]
        public DateTime BeginTime { get; set; }

        /// <summary>
        /// 日结结束时间
        /// </summary>        
        [SugarColumn(IsNullable = false)]
        public DateTime EndTime { get; set; }

        /// <summary>
        /// 操作员
        /// </summary>        
        [SugarColumn(IsNullable = false, Length = 10)]
        public string OperatorCode { get; set; }

        /// <summary>
        /// 操作日期
        /// </summary>        
        [SugarColumn(IsNullable = false)]
        public DateTime OperateDate { get; set; }

        /// <summary>
        /// 院区编码
        /// </summary>        
        [SugarColumn(IsNullable = false, Length = 1)]
        public string HospCode { get; set; }
    }
}