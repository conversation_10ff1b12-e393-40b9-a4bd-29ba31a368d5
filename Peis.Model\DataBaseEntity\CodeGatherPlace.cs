﻿using Peis.Model.TableFilter;
using SqlSugar;

namespace Peis.Model.DataBaseEntity
{
    ///<summary>
    ///采集地点信息
    ///</summary>
    [SugarTable("CodeGatherPlace")]
    public class CodeGatherPlace: IHospCodeFilter
    {
        /// <summary>
        /// 采集地点代码
        /// </summary>        
        [SugarColumn(IsNullable = false, IsPrimaryKey = true, Length = 6)]
        public string PlaceCode { get; set; }

        /// <summary>
        /// 采集地点名称
        /// </summary>        
        [SugarColumn(IsNullable = false, Length = 30)]
        public string PlaceName { get; set; }

        /// <summary>
        /// 备注
        /// </summary>        
        [SugarColumn(IsNullable = true, Length = 255)]
        public string Note { get; set; }

        /// <summary>
        /// 院区代码
        /// </summary>
        [SugarColumn(IsNullable = false, Length = 1)]
        public string HospCode { get; set; }
    }
}