﻿using Peis.Model.Other.PeEnum;

namespace Peis.External.Wx.Service.Model.DTO
{
    /// <summary>
    /// 个人套餐
    /// </summary>
    public class PersonalPackage
    {
        /// <summary>
        /// 套餐编码
        /// </summary>
        public string ClusCode { get; set; }

        /// <summary>
        /// 套餐名称
        /// </summary>
        public string ClusName { get; set; }

        /// <summary>
        /// 体检须知
        /// </summary>
        public string Note { get; set; }

        /// <summary>
        /// 套餐价格
        /// </summary>
        public decimal Price { get; set; }

        /// <summary>
        /// 性别
        /// </summary>
        public Sex? sex { get; set; }
    }
}
