﻿using Peis.Model.Other.PeEnum;

namespace Peis.Model.DataBaseEntity.Occupation
{
    /// <summary>
    /// 职业病证件类型映射表
    /// </summary>
    [SugarTable]
    public class MapOccupationalCardType
    {
        /// <summary>
        /// 职业代码
        /// </summary>
        [SugarColumn(Length = 2, IsPrimaryKey = true)]
        public string OccupationalCode { get; set; }
        /// <summary>
        /// 系统内代码
        /// </summary>
        [SugarColumn(IsPrimaryKey = true)]
        public CardType PeCode { get; set; }
    }
}
