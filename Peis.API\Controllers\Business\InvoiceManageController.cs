﻿using Microsoft.AspNetCore.Mvc;
using Peis.Model.DTO;
using Peis.Model.Other.Input;
using Peis.Service.IService;
using System;
using System.Collections.Generic;

namespace Peis.API.Controllers.Business
{
    /// <summary>
    /// 发票管理
    /// </summary>
    [Route("api/[controller]")]
    [ApiController]
    public class InvoiceManageController : BaseApiController
    {
        private readonly IInvoiceService _invoiceService;
        public InvoiceManageController(IInvoiceService invoiceService)
        {
            _invoiceService = invoiceService;
        }

        #region 发票入库

        /// <summary>
        /// 购入发票
        /// </summary>
        /// <param name="invoiceWarehouse"></param>
        /// <returns></returns>
        [HttpPost("PurchaseInvoice")]
        public IActionResult PurchaseInvoice(InvoiceWarehouse invoiceWarehouse)
        {
            try
            {
                string msg = string.Empty;
                var flag = _invoiceService.PurchaseInvoice(invoiceWarehouse, ref msg);
                if (!flag)
                {
                    result.Success = false;
                    result.ReturnMsg = msg;
                }
            }
            catch (Exception e)
            {
                result.Success = false;
                result.ReturnMsg = e.Message;
            }
            return Ok(result);
        }

        /// <summary>
        /// 获取发票购入列表
        /// </summary>
        /// <param name="invoiceQuery"></param>
        /// <returns></returns>
        /// <response code="200"></response>
        [HttpPost("ReadInvoiceWarehouse")]
        [ProducesResponseType(typeof(InvoicePurchase[]), 200)]
        public IActionResult ReadInvoiceWarehouse([FromBody] InvoiceQuery invoiceQuery)
        {
            try
            {
                result.ReturnData = _invoiceService.ReadInvoiceWarehouse(invoiceQuery);
            }
            catch (Exception e)
            {
                result.Success = false;
                result.ReturnMsg = e.Message;
            }
            return Ok(result);
        }

        /// <summary>
        /// 删除发票入库
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpPost("DeleteInvoiceWarehouse")]
        public IActionResult DeleteInvoiceWarehouse([FromQuery] int id)
        {
            try
            {
                string msg = string.Empty;
                var flag = _invoiceService.DeleteInvoiceWarehouse(id, ref msg);
                if (!flag)
                {
                    result.Success = false;
                    result.ReturnMsg = msg;
                }
            }
            catch (Exception e)
            {
                result.Success = false;
                result.ReturnMsg = e.Message;
            }
            return Ok(result);
        }

        #endregion

        #region 发票发放

        /// <summary>
        /// 发放发票
        /// </summary>
        /// <param name="invoiceAllocate"></param>
        /// <returns></returns>
        [HttpPost("AllocateInvoice")]
        public IActionResult AllocateInvoice(InvoiceAllocate invoiceAllocate)
        {
            try
            {
                string msg = string.Empty;
                var flag = _invoiceService.AllocateInvoice(invoiceAllocate, ref msg);
                if (!flag)
                {
                    result.Success = false;
                    result.ReturnMsg = msg;
                }
            }
            catch (Exception e)
            {
                result.Success = false;
                result.ReturnMsg = e.Message;
            }
            return Ok(result);
        }

        /// <summary>
        /// 获取发放发票数据
        /// </summary>
        /// <param name="invoiceQuery"></param>
        /// <returns></returns>
        /// <response code="200"></response>
        [HttpPost("ReadInvoiceAllocation")]
        [ProducesResponseType(typeof(InvoiceAllocation[]), 200)]
        public IActionResult ReadInvoiceAllocation([FromBody] InvoiceQuery invoiceQuery)
        {
            try
            {
                result.ReturnData = _invoiceService.ReadInvoiceAllocation(invoiceQuery);
            }
            catch (Exception e)
            {
                result.Success = false;
                result.ReturnMsg = e.Message;
            }
            return Ok(result);
        }

        /// <summary>
        /// 删除发票发放数据
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpPost("DeleteInvoiceAllocation")]
        public IActionResult DeleteInvoiceAllocation([FromQuery] int id)
        {
            try
            {
                string msg = string.Empty;
                var flag = _invoiceService.DeleteInvoiceAllocation(id, ref msg);
                if (!flag)
                {
                    result.Success = false;
                    result.ReturnMsg = msg;
                }
            }
            catch (Exception e)
            {
                result.Success = false;
                result.ReturnMsg = e.Message;
            }
            return Ok(result);
        }
        #endregion

        #region 发票核销报表
        /// <summary>
        /// 发票核销报表
        /// </summary>
        /// <param name="writeOffQuery"></param>
        /// <returns></returns>
        [HttpPost("ReadInvoiceWriteOffReport")]
        [ProducesResponseType(typeof(List<InvoiceWriteOffReport>), 200)]
        public IActionResult ReadInvoiceWriteOffReport([FromBody] InvoiceWriteOffQuery writeOffQuery)
        {
            string msg = string.Empty;
            result.ReturnData = _invoiceService.ReadInvoiceWriteOffReport(writeOffQuery, ref msg);
            result.ReturnMsg = msg;
            return Ok(result);
        }
        #endregion

        #region 发票转交
        /// <summary>
        /// 发票转交
        /// </summary>
        /// <param name="transfer"></param>
        /// <returns></returns>
        [HttpPost("TransferInvoice")]
        public IActionResult TransferInvoice(InvoiceTransfer transfer)
        {
            try
            {
                string msg = string.Empty;
                var flag = _invoiceService.TransferInvoice(transfer, ref msg);
                if (!flag)
                {
                    result.Success = false;
                    result.ReturnMsg = msg;
                }
            }
            catch (Exception e)
            {
                result.Success = false;
                result.ReturnMsg = e.Message;
            }
            return Ok(result);
        }
        #endregion

        #region 发票收回
        /// <summary>
        /// 发票收回
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpPost("TakeBackInvoice")]
        public IActionResult TakeBackInvoice([FromQuery] int id)
        {
            try
            {
                string msg = string.Empty;
                var flag = _invoiceService.TakeBackInvoice(id, ref msg);
                if (!flag)
                {
                    result.Success = false;
                    result.ReturnMsg = msg;
                }
            }
            catch (Exception e)
            {
                result.Success = false;
                result.ReturnMsg = e.Message;
            }
            return Ok(result);
        }
        #endregion
    }
}
