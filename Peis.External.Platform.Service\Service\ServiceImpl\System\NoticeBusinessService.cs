﻿using Microsoft.Extensions.Logging;
using Peis.External.Platform.Service.Service.IService.System;
using Peis.Model.DataBaseEntity.External;
using Peis.Model.DTO.External.DataMaintenances;
using Peis.Model.DTO.External.His;
using Peis.Model.DTO.Notice;
using Peis.Model.Other.PeEnum;
using Peis.Repository.IRepository;
using Peis.Service.IService;
using Peis.Service.IService.ExternalSystem;

namespace Peis.External.Platform.Service.Service.ServiceImpl.System;

/// <summary>
/// 业务通知公告服务
/// </summary>
public class NoticeBusinessService : INoticeBusinessService
{
    readonly IShenShanMainDataRepository _shenShanMainDataRepository;
    readonly INoticeService _noticeService;
    readonly ILogger<NoticeBusinessService> _logger;
    readonly IExternalSystemDataSyncService _externalSystemDataSyncService;
    readonly IShenShanRegisterRepository _shenShanRegisterRepository;
    readonly IRegisterRepository _registerRepository;

    public NoticeBusinessService(IShenShanMainDataRepository shenShanMainDataRepository, INoticeService noticeService, ILogger<NoticeBusinessService> logger, IExternalSystemDataSyncService externalSystemDataSyncService, IShenShanRegisterRepository shenShanRegisterRepository, IRegisterRepository registerRepository)
    {
        _shenShanMainDataRepository = shenShanMainDataRepository;
        _noticeService = noticeService;
        _logger = logger;
        _externalSystemDataSyncService = externalSystemDataSyncService;
        _shenShanRegisterRepository = shenShanRegisterRepository;
        _registerRepository = registerRepository;
    }

    #region 基础数据
    /// <summary>
    /// 发消息通知：收费项目与医嘱项目明细不一致通知
    /// </summary>
    /// <param name="msg"></param>
    /// <returns>bool</returns>
    public bool SendNoticeByCombChargeDiff(ref string msg)
    {
        try
        {
            /*            
            公告标题：
            收费项目变动通知-2024.01.11

            公告内容：
            一、医嘱收费明细差异：
            血脂四项
              收费项目缺失：
                250303004-2：血清高密度脂蛋白胆固醇测定 - 其他方法

              收费项目失效：
                250303001-2：血清总胆固醇测定 - 化学法或酶法

            二、收费项目价格差异
            250303005-2: 血清低密度脂蛋白胆固醇测定 - 其他方法 价格：9.11-> 10.00 折扣：1 => 0.8
              受影响组合：血脂四项

            250303001-2: 血清总胆固醇测定 - 化学法或酶法 价格：9.11-> 10.00
              受影响组合：血脂四项
            
            三、医嘱项目禁用/启用变化
              新的启用：
                受影响的组合：
              新的禁用：
                受影响的组合：
             */

            int totalNumber = 0;
            int totalPage = 0;
            int pageSize = 100;
            var mapChargeDiffs = new List<MapHisChargeDiffDto>();
            var pageList = _externalSystemDataSyncService.GetCodeItemCombsByPage(1, pageSize, ref totalNumber, ref totalPage);
            for (int i = 1; i <= totalPage; i++)
            {
                if (i > 1)
                {
                    pageList.Clear();
                    var thatPageList = _externalSystemDataSyncService.GetCodeItemCombsByPage(i, pageSize, ref totalNumber, ref totalPage);
                    pageList.AddRange(thatPageList);
                }

                pageList = pageList.FindAll(x => x.IsEnabled);
                var combCodes = pageList.Select(x => x.CombCode).ToList();
                var mapCharges = GetCombHisMapChargeItems(combCodes);
                var orderCharges = GetCombHisOrderChargeItems(combCodes);
                foreach (var item in pageList)
                {
                    var curMapCharges = mapCharges.FindAll(x => x.CombCode.Equals(item.CombCode));
                    var curOrderCharges = orderCharges.FindAll(x => x.CombCode.Equals(item.CombCode));
                    var mapChargeDiff = mapChargeDiffs.FirstOrDefault(x => x.HospCode.Equals(item.HospCode));
                    var eixtCurHospDiff = !mapChargeDiff.IsNullOrEmpty();
                    if (!eixtCurHospDiff)
                        mapChargeDiff = new MapHisChargeDiffDto { HospCode = item.HospCode };

                    mapChargeDiff.Diff(item, curMapCharges, curOrderCharges);
                    if (!eixtCurHospDiff)
                        mapChargeDiffs.Add(mapChargeDiff);
                }
            }

            var title = $"收费项目变动通知-{DateTime.Now.ToString("yyyy.MM.dd")}";
            foreach (var item in mapChargeDiffs.FindAll(x => x.ExistDiff))
            {
                var newNotice = new NewNotice(title, item.ToString(), hospCode: item.HospCode);
                _noticeService.CreateNoticeAsync(newNotice);
            }
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogException(ex);
            msg = ex.Message;
            return false;
        }
    }

    /// <summary>
    /// 根据组合获取收费收费项目信息
    /// </summary>
    /// <param name="combCodes"></param>
    /// <returns></returns>
    List<MapCodeItemCombHisChargeItem> GetCombHisMapChargeItems(List<string> combCodes)
    {
        return _shenShanMainDataRepository.QueryCodeCombMapChargeItems(combCodes)
             .Where((a, b) => b.ItemType.Equals(EnumCombHisItemType.Exam))
             .Where((a, b) => b.IsOrder)
             .Select((a, b) => b)
             .ToList();
    }

    /// <summary>
    /// 根据组合获取医嘱项目明细
    /// </summary>
    /// <param name="combCodes"></param>
    /// <returns></returns>
    List<CodeHisOrderItemCharge> GetCombHisOrderChargeItems(List<string> combCodes)
    {
        return _shenShanMainDataRepository.QueryCodeItemCombMapOrders(combCodes)
                    .Select((a, b, c) => new CodeHisOrderItemCharge
                    {
                        PId = c.PId.SelectAll(),
                        CombCode = a.CombCode,
                        CombName = a.CombName,
                        MainOrderCode = b.OrderItemCode,
                        MainOrderCNName = b.OrderItemCNName,
                        MainOrderIsAvailable = b.IsAvailable,
                        MainOrderIsDeleted = b.IsDeleted,
                        MapOrderCode = a.HisOrderCode,
                        MapOrderIsAvailable = a.HisOrderIsAvailable,
                        IsDedicatePies = a.IsDedicatePies,
                    })
                    .ToList();
    }

    #endregion

    #region 体检

    /// <summary>
    /// 每日需要推送当日退费人员列表
    /// </summary>
    /// <param name="msg">消息</param>
    /// <param name="preDays">查询往日记录，往前推天数,默认0(当天)</param>
    /// <returns>bool</returns>
    public bool SendNoticeByPatientRefund(out string msg, int preDays = 0)
    {
        var startTime = DateTime.Now.Date.AddDays(-Math.Abs(preDays));
        var endTime = DateTime.Now.Date.AddDays(1).AddMilliseconds(-1);
        var refundBills = _shenShanRegisterRepository.ReadHisBillList()
          .Where((order, bill) => SqlFunc.Between(bill.CreateTime, startTime, endTime))
          .Where((order, bill) => SqlFunc.Equals(bill.PayStatus, PayStatus.已退费))
          .Where((order, bill) => SqlFunc.Equals(bill.FeeType, FeeType.正常))
          .Select((order, bill) => new
          {
              order.RegNo,
              order.Name,
              order.Tel,
              bill.Price
          })
          .ToList();

        var refundPatients = new List<NoticePatRefund>();
        foreach (var item in refundBills.GroupBy(x => x.RegNo))
        {
            var reg = _registerRepository.ReadRegister(item.Key).First();
            var patRefund = new NoticePatRefund();
            patRefund.RegNo = item.Key;
            patRefund.Name = item.First().Name;
            patRefund.Tel = item.First().Tel;
            patRefund.Price = item.Sum(x => x.Price);
            patRefund.HospCode = reg?.HospCode ?? "";
            refundPatients.Add(patRefund);
        }
        var title = $"退费人员通知-{DateTime.Now.ToString("yyyy.MM.dd.HH")}";
        foreach (var item in refundPatients.GroupBy(x => x.HospCode))
        {
            var content = string.Join("<br>", item.Select(x => x.ToString()));
            var newNotice = new NewNotice(title, content, hospCode: item.Key);
            _noticeService.CreateNoticeAsync(newNotice);
        }

        msg = ResxCommon.Success;
        return true;
    }

    #endregion
}
