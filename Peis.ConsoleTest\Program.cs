﻿using Peis.Model.DataBaseEntity;
using Peis.Utility.SqlSugarExtension;
using SqlSugar;
using System;
using System.Linq;

try
{
    InitializedDatabase();
}
catch (Exception e)
{
    Console.WriteLine(e);
}

Console.WriteLine("\n任意键退出...");
Console.ReadKey();

void InitializedDatabase()
{
    var _db = new SqlSugarClient(new ConnectionConfig()
    {
        ConnectionString = "Data Source=192.168.2.200\\MSSQLSERVER2014,11433;Initial Catalog=PeisHub_ShenShan_Occupation;User ID=sa;Password=****;",
        DbType = DbType.SqlServer,
        IsAutoCloseConnection = true,//开启自动释放模式
        InitKeyType = InitKeyType.Attribute,//从特性读取主键和自增列信息
    });

    _db.CurrentConnectionConfig.ConfigureExternalServices = new ConfigureExternalServices()
    {
        SplitTableService = new yyyySplitTableService()
    };

    //Print sql
    _db.Aop.OnLogExecuting = (sql, pars) =>
    {
        Console.WriteLine($"{Environment.NewLine}[PE] {sql}{Environment.NewLine}{_db.Utilities.SerializeObject(pars.ToDictionary(it => it.ParameterName, it => it.Value))}");
    };

    //_db.CodeFirst.InitTables(typeof(CodeOccupationalAuditoryCorrectedValue));
    _db.CodeFirst.InitTables(typeof(SysOperator));
}