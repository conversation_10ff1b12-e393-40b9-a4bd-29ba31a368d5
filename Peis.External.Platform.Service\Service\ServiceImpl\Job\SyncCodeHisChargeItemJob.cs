﻿using Peis.Quartz.UI.BaseService;
using Peis.Service.IService.ExternalSystem;

namespace Peis.External.Platform.Service.Service.ServiceImpl.Job;

/// <summary>
/// 作业：同步His收费项目信息
/// </summary>
public class SyncCodeHisChargeItemJob : IJobService
{
    readonly IExternalSystemDataSyncService _extSystemDataSyncService;

    public SyncCodeHisChargeItemJob(IExternalSystemDataSyncService extSystemDataSyncService)
    {
        _extSystemDataSyncService = extSystemDataSyncService;
    }

    public string ExecuteService(string parameter)
    {
        int.TryParse(parameter, out var pageSize);
        pageSize = pageSize <= 0 ? 50 : pageSize;
        _extSystemDataSyncService.SyncCodeHisChargeItems(pageSize, out var msg);
        return msg;
    }
}
