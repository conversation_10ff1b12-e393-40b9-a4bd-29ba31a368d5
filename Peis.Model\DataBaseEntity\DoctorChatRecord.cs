using SqlSugar;
using System;

namespace Peis.Model.DataBaseEntity
{
    ///<summary>
    ///医生会诊记录
    ///</summary>
    [SplitTable(SplitType.Year)]
    [SugarTable("DoctorChatRecord_{yyyy}", "医生会诊记录")]
    public class DoctorChatRecord
    {
        /// <summary>
        /// 雪花Id
        /// </summary>        
        [SugarColumn(IsNullable = false, IsPrimaryKey = true)]
        public long Id { get; set; }

        /// <summary>
        /// DoctorChat的Id 用与聊天数据关联
        /// </summary>        
        [SugarColumn(IsNullable = false)]
        public long ChatId { get; set; }

        /// <summary>
        /// 科室编码
        /// </summary>        
        [SugarColumn(IsNullable = false, Length = 6)]
        public string DeptCode { get; set; }

        /// <summary>
        /// 发送人
        /// </summary>        
        [SugarColumn(IsNullable = false, Length = 10)]
        public string SendUser { get; set; }

        /// <summary>
        /// 发送的内容
        /// </summary>        
        [SugarColumn(IsNullable = false, Length = 200)]
        public string Content { get; set; }

        /// <summary>
        /// 发送时间
        /// </summary>        
        [SugarColumn(IsNullable = false)]
        public DateTime SendTime { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>   
        [SplitField]
        [SugarColumn(IsNullable = false)]
        public DateTime CreateTime { get; set; }
    }
}