﻿using Microsoft.AspNetCore.Mvc;
using Peis.Model.DataBaseEntity;
using Peis.Model.Other.Input;
using Peis.Service.IService;

namespace Peis.API.Controllers.Business
{
    /// <summary>
    /// 日志
    /// </summary>
    [Route("api/[controller]")]
    [ApiController]
    public class LogController : BaseApiController
    {
        private readonly ILogBusinessNewService _logBusinessService;

        public LogController(ILogBusinessNewService logBusinessService)
            => _logBusinessService = logBusinessService;

        /// <summary>
        /// 获取业务日志
        /// </summary>
        /// <param name="logBusinessQuery"></param>
        /// <response code="200">返回日志查询结果</response>
        /// <returns></returns>
        [HttpPost("ReadLogBusiness")]
        [ProducesResponseType(typeof(LogBusinessNew[]), 200)]
        public IActionResult ReadLogBusiness(LogBusinessNewQuery logBusinessQuery)
        {
            result.ReturnData = _logBusinessService.Read(logBusinessQuery);
            return Ok(result);
        }
    }
}



