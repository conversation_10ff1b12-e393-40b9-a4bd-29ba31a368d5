﻿using Microsoft.AspNetCore.Mvc;
using Peis.Model.DTO.CriticalValue;
using Peis.Model.DTO.DataQuery;
using Peis.Model.Other.Input.DataQuery;
using Peis.Service.IService;
using System.Collections.Generic;

namespace Peis.API.Controllers.Business
{
    /// <summary>
    /// 数据查询控制器
    /// </summary>
    [Route("api/[controller]")]
    [ApiController]
    public class DataQueryController : BaseApiController
    {
        private readonly IDataQueryService _dataQueryService;

        public DataQueryController(IDataQueryService dataQueryService)
        {
            _dataQueryService = dataQueryService;
        }

        #region 体检综合查询
        /// <summary>
        /// 体检综合查询
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        [HttpPost("PeComprehensiveQuery")]
        [ProducesResponseType(typeof(PeComprehensiveData), 200)]
        public IActionResult PeComprehensiveQuery([FromBody] PeComprehensiveQuery query)
        {
            int totalNumber = 0;
            int totalPage = 0;
            result.ReturnData = _dataQueryService.PeComprehensiveQuery(query,ref totalNumber,ref totalPage);
            result.TotalNumber = totalNumber;
            result.TotalPage = totalPage;
            return Ok(result);
        }

        /// <summary>
        /// 获取患者基本信息
        /// </summary>
        /// <param name="regNo">体检号</param>
        /// <returns></returns>
        [HttpPost("GetPatientBasicInfo")]
        [ProducesResponseType(typeof(PatientBasicInfo), 200)]
        public IActionResult GetPatientBasicInfo([FromQuery] string regNo)
        {
            string msg = string.Empty;
            result.ReturnData = _dataQueryService.GetPatientBasicInfo(regNo, ref msg);
            result.ReturnMsg = msg;
            return Ok(result);
        }

        /// <summary>
        /// 获取项目情况
        /// </summary>
        /// <param name="regNo">体检号</param>
        /// <returns></returns>
        [HttpPost("GetItemsInfo")]
        [ProducesResponseType(typeof(ItemData), 200)]
        public IActionResult GetItemsInfo([FromQuery] string regNo)
        {
            string msg = string.Empty;
            result.ReturnData = _dataQueryService.GetItemsInfo(regNo, ref msg);
            result.ReturnMsg = msg;
            return Ok(result);
        }

        /// <summary>
        /// 获取费用清单
        /// </summary>
        /// <param name="regNo">体检号</param>
        /// <returns></returns>
        [HttpPost("GetExpenseList")]
        [ProducesResponseType(typeof(ExpenseData), 200)]
        public IActionResult GetExpenseList([FromQuery] string regNo)
        {
            string msg = string.Empty;
            ExpenseData data = new();
            result.Success = _dataQueryService.GetExpenseList(regNo, ref data, ref msg);
            result.ReturnData = data;
            result.ReturnMsg = msg;
            return Ok(result);
        }

        /// <summary>
        /// 获取标本数据
        /// </summary>
        /// <param name="regNo">体检号</param>
        /// <returns></returns>
        [HttpPost("GetSampleData")]
        [ProducesResponseType(typeof(PeSampleData[]), 200)]
        public IActionResult GetSampleData([FromQuery] string regNo)
        {
            string msg = string.Empty;
            result.ReturnData = _dataQueryService.GetSampleData(regNo, ref msg);
            result.ReturnMsg = msg;
            return Ok(result);
        }

        /// <summary>
        /// 获取日志数据
        /// </summary>
        /// <param name="regNo">体检号</param>
        /// <returns></returns>
        [HttpPost("GetLogData")]
        [ProducesResponseType(typeof(LogData[]), 200)]
        public IActionResult GetLogData([FromQuery] string regNo)
        {
            string msg = string.Empty;
            result.ReturnData = _dataQueryService.GetLogData(regNo, ref msg);
            result.ReturnMsg = msg;
            return Ok(result);
        }

        /// <summary>
        /// 危急异常
        /// </summary>
        /// <param name="regNo">体检号</param>
        /// <returns></returns>
        [HttpPost("GetCriticalException")]
        [ProducesResponseType(typeof(CriticalExceptionData[]), 200)]
        public IActionResult GetCriticalException([FromQuery] string regNo)
        {
            string msg = string.Empty;
            result.ReturnData = _dataQueryService.GetCriticalException(regNo, ref msg);
            result.ReturnMsg = msg;
            return Ok(result);
        }

        /// <summary>
        /// 生成可上传的健康证XML
        /// </summary>
        /// <param name="regNo"></param>
        /// <returns></returns>
        [HttpPost("GenerateHealthCardXML")]
        public IActionResult GenerateHealthCardXML([FromQuery] string regNo)
        {
            var xml = string.Empty;
            var msg = string.Empty;
            result.Success = _dataQueryService.GenerateHealthCardXML(regNo, ref xml, ref msg);
            result.ReturnData = xml;
            result.ReturnMsg = msg;
            return Ok(result);
        }
        #endregion

        #region 检验数据查询
        /// <summary>
        /// 检验数据查询
        /// </summary>
        /// <param name="regNo"></param>
        /// <returns></returns>
        [HttpPost("InspectionDataQuery")]
        [ProducesResponseType(typeof(InspectionData), 200)]
        public IActionResult InspectionDataQuery([FromQuery] string regNo)
        {
            string msg = string.Empty;
            result.ReturnData = _dataQueryService.InspectionDataQuery(regNo, ref msg);
            result.ReturnMsg = msg;
            return Ok(result);
        }

        #endregion

        #region 体检漏项查询
        /// <summary>
        /// 体检漏项查询
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        [HttpPost("PeMissingItemQuery")]
        [ProducesResponseType(typeof(MissingItemData), 200)]
        public IActionResult PeMissingItemQuery([FromBody] MissingItemQuery query)
        {
            var data = new MissingItemData();
            var msg = string.Empty;
            result.Success = _dataQueryService.PeMissingItemQuery(query, ref data, ref msg);
            result.ReturnData = data;
            result.ReturnMsg = msg;
            return Ok(result);
        }
        #endregion

        #region 危急异常查询
        /// <summary>
        /// 危急异常查询
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        [HttpPost("CriticalExceptionQuery")]
        [ProducesResponseType(typeof(CriticalExceptionData[]), 200)]
        public IActionResult CriticalExceptionQuery([FromBody] CriticalExceptionQuery query)
        {
            result.ReturnData = _dataQueryService.CriticalExceptionQuery(query);
            return Ok(result);
        }

        /// <summary>
        /// 删除危急异常项目
        /// </summary>
        /// <param name="Id"></param>
        /// <returns></returns>
        [HttpPost("DeleteCriticalException")]
        public IActionResult DeleteCriticalException([FromQuery] long Id)
        {
            result.Success = _dataQueryService.DeleteCriticalException(Id);
            return Ok(result);
        }
        #endregion

        #region 重大阳性查询
        /// <summary>
        /// 重大阳性查询
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        [HttpPost("MajorPositiveQuery")]
        [ProducesResponseType(typeof(MajorPositiveRecord[]), 200)]
        public IActionResult MajorPositiveQuery([FromBody] MajorPositiveQuery query)
        {
            result.ReturnData = _dataQueryService.MajorPositiveQuery(query);
            return Ok(result);
        }

        /// <summary>
        /// 删除重大阳性
        /// </summary>
        /// <param name="regNo">体检号</param>
        /// <returns></returns>
        [HttpPost("DeleteMajorPositive")]
        public IActionResult DeleteMajorPositive([FromQuery] string regNo)
        {
            result.Success = _dataQueryService.DeleteMajorPositive(regNo);
            return Ok(result);
        }
        #endregion

        #region 体检报告进程
        /// <summary>
        /// 体检报告进程分析
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        [HttpPost("PeReportProcessAnalize")]
        [ProducesResponseType(typeof(PeReportProcessAnalyzeData), 200)]
        public IActionResult PeReportProcessAnalize([FromBody] PeReportProcessQuery query)
        {
            var data = new PeReportProcessAnalyzeData();
            var msg = string.Empty;
            result.Success = _dataQueryService.PeReportProcessAnalize(query, ref data, ref msg);
            result.ReturnData = data;
            result.ReturnMsg = msg;
            return Ok(result);
        }

        /// <summary>
        /// 体检报告进程查询
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        [HttpPost("PeReportProcessQuery")]
        [ProducesResponseType(typeof(PeReportProcessData), 200)]
        public IActionResult PeReportProcessQuery([FromBody] PeReportProcessQuery query)
        {
            var data = new List<PeReportProcessData>();
            var msg = string.Empty;
            result.Success = _dataQueryService.PeReportProcessQuery(query, ref data, ref msg);
            result.ReturnData = data;
            result.ReturnMsg = msg;
            return Ok(result);
        }
        #endregion

        #region 体检报告未出列表查询
        /// <summary>
        /// 体检报告未出列表查询
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        [HttpPost("PeNoReportQuery")]
        [ProducesResponseType(typeof(PeNoReportData), 200)]
        public IActionResult PeNoReportQuery([FromBody] PeNoReportQuery query)
        {
            var data = new List<PeNoReportData>();
            var msg = string.Empty;
            result.Success = _dataQueryService.PeNoReportQueay(query, ref data, ref msg);
            result.ReturnData = data;
            result.ReturnMsg = msg;
            return Ok(result);
        }
        #endregion

        #region 科研项目分析
        /// <summary>
        /// 科研项目分析
        /// </summary>
        /// <returns></returns>
        [HttpPost("ResearchAnalysis")]
        [ProducesResponseType(typeof(ResearchAnalysisData), 200)]
        public IActionResult ResearchAnalysis([FromBody] ResearchAnalysisQuery query)
        {
            ResearchAnalysisData data = new();
            string msg = string.Empty;
            result.Success = _dataQueryService.ResearchAnalysis(query, ref data, ref msg);
            result.ReturnData = data;
            result.ReturnMsg = msg;
            return Ok(result);
        }
        #endregion

        #region
        /// <summary>
        /// 检查项目明细报表
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        [HttpPost("ReadExamItemEntryReport")]
        [ProducesResponseType(typeof(ExamItemEntryReport), 200)]
        public IActionResult ReadExamItemEntryReport([FromBody] ExamItemEntryQuery query)
        {
            int totalNumber = 0;
            int totalPage = 0;
            result.ReturnData = _dataQueryService.ReadExamItemEntryReport(query, ref totalNumber, ref totalPage);
            result.TotalNumber = totalNumber;
            result.TotalPage = totalPage;
            result.Success = true;
            return Ok(result);
        }
        #endregion
    }
}
