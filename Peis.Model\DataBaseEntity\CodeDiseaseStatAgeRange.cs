﻿namespace Peis.Model.DataBaseEntity;

/// <summary>
/// 疾病统计年龄段
/// </summary>
[SugarTable]
public class CodeDiseaseStatAgeRange
{
    /// <summary>
    /// 代码
    /// </summary>
    [SugarColumn(IsPrimaryKey = true, Length = 10)]
    public string RangeCode { get; set; }
    /// <summary>
    /// 名称
    /// </summary>
    [SugarColumn(Length = 50, IsNullable = false)]
    public string RangeName { get; set; }
    /// <summary>
    /// 年龄下限
    /// </summary>
    [SugarColumn(IsNullable = false)]
    public int AgeLowerLimit { get; set; }
    /// <summary>
    ///  年龄上限
    /// </summary> 
    [SugarColumn(IsNullable = false)]
    public int AgeUpperLimit { get; set; }
    /// <summary>
    /// 排序
    /// </summary>
    [SugarColumn(IsNullable = true)]
    public int? SortIndex { get; set; }
}
