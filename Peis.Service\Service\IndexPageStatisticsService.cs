﻿using Peis.Model.DTO;
using Peis.Model.DTO.IndexPageStatistics;
using Peis.Model.Other.PeEnum;
using Peis.Repository.IRepository;
using Peis.Service.IService;
using SqlSugar;
using System;
using System.Linq;

namespace Peis.Service.Service
{
    public class IndexPageStatisticsService : IIndexPageStatisticsService
    {
        private readonly IIndexPageStatisticsRepository _indexPageStatisticsRepository;

        public IndexPageStatisticsService(
            IIndexPageStatisticsRepository indexPageStatisticsRepository
        )
        {
            _indexPageStatisticsRepository = indexPageStatisticsRepository;
        }

        /// <summary>
        /// 获取首页统计
        /// </summary>
        /// <param name="indexPageStatisticses"></param>
        /// <returns></returns>
        public bool GetIndexPageStatistics(ref IndexPageStatistics indexPageStatisticses)
        {
            var today = DateTime.Now.Date;
            var tomorrow = today.AddDays(1);

            #region 当天报到的信息
            var activedTodayList = _indexPageStatisticsRepository.GetPeRegister()
                .Where(a => a.ActiveTime >= today && a.ActiveTime < tomorrow)
                .ToList();

            var totalActived = activedTodayList.Count;//报到总数
            #endregion

            #region 已预约/预约报到
            indexPageStatisticses.BookedNumInfo.BookedNum = _indexPageStatisticsRepository.GetPeRegister()
                .Where(a => a.BookBeginTime > today && a.BookBeginTime < tomorrow)
                .Count();

            indexPageStatisticses.BookedNumInfo.BookedActivedNum = activedTodayList
                .Where(a => a.BookBeginTime > today && a.BookBeginTime < tomorrow)
                .Count();

            if (indexPageStatisticses.BookedNumInfo.BookedActivedNum > 0)
            {
                indexPageStatisticses.BookedNumInfo.BookedActivedPercent = (indexPageStatisticses.BookedNumInfo.BookedActivedNum / indexPageStatisticses.BookedNumInfo.BookedNum).ToString("P");
            }
            #endregion

            #region 报到男女比例
            indexPageStatisticses.ActivedSexInfo.MaleNum = activedTodayList
                .Where(a => a.Sex == Sex.男)
                .Count();

            indexPageStatisticses.ActivedSexInfo.FemaleNum = activedTodayList
                .Where(a => a.Sex == Sex.女)
                .Count();

            if (totalActived > 0)
            {
                indexPageStatisticses.ActivedSexInfo.MalePercent = (indexPageStatisticses.ActivedSexInfo.MaleNum / totalActived).ToString("P");

                indexPageStatisticses.ActivedSexInfo.FemalePercent = (indexPageStatisticses.ActivedSexInfo.FemaleNum / totalActived).ToString("P");
            }
            #endregion

            #region 个人/团体报到人数对比
            indexPageStatisticses.ActivedPersonCompanyInfo.PersionNum = activedTodayList
                .Where(a => !a.IsCompanyCheck)
                .Count();

            indexPageStatisticses.ActivedPersonCompanyInfo.CompanyNum = activedTodayList
                .Where(a => a.IsCompanyCheck)
                .Count();

            if (totalActived > 0)
            {
                indexPageStatisticses.ActivedPersonCompanyInfo.PersionPercent = (indexPageStatisticses.ActivedPersonCompanyInfo.PersionNum / totalActived).ToString("P");

                indexPageStatisticses.ActivedPersonCompanyInfo.CompanyPercent = (indexPageStatisticses.ActivedPersonCompanyInfo.CompanyNum / totalActived).ToString("P");
            }
            #endregion

            #region 预约报到/现场登记人数对比
            indexPageStatisticses.ActivedBookedLocalInfo.BookedActivedNum = indexPageStatisticses.BookedNumInfo.BookedActivedNum;

            indexPageStatisticses.ActivedBookedLocalInfo.LocalActivedNum = totalActived - indexPageStatisticses.ActivedBookedLocalInfo.BookedActivedNum;

            indexPageStatisticses.ActivedBookedLocalInfo.ActivedNum = totalActived;

            if (totalActived > 0)
            {
                indexPageStatisticses.ActivedBookedLocalInfo.BookedActivedPercent = (indexPageStatisticses.ActivedBookedLocalInfo.BookedActivedNum / totalActived).ToString("P");

                indexPageStatisticses.ActivedBookedLocalInfo.LocalActivedPercent = (indexPageStatisticses.ActivedBookedLocalInfo.LocalActivedNum / totalActived).ToString("P");
            }
            #endregion

            #region 各个分类的人数对比
            var PeClsDict = Enum.GetValues<PeCls>()
                .ToDictionary(x => Convert.ToInt32(x).ToString(), x => Enum.GetName(x));

            foreach (var peCls in PeClsDict)
            {
                var peClsKey = (PeCls)Enum.Parse(typeof(PeCls), peCls.Key);

                var num = activedTodayList.Where(a => a.PeCls == peClsKey).Count();

                indexPageStatisticses.ActivedPeClsInfo.ActivedPeClses.Add(new ActivedPeCls
                {
                    PeCls = peCls.Value,
                    ActivedNum = num
                });
            }

            indexPageStatisticses.ActivedPeClsInfo.TotalActived = totalActived;
            #endregion

            #region 累计待总检人数
            indexPageStatisticses.StatusNumInfo.UnCheckedNum = _indexPageStatisticsRepository.GetPeRegister()
                .Where(a => a.PeStatus == PeStatus.已检完)
                .Count();
            #endregion

            #region 累计检中总人数
            indexPageStatisticses.StatusNumInfo.CheckingNum = _indexPageStatisticsRepository.GetPeRegister()
                .Where(a => a.PeStatus == PeStatus.正在检查)
                .Count();
            #endregion

            #region 今天已发报告人数
            indexPageStatisticses.StatusNumInfo.TodayReportedNum = _indexPageStatisticsRepository.GetPeRegister()
                .Where(a => a.ReportPrintedTime > today && a.ReportPrintedTime < tomorrow)
                .Count();
            #endregion

            #region 近期已报到人数
            var monthBefore = today.AddMonths(-3);//查询的数据范围
            var dayRecent = 6;//最近有数据的7天

            var actvicedMonthBefore = _indexPageStatisticsRepository.GetPeRegister()
                .Where(a => a.ActiveTime <= today && a.ActiveTime > monthBefore)
                .ToList()
                .OrderByDescending(a => a.ActiveTime);

            foreach (var item in actvicedMonthBefore)
            {
                if (indexPageStatisticses.ActivedDayNums.Count <= dayRecent)
                {
                    var activedDate = ((DateTime)item.ActiveTime).ToString("d");

                    var activedDayNum = indexPageStatisticses.ActivedDayNums
                    .Where(a => a.ActivedDate == activedDate);

                    if (activedDayNum.Any())
                    {
                        activedDayNum.First().ActivedNum++;
                    }
                    else
                    {
                        indexPageStatisticses.ActivedDayNums.Add(new ActivedDayNum
                        {
                            ActivedDate = activedDate,
                            ActivedNum = 1
                        });
                    }
                }
            }

            indexPageStatisticses.ActivedDayNums.Reverse();
            #endregion

            #region 近期未完成人数
            var undoneMonthBefore = _indexPageStatisticsRepository.GetPeRegister()
                .Where(a => a.PeStatus == PeStatus.正在检查 && a.ActiveTime <= today && a.ActiveTime > monthBefore)
                .ToList()
                .OrderByDescending(a => a.ActiveTime);

            foreach (var item in undoneMonthBefore)
            {
                if (indexPageStatisticses.UndoneDayNums.Count <= dayRecent)
                {
                    var activedDate = ((DateTime)item.ActiveTime).ToString("d");

                    var undoneDayNum = indexPageStatisticses.UndoneDayNums
                    .Where(a => a.ActivedDate == activedDate);

                    if (undoneDayNum.Any())
                    {
                        undoneDayNum.First().UndoneNum++;
                    }
                    else
                    {
                        indexPageStatisticses.UndoneDayNums.Add(new UndoneDayNum
                        {
                            ActivedDate = activedDate,
                            UndoneNum = 1
                        });
                    }
                }
            }

            indexPageStatisticses.UndoneDayNums.Reverse();
            #endregion

            #region 各个科室接待压力
            var todayDateRange = new RegisterDateRange(today, today);
            indexPageStatisticses.DeptActiveds = _indexPageStatisticsRepository.GetRegisterCombInfo(todayDateRange.MinRegTime, todayDateRange.MaxRegTime)
                .Select((a, b, c) => new
                {
                    a.RegNo,
                    c.DeptName
                })
                .Distinct()
                .ToList()
                .GroupBy(a => new
                {
                    a.DeptName
                })
                .Select(a => new DeptActived
                {
                    DeptName = a.Key.DeptName,
                    ActvicedNum = a.Count()
                })
                .OrderBy(a => a.ActvicedNum)
                .ToList();
            #endregion

            return true;
        }
    }
}
