﻿namespace Peis.Model.DataBaseEntity.External;

///<summary>
/// 基础数据：His药品信息(来源系统对接数据提取)
///</summary>
[SugarTable(nameof(CodeHisDrugItem), TableDescription = "His药品信息(来源系统对接数据提取)")]
[SugarIndex("index_CodeHisDrugItem_Code_Name", nameof(DrugCode), OrderByType.Asc, nameof(DrugCNName), OrderByType.Asc)]
public class CodeHisDrugItem
{
    /// <summary>
    /// 主键，UUID2
    /// </summary>
    [SugarColumn(IsNullable = false, IsPrimaryKey = true, Length = 64)]
    public string PId { get; set; }

    /// <summary>
    /// 药品ID
    /// </summary>
    [SugarColumn(IsNullable = false, Length = 64)]
    public string DrugId { get; set; }

    /// <summary>
    /// 药品代码
    /// </summary>
    [SugarColumn(IsNullable = false, Length = 64)]
    public string DrugCode { get; set; }

    /// <summary>
    /// 药品名称 通用名
    /// </summary>
    [SugarColumn(IsNullable = true, ColumnDataType = "nvarchar(256)")]
    public string DrugCNName { get; set; }

    /// <summary>
    /// 药品别名 商品名
    /// </summary>
    [SugarColumn(IsNullable = true, ColumnDataType = "nvarchar(256)")]
    public string DrugCNAlias { get; set; }

    /// <summary>
    /// 药品英文
    /// </summary>
    [SugarColumn(IsNullable = true, ColumnDataType = "nvarchar(256)")]
    public string DrugENName { get; set; }

    /// <summary>
    /// 英文别名
    /// </summary>
    [SugarColumn(IsNullable = true, ColumnDataType = "nvarchar(256)")]
    public string DrugENAlias { get; set; }

    /// <summary>
    /// 药品备注
    /// </summary>
    [SugarColumn(IsNullable = true, ColumnDataType = "nvarchar(512)")]
    public string DrugDesc { get; set; }

    /// <summary>
    /// 药品的注意事项, 如双岐杆菌片要求冰箱保存、禁忌
    /// </summary>
    [SugarColumn(IsNullable = true, ColumnDataType = "nvarchar(512)")]
    public string DrugNotice { get; set; }

    /// <summary>
    /// 药品类型ID
    /// </summary>
    [SugarColumn(IsNullable = true, Length = 64)]
    public string DrugTypeId { get; set; }

    /// <summary>
    /// 药品类型名称
    /// </summary>
    [SugarColumn(IsNullable = true, ColumnDataType = "nvarchar(256)")]
    public string DrugTypeName { get; set; }

    /// <summary>
    /// 大包装规格
    /// </summary>
    [SugarColumn(IsNullable = true, Length = 64)]
    public string BigPkgSpec { get; set; }

    /// <summary>
    /// 小包装规格
    /// </summary>
    [SugarColumn(IsNullable = true, Length = 64)]
    public string MinPackSepc { get; set; }

    /// <summary>
    /// 含量
    /// </summary>
    [SugarColumn(IsNullable = false, Length = 12, DecimalDigits = 4)]
    public decimal MinDosage { get; set; }

    /// <summary>
    /// 含量单位
    /// </summary>
    [SugarColumn(IsNullable = true, Length = 64)]
    public string MinDosageUnit { get; set; }

    /// <summary>
    /// 最小包装单位
    /// </summary>
    [SugarColumn(IsNullable = true, Length = 64)]
    public string MinPkgUnit { get; set; }

    /// <summary>
    /// 大包装单位
    /// </summary>
    [SugarColumn(IsNullable = true, Length = 64)]
    public string BigPkgUnit { get; set; }

    /// <summary>
    /// 大包装比例
    /// </summary>
    [SugarColumn(IsNullable = false, Length = 12, DecimalDigits = 4)]
    public decimal BigPkgUnitRatio { get; set; }

    /// <summary>
    /// 药库大包装单价
    /// </summary>
    [SugarColumn(IsNullable = false, Length = 12, DecimalDigits = 4)]
    public decimal BigPrice { get; set; }

    /// <summary>
    /// 小包装单价
    /// </summary>
    [SugarColumn(IsNullable = false, Length = 12, DecimalDigits = 4)]
    public decimal MinPrice { get; set; }

    /// <summary>
    /// 创建时间
    /// </summary>
    [SugarColumn(IsNullable = false)]
    public DateTime CreatedTime { get; set; }

    /// <summary>
    /// 修改时间
    /// </summary>        
    [SugarColumn(IsNullable = false)]
    public DateTime? UpdatedTime { get; set; }

    /// <summary>
    /// 描述
    /// </summary>
    [SugarColumn(IsNullable = true, ColumnDataType = "nvarchar(256)")]
    public string Description { get; set; }

    /// <summary>
    /// 是否可用(Y:可用,N:不可用)
    /// </summary>
    [SugarColumn(IsNullable = false, Length = 1)]
    public string IsAvailable { get; set; }

    /// <summary>
    /// 是否正式发布Y/N
    /// </summary>
    [SugarColumn(IsNullable = false, Length = 1)]
    public string IsOfficial { get; set; }

    /// <summary>
    /// 是否最新Y/N
    /// </summary>
    [SugarColumn(IsNullable = false, Length = 1)]
    public string IsLast { get; set; }

    /// <summary>
    /// 是否删除Y/N
    /// </summary>
    [SugarColumn(IsNullable = false, Length = 1)]
    public string IsDeleted { get; set; }

    /// <summary>
    /// 是否归档Y/N
    /// </summary>
    [SugarColumn(IsNullable = false, Length = 1)]
    public string IsArchived { get; set; }

    [SugarColumn(IsNullable = true, Length = 64)]
    public string GBCode { get; set; }

    [SugarColumn(IsNullable = true, ColumnDataType = "nvarchar(256)")]
    public string GBName { get; set; }

    /// <summary>
    /// 名称助记码
    /// </summary>
    [SugarColumn(IsNullable = true, Length = 64)]
    public string CnnameMemoryCode { get; set; }

    /// <summary>
    /// 别名助记码
    /// </summary>
    [SugarColumn(IsNullable = true, Length = 64)]
    public string AliasMemoryCode { get; set; }

    /// <summary>
    /// 拼音输入码
    /// </summary>
    [SugarColumn(IsNullable = true, Length = 64)]
    public string InputPinyin { get; set; }

    /// <summary>
    /// 目录字典,树的分类,和分类表关联, 树不能拖动只能象药性一样属于哪个最末端分支
    /// </summary>
    [SugarColumn(IsNullable = true, Length = 64)]
    public string CatalogTreePid { get; set; }

    /// <summary>
    /// 目录树名称
    /// </summary>
    [SugarColumn(IsNullable = true, ColumnDataType = "nvarchar(256)")]
    public string CatalogTreeName { get; set; }

    /// <summary>
    /// 字典,药性分类ID
    /// </summary>
    [SugarColumn(IsNullable = true, Length = 64)]
    public string ClassId { get; set; }

    /// <summary>
    /// 字典,药性分类NAME
    /// </summary>
    [SugarColumn(IsNullable = true, ColumnDataType = "nvarchar(256)")]
    public string ClassName { get; set; }

    /// <summary>
    /// 字典,剂型分类ID
    /// </summary>
    [SugarColumn(IsNullable = true, Length = 64)]
    public string DosageFormId { get; set; }

    /// <summary>
    /// 字典,剂型分类NAME
    /// </summary>
    [SugarColumn(IsNullable = true, ColumnDataType = "nvarchar(256)")]
    public string DosageFormName { get; set; }

    /// <summary>
    /// 物价编码
    /// </summary>
    [SugarColumn(IsNullable = true, Length = 64)]
    public string ItemPriceCode { get; set; }

    /// <summary>
    /// 医保项目编码
    /// </summary>
    [SugarColumn(IsNullable = true, Length = 64)]
    public string ItemMiCode { get; set; }

    /// <summary>
    /// 基本药物标识 N 普通药 S 国基 P 省基 C 地市级
    /// </summary>
    [SugarColumn(IsNullable = true, Length = 64)]
    public string BaseDrugFlag { get; set; }

    /// <summary>
    /// 基本药物说明
    /// </summary>
    [SugarColumn(IsNullable = true, ColumnDataType = "nvarchar(512)")]
    public string BaseDrugDesc { get; set; }

    /// <summary>
    /// 医保甲乙类标识 0 自费 1 甲类 2 乙类
    /// </summary>
    [SugarColumn(IsNullable = true, Length = 64)]
    public string BaseDrugDescmedicareFlag { get; set; }

    /// <summary>
    /// 来源CODE, 1 国产 2进口 3 合资 0 其他 4 自制
    /// </summary>
    [SugarColumn(IsNullable = true, Length = 64)]
    public string SourceCode { get; set; }

    /// <summary>
    /// 来源NAME
    /// </summary>
    [SugarColumn(IsNullable = true, ColumnDataType = "nvarchar(256)")]
    public string SourceName { get; set; }

    /// <summary>
    /// 厂商代码
    /// </summary>
    [SugarColumn(IsNullable = true, Length = 64)]
    public string ManufacturerCode { get; set; }

    /// <summary>
    /// 厂商名称
    /// </summary>
    [SugarColumn(IsNullable = true, ColumnDataType = "nvarchar(256)")]
    public string ManufacturerName { get; set; }

    /// <summary>
    /// 药品本位码
    /// </summary>
    [SugarColumn(IsNullable = true, Length = 64)]
    public string DrugStandardCode { get; set; }

    /// <summary>
    /// 批准文号
    /// </summary>
    [SugarColumn(IsNullable = true, Length = 64)]
    public string ApprovalNum { get; set; }

    /// <summary>
    /// 是否新药品Y/N
    /// </summary>
    [SugarColumn(IsNullable = true, Length = 1)]
    public string IsNewDrug { get; set; }
}
