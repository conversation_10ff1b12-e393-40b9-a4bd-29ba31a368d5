﻿using SqlSugar;

namespace Peis.Model.DataBaseEntity
{
    ///<summary>
    ///危急值项目表
    ///</summary>
    [SugarTable("CriticalValueMsgItem")]
    public class CriticalValueMsgItem
    {
        /// <summary>
        /// 危急值Id
        /// </summary>        
        [SugarColumn(IsNullable = false, IsPrimaryKey = true)]
        public long CriticalId { get; set; }

        /// <summary>
        /// 项目代码
        /// </summary>        
        [SugarColumn(IsNullable = false, IsPrimaryKey = true, Length = 6)]
        public string ItemCode { get; set; }

        /// <summary>
        /// 项目结果
        /// </summary>        
        [SugarColumn(IsNullable = false, ColumnDataType = StaticConfig.CodeFirst_BigString)]
        public string ItemResult { get; set; }

        /// <summary>
        /// 结果下限
        /// </summary>        
        [SugarColumn(IsNullable = false, Length = 10)]
        public string LowerLimit { get; set; }

        /// <summary>
        /// 结果上限
        /// </summary>        
        [SugarColumn(IsNullable = false, Length = 10)]
        public string UpperLimit { get; set; }
    }
}