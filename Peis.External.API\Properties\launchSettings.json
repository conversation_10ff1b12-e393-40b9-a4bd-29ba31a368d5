{"profiles": {"Peis.External.API": {"commandName": "Project", "launchUrl": "swagger", "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "Development"}, "dotnetRunMessages": true, "applicationUrl": "http://localhost:5265"}, "IIS Express": {"commandName": "IISExpress", "launchBrowser": true, "launchUrl": "swagger", "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "Development"}}, "Container (Dockerfile)": {"commandName": "<PERSON>er", "launchUrl": "{Scheme}://{ServiceHost}:{ServicePort}/swagger", "environmentVariables": {"ASPNETCORE_URLS": "http://+:80"}, "publishAllPorts": true, "useSSL": false}}, "$schema": "https://json.schemastore.org/launchsettings.json", "iisSettings": {"windowsAuthentication": false, "anonymousAuthentication": true, "iisExpress": {"applicationUrl": "http://localhost:15086", "sslPort": 0}}}