﻿using SqlSugar;

namespace Peis.Model.DataBaseEntity
{
    ///<summary>
    ///疾病包含关系
    ///</summary>
    [SugarTable("MapDiseaseDisease")]
    public class MapDiseaseDisease
    {
        /// <summary>
        /// 父疾病
        /// </summary>        
        [SugarColumn(IsNullable = false, IsPrimaryKey = true, Length = 8)]
        public string ParentDiseaseCode { get; set; }

        /// <summary>
        /// 子疾病
        /// </summary>        
        [SugarColumn(IsNullable = false, IsPrimaryKey = true, Length = 8)]
        public string ChildDiseaseCode { get; set; }
    }
}