﻿using Peis.Model.Other.PeEnum;
using SqlSugar;

namespace Peis.Model.DataBaseEntity
{
    ///<summary>
    ///费用分类分组信息（收费类代码）
    ///</summary>
    [SugarTable("CodeFeeClsGroup")]
    public class CodeFeeClsGroup
    {
        /// <summary>
        /// 分组代码
        /// </summary>        
        [SugarColumn(IsNullable = false, IsPrimaryKey = true, Length = 10)]
        public string FeeClsGroupCode { get; set; }

        /// <summary>
        /// 分组名称
        /// </summary>        
        [SugarColumn(IsNullable = false, Length = 20)]
        public string FeeClsGroupName { get; set; }

        /// <summary>
        /// 分组类型
        /// </summary>        
        [SugarColumn(IsNullable = false)]
        public FeeClsGroupType FeeClsGroupType { get; set; }

        /// <summary>
        /// 是否累计
        /// </summary>        
        [SugarColumn(IsNullable = false)]
        public bool IsAddUp { get; set; }
    }
}