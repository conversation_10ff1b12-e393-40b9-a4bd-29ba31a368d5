﻿using Peis.Model.DTO;
using Peis.Model.DTO.CriticalValue;
using Peis.Model.DTO.DataQuery;
using Peis.Model.Other.Input.DataQuery;
using Peis.Model.Other.PeEnum;
using Peis.Repository.IRepository;
using Peis.Service.IService;
using Peis.Utility.PeUser;
using System.Text;

namespace Peis.Service.Service
{
    public class DataQueryService : IDataQueryService
    {
        private readonly IDataTranRepository _dataTranRepository;
        private readonly IRegisterRepository _registerRepository;
        private readonly ISampleRepository _sampleRepository;
        private readonly IRecordRepository _recordRepository;
        private readonly ILogBusinessRepository _logBusinessRepository;
        private readonly ICriticalValueRepository _criticalValueRepository;  
        private readonly IMajorPositiveRepository _majorPositiveRepository;
        private readonly IReportConclusionRepository _reportConclusionRepository;
        private readonly IDataRepository<CriticalValueMsg> _criticalValueMsgRepository;
        private readonly IDataRepository<CriticalValueMsgItem> _criticalValueMsgItemRepository;
        private readonly IDataRepository<MajorPositivePerson> _mjorPositivePersonRepository;
        private readonly IDataRepository<MajorPositivePersonDetail> _majorPositivePersonDetailRepository;
        private readonly IDataRepository<RecordContact> _recordContactRepository;
        private readonly IDataRepository<PeQuestionToDoctor> _questionToDoctorRepository;
        private readonly IDataRepository<FollowUp> _peFollowUpRepository;
        private readonly IHttpContextUser _httpContextUser;
        private readonly ISplitTable _splitTable;
        private readonly ICacheRepository _cacheRepository;

        public DataQueryService(
            IDataTranRepository dataTranRepository,
            IRegisterRepository registerRepository,
            ISampleRepository sampleRepository,
            IRecordRepository recordRepository,
            ILogBusinessRepository logBusinessRepository,
            ICriticalValueRepository criticalValueRepository,
            IMajorPositiveRepository majorPositiveRepository,
            IReportConclusionRepository reportConclusionRepository,
            IDataRepository<CriticalValueMsg> criticalValueMsgRepository,
            IDataRepository<CriticalValueMsgItem> criticalValueMsgItemRepository,
            IDataRepository<MajorPositivePerson> mjorPositivePersonRepository,
            IDataRepository<MajorPositivePersonDetail> majorPositivePersonDetailRepository,
            IDataRepository<RecordContact> recordContactRepository,
            IDataRepository<PeQuestionToDoctor> questionToDoctorRepository,
            IHttpContextUser httpContextUser,
            IDataRepository<SysOperator> sysOperatorRepository,
            ISplitTable splitTable,
            ICacheRepository cacheRepository,
            IDataRepository<FollowUp> peFollowUpRepository)
        {
            _dataTranRepository = dataTranRepository;
            _registerRepository = registerRepository;
            _sampleRepository = sampleRepository;
            _recordRepository = recordRepository;
            _logBusinessRepository = logBusinessRepository;
            _criticalValueRepository = criticalValueRepository;
            _majorPositiveRepository = majorPositiveRepository;
            _criticalValueMsgRepository = criticalValueMsgRepository;
            _criticalValueMsgItemRepository = criticalValueMsgItemRepository;
            _mjorPositivePersonRepository = mjorPositivePersonRepository;
            _majorPositivePersonDetailRepository = majorPositivePersonDetailRepository;
            _reportConclusionRepository = reportConclusionRepository;
            _recordContactRepository = recordContactRepository;
            _questionToDoctorRepository = questionToDoctorRepository;
            _httpContextUser = httpContextUser;
            _splitTable = splitTable;
            _peFollowUpRepository = peFollowUpRepository;
            _cacheRepository = cacheRepository;
        }

        #region 体检综合查询
        /// <summary>
        /// 体检综合查询
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        public PeComprehensiveData PeComprehensiveQuery(PeComprehensiveQuery query, ref int totalNumber, ref int totalPage)
        {
            var beginDate = DateTime.Now;
            var endDate = DateTime.Now.Date.Add(new TimeSpan(23, 59, 59));
            RegisterDateRange regRang = new RegisterDateRange
            {
                MinRegTime = beginDate,
                MaxRegTime = endDate
            };
            if (query.StartTime.HasValue && query.EndTime.HasValue)
            {
                beginDate = query.StartTime.Value;
                endDate = query.EndTime.Value;
                regRang.MinRegTime = query.StartTime.Value;
                regRang.MaxRegTime = query.EndTime.Value;
            }

            // 如果根据体检时间查询，则需要查出分表依据
            if (query.QueryType == 2)
            {
                regRang = _registerRepository.ReadRegDateRangeByActiveTime(beginDate, endDate);
            }

            
            var combCharges = _registerRepository.ReadRegisterCombs(regRang.MinRegTime, regRang.MaxRegTime)
               .GroupBy(x => x.RegNo)
               .Select(x => new {
                   x.RegNo,
                   zf = SqlFunc.AggregateCount(x.IsPayBySelf == true ? x.RegNo : null),
                   jz = SqlFunc.AggregateCount(x.IsPayBySelf != true ? x.RegNo : null)
               }).MergeTable();
            var sugarQuery = _registerRepository.ReadRegister()
                .LeftJoin(combCharges, (reg, charge) => reg.RegNo == charge.RegNo);
            if (!query.RegNo.IsNullOrEmpty())
                sugarQuery.Where(reg => reg.RegNo == query.RegNo);
            else if (!query.PatCode.IsNullOrEmpty())
                sugarQuery.Where(reg => reg.PatCode == query.PatCode);
            else if (!query.CardNo.IsNullOrEmpty())
                sugarQuery.Where(reg => reg.CardNo == query.CardNo);
            else if (!query.Name.IsNullOrEmpty())
                sugarQuery.Where(reg => reg.Name.Contains(query.Name));
            else
            {
                sugarQuery
                    .WhereIF(!query.CompanyCode.IsNullOrEmpty(), reg => reg.CompanyCode == query.CompanyCode)
                    .WhereIF(Enum.IsDefined(typeof(PeCls), query.PeCls), reg => reg.PeCls == query.PeCls)
                    .WhereIF(Enum.IsDefined(typeof(PeStatus), query.PeStatus), reg => reg.PeStatus == query.PeStatus)
                    .WhereIF(Enum.IsDefined(typeof(Sex), query.Sex), reg => reg.Sex == query.Sex)
                    .WhereIF(query.RegisterTimes.HasValue, reg => reg.RegisterTimes == query.RegisterTimes)
                    .WhereIF(query.IsRecheck.HasValue, reg => SqlFunc.HasValue(reg.RecheckNo))
                    .WhereIF(query.QueryType == 1, reg => SqlFunc.Between(reg.RegisterTime, beginDate, endDate))
                    .WhereIF(query.QueryType == 2, reg => SqlFunc.Between(reg.ActiveTime, beginDate, endDate))
                    .WhereIF(query.QueryType == 3, reg => SqlFunc.Between(reg.BookBeginTime, beginDate, endDate) ||
                                                          SqlFunc.Between(reg.BookEndTime, beginDate, endDate))
                    .WhereIF(!query.CompanyDeptCode.IsNullOrEmpty(), reg => reg.CompanyDeptCode == query.CompanyDeptCode)
                    .WhereIF(query.IsActive.HasValue, reg => reg.IsActive == query.IsActive)
                    .WhereIF(query.CompanyTimes.HasValue && query.CompanyTimes > 0, reg => reg.CompanyTimes == query.CompanyTimes)
                    .WhereIF(query.IsCompanyCheck.HasValue, reg => reg.IsCompanyCheck == query.IsCompanyCheck)
                    .WhereIF(query.GuidancePrinted.HasValue, reg => reg.GuidancePrinted == query.GuidancePrinted)
                    .WhereIF(!query.ClusCode.IsNullOrEmpty(), reg => SqlFunc.Subqueryable<PeRegisterCluster>().Where(x => x.RegNo == reg.RegNo && x.ClusCode == query.ClusCode).Any())
                    .WhereIF(query.CombChargeMode == 1, (reg, charge) => charge.zf > 0 && charge.jz == 0)
                    .WhereIF(query.CombChargeMode == 2, (reg, charge) => charge.zf > 0 && charge.jz > 0)
                    .WhereIF(query.CombChargeMode == 3, (reg, charge) => charge.zf == 0 && charge.jz > 0);
            }

            var summaryList = sugarQuery.Clone().Select(reg => new
            {
                reg.RegNo,
                reg.IsActive,
                reg.PeStatus,
                reg.ReportPrinted
            }).ToList();
            PeComprehensiveData regData = new();
            if (summaryList.IsNullOrEmpty())
            {
                regData.ComprehensiveData = new();
                regData.RecordStatus = new();
                return regData;
            }
           
            var mergeQuery = sugarQuery
               .LeftJoin<CodeCompanyDepartment>((reg, charge, dept) => dept.DeptCode == reg.CompanyDeptCode)
               .LeftJoin<CodeCompany>((reg, charge, dept, comp) => comp.CompanyCode == reg.CompanyCode)
               
               .Select((reg, charge, dept, comp) => new ComprehensiveData
               {
                   Sex               = reg.Sex,
                   Age               = reg.Age,
                   Tel               = reg.Tel,
                   Name              = reg.Name,
                   PatCode           = reg.PatCode,
                   RegNo             = reg.RegNo,
                   RegisterTimes     = reg.RegisterTimes,
                   IsVIP             = reg.IsVIP,
                   PeCls             = reg.PeCls,
                   CardNo            = reg.CardNo,
                   BookType          = reg.BookType,
                   PeStatus          = reg.PeStatus,
                   IsActive          = reg.IsActive,
                   IsCompanyCheck    = reg.IsCompanyCheck,
                   MarryStatus       = reg.MarryStatus,
                   GuidanceType      = reg.GuidanceType,
                   ReportPrinted     = reg.ReportPrinted,
                   ReportPrintedTime = reg.ReportPrintedTime,
                   ActiveTime        = reg.ActiveTime,
                   Price             = reg.TotalPrice,
                   DeptName          = dept.DeptName,
                   CompanyName       = comp.CompanyName,
                   ClusterName       = SqlFunc.Subqueryable<PeRegisterCluster>().Where(z => z.RegNo == reg.RegNo).SelectStringJoin(z => z.ClusName, "，"),
                   GuidancePrinted   = reg.GuidancePrinted,
                   GuidancePrintTime = reg.GuidancePrintTime,
                   CombChargeMode    = charge.jz > 0 ? (charge.zf > 0 ? "部分自费" : "团体记账") : "全部自费",
                   IsRecheck         = SqlFunc.HasValue(reg.RecheckNo),
                   RecheckNo         = reg.RecheckNo,
               })
               .MergeTable()
               .OrderByIF(query.OrderByList.IsNullOrEmpty(), x => x.RegNo)
               .OrderBy(query.OrderByList);
            
            if (query.PageSize > 0)
            {
                regData.ComprehensiveData = mergeQuery.ToPageList(query.PageNumber, query.PageSize);
                totalPage = (int)Math.Ceiling(totalNumber * 1.0 / query.PageSize);
            }
            else
            {
                regData.ComprehensiveData = mergeQuery.ToList();
                totalPage = 1;
            }

            var conclusions = _reportConclusionRepository.GetReportConclusions(regRang.MinRegTime, regRang.MaxRegTime)
                .Where(x => SqlFunc.ContainsArray(regData.ComprehensiveData.Select(reg => reg.RegNo).ToArray(), x.RegNo))
                .Where(x => x.IsOccupaiton == false)
                .Select(x => new { x.RegNo, x.AuditDoctorName, x.CheckDoctorName })
                .ToList();
            
            regData.ComprehensiveData.ForEach(x =>
            {
                if(conclusions.Any(y => y.RegNo == x.RegNo))
                {
                    x.AuditDoctorName = conclusions.First(y => y.RegNo == x.RegNo).AuditDoctorName;
                    x.CheckDoctorName = conclusions.First(y => y.RegNo == x.RegNo).CheckDoctorName;
                }
            });
            regData.RecordStatus = new RecordStatus
            {
                TotalCount        = summaryList.Count,
                InActiveCount     = summaryList.Count(x => !x.IsActive),
                UnCheckedCount    = summaryList.Count(x => x.PeStatus == PeStatus.未检查),
                IsCheckingCount   = summaryList.Count(x => x.PeStatus == PeStatus.正在检查),
                CheckedCount      = summaryList.Count(x => x.PeStatus == PeStatus.已检完),
                ApprovedCount     = summaryList.Count(x => x.PeStatus == PeStatus.已审核),
                IssuedReportCount = summaryList.Count(x => x.ReportPrinted)
            };
            totalNumber = summaryList.Count;

            return regData;
        }

        /// <summary>
        /// 获取患者基本信息
        /// </summary>
        /// <param name="regNo"></param>
        /// <param name="msg"></param>
        /// <returns></returns>
        public PatientBasicInfo GetPatientBasicInfo(string regNo, ref string msg)
        {
            if (string.IsNullOrEmpty(regNo))
            {
                msg = "体检号不能为空";
                return null;
            }

            //TODO:发票名称未处理
            var registerData = _registerRepository.ReadRegister(regNo).First();

            // 使用缓存获取关联数据
            var jobDict = _cacheRepository.DictJob();
            var companyDict = _cacheRepository.DictCompany();
            var deptDict = _cacheRepository.DictCompanyDepartment();

            var regInfo = new PatientBasicInfo
            {
                PhotoUrl = registerData.PhotoUrl ?? "",
                RegNo = registerData.RegNo,
                PatCode = registerData.PatCode,
                RegisterTimes = registerData.RegisterTimes,
                Name = registerData.Name,
                Sex = registerData.Sex,
                PeCls = registerData.PeCls,
                Birthday = registerData.Birthday,
                Age = registerData.Age,
                NativePlace = registerData.NativePlace,
                CardType = registerData.CardType,
                CardNo = registerData.CardNo,
                MarryStatus = registerData.MarryStatus,
                Tel = registerData.Tel,
                JobHistory = registerData.JobHistory,
                Address = registerData.Address,
                MedicalHistory = registerData.MedicalHistory,
                Note = registerData.Note,
                Introducer = registerData.Introducer,
                OperatorCode = registerData.OperatorCode,
                RecheckNo = registerData.RecheckNo,
                RegisterTime = registerData.RegisterTime,
                ActiveTime = registerData.ActiveTime,
                IsLeader = registerData.IsLeader,
                IsRecheck = !string.IsNullOrEmpty(registerData.RecheckNo),
                IsVIP = registerData.IsVIP,
                IsConstitution = registerData.IsConstitution,
                JobName = jobDict.TryGetValue(registerData.JobCode ?? "", out var job) ? job.JobName : "",
                InvoiceNo = "",
                //PePrice = 0,
                //AddItemPrice = 0,
                CompanyContact = companyDict.TryGetValue(registerData.CompanyCode ?? "", out var company) ? company.CompanyContact : "",
                DeptName = deptDict.TryGetValue(registerData.CompanyDeptCode ?? "", out var dept) ? dept.DeptName : "",
                CompanyName = companyDict.TryGetValue(registerData.CompanyCode ?? "", out var comp) ? comp.CompanyName : "",
                IsCompanyCheck = registerData.IsCompanyCheck
            };

            if (!string.IsNullOrWhiteSpace(regInfo.PhotoUrl))
                regInfo.PhotoUrl = Appsettings.GetSectionValue("WebserviceHub:FileServiceUrl") + regInfo.PhotoUrl;

            //体检价格
            var combs = _registerRepository.ReadRegisterCombs(regNo)
                        .Select(x => new
                        {
                            x.Price,
                            x.IsPayBySelf
                        }).ToArray();

            regInfo.PePrice = combs.Sum(x => x.Price);
            regInfo.AddItemPrice = 0;

            if (regInfo.IsCompanyCheck)
                regInfo.AddItemPrice = combs.Where(x => x.IsPayBySelf).Sum(x => x.Price);

            return regInfo;
        }

        /// <summary>
        /// 获取项目情况
        /// </summary>
        /// <param name="regNo"></param>
        /// <param name="msg"></param>
        /// <returns></returns>
        public ItemData GetItemsInfo(string regNo, ref string msg)
        {
            if (string.IsNullOrEmpty(regNo))
            {
                msg = "体检号不能为空";
                return null;
            }

            //个人信息
            var regInfo = _registerRepository.ReadRegister()
                          .Where(reg => reg.RegNo == regNo)
                          .Select(reg => new
                          {
                              reg.IsCompanyCheck,
                              ClusName = SqlFunc.Subqueryable<PeRegisterCluster>()
                                          .Where(x => x.RegNo == regNo)
                                          .SelectStringJoin(x => x.ClusName, ",")
                          }).First();

            //读取体检号所有支付的组合及结果
            var combsResult = _recordRepository.ReadCombsInfo(regNo, false)
                              .Distinct()
                              .Select((regComb, recComb) => new CombResultData
                              {
                                  ClsCode = regComb.ClsCode,
                                  ClsName = regComb.ClsName,
                                  CombCode = regComb.CombCode,
                                  CombName = regComb.CombName,
                                  DoctorName = recComb.DoctorName,
                                  IsPayBySelf = regComb.IsPayBySelf
                              }).ToList();

            ItemData itemData = new ItemData();
            itemData.ClusName = regInfo?.ClusName;
            itemData.ItemCls = combsResult.Select(x => new ItemCls { ClsCode = x.ClsCode, ClsName = x.ClsName }).DistinctBy(x => x.ClsCode).ToArray();
            itemData.NoCheckItems = GetCombData(x => string.IsNullOrEmpty(x.DoctorName));
            itemData.AbandonItems = GetCombData(x => x.DoctorName == "弃检");

            if (regInfo != null && regInfo.IsCompanyCheck)
            {
                itemData.AddItem = GetCombData(x => x.IsPayBySelf);
                itemData.CompanyPayItems = GetCombData(x => x.IsPayBySelf);
                //itemData.PlatformPayItems = null;
            }
            else
            {
                itemData.SelfPayItems = GetCombData(x => x.IsPayBySelf);
                //itemData.PlatformPayItems = null;
            }

            return itemData;

            #region 内部方法
            CombData[] GetCombData(Func<CombResultData, bool> match)
            {
                return combsResult.Where(match)
                    .DistinctBy(x => x.CombCode)
                    .Select(x => new CombData
                    {
                        CombCode = x.CombCode,
                        CombName = x.CombName
                    }).ToArray();
            }
            #endregion

        }

        /// <summary>
        /// 获取费用清单
        /// </summary>
        /// <param name="regNo">体检号</param>
        /// <param name="msg"></param>
        /// <returns></returns>
        public bool GetExpenseList(string regNo, ref ExpenseData data, ref string msg)
        {
            if (string.IsNullOrWhiteSpace(regNo))
            {
                msg = "体检号不能为空";
                return false;
            }

            // 个人信息
            var regInfo = _registerRepository.ReadRegister(regNo).First();
            // 获取体检组合对应什么收费分类
            var itemFee = _registerRepository.ReadCombAndFeeClsDetails(regNo)
                          .WhereIF(!regInfo.IsCompanyCheck, regComb => regComb.PayStatus == PayStatus.收费)
                          .WhereIF(regInfo.IsCompanyCheck, regComb => !regComb.IsPayBySelf || regComb.PayStatus == PayStatus.收费)
                          .Select((regComb, itemComb, feeCls) => new
                          {
                              feeCls.FeeClsCode,
                              feeCls.FeeClsName,
                              regComb.CombName,
                              regComb.OriginalPrice,
                              regComb.Price,
                          }).ToList();

            // 不同类别收费金额
            var itemList = itemFee
                .Select(x => new Item
                {
                    FeeClsName = x.FeeClsName ?? "无",
                    CombName = x.CombName,
                    OriginalPrice = x.OriginalPrice,
                    Price = x.Price,
                    SelfPayPrice = !regInfo.IsCompanyCheck ? x.Price : 0,
                    CompanyPayPrice = regInfo.IsCompanyCheck && regInfo.BookType == BookType.团体导入 ? x.Price : 0,
                    PlatformPayPrice = (regInfo.IsCompanyCheck && regInfo.BookType != BookType.团体导入) ? x.Price : 0
                }).ToList();

            // 合计
            var feeCls = itemList.GroupBy(x => x.FeeClsName)
                .Select(x => new ItemFeeCls
                {
                    Items = x.ToArray(),
                    Price = x.Sum(x => x.Price),
                    SelfPayPrice = !regInfo.IsCompanyCheck ? x.Sum(x => x.Price) : 0,
                    CompanyPayPrice = regInfo.IsCompanyCheck && regInfo.BookType == BookType.团体导入 ? x.Sum(x => x.Price) : 0,
                    PlatformPayPrice = !(regInfo.IsCompanyCheck || regInfo.BookType == BookType.团体导入) ? x.Sum(x => x.Price) : 0
                })
                .ToList();

            data.Name = regInfo.Name;
            data.Sex = regInfo.Sex;
            data.Age = regInfo.Age;
            data.RegNo = regInfo.RegNo;
            //expenseData.OriginalPrice = 0;
            data.Price = feeCls.Sum(x => x.Price);
            data.ItemFeeCls = feeCls.ToArray();

            if (!regInfo.IsCompanyCheck) //个检
                data.SelfPayPrice = feeCls.Sum(x => x.SelfPayPrice);
            else if (regInfo.IsCompanyCheck && regInfo.BookType == BookType.团体导入)  //团体
                data.CompanyPayPrice = feeCls.Sum(x => x.CompanyPayPrice);
            else //其他平台
                data.PlatformPayPrice = feeCls.Sum(x => x.PlatformPayPrice);

            return true;
        }

        /// <summary>
        /// 获取标本数据
        /// </summary>
        /// <param name="regNo"></param>
        /// <param name="msg"></param>
        /// <returns></returns>
        public PeSampleData[] GetSampleData(string regNo, ref string msg)
        {
            if (string.IsNullOrEmpty(regNo))
            {
                msg = "体检号不能为空";
                return null;
            }

            return _sampleRepository.ReadSampleBarcodeData(regNo)
                .Where(sample => !string.IsNullOrEmpty(sample.SampleNo))
                .Select((sample, comb, barcode, pack) => new PeSampleData
                {
                    SampleNo = sample.SampleNo,
                    BarcodeName = barcode.BarcodeName,
                    CombName = comb.CombName,
                    GatherTime = (DateTime)sample.GatherTime,
                    GatherOperator = sample.GatherOperator,
                    TransportTime = pack.TransportTime,
                    Status = SqlFunc.IIF(pack.HasTransport == true, 3, 2),//HasTransport 可能为空,所以需要判断
                }).ToArray();
        }

        /// <summary>
        /// 获取日志数据
        /// </summary>
        /// <param name="regNo"></param>
        /// <param name="msg"></param>
        /// <returns></returns>
        public LogData[] GetLogData(string regNo, ref string msg)
        {
            if (string.IsNullOrEmpty(regNo))
            {
                msg = "体检号不能为空";
                return null;
            }

            return _logBusinessRepository.Read()
                .Where(x => x.RegNo == regNo)
                .Select(x => new LogData
                {
                    LogTime = x.LogTime,
                    Message = x.Message,
                    OperatorName = x.OperatorName,
                }).ToArray();
        }

        /// <summary>
        /// 危急异常
        /// </summary>
        /// <param name="regNo"></param>
        /// <param name="msg"></param>
        /// <returns></returns>
        public CriticalExceptionData[] GetCriticalException(string regNo, ref string msg)
        {
            if (string.IsNullOrEmpty(regNo))
            {
                msg = "体检号不能为空";
                return null;
            }

            var criticalData = _criticalValueRepository.ReadCriticalValueMsg(regNo)
                .Select((msg, item, followUp) => new
                {
                    ItemCode = item.ItemCode,
                    ItemResult = item.ItemResult,
                    Creator = msg.Creator,
                    CreateTime = msg.CreateTime,
                    Confirmer = msg.Confirmer,
                    ConfirmTime = msg.ConfirmTime,
                    ReplyContent = msg.ReplyContent,
                    ReplyPerson = msg.ReplyPerson,
                    ReplyTime = msg.ReplyTime,
                    SecondaryAfterFollowUp = followUp.SecondaryAfterFollowUp,
                    SecondaryNotifier = followUp.SecondaryNotifier,
                }).ToArray();

            var itemDict = _cacheRepository.DictCodeItem();

            return criticalData.Select(x => new CriticalExceptionData
            {
                ItemName               = itemDict.TryGetValue(x.ItemCode ?? "", out var itemInfo) ? itemInfo.ItemName : "",
                ItemResult             = x.ItemResult,
                Creator                = x.Creator,
                CreateTime             = x.CreateTime,
                Confirmer              = x.Confirmer,
                ConfirmTime            = x.ConfirmTime,
                ReplyContent           = x.ReplyContent,
                ReplyPerson            = x.ReplyPerson,
                ReplyTime              = x.ReplyTime,
                SecondaryAfterFollowUp = x.SecondaryAfterFollowUp,
                SecondaryNotifier      = x.SecondaryNotifier,
            }).ToArray();
        }

        /// <summary>
        /// 生成可上传的健康证XML
        /// </summary>
        /// <param name="regNo"></param>
        /// <param name="xml"></param>
        /// <param name="msg"></param>
        /// <returns></returns>
        public bool GenerateHealthCardXML(string regNo, ref string xml, ref string msg)
        {
            if (string.IsNullOrEmpty(regNo))
            {
                msg = "体检号不能为空";
                return false;
            }

            var regInfo = _registerRepository.ReadRegisterNoHosp()
                         .First(x => x.RegNo == regNo);

            if (regInfo == null)
            {
                msg = "暂无当前体检号信息!";
                return false;
            }

            if (regInfo.PeCls != PeCls.健康证)
            {
                msg = "当前人员不是健康证体检!";
                return false;
            }

            if (regInfo.PeStatus != PeStatus.已审核)
            {
                msg = "当前状态无法生成XML!";
                return false;
            }

            var photoUrl = GetImageHex();
            if (string.IsNullOrEmpty(photoUrl))
            {
                msg = "当前人员暂无头像,请上传头像后再进行操作!";
                return false;
            }

            var data = new Datafields
            {
                ZJLB     = HandleCardType(),    // 证件类别
                ZJHM     = regInfo.CardNo,      // 证件号码
                CZRXM    = regInfo.Name,       // 姓名
                XB       = HandleSex(),// 性别
                TJRQ     = regInfo.ActiveTime.Value.ToString("yyyy-MM-dd"),// 体检日期
                JKZZH    = regInfo.CardNo,// 健康证证号,与证件号码一致
                DYZT     = "0",// 打印状态 数据字典：0 未打印;1 已打印
                LZZT     = "0",// 领证状态 数据字典：0 未领;1 已领
                ZSZT     = "0",// 证书状态 数据字典：1 已过期;0 正常
                SQRQ     = regInfo.ActiveTime.Value.ToString("yyyy-MM-dd"),// 申请日期
                NL       = regInfo.Age.ToString(),// 年龄
                JKZLB    = "0",// 健康证类别：0 食品安全
                JKZJCDW  = "武警广东总队医院",// 检查单位
                JCDWSSDQ = "440106000000",// 检查单位所属地区
                GZDW     = "",// 工作单位
                LXDH     = regInfo.Tel,// 联系电话
                ZP       = photoUrl,// 相片
            };

            var healthCard = new HealthCardData { Datafields = data };

            var header = "<?xml version='1.0' encoding='utf-8'?>";
            xml = header + XmlHelper.Serialize(healthCard);

            return true;

            // 处理证件类型 (体检类型证件类型与健康证证件类型不一致)
            string HandleCardType()
            {
                return regInfo.CardType switch
                {
                    "1" => "0",
                    "6" => "1",
                    "2" => "2",
                    "8" => "3",
                    "4" => "4",
                    _ => "",
                };
            }

            // 处理性别
            string HandleSex()
            {
                return regInfo.Sex switch
                {
                    Sex.男 => "1",
                    Sex.女 => "2",
                    _ => "4",
                };
            }

            // 处理图片
            string GetImageHex()
            {
                try
                {
                    string rootPath = AppDomain.CurrentDomain.BaseDirectory;
                    string imageUrl = Path.Combine(rootPath, "wwwroot", regInfo.PhotoUrl);
                    using FileStream fs = new(imageUrl, FileMode.Open, FileAccess.Read);
                    using BinaryReader br = new BinaryReader(fs);
                    byte[] bytes = br.ReadBytes((int)fs.Length);

                    StringBuilder hexString = new StringBuilder();
                    foreach (byte b in bytes)
                    {
                        hexString.Append(b.ToString("X2"));
                    }

                    return hexString.ToString();
                }
                catch (Exception)
                {
                    return "";
                }
            }
        }
        #endregion

        #region 检验数据查询
        /// <summary>
        /// 检验数据查询
        /// </summary>
        /// <param name="regNo"></param>
        /// <param name="msg"></param>
        /// <returns></returns>
        public InspectionData InspectionDataQuery(string regNo, ref string msg)
        {
            if (string.IsNullOrEmpty(regNo))
            {
                msg = "体检号不能为空";
                return null;
            }

            //个人信息
            var inspectData = _registerRepository.ReadRegister(regNo)
                .Select(x => new InspectionData
                {
                    RegNo = x.RegNo,
                    Name = x.Name,
                    Sex = x.Sex,
                    Age = x.Age.ToString(),//TODO:AgeUnit  年龄需要拼接单位显示
                    CompanyName = SqlFunc.Subqueryable<CodeCompany>().Where(comp => comp.CompanyCode == x.CompanyCode).Select(comp => comp.CompanyName) ?? "",
                    ClusName = SqlFunc.Subqueryable<PeRegisterCluster>()
                                   .Where(clus => clus.RegNo == x.RegNo)
                                   .SelectStringJoin(clus => clus.ClusName, ",") ?? "",
                    PeStatus = x.PeStatus
                }).First();

            //体检项目
            inspectData.PeItems = _recordRepository.ReadCombItemRecord(regNo)
                                 .Where((regComb, recordComb, recordItem) =>
                                         !string.IsNullOrEmpty(recordComb.DoctorName) &&
                                         recordComb.DoctorName != "弃检" && !string.IsNullOrEmpty(recordItem.ItemResult))
                                 .Select((regComb, recordComb, recordItem) => new PeItem
                                 {
                                     CombCode = regComb.CombCode,
                                     CombName = regComb.CombName,
                                     ItemCode = recordItem.ItemCode,
                                     ItemName = recordItem.ItemName,
                                     ItemResult = recordItem.ItemResult,
                                     LowerLimit = recordItem.LowerLimit,
                                     UpperLimit = recordItem.UpperLimit
                                 }).ToArray();

            return inspectData;
        }
        #endregion

        #region 体检漏项查询
        /// <summary>
        /// 体检漏项查询
        /// </summary>
        /// <param name="query"></param>
        /// <param name="data"></param>
        /// <param name="msg"></param>
        /// <returns></returns>
        public bool PeMissingItemQuery(MissingItemQuery query, ref MissingItemData data, ref string msg)
        {
            query.BeginDate = query.BeginDate.Date;
            query.EndDate   = query.EndDate.Date.Add(new TimeSpan(23, 59, 59));

            //通过体检时间查询 最小、最大登记时间 用于下方查询分表
            var register = _registerRepository.ReadRegister()
                .Where(x => SqlFunc.Between(x.ActiveTime, query.BeginDate, query.EndDate))
                .Select(x => new RegisterDateRange
                {
                    MinRegTime = SqlFunc.AggregateMin(x.RegisterTime),
                    MaxRegTime = SqlFunc.AggregateMax(x.RegisterTime),
                }).First();

            //如果没有数据,直接返回
            if (!register.IsValidDate)
            {
                msg = "未查询到符合的数据";
                return false;
            }

            var sugarqueryable = _recordRepository.ReadCombResult(register.MinRegTime, register.MaxRegTime)
                .WhereIF(query.PeType == 1, reg => !reg.IsCompanyCheck)
                .WhereIF(query.PeType == 2, reg => reg.IsCompanyCheck)
                .WhereIF(!string.IsNullOrEmpty(query.CompanyCode), reg => reg.CompanyCode == query.CompanyCode)
                .Where((reg, comb, record) => string.IsNullOrEmpty(record.RegNo))
                .Select((reg, comb, record) => new MissingItem
                {
                    RegNo = reg.RegNo,
                    Name = reg.Name,
                    Sex = reg.Sex,
                    Age = reg.Age,
                    CombCode = comb.CombCode,
                    CombName = comb.CombName
                }).ToArray();

            data.Header = CreateHeader(sugarqueryable);
            data.BodyData = CreateData(sugarqueryable);

            #region 内部方法组装数据
            Dictionary<string, string> CreateHeader(MissingItem[] results)
            {
                var header = new Dictionary<string, string>();
                var codeCombQuery = results.DistinctBy(x => x.CombCode).Select(x => new
                {
                    x.CombCode,
                    x.CombName
                })
                .ToDictionary(x => x.CombCode, x => x.CombName);

                foreach (var comb in results.GroupBy(x => x.CombCode))
                {
                    header.Add(comb.Key, codeCombQuery[comb.Key]);
                }

                return header;
            }

            List<Dictionary<string, string>> CreateData(MissingItem[] results)
            {
                var data = new List<Dictionary<string, string>>();

                foreach (var reg in results.GroupBy(x => x.RegNo))
                {
                    var regData = new Dictionary<string, string>
                    {
                        { "regNo", reg.First().RegNo },
                        { "name", reg.First().Name },
                        { "sex",  reg.First().Sex.ToString() },
                        { "age",  reg.First().Age.ToString() }
                    };

                    foreach (var comb in reg)
                        regData.Add(comb.CombCode, "缺");

                    data.Add(regData);
                }

                return data;
            }

            #endregion

            return true;
        }
        #endregion

        #region 危急异常查询
        /// <summary>
        /// 危急异常查询
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        public CriticalExceptionData[] CriticalExceptionQuery(CriticalExceptionQuery query)
        {
            if (query.BeginDate.HasValue && query.EndDate.HasValue)
            {
                query.BeginDate = query.BeginDate.Value.Date;
                query.EndDate = query.EndDate.Value.Date.Add(new TimeSpan(23, 59, 59));
            }
            var criticalData = _criticalValueRepository.ReadCriticalValueMsg()
                .WhereIF(query.BeginDate.HasValue,(reg, msg, item)=>SqlFunc.Between(msg.CreateTime, query.BeginDate, query.EndDate))
                .WhereIF(!string.IsNullOrEmpty(query.KeyWord), reg => reg.RegNo == query.KeyWord || reg.Name.Contains(query.KeyWord))
                .Select((reg, msg, item, followUp) => new
                {
                    RegNo = reg.RegNo,
                    Name = reg.Name,
                    Sex = reg.Sex,
                    Age = reg.Age,
                    Tel = reg.Tel,
                    ItemCode = item.ItemCode,
                    ItemResult = item.ItemResult,
                    Id = msg.Id,
                    Creator = msg.Creator,
                    CreateTime = msg.CreateTime,
                    Confirmer = msg.Confirmer,
                    ConfirmTime = msg.ConfirmTime,
                    ReplyContent = msg.ReplyContent,
                    ReplyPerson = msg.ReplyPerson,
                    ReplyTime = msg.ReplyTime,
                    HasFollowUp = msg.FollowUpId != null,
                    SecondaryNotifier = followUp.SecondaryNotifier,
                    SecondaryAfterFollowUp = followUp.SecondaryAfterFollowUp,
                    FinallyNotifier = followUp.FinallyNotifier,
                    FinallyAfterFollowUp = followUp.FinallyAfterFollowUp,
                }).ToArray();

            var itemDict = _cacheRepository.DictCodeItem();

            return criticalData.Select(x => new CriticalExceptionData
            {
                RegNo                  = x.RegNo,
                Name                   = x.Name,
                Sex                    = x.Sex,
                Age                    = x.Age,
                Tel                    = x.Tel,
                ItemName               = itemDict.TryGetValue(x.ItemCode ?? "", out var itemInfo) ? itemInfo.ItemName : "",
                ItemResult             = x.ItemResult,
                Id                     = x.Id,
                Creator                = x.Creator,
                CreateTime             = x.CreateTime,
                Confirmer              = x.Confirmer,
                ConfirmTime            = x.ConfirmTime,
                ReplyContent           = x.ReplyContent,
                ReplyPerson            = x.ReplyPerson,
                ReplyTime              = x.ReplyTime,
                HasFollowUp            = x.HasFollowUp,
                SecondaryNotifier      = x.SecondaryNotifier,
                SecondaryAfterFollowUp = x.SecondaryAfterFollowUp,
                FinallyNotifier        = x.FinallyNotifier,
                FinallyAfterFollowUp   = x.FinallyAfterFollowUp,
            }).ToArray();
        }

        /// <summary>
        /// 删除危急异常项目
        /// </summary>
        /// <param name="Id"></param>
        /// <returns></returns>
        public bool DeleteCriticalException(long Id)
        {
            var criticalValue = _criticalValueMsgRepository.First(x => x.Id == Id);

            if (criticalValue == null) 
                return true;

            _dataTranRepository.ExecTran(() =>
            {
                _peFollowUpRepository.Delete(x => x.RegNo == criticalValue.RegNo);
                _criticalValueMsgItemRepository.Delete(x => x.CriticalId == Id);
                _criticalValueMsgRepository.Delete(criticalValue);
            });
            return true;
        }
        #endregion

        #region 重大异常查询
        /// <summary>
        /// 重大阳性查询
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        public MajorPositiveRecord[] MajorPositiveQuery(MajorPositiveQuery query)
        {
            var data = _majorPositiveRepository.ReadMajorPositiveRecord()
                  .WhereIF(!string.IsNullOrEmpty(query.KeyWord),
                            person => person.RegNo == query.KeyWord || person.Name.Contains(query.KeyWord))
                  .WhereIF(!string.IsNullOrEmpty(query.CompanyName), person => person.CompanyName == query.CompanyName)
                  .WhereIF(query.IsCompanyCheck == PersonCompany.个人, person => !person.IsCompanyCheck)
                  .WhereIF(query.IsCompanyCheck == PersonCompany.团体, person => person.IsCompanyCheck)
                  .Where(person => SqlFunc.Between(person.CreateTime, query.StartTime, query.EndTime.AddDays(1)))
                  .Select((person, detail) => new
                  {
                      person.RegNo,
                      person.Name,
                      person.Sex,
                      person.Age,
                      person.Tel,
                      person.Creator,
                      person.CreateTime,
                      detail.PositiveCode,
                      detail.PositiveName,
                      detail.PositiveType
                  }).ToList();

            return data.GroupBy(x => new { x.RegNo, x.Name, x.Sex, x.Age, x.Tel, x.Creator, x.CreateTime })
                .Select(x => new MajorPositiveRecord
                {
                    RegNo = x.Key.RegNo,
                    Name = x.Key.Name,
                    Sex = x.Key.Sex,
                    Age = x.Key.Age,
                    Tel = x.Key.Tel,
                    Creator = x.Key.Creator,
                    CreateTime = x.Key.CreateTime,
                    Details = x.ToList().Select(y => new MajorPositivePersonDetail
                    {
                        RegNo = x.Key.RegNo,
                        PositiveCode = y.PositiveCode,
                        PositiveName = y.PositiveName,
                        PositiveType = y.PositiveType,
                    }).ToArray()
                }).ToArray();
        }

        /// <summary>
        /// 删除重大阳性
        /// </summary>
        /// <param name="regNo"></param>
        /// <returns></returns>
        public bool DeleteMajorPositive(string regNo)
        {
            _dataTranRepository.ExecTran(() =>
            {
                _peFollowUpRepository.Delete(x => x.RegNo == regNo);
                _majorPositivePersonDetailRepository.Delete(x => x.RegNo == regNo);
                _mjorPositivePersonRepository.Delete(x => x.RegNo == regNo);
            });
            return true;
        }
        #endregion

        #region 体检报告进程
        #region 体检报告进程列表查询
        /// <summary>
        /// 体检报告进程列表查询
        /// </summary>
        /// <param name="query"></param>
        /// <param name="list"></param>
        /// <param name="msg"></param>
        /// <returns></returns>
        public bool PeReportProcessQuery(PeReportProcessQuery query, ref List<PeReportProcessData> list, ref string msg)
        {
            var peReportProcessesList = ReadPeReportProcess(query, ref msg);
            if (peReportProcessesList == null)
            {
                return false;
            }
            list = peReportProcessesList
                .Select((reg, regComb, report) => new PeReportProcessData()
                {
                    RegNo = reg.RegNo,
                    Name = reg.Name,
                    Sex = reg.Sex,
                    Age = reg.Age,
                    CardNo = reg.CardNo,
                    AuditDoctor = report.AuditDoctorCode,
                    BeginDate = (DateTime)SqlFunc.IF(query.BeginProcess == ProcessType.登记时间).Return(SqlFunc.AggregateMin(reg.RegisterTime))
                .ElseIF(query.BeginProcess == ProcessType.激活时间).Return(SqlFunc.AggregateMin(reg.ActiveTime))
                .ElseIF(query.BeginProcess == ProcessType.首结果时间).Return(SqlFunc.AggregateMin(regComb.ExamTime))
                .ElseIF(query.BeginProcess == ProcessType.末结果时间).Return(SqlFunc.AggregateMax(regComb.ExamTime))
                .ElseIF(query.BeginProcess == ProcessType.主检时间).Return(SqlFunc.AggregateMin(report.CheckTime))
                .ElseIF(query.BeginProcess == ProcessType.审核时间).Return(SqlFunc.AggregateMin(report.AuditTime))
                .ElseIF(query.BeginProcess == ProcessType.报告打印时间).Return(SqlFunc.AggregateMin(reg.ReportPrintedTime))
                .ElseIF(query.BeginProcess == ProcessType.指引单回收时间).Return(SqlFunc.AggregateMin(reg.GuidanceRecyclyTime))
                .End(SqlFunc.AggregateMin(reg.RegisterTime)),
                    EndDate = (DateTime)SqlFunc.IF(query.EndProcess == ProcessType.登记时间).Return(SqlFunc.AggregateMin(reg.RegisterTime))
                .ElseIF(query.EndProcess == ProcessType.激活时间).Return(SqlFunc.AggregateMin(reg.ActiveTime))
                .ElseIF(query.EndProcess == ProcessType.首结果时间).Return(SqlFunc.AggregateMin(regComb.ExamTime))
                .ElseIF(query.EndProcess == ProcessType.末结果时间).Return(SqlFunc.AggregateMax(regComb.ExamTime))
                .ElseIF(query.EndProcess == ProcessType.主检时间).Return(SqlFunc.AggregateMin(report.CheckTime))
                .ElseIF(query.EndProcess == ProcessType.审核时间).Return(SqlFunc.AggregateMin(report.AuditTime))
                .ElseIF(query.EndProcess == ProcessType.报告打印时间).Return(SqlFunc.AggregateMin(reg.ReportPrintedTime))
                .ElseIF(query.EndProcess == ProcessType.指引单回收时间).Return(SqlFunc.AggregateMin(reg.GuidanceRecyclyTime))
                .End(SqlFunc.AggregateMin(reg.RegisterTime)),
                    CompanyName = SqlFunc.Subqueryable<CodeCompany>().Where(x => x.CompanyCode == SqlFunc.AggregateMax(reg.CompanyCode)).Select(x => x.CompanyName),
                    GuidanceRecycleNote = SqlFunc.AggregateMin(reg.GuidanceRecycleNote),
                }).
                ToList().Where(x => x.Interval >= query.Interval).ToList();
            return true;
        }
        #endregion

        #region 体检报告进程分析
        /// <summary>
        /// 体检报告进程分析
        /// </summary>
        /// <param name="query"></param>
        /// <param name="data"></param>
        /// <param name="msg"></param>
        /// <returns></returns>
        public bool PeReportProcessAnalize(PeReportProcessQuery query, ref PeReportProcessAnalyzeData data, ref string msg)
        {
            var peReportProcessesList = ReadPeReportProcess(query, ref msg);
            if (peReportProcessesList == null)
            {
                return false;
            }
            var list = peReportProcessesList.Select((reg, regComb, report) => new PeReportProcessData
            {
                RegNo = reg.RegNo,
                BeginDate = (DateTime)SqlFunc.IF(query.BeginProcess == ProcessType.登记时间).Return(SqlFunc.AggregateMin(reg.RegisterTime))
                .ElseIF(query.BeginProcess == ProcessType.激活时间).Return(SqlFunc.AggregateMin(reg.ActiveTime))
                .ElseIF(query.BeginProcess == ProcessType.首结果时间).Return(SqlFunc.AggregateMin(regComb.ExamTime))
                .ElseIF(query.BeginProcess == ProcessType.末结果时间).Return(SqlFunc.AggregateMax(regComb.ExamTime))
                .ElseIF(query.BeginProcess == ProcessType.主检时间).Return(SqlFunc.AggregateMin(report.CheckTime))
                .ElseIF(query.BeginProcess == ProcessType.审核时间).Return(SqlFunc.AggregateMin(report.AuditTime))
                .ElseIF(query.BeginProcess == ProcessType.报告打印时间).Return(SqlFunc.AggregateMin(reg.ReportPrintedTime))
                .ElseIF(query.BeginProcess == ProcessType.指引单回收时间).Return(SqlFunc.AggregateMin(reg.GuidanceRecyclyTime))
                .End(SqlFunc.AggregateMin(reg.RegisterTime)),
                EndDate = (DateTime)SqlFunc.IF(query.EndProcess == ProcessType.登记时间).Return(SqlFunc.AggregateMin(reg.RegisterTime))
                .ElseIF(query.EndProcess == ProcessType.激活时间).Return(SqlFunc.AggregateMin(reg.ActiveTime))
                .ElseIF(query.EndProcess == ProcessType.首结果时间).Return(SqlFunc.AggregateMin(regComb.ExamTime))
                .ElseIF(query.EndProcess == ProcessType.末结果时间).Return(SqlFunc.AggregateMax(regComb.ExamTime))
                .ElseIF(query.EndProcess == ProcessType.主检时间).Return(SqlFunc.AggregateMin(report.CheckTime))
                .ElseIF(query.EndProcess == ProcessType.审核时间).Return(SqlFunc.AggregateMin(report.AuditTime))
                .ElseIF(query.EndProcess == ProcessType.报告打印时间).Return(SqlFunc.AggregateMin(reg.ReportPrintedTime))
                .ElseIF(query.EndProcess == ProcessType.指引单回收时间).Return(SqlFunc.AggregateMin(reg.GuidanceRecyclyTime))
                .End(SqlFunc.AggregateMin(reg.RegisterTime))
            }).ToList();
            list = list.OrderBy(l => l.Interval).ToList();
            var peReportProcessAnalyzeData = new PeReportProcessAnalyzeData();
            peReportProcessAnalyzeData.CountLessThanThree = list.Where(l => l.Interval <= 3).Count();
            peReportProcessAnalyzeData.CountThreeToSix = list.Where(l => l.Interval > 3 && l.Interval <= 6).Count();
            peReportProcessAnalyzeData.CountSixToNine = list.Where(l => l.Interval > 6 && l.Interval <= 9).Count();
            peReportProcessAnalyzeData.CountNineToTwelve = list.Where(l => l.Interval > 9 && l.Interval <= 12).Count();
            peReportProcessAnalyzeData.CountMoreThanTwelve = list.Where(l => l.Interval > 12).Count();
            peReportProcessAnalyzeData.CountAllReport = list.Count();
            peReportProcessAnalyzeData.MedianDays = list.Count() == 0 ? 0 : list[list.Count() / 2].Interval;
            peReportProcessAnalyzeData.NinetyPercentDays = list.Count() == 0 ? 0 : list[list.Count() / 10].Interval;
            peReportProcessAnalyzeData.MinDays = list.Count() == 0 ? 0 : list.Min(l => l.Interval);
            peReportProcessAnalyzeData.MaxDays = list.Count() == 0 ? 0 : list.Max(l => l.Interval);
            peReportProcessAnalyzeData.AvgDays = list.Count() == 0 ? 0 : list.Average(l => l.Interval);
            data = peReportProcessAnalyzeData;
            return true;
        }
        #endregion 
        #endregion

        #region 体检报告未出列表查询
        /// <summary>
        /// 体检报告未出列表查询
        /// </summary>
        /// <param name="query"></param>
        /// <param name="list"></param>
        /// <param name="msg"></param>
        /// <returns></returns>
        public bool PeNoReportQueay(PeNoReportQuery query, ref List<PeNoReportData> list, ref string msg)
        {
            query.BeginDate = query.BeginDate.Date;
            query.EndDate   = query.EndDate.Date.Add(new TimeSpan(23, 59, 59));

            var register = _registerRepository.ReadRegister()
                .Where(x => SqlFunc.Between(x.ActiveTime, query.BeginDate, query.EndDate))
                .Select(x => new RegisterDateRange
                {
                    MinRegTime = SqlFunc.AggregateMin(x.RegisterTime),
                    MaxRegTime = SqlFunc.AggregateMax(x.RegisterTime),
                }).First();

            if (!register.IsValidDate)
            {
                msg = "暂无数据,请选择其他时间查询!";
                return false;
            }

            Console.WriteLine($"开始{DateTime.Now.ToString("yyyy.mm.dd hh:mm:ss")}");
            var registerList = _recordRepository.ReadCombResult(register.MinRegTime, register.MaxRegTime)//_registerRepository.ReadRegister()
                .Where(reg => SqlFunc.Between(reg.RegisterTime, register.MinRegTime, register.MaxRegTime))
                .Where(reg => reg.PeStatus < PeStatus.已审核)
                .Where(reg => reg.IsActive == true)
                .WhereIF(query.PeType == 1, reg => !reg.IsCompanyCheck)
                .WhereIF(query.PeType == 2, reg => reg.IsCompanyCheck)
                .WhereIF(Enum.IsDefined(typeof(PeCls), query.PeCls), reg => reg.PeCls == query.PeCls)
                .WhereIF(!string.IsNullOrEmpty(query.CompanyCode), reg => reg.CompanyCode == query.CompanyCode)
                .WhereIF(!string.IsNullOrEmpty(query.ExtralParm), (reg, comb) => reg.RegNo == query.ExtralParm || reg.Name.Contains(query.ExtralParm) || comb.CombName.Contains(query.ExtralParm))
                .Select(reg => new PeNoReportData
                {
                    RegNo               = reg.RegNo,
                    Name                = reg.Name,
                    Sex                 = reg.Sex,
                    Age                 = reg.Age,
                    Tel                 = reg.Tel,
                    ActiveTime          = reg.ActiveTime,
                    PeCls               = reg.PeCls,
                    IsVIP               = reg.IsVIP,
                    CompanyCode         = reg.CompanyCode,
                    IsGuidanceRecycly   = reg.GuidanceRecyclyTime == null ? false : true,
                    GuidanceRecyclyTime = reg.GuidanceRecyclyTime,
                    PeStatus            = SqlFunc.IF(reg.PeStatus == PeStatus.未检查).Return(0).ElseIF(reg.PeStatus == PeStatus.正在检查).Return(1).End(2),
                    IsCheck             = reg.PeStatus == PeStatus.已总检 || reg.PeStatus == PeStatus.已审核 ? true : false,
                    IsComfirm           = reg.PeStatus == PeStatus.已审核 ? true : false
                }).Distinct().ToList();

            var regNoList = registerList.Select(r => r.RegNo).ToArray();

            var combList = _recordRepository.ReadCombResult(register.MinRegTime, register.MaxRegTime)
                .Where(reg => regNoList.Contains(reg.RegNo))
                .Select((reg, comb, record) => new NoCheckComb
                {
                    RegNo     = reg.RegNo,
                    CombCode  = comb.CombCode,
                    CombName  = comb.CombName,
                    RegCombId = comb.Id
                }).ToArray();

            var recordContactList = _recordContactRepository.FindAll(r => regNoList.Contains(r.RegNo));
            var replyDcotorList = _questionToDoctorRepository.FindAll(r => regNoList.Contains(r.RegNo));

            var reportlist = _reportConclusionRepository.GetReportConclusion(regNoList)
                .Select((reg, report) => new { RegNo = reg.RegNo, CheckDoctorName = report.CheckDoctorName }).ToArray();

            foreach (var listItem in registerList)
            {
                listItem.NoCheckCombList = string.Join(",", combList.Where(n => n.RegNo == listItem.RegNo).Select(n => n.CombName).ToArray());
                listItem.RecordContactList = string.Join(",", recordContactList.Where(n => n.RegNo == listItem.RegNo).Select(n => n.CreateTime + " " + n.ContactPerson + " " + n.Content).ToArray());
                listItem.CheckDcotor = reportlist.SingleOrDefault(r => r.RegNo == listItem.RegNo, new { RegNo = listItem.RegNo, CheckDoctorName = "-" }).CheckDoctorName;
                listItem.ReplyDcotor = string.Join(",", replyDcotorList.Where(n => n.RegNo == listItem.RegNo).Select(n => n.ReplyerName).ToArray()); ;
            }
            Console.WriteLine($"结束{DateTime.Now.ToString("yyyy.mm.dd hh:mm:ss")}");
            list = registerList;
            return true;
        }
        #endregion

        #region 科研项目分析
        /// <summary>
        /// 科研项目分析
        /// </summary>
        /// <param name="query"></param>
        /// <param name="data"></param>
        /// <param name="msg"></param>
        /// <returns></returns>
        public bool ResearchAnalysis(ResearchAnalysisQuery query, ref ResearchAnalysisData data, ref string msg)
        {
            if (query.ExpList.Count == 0)
            {
                msg = "请选择需要分析的项目";
                return false;
            }

            #region 如果是体检时间，需要处理
            query.EndDate = query.EndDate.Add(new TimeSpan(23,59,59));
            // 如果是体检时间,则查出登记时间用于分表查询
            if (query.TimeType == TimeType.体检时间)
            {
                var regDate = _registerRepository.ReadRegister()
                   .Where(x => SqlFunc.Between(x.ActiveTime, query.BeinDate, query.EndDate))
                   .Select(x => new
                   {
                       minTime = SqlFunc.AggregateMin(x.RegisterTime),
                       maxTime = SqlFunc.AggregateMax(x.RegisterTime)
                   }).First();

                query.BeinDate = regDate.minTime;
                query.EndDate = regDate.maxTime;
            }
            #endregion

            var results = Array.Empty<ResearchAnalysis>();
            var expDic = query.ExpList.ToDictionary(x => x.Code, x => x.Name);
            // 处理动态表达式
            string expText = HandleExp(query.QueryType, query.Operator, query.ExpList);
            ISugarQueryable<PeRegister, PeRecordComb, PeRecordItem> queryable = null;

            queryable = _recordRepository.ReadItem(query.BeinDate, query.EndDate)
                  .Where(reg => reg.IsActive && reg.PeStatus != PeStatus.未检查)
                  .WhereIF(query.TimeType == TimeType.登记时间, reg => SqlFunc.Between(reg.RegisterTime, query.BeinDate, query.EndDate))
                  .WhereIF(query.TimeType == TimeType.体检时间, reg => SqlFunc.Between(reg.ActiveTime, query.BeinDate, query.EndDate))
                  .WhereIF(!string.IsNullOrEmpty(query.RegNo), reg => reg.RegNo == query.RegNo)
                  .WhereIF(!string.IsNullOrEmpty(query.PatCode), reg => reg.PatCode == query.PatCode)
                  .WhereIF(!string.IsNullOrEmpty(query.Name), reg => reg.Name == query.Name)
                  .WhereIF(!string.IsNullOrEmpty(query.CardNo), reg => reg.CardNo == query.CardNo)
                  .WhereIF(Enum.IsDefined(typeof(Sex), query.Sex), reg => reg.Sex == query.Sex)
                  .WhereIF(query.PersonCompany == PersonCompany.个人, reg => !reg.IsCompanyCheck)
                  .WhereIF(query.PersonCompany == PersonCompany.团体 && string.IsNullOrEmpty(query.CompanyCode), reg => reg.IsCompanyCheck)
                  .WhereIF(query.PersonCompany == PersonCompany.团体 && !string.IsNullOrEmpty(query.CompanyCode),
                           reg => reg.IsCompanyCheck && reg.CompanyCode == query.CompanyCode)
                  .WhereIF(Enum.IsDefined(typeof(PeCls), query.PeCls), reg => reg.Sex == query.Sex);

            // 根据组合查询
            if (query.QueryType == 1)
            {
                var combData = queryable
                      .WhereIF(!string.IsNullOrEmpty(expText), expText)
                      .Select((reg, recComb, recItem) => new
                      {
                          CompanyCode = reg.CompanyCode,
                          CompanyDeptCode = reg.CompanyDeptCode,
                          RegNo = reg.RegNo,
                          Name = reg.Name,
                          Sex = reg.Sex,
                          Age = reg.Age,
                          Tel = reg.Tel,
                          CardNo = reg.CardNo,
                          ActiveTime = reg.ActiveTime,
                          DoctorName = recComb.DoctorName,
                          DynamicColumn = recComb.CombCode,
                          DynamicColumnTag = recComb.CombResult
                      }).ToArray();

                // 获取缓存数据
                var companyDict = _cacheRepository.DictCompany();
                var deptDict = _cacheRepository.DictCompanyDepartment();

                results = combData.Select(x => new ResearchAnalysis
                {
                    CompanyName = companyDict.TryGetValue(x.CompanyCode ?? "", out var company) ? company.CompanyName : "",
                    DeptName = deptDict.TryGetValue(x.CompanyDeptCode ?? "", out var dept) ? dept.DeptName : "",
                    RegNo = x.RegNo,
                    Name = x.Name,
                    Sex = x.Sex,
                    Age = x.Age,
                    Tel = x.Tel,
                    CardNo = x.CardNo,
                    ActiveDate = x.ActiveTime?.ToString("yyyy.MM.dd") ?? "",
                    DoctorName = x.DoctorName,
                    DynamicColumn = x.DynamicColumn,
                    DynamicColumnTag = x.DynamicColumnTag
                }).ToArray();
            }
            // 根据项目查询
            else
            {
                var itemData = queryable
                          .WhereIF(!string.IsNullOrEmpty(expText), expText)
                          .Select((reg, recComb, recItem) => new
                          {
                              CompanyCode = reg.CompanyCode,
                              CompanyDeptCode = reg.CompanyDeptCode,
                              RegNo = reg.RegNo,
                              Name = reg.Name,
                              Sex = reg.Sex,
                              Age = reg.Age,
                              Tel = reg.Tel,
                              CardNo = reg.CardNo,
                              ActiveTime = reg.ActiveTime,
                              DoctorName = recComb.DoctorName,
                              DynamicColumn = recItem.ItemCode,
                              DynamicColumnTag = recItem.ItemResult
                          }).ToArray();

                // 获取缓存数据
                var companyDict = _cacheRepository.DictCompany();
                var deptDict = _cacheRepository.DictCompanyDepartment();

                results = itemData.Select(x => new ResearchAnalysis
                {
                    CompanyName = companyDict.TryGetValue(x.CompanyCode ?? "", out var company) ? company.CompanyName : "",
                    DeptName = deptDict.TryGetValue(x.CompanyDeptCode ?? "", out var dept) ? dept.DeptName : "",
                    RegNo = x.RegNo,
                    Name = x.Name,
                    Sex = x.Sex,
                    Age = x.Age,
                    Tel = x.Tel,
                    CardNo = x.CardNo,
                    ActiveDate = x.ActiveTime?.ToString("yyyy.MM.dd") ?? "",
                    DoctorName = x.DoctorName,
                    DynamicColumn = x.DynamicColumn,
                    DynamicColumnTag = x.DynamicColumnTag
                }).ToArray();
            }

            data.Header = CreateHeader(results);
            data.BodyData = CreateData(results);
            
            #region 内部方法组装数据
            Dictionary<string, string> CreateHeader(ResearchAnalysis[] results)
            {
                var header = new Dictionary<string, string>();
                var codeCombQuery = results.DistinctBy(x => x.DynamicColumn)
                                .ToDictionary(x => x.DynamicColumn, x => expDic[x.DynamicColumn]);

                foreach (var comb in results.GroupBy(x => x.DynamicColumn))
                    header.Add(comb.Key, codeCombQuery[comb.Key]);

                return header;
            }

            List<Dictionary<string, string>> CreateData(ResearchAnalysis[] results)
            {
                var data = new List<Dictionary<string, string>>();

                foreach (var reg in results.GroupBy(x => x.RegNo))
                {
                    var dynamicColumns = reg.DistinctBy(x => x.DynamicColumn);
                    if (query.Operator == "and" && dynamicColumns.Count() < query.ExpList.Count)
                        continue;

                    var regInfo = reg.FirstOrDefault();
                    var regData = new Dictionary<string, string>
                    {
                        { "companyName", regInfo.CompanyName },
                        { "deptName", regInfo.DeptName },
                        { "regNo", regInfo.RegNo },
                        { "name", regInfo.Name },
                        { "sex",  regInfo.Sex.ToString() },
                        { "age",  regInfo.Age.ToString() },
                        { "tel", regInfo.Tel },
                        { "cardNo", regInfo.CardNo },
                        { "activeDate",  regInfo.ActiveDate },
                        { "doctorName",  regInfo.DoctorName }
                    };

                    foreach (var comb in dynamicColumns)
                        regData.Add(comb.DynamicColumn, comb.DynamicColumnTag);
                    
                    data.Add(regData);
                }

                return data;
            }
            #endregion

            return true;
        }
        #endregion

        #region 检查项目明细报表
        public ExamItemEntryReport ReadExamItemEntryReport(ExamItemEntryQuery query,ref int totalNumber,ref int totalPage)
        {
            var report = new ExamItemEntryReport();
            var register = _registerRepository.ReadRegister()
                .Where(x => SqlFunc.Between(x.ActiveTime, query.BeginTime, query.EndTime))
                .Select(x => new RegisterDateRange
                {
                    MinRegTime = SqlFunc.AggregateMin(x.RegisterTime),
                    MaxRegTime = SqlFunc.AggregateMax(x.RegisterTime),
                }).First();
            if (!register.IsValidDate)
            {
                return new();
            }
            var combQueryable = _splitTable.GetTableOrDefault<PeRegisterComb>(register.MinRegTime, register.MaxRegTime);

            var mainQuery =
             _registerRepository.ReadRegister()
            .InnerJoin(combQueryable, (x, y) => x.RegNo == y.RegNo)
            .WhereIF(!query.RegNo.IsNullOrEmpty(), x => x.RegNo == query.RegNo)
            .WhereIF(!query.PatCode.IsNullOrEmpty(), x => x.PatCode == query.PatCode)
            .WhereIF(Enum.IsDefined<Sex>(query.Sex), x => x.Sex == query.Sex)
            .WhereIF(!query.CardNo.IsNullOrEmpty(), x => x.CardNo == query.CardNo)
            .WhereIF(!query.Name.IsNullOrEmpty(), x => x.Name.Contains(query.Name))
            .WhereIF(query.PersonCompany == PersonCompany.团体, x => x.IsCompanyCheck == true)
            .WhereIF(query.PersonCompany == PersonCompany.个人, x => x.IsCompanyCheck == false)
            .WhereIF(!query.CompanyCode.IsNullOrEmpty(), x => x.CompanyCode == query.CompanyCode)
            .WhereIF(!query.ClusterCode.IsNullOrEmpty(), x => SqlFunc.Subqueryable<PeRegisterCluster>().Where(z => z.RegNo == x.RegNo && z.ClusCode == query.ClusterCode).Any())
            .WhereIF(Enum.IsDefined<PeCls>(query.PeCls), x => x.PeCls == query.PeCls)
            .WhereIF(Enum.IsDefined<CheckCls>(query.CheckCls), (x, y) => y.CheckCls == query.CheckCls)
            .WhereIF(query.CompanyTimes > 0, (x, y) => x.CompanyTimes == query.CompanyTimes)
            .Where(x => SqlFunc.Between(x.ActiveTime, query.BeginTime, query.EndTime))
            .Where((x, y) => y.ReportShow == true)
            .OrderBy(x => x.RegNo)
            .Distinct();

            var summaryList = mainQuery.Clone().Select(x => new
            {
                x.RegNo,
                x.IsActive,
                x.PeStatus,
                x.ReportPrinted
            }).ToList();

            var dataQuery = mainQuery
            .Select(x => new ExamItemEntry {
                RegNo = x.RegNo,
                Name = x.Name,
                Sex = x.Sex,
                Age = x.Age,
                CardNo = x.CardNo,
                PeCls = x.PeCls,
                BookType = x.BookType,
                PeStatus = x.PeStatus,
                ClusterName = SqlFunc.Subqueryable<PeRegisterCluster>()
                  .Where(y => y.RegNo == x.RegNo)
                  .SelectStringJoin(y => y.ClusName, ",")
            });

            if (query.PageSize > 0)
            {
                report.ExamItemEntryReports = dataQuery.ToPageList(query.PageNumber, query.PageSize);
                totalPage = (int)Math.Ceiling(totalNumber * 1.0 / query.PageSize);
            }
            else
            {
                report.ExamItemEntryReports = dataQuery.ToList();
                totalPage = 1;
            }

            var regNos = report.ExamItemEntryReports.Select(x => x.RegNo).ToArray();
            var combs = _registerRepository.ReadRegisterCombs(regNos)
                .WhereIF(Enum.IsDefined<CheckCls>(query.CheckCls), x => x.CheckCls == query.CheckCls)
                .Where(x => SqlFunc.ContainsArray(regNos, x.RegNo))
                .Where(x => x.ReportShow == true)
                .Where(x => (x.IsPayBySelf == true && x.PayStatus == PayStatus.收费) || x.IsPayBySelf == false)
                .Select(x => new { x.RegNo, x.CombName }).ToList();
            report.ExamItemEntryReports.BatchUpdate(x => x.RegisterCombs = string.Join(",", combs.Where(y => y.RegNo == x.RegNo).Select(y => y.CombName)));
            report.RecordStatus = new RecordStatus
            {
                TotalCount = summaryList.Count,
                InActiveCount = summaryList.Count(x => !x.IsActive),
                UnCheckedCount = summaryList.Count(x => x.PeStatus == PeStatus.未检查),
                IsCheckingCount = summaryList.Count(x => x.PeStatus == PeStatus.正在检查),
                CheckedCount = summaryList.Count(x => x.PeStatus == PeStatus.已检完),
                ApprovedCount = summaryList.Count(x => x.PeStatus == PeStatus.已审核),
                IssuedReportCount = summaryList.Count(x => x.ReportPrinted)
            };
            totalNumber = summaryList.Count;
            return report;
        }
        #endregion

        #region 私有方法
        /// <summary>
        /// 获取体检报告进程查询，进程分析和列表查询通用
        /// </summary>
        /// <param name="query"></param>
        /// <param name="msg"></param>
        /// <returns></returns>
        private ISugarQueryable<PeRegister, PeRecordComb, PeReportConclusion, MapItemComb> ReadPeReportProcess(PeReportProcessQuery query, ref string msg)
        {
            if (!Enum.IsDefined(typeof(ProcessType), query.BeginProcess))
            {
                msg = "请选择起始进程!";
                return null;
            }
            if (!Enum.IsDefined(typeof(ProcessType), query.BeginProcess))
            {
                msg = "请选择结束进程!";
                return null;
            }

            query.BeginDate = query.BeginDate.Date;
            query.EndDate   = query.EndDate.Date.Add(new TimeSpan(23, 59, 59));

            var register = _registerRepository.ReadRegister()
                .WhereIF(!query.Keyword.IsNullOrEmpty(), x => x.RegNo.Contains(query.Keyword) || x.Name.Contains(query.Keyword))
                .Where(x => SqlFunc.Between(x.ActiveTime, query.BeginDate, query.EndDate))
                .Select(x => new RegisterDateRange
                {
                    MinRegTime = SqlFunc.AggregateMin(x.RegisterTime),
                    MaxRegTime = SqlFunc.AggregateMax(x.RegisterTime),
                }).First();

            if (!register.IsValidDate)
            {
                msg = "暂无数据,请选择其他时间查询!";
                return null;
            }

            return _reportConclusionRepository.ReadReportCombItemData(register.MinRegTime, register.MaxRegTime)
                .Where(reg => SqlFunc.Between(reg.ActiveTime, query.BeginDate, query.EndDate))
                .GroupBy((reg, regComb, report) => new { reg.RegNo, reg.Name, reg.Sex, reg.Age, reg.CardNo, report.AuditDoctorCode, reg.PeStatus })
                .WhereIF(!query.Keyword.IsNullOrEmpty(), reg => reg.RegNo.Contains(query.Keyword) || reg.Name.Contains(query.Keyword))
                .Where(reg => reg.PeStatus == PeStatus.已审核)
                .WhereIF(query.PeType == 1, reg => !reg.IsCompanyCheck)
                .WhereIF(query.PeType == 2, reg => reg.IsCompanyCheck)
                .WhereIF(query.CombItemType == 0 && query.CombItemArray.Length > 0, (reg, regComb) => query.CombItemArray.Contains(regComb.CombCode))
                .WhereIF(query.CombItemType == 1 && query.CombItemArray.Length > 0, (reg, regComb, report, itemComb) => query.CombItemArray.Contains(itemComb.ItemCode))
                .WhereIF(!string.IsNullOrEmpty(query.CompanyCode), reg => reg.CompanyCode == query.CompanyCode)
                .WhereIF(query.IsVIP != null, reg => reg.IsVIP == query.IsVIP)
                .WhereIF(Enum.IsDefined(typeof(PeCls), query.PeCls), reg => reg.PeCls == query.PeCls)
                //.WhereIF(query.OutsideUploaded != null, reg => reg.OutsideUploaded == query.OutsideUploaded)
                .Where(reg => SqlFunc.Between(reg.ActiveTime, register.MinRegTime, register.MaxRegTime));
        }

        /// <summary>
        /// 处理表达式
        /// </summary>
        /// <param name="queryType">查询类型(1:组合 2:项目)</param>
        /// <param name="oper">操作符(1:或 2:且)</param>
        /// <param name="expList"></param>
        /// <returns></returns>
        private static string HandleExp(int queryType, string oper, List<ExpList> expList)
        {
            StringBuilder expStr = new(); // 使用StringBuilder来组合字符串

            if (expList.Count == 0)
                return expStr.ToString();

            string column = queryType == 1 ? "recComb.CombCode" : "recItem.ItemCode";      // 根据queryType确定列
            string result = queryType == 1 ? "recComb.CombResult" : "recItem.ItemResult";  // 根据queryType确定查组合结果还是项目结果

            var operatorArray = new string[] {"=","<>","like" };// 操作符不在数组里面则是查数值类型的结果
            foreach (var data in expList)
            {
                if (operatorArray.Contains(data.Operator))
                {
                    if (data.Operator == "like")
                        expStr.AppendFormat("or ({0}='{1}' and {2} {3} '%{4}%')", column, data.Code, result, data.Operator, data.CodeValue);
                    else
                        expStr.AppendFormat("or ({0}='{1}' and {2} {3} '{4}')", column, data.Code, result, data.Operator, data.CodeValue);

                    continue;
                }

                if (double.TryParse(data.CodeValue, out double value))
                    expStr.AppendFormat("or ({0}='{1}' and TRY_CONVERT(FLOAT,{2}) {3}{4})", column, data.Code, result, data.Operator, value);
            }

            return expStr.Length > 0 ? expStr.ToString().Substring(oper.Length) : expStr.ToString();
        }
        #endregion
    }
}
