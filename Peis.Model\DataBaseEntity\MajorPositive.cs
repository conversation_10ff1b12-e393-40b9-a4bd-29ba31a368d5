﻿using Peis.Model.DTO.DiseaseMgt;
using Peis.Model.Other.PeEnum;

namespace Peis.Model.DataBaseEntity;

///<summary>
///重大阳性列表
///</summary>
[SugarTable("MajorPositive")]
public class MajorPositive
{
    /// <summary>
    /// 重大阳性代码
    /// </summary>        
    [SugarColumn(IsNullable = false, IsPrimaryKey = true, Length = 8)]
    public string PositiveCode { get; set; }

    /// <summary>
    /// 重大阳性名称
    /// </summary>        
    [SugarColumn(IsNullable = false, Length = 200)]
    public string PositiveName { get; set; }

    /// <summary>
    /// 重大阳性类型 1:A类 2:B类
    /// </summary>        
    [SugarColumn(IsNullable = false)]
    public PositiveType PositiveType { get; set; }

    /// <summary>
    /// 检查类型
    /// </summary>        
    [SugarColumn(IsNullable = true)]
    public MajorCheckType? CheckType { get; set; }

    #region Ext

    /// <summary>
    /// 检查类型名称
    /// </summary>        
    [SugarColumn(IsIgnore = true)]
    public string CheckTypeName  => CheckType.HasValue ? CheckType.ToString() : string.Empty;

    /// <summary>
    /// 疾病-项目分类代码
    /// </summary>        
    [SugarColumn(IsIgnore = true)]
    public string ClsCode { get; set; }

    /// <summary>
    /// 疾病-项目分类名称
    /// </summary>        
    [SugarColumn(IsIgnore = true)]
    public string ClsName { get; set; }

    /// <summary>
    /// 疾病-建议内容
    /// </summary>        
    [SugarColumn(IsIgnore = true)]
    public string SuggestContent { get; set; }

    /// <summary>
    /// 疾病-编码
    /// </summary>        
    [SugarColumn(IsIgnore = true)]
    public string DiseaseCode { get; set; }

    /// <summary>
    /// 疾病-名称
    /// </summary>        
    [SugarColumn(IsIgnore = true)]
    public string DiseaseName { get; set; }
    #endregion
}