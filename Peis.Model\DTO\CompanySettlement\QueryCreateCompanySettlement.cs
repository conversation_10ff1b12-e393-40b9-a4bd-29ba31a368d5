﻿namespace Peis.Model.DTO.CompanySettlement
{
    public class QueryCreateCompanySettlement
    {
        /// <summary>
        /// 单位代码
        /// </summary>
        public string CompanyCode { set; get; }
        /// <summary>
        /// 单位次数
        /// </summary>
        public int CompanyTimes { set; get; }
        /// <summary>
        /// 统计类型
        /// </summary>
        public StatisticType Type { set; get; }
        /// <summary>
        /// 发票抬头
        /// </summary>
        public string InvoiceName { set; get; }
        /// <summary>
        /// 体检号列表
        /// </summary>
        public string[] regNos { set; get; }

        /// <summary>
        /// 时间范围 格式：yyyy.MM.dd-yyyy.MM.dd
        /// </summary>
        public string ChargeDateScope { set; get; }
    }
}
