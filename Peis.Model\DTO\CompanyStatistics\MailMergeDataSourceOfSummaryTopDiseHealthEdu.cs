﻿using Aspose.Words.MailMerging;

namespace Peis.Model.DTO.CompanyStatistics;

/// <summary>
/// Aspose.Words-自定义邮件合并数据源-前几项异常项目科普与健康教育
/// </summary>
public class MailMergeDataSourceOfSummaryTopDiseHealthEdu : IMailMergeDataSource
{
    private readonly List<CompanySumTopDiseHealthEdu> _customers;
    private int _currentIndex;

    public MailMergeDataSourceOfSummaryTopDiseHealthEdu(List<CompanySumTopDiseHealthEdu> customers)
    {
        _customers = customers;
        _currentIndex = -1;
    }

    public string TableName => "SummaryTopDiseHealthEduList";

    public bool GetValue(string fieldName, out object fieldValue)
    {
        var customer = _customers[_currentIndex];
        var prop = customer.GetType().GetProperty(fieldName);

        if (prop != null)
        {
            fieldValue = prop.GetValue(customer);
            return true;
        }

        fieldValue = default;
        return default;
    }

    public bool MoveNext()
    {
        _currentIndex++;
        return _currentIndex < (_customers?.Count ?? 0);
    }

    public IMailMergeDataSource GetChildDataSource(string tableName)
    {
        return default;
    }
}