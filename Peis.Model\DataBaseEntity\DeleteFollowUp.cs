﻿using Peis.Model.Other.PeEnum;

namespace Peis.Model.DataBaseEntity
{
    ///<summary>
    ///随访登记删除备份表
    ///</summary>
    [SugarTable("DeleteFollowUp")]
    [SugarIndex("index_RegNo_DeleteFollowUp", nameof(RegNo), OrderByType.Asc)]
    public class DeleteFollowUp
    {
        /// <summary>
        /// 雪花Id
        /// </summary>        
        [SugarColumn(IsNullable = false, IsPrimaryKey = true)]
        public long Id { get; set; }

        /// <summary>
        /// 体检号
        /// </summary>        
        [SugarColumn(IsNullable = false, Length = 12)]
        public string RegNo { get; set; }

        /// <summary>
        /// 诊断
        /// </summary>        
        [SugarColumn(IsNullable = true, Length = 255)]
        public string Diagnosis { get; set; }

        /// <summary>
        /// 发现的问题
        /// </summary>        
        [SugarColumn(IsNullable = false, ColumnDataType = StaticConfig.CodeFirst_BigString)]
        public string FindProblems { get; set; }

        /// <summary>
        /// 发现问题的科室
        /// </summary>        
        [SugarColumn(IsNullable = true, Length = 10)]
        public string FindProblemsDept { get; set; }

        /// <summary>
        /// 通知人
        /// </summary>        
        [SugarColumn(IsNullable = true, Length = 10)]
        public string Notifier { get; set; }

        /// <summary>
        /// 被通知人
        /// </summary>        
        [SugarColumn(IsNullable = true, Length = 10)]
        public string NotifiedPerson { get; set; }

        /// <summary>
        /// 后续随访情况
        /// </summary>        
        [SugarColumn(IsNullable = true, Length = 255)]
        public string AfterFollowUp { get; set; }

        /// <summary>
        /// 二次随访员 
        /// </summary>        
        [SugarColumn(IsNullable = true, Length = 10)]
        public string SecondaryNotifier { get; set; }

        /// <summary>
        /// 二次随访情况
        /// </summary>        
        [SugarColumn(IsNullable = true, Length = 255)]
        public string SecondaryAfterFollowUp { get; set; }

        /// <summary>
        /// 记录人
        /// </summary>        
        [SugarColumn(IsNullable = true, Length = 10)]
        public string Recorder { get; set; }
        /// <summary>
        /// 创建时间
        /// </summary>        
        [SugarColumn(IsNullable = false)]
        public DateTime CreateTime { get; set; }
        /// <summary>
        /// 删除时间
        /// </summary>        
        [SugarColumn(IsNullable = false)]
        public DateTime DeleteTime { get; set; }
        /// <summary>
        /// 随访来源
        /// </summary>
        [SugarColumn(IsNullable = false)]
        public FollowUpSource Source { get; set; }
        /// <summary>
        /// 是否需要后续随访
        /// </summary>
        [SugarColumn(IsNullable = false)]
        public bool NeedFinallyNotify { get; set; }
        /// <summary>
        /// 后续随访员 
        /// </summary>        
        [SugarColumn(IsNullable = true, Length = 10)]
        public string FinallyNotifier { get; set; }

        /// <summary>
        /// 后续随访情况
        /// </summary>        
        [SugarColumn(IsNullable = true, Length = 512)]
        public string FinallyAfterFollowUp { get; set; }
        /// <summary>
        /// 随访时间
        /// </summary>        
        public DateTime? AfterFollowUpTime { get; set; }
        /// <summary>
        /// 二次随访时间
        /// </summary>        
        public DateTime? SecondaryAfterFollowUpTime { get; set; }
    }
}
