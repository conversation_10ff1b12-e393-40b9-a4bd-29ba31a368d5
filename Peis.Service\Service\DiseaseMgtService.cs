﻿using Peis.Model.DTO;
using Peis.Model.DTO.DiseaseMgt;
using Peis.Model.DTO.DoctorStation;
using Peis.Model.DTO.ReportConclusionNew;
using Peis.Model.Other.Input.DiseaseMgt;
using Peis.Model.Other.PeEnum;
using Peis.Repository.IRepository;
using Peis.Service.IService;
using Peis.Service.IService.Helper;

namespace Peis.Service.Service;

/// <summary>
/// 疾病管理
/// </summary>
public class DiseaseMgtService : IDiseaseMgtService
{
    #region 仓储 
    private readonly IDataTranRepository _dataTranRepository;
    private readonly IDiseaseMgtRepository _diseaseMgtRepository;
    private readonly IReportConclusionNewRepository _reportConclusionRepository;
    private readonly ICacheRepository _cacheRepository;
    private readonly IDataRepository<CodeDiseaseCls> _codeDiseaseClsRepository;
    private readonly IDataRepository<MapDiseaseClsDisease> _mapDiseaseClsDiseaseRepository;
    private readonly IDataRepository<MapDiseaseDisease> _mapDiseaseDiseaseRepository;
    private readonly IDataRepository<CodeDisease> _codeDiseaseRepository;
    private readonly IDataRepository<CodeDiseaseEntry> _codeDiseaseEntryRepository;
    private readonly IDataRepository<CodeDiseaseCriteria> _codeDiseaseCriteriaRepository;
    private readonly IDataRepository<CodeDiseaseCriteriaItem> _codeDiseaseCriteriaItemRepository;
    private readonly IDataRepository<CodeDiseaseExpression> _codeDiseaseExpressionRepository;
    private readonly IDataRepository<CodeDiseaseExpressionItem> _codeDiseaseExpressionItemRepository;
    private readonly IDataRepository<CodeDiseaseBackup> _codeDiseaseBackupRepository;
    private readonly IDataRepository<MapDiseaseMajorPositive> _mapDiseaseMajorPositiveRepository;
    private readonly IDataRepository<CodeDiseaseStatAgeRange> _codeDiseaseStatAgeRangeRepository;
    private readonly IDataRepository<MapDiseaseClsComb> _mapDiseaseClsCombRepository;

    private readonly INoGeneration _noGeneration;
    private readonly IMapper _mapper;
    #endregion

    #region 构造函数
    public DiseaseMgtService(
        IDataTranRepository dataTranRepository,
        IDiseaseMgtRepository diseaseMgtRepository,
        IReportConclusionNewRepository reportConclusionNewRepository,
        IDataRepository<CodeDiseaseCls> codeDiseaseClsRepository,
        IDataRepository<MapDiseaseClsDisease> mapDiseaseClsDiseaseRepository,
        IDataRepository<MapDiseaseDisease> mapDiseaseDiseaseRepository,
        IDataRepository<CodeDisease> codeDiseaseRepository,
        IDataRepository<CodeDiseaseEntry> codeDiseaseEntryRepository,
        IDataRepository<CodeDiseaseCriteria> codeDiseaseCriteriaRepository,
        IDataRepository<CodeDiseaseCriteriaItem> codeDiseaseCriteriaItemRepository,
        IDataRepository<CodeDiseaseExpression> codeDiseaseExpressionRepository,
        IDataRepository<CodeDiseaseExpressionItem> codeDiseaseExpressionItemRepository,
        IDataRepository<CodeDiseaseBackup> codeDiseaseBackupRepository,
        IDataRepository<MapDiseaseMajorPositive> mapDiseaseMajorPositiveRepository,

        IMapper mapper,
        IDataRepository<CodeDiseaseStatAgeRange> codeDiseaseStatAgeRangeRepository,
        ICacheRepository cacheRepository,
        IDataRepository<MapDiseaseClsComb> mapDiseaseClsCombRepository,
        INoGeneration noGeneration)
    {
        _dataTranRepository = dataTranRepository;
        _diseaseMgtRepository = diseaseMgtRepository;
        _codeDiseaseClsRepository = codeDiseaseClsRepository;
        _mapDiseaseClsDiseaseRepository = mapDiseaseClsDiseaseRepository;
        _mapDiseaseDiseaseRepository = mapDiseaseDiseaseRepository;
        _codeDiseaseRepository = codeDiseaseRepository;
        _codeDiseaseEntryRepository = codeDiseaseEntryRepository;
        _codeDiseaseCriteriaRepository = codeDiseaseCriteriaRepository;
        _codeDiseaseCriteriaItemRepository = codeDiseaseCriteriaItemRepository;
        _codeDiseaseExpressionRepository = codeDiseaseExpressionRepository;
        _codeDiseaseExpressionItemRepository = codeDiseaseExpressionItemRepository;
        _codeDiseaseBackupRepository = codeDiseaseBackupRepository;
        _reportConclusionRepository = reportConclusionNewRepository;
        _mapDiseaseMajorPositiveRepository = mapDiseaseMajorPositiveRepository;

        _mapper = mapper;
        _codeDiseaseStatAgeRangeRepository = codeDiseaseStatAgeRangeRepository;
        _cacheRepository = cacheRepository;
        _mapDiseaseClsCombRepository = mapDiseaseClsCombRepository;
        _noGeneration = noGeneration;
    }
    #endregion

    #region 获取科室包含的疾病
    public List<DiseaseInDept> GetDiseaseInDept()
    {
        return _diseaseMgtRepository.QueryDeptDisease()
            .Select((a, b) => new
            {
                b.ClsName,
                a.DiseaseCode,
                a.DiseaseName,
                a.DiseaseGrade
            })
            .ToList()
            .GroupBy(a => new { a.ClsName })
            .Select(a => new DiseaseInDept
            {
                ClsName = a.Key.ClsName ?? "(无项目分类)",
                Diseases = a.Select(b =>
                {
                    var major = _cacheRepository.DictMajorPositive().Values.FirstOrDefault(x => x.DiseaseCode == b.DiseaseCode);
                    return new Diseases
                    {
                        DiseaseCode = b.DiseaseCode,
                        DiseaseName = b.DiseaseName,
                        DiseaseGrade = b.DiseaseGrade,
                        PositiveCode = major?.PositiveCode,
                        PositiveName = major?.PositiveName
                    };
                })
                .ToList()
            })
            .ToList();

    }
    #endregion

    #region 疾病信息
    /// <summary>
    /// 新增疾病信息
    /// </summary>
    /// <param name="codeDisease"></param>
    /// <param name="msg"></param>
    /// <returns></returns>
    public bool CreateCodeDisease(CodeDisease codeDisease, ref string msg)
    {
        if (_codeDiseaseRepository.Any(x => x.DiseaseName == codeDisease.DiseaseName))
        {
            msg = $"疾病名称{codeDisease.DiseaseName}已存在";
            return false;
        }

        codeDisease.DiseaseName = codeDisease.DiseaseName.Trim();
        _dataTranRepository.ExecTran(() =>
        {
            codeDisease = _codeDiseaseRepository.Insert(codeDisease);

            if (codeDisease.CodeDiseaseEntry.Count > 0)
            {
                codeDisease.CodeDiseaseEntry.ForEach(x => x.DiseaseCode = codeDisease.DiseaseCode);
                _codeDiseaseEntryRepository.Insert(codeDisease.CodeDiseaseEntry);
            }

            if (codeDisease.CreatedFrom == 1)
            {
                // 更新自定义疾病码
                var diseaseNames = new string[] { codeDisease.DiseaseName };
                diseaseNames = diseaseNames.Union(codeDisease.CodeDiseaseEntry.Select(x => x.EntryText).ToArray()).ToArray();
                _reportConclusionRepository.UpdateReportSuggDiseaseCode(codeDisease.DiseaseCode, diseaseNames);
            }
        });
        CacheHelper.Remove("DiseaseDictionary");
        CacheHelper.Remove("MajorPositiveDictionary");

        return true;
    }

    /// <summary>
    /// 更新疾病信息
    /// </summary>
    /// <param name="codeDisease"></param>
    /// <param name="msg"></param>
    /// <returns></returns>
    public bool UpdateCodeDisease(CodeDisease codeDisease, ref string msg)
    {
        //查出有无此疾病
        if (!_codeDiseaseRepository.Any(codeDisease))
        {
            msg = $"疾病代码{codeDisease.DiseaseCode}不存在";
            return false;
        }

        codeDisease.DiseaseName = codeDisease.DiseaseName.Trim();
        //先查出疾病词条
        var diseaseEntry = _codeDiseaseEntryRepository.FindAll(x => x.DiseaseCode == codeDisease.DiseaseCode).ToList();

        _dataTranRepository.ExecTran(() =>
        {
            //删除词条
            if (diseaseEntry.Count > 0)
                _codeDiseaseEntryRepository.Delete(diseaseEntry);
            //保存词条
            if (codeDisease.CodeDiseaseEntry.Count > 0)
                _codeDiseaseEntryRepository.Insert(codeDisease.CodeDiseaseEntry);

            _codeDiseaseRepository.Update(codeDisease);
        });
        CacheHelper.Remove("DiseaseDictionary");
        CacheHelper.Remove("MajorPositiveDictionary");
        return true;
    }

    /// <summary>
    /// 删除疾病信息
    /// </summary>
    /// <param name="diseaseCode"></param>
    /// <param name="msg"></param>
    /// <returns></returns>
    public bool DeleteCodeDisease(string diseaseCode, ref string msg)
    {
        if (string.IsNullOrEmpty(diseaseCode))
        {
            msg = "疾病代码为空";
            return false;
        }
        //todo:疾病计算式跟项目没删除
        _dataTranRepository.ExecTran(() =>
        {
            //疾病逻辑值
            _codeDiseaseCriteriaRepository.Delete(x => x.DiseaseCode == diseaseCode);
            //疾病审核条件项目
            _codeDiseaseCriteriaItemRepository.Delete(x => x.DiseaseCode == diseaseCode);
            //疾病词条
            _codeDiseaseEntryRepository.Delete(x => x.DiseaseCode == diseaseCode);
            //疾病信息
            _codeDiseaseRepository.Delete(x => x.DiseaseCode == diseaseCode);
            // 对应重大阳性
            _mapDiseaseMajorPositiveRepository.Delete(x => x.DiseaseCode == diseaseCode);
        });
        CacheHelper.Remove("DiseaseDictionary");
        CacheHelper.Remove("MajorPositiveDictionary");
        return true;
    }

    /// <summary>
    /// 获取疾病信息
    /// </summary>
    /// <param name="diseaseCode"></param>
    /// <returns></returns>
    public CodeDisease ReadCodeDisease(string diseaseCode)
    {
        if (string.IsNullOrEmpty(diseaseCode))
        {
            return null;
        }

        var codeDisease = _codeDiseaseRepository.First(x => x.DiseaseCode == diseaseCode);
        if (codeDisease != null)
            codeDisease.CodeDiseaseEntry = _codeDiseaseEntryRepository.FindAll(x => x.DiseaseCode == diseaseCode).ToList();

        var major = _cacheRepository.DictMajorPositive().Values.FirstOrDefault(x => x.DiseaseCode == diseaseCode);
        codeDisease.PositiveCode = major?.PositiveCode;
        codeDisease.PositiveName = major?.PositiveName;

        return codeDisease;
    }

    /// <summary>
    /// 获取疾病集合
    /// </summary>
    /// <param name="query">查询条件</param>
    /// <param name="totalNumber">返回总数</param>
    /// <param name="totalPage">返回总页码</param>
    /// <returns>疾病集合</returns>
    public List<CodeDisease> GetCodeDiseases(CodeDiseaseQuery query, ref int totalNumber, ref int totalPage)
    {
        CheckHelper.NotNullAndEmpty(query, nameof(query));

        return _diseaseMgtRepository.QueryDeptDisease()
            .WhereIF(!query.Keyword.IsNullOrEmpty(), a => SqlFunc.Contains(a.DiseaseCode, query.Keyword)
            || SqlFunc.Contains(a.DiseaseName, query.Keyword))
            .Select((a, b) => new CodeDisease
            {
                DiseaseCode = a.DiseaseCode.SelectAll(),
                ClsName = SqlFunc.IsNull(b.ClsName, "(无项目分类)"),
                PositiveCode = SqlFunc.Subqueryable<MapDiseaseMajorPositive>().Where(sub => sub.DiseaseCode == a.DiseaseCode).Select(sub => sub.DiseaseCode)
            })
            .ToMyPageList(query.PageNumber, query.PageSize, ref totalNumber, ref totalPage);
    }

    /// <summary>
    /// 批量疾病词条转所属疾病词条
    /// </summary>
    /// <param name="codeDiseaseEntries">词条内容集合</param>
    /// <param name="msg">消息</param>
    /// <returns>bool</returns>
    public bool BatchCodeDiseasesToEntries(List<CodeDiseaseEntryDto> codeDiseaseEntries, out string msg)
    {
        if (CheckHelper.IsNullOrEmpty(codeDiseaseEntries, nameof(codeDiseaseEntries), out msg))
            return false;

        var diseaseCodes = codeDiseaseEntries.Select(x => x.DiseaseCode);
        var diseaseCodeFroms = codeDiseaseEntries.Select(x => x.DiseaseCodeFrom);
        var diseaseCodeAlls = diseaseCodes.Concat(diseaseCodeFroms).Distinct().ToList();
        var diseases = _diseaseMgtRepository.QueryDiseases()
            .Where(a => diseaseCodeAlls.Contains(a.DiseaseCode)).ToList();
        if (diseases.IsNullOrEmpty())
        {
            msg = "暂未找到疾病信息！";
            return false;
        }

        var diseaseBackups = new List<CodeDiseaseBackup>();
        // 保存词条 
        foreach (var gItem in codeDiseaseEntries.GroupBy(x => x.DiseaseCode))
        {
            var diseaseEntries = new List<CodeDiseaseEntry>();
            foreach (var item in gItem)
            {
                if (item.EntryText.IsNullOrEmpty()) continue;
                if (item.DiseaseCode.Equals(item.DiseaseCodeFrom)) continue;

                var disease = diseases.FirstOrDefault(x => x.DiseaseCode.Equals(item.DiseaseCode));
                if (disease.IsNullOrEmpty()) continue;

                if (!diseaseEntries.Any(x => x.EntryText.Equals(item.EntryText)))
                    diseaseEntries.Add(_mapper.Map<CodeDiseaseEntry>(item));

                // 无转记录，跳过，无需处理
                var diseaseFrom = diseases.FirstOrDefault(x => x.DiseaseCode.Equals(item.DiseaseCodeFrom));
                if (diseaseFrom.IsNullOrEmpty()) continue;

                if (!diseaseBackups.Any(x => x.DiseaseCode.Equals(item.DiseaseCodeFrom)))
                {
                    var diseaseBackup = _mapper.Map<CodeDiseaseBackup>(diseaseFrom);
                    diseaseBackup.DiseaseCodeTo = item.DiseaseCode;
                    diseaseBackups.Add(diseaseBackup);
                }
            }

            var extDiseaseEntries = _codeDiseaseEntryRepository.FindAll(x => x.DiseaseCode == gItem.Key).ToList();

            // 删除原有词条
            if (extDiseaseEntries.Count > 0)
                _codeDiseaseEntryRepository.Delete(extDiseaseEntries);

            // 保存词条
            if (diseaseEntries.Count > 0)
                _codeDiseaseEntryRepository.Insert(diseaseEntries);
        }

        if (diseaseBackups.Count > 0)
        {
            // 备份原有疾病
            _codeDiseaseBackupRepository.Delete(diseaseBackups);
            _codeDiseaseBackupRepository.Insert(diseaseBackups);
            // 删除疾病
            _codeDiseaseRepository.Delete(_mapper.Map<List<CodeDisease>>(diseaseBackups));
            // 更新至报告疾病
            foreach (var item in diseaseBackups)
            {
                var disease = codeDiseaseEntries.FirstOrDefault(x => x.DiseaseCodeFrom.Equals(item.DiseaseCode));
                if (disease == null) continue;

                _reportConclusionRepository.UpdateReportSuggDiseaseCode(disease.DiseaseCode, item.DiseaseCode);
            }
        }

        msg = ResxCommon.Success;
        return true;

    }

    /// <summary>
    /// 汇总统计自定义疾病
    /// </summary>
    /// <param name="query">查询</param>
    /// <returns></returns>
    /// <exception cref="BusinessException"></exception>
    public List<DiseaseSumDto> GetCustomDiseaseSummaries(DiseaseSumQuery query)
    {
        query.NotNullAndEmpty(nameof(query));

        return _reportConclusionRepository.QueryReportSuggDisease(query.StartTime, query.EndTime, PeStatus.已审核)
            .Where(a => a.DiseaseCode == string.Empty)
            .WhereIF(!query.Keyword.IsNullOrEmpty(), a => a.DiseaseTag.Contains(query.Keyword))
            .GroupBy(a => a.DiseaseTag)
            .Select(a => new DiseaseSumDto
            {
                DiseaseName = SqlFunc.AggregateMax(a.DiseaseTag),
                TotalNumber = SqlFunc.AggregateCount(a.DiseaseTag),
            })
            .ToArray()
            .OrderByDescending(x => x.TotalNumber)
            .ToList();
    }

    /// <summary>
    /// 获取自定义疾病建议
    /// </summary>
    /// <param name="query">查询</param>
    /// <returns></returns>
    /// <exception cref="BusinessException"></exception>
    public List<DiseaseSuggDto> GetCustomDiseaseSuggestions(DiseaseSuggQuery query)
    {
        query.NotNullAndEmpty(nameof(query));
        query.DiseaseNames.NotNullAndEmpty(nameof(query.DiseaseNames));

        var diseaseNames = query.DiseaseNames.Distinct().ToArray();
        var customDiseases = _reportConclusionRepository.QueryReportSuggDisease(query.StartTime, query.EndTime, PeStatus.已审核)
            .Where(a => a.DiseaseCode == string.Empty)
            .Where(a => SqlFunc.ContainsArray(diseaseNames, a.DiseaseTag))
            .Select(x => new { x.SuggId, x.DiseaseTag })
            .ToArray();

        var suggIds = customDiseases.Select(a => a.SuggId).Distinct().ToArray();
        var suggs = _reportConclusionRepository.QueryReportSuggestion(query.StartTime, query.EndTime, PeStatus.已审核)
            .Where(a => SqlFunc.ContainsArray(suggIds, a.Id))
            .Select(a => new { a.Id, a.Suggestion })
            .ToArray();

        return customDiseases.GroupBy(a => a.DiseaseTag)
            .OrderBy(item => Array.IndexOf(query.DiseaseNames, item.Key))
            .Select(x => new DiseaseSuggDto
            {
                DiseaseName = x.Key,
                Suggestions = suggs.Where(a => !a.Suggestion.IsNullOrEmpty() && x.Select(b => b.SuggId).Contains(a.Id))
                .GroupBy(a => a.Suggestion)
                .Select(a => new
                {
                    Suggestion = a.Key,
                    Count = a.Count()
                })
                .OrderByDescending(a => a.Count)
                .Select(a => a.Suggestion)
                .ToArray()
            })
            .ToList();
    }

    /// <summary>
    /// 设置报告自定义疾病code默认处理
    /// </summary>
    /// <param name="query"></param>
    /// <returns>bool</returns>
    public bool SetCustomDiseaseCodeDefault(DiseaseSumQuery query)
    {
        query.NotNullAndEmpty(nameof(query));
        query.DiseaseNames.NotNullAndEmpty(nameof(query.DiseaseNames));

        return _reportConclusionRepository.UpdateReportSuggDiseaseCode("@", query.DiseaseNames);
    }

    /// <summary>
    /// 通过关键词获取疾病信息
    /// </summary>
    /// <param name="keyword">查询条件(疾病名称，疾病词条，疾病建议)</param>
    /// <returns></returns>
    public Disease[] GetDiseaseInfo(string keyword)
    {
        var data = _diseaseMgtRepository.QueryDiseaseEntry()
            .WhereIF(!string.IsNullOrEmpty(keyword), (disease, diseaseEntry) => disease.DiseaseName.Contains(keyword)
                    || diseaseEntry.EntryText.Contains(keyword))
            .Select((disease, diseaseEntry) => new
            {
                disease.DiseaseCode,
                disease.DiseaseName,
                disease.SuggestContent,
                diseaseEntry.EntryText
            }).ToList();

        return data.GroupBy(x => new { x.DiseaseCode, x.DiseaseName, x.SuggestContent })
            .Select(x => new Disease
            {
                DiseaseCode = x.Key.DiseaseCode,
                DiseaseName = x.Key.DiseaseName,
                SuggestContent = x.Key.SuggestContent,
                DiseaseEntries = x.Where(y => y.EntryText != null).Select(y => y.EntryText).ToArray()
            })
            .ToArray();
    }

    /// <summary>
    /// 根据疾病名获取疾病信息
    /// </summary>
    /// <param name="diseaseName"></param>
    /// <returns></returns>
    public Disease[] GetDiseasesByName(string diseaseName)
    {
        return _diseaseMgtRepository.QueryDiseases()
            .Where(disease => disease.DiseaseName.Contains(diseaseName))
            .Select(disease => new Disease
            {
                DiseaseCode = disease.DiseaseCode,
                DiseaseName = disease.DiseaseName,
                SuggestContent = disease.SuggestContent,
            })
            .ToArray();
    }
    #endregion

    #region 疾病审核条件包含的项目

    #region 疾病逻辑值 CodeDiseaseCriteria
    /// <summary>
    /// 新增疾病逻辑值
    /// </summary>
    /// <param name="deaCriteria"></param>
    /// <param name="msg"></param>
    /// <returns></returns>
    public bool CreateCodeDiseaseCriteria(CodeDiseaseCriteria deaCriteria, ref string msg)
    {
        if (_codeDiseaseCriteriaRepository.Any(deaCriteria))
        {
            msg = $"疾病:'{deaCriteria.DiseaseCode}'的逻辑值已存在";
            return false;
        }

        _codeDiseaseCriteriaRepository.Insert(deaCriteria);
        return true;
    }

    /// <summary>
    /// 更新疾病逻辑值
    /// </summary>
    /// <param name="deaCriteria"></param>
    /// <param name="msg"></param>
    /// <returns></returns>
    public bool UpdateCodeDiseaseCriteria(CodeDiseaseCriteria deaCriteria, ref string msg)
    {
        if (!_codeDiseaseCriteriaRepository.Any(deaCriteria))
        {
            msg = $"疾病:'{deaCriteria.DiseaseCode}'的逻辑值不存在";
            return false;
        }

        return _codeDiseaseCriteriaRepository.Update(deaCriteria);
    }

    /// <summary>
    /// 获取疾病的逻辑值
    /// </summary>
    /// <param name="diseaseCode">疾病代码</param>
    /// <returns></returns>
    public CodeDiseaseCriteria ReadCodeDiseaseCriteria(string diseaseCode)
    {
        return _codeDiseaseCriteriaRepository.First(x => x.DiseaseCode == diseaseCode);
    }
    #endregion

    /// <summary>
    /// 新增疾病审核条件包含的项目
    /// </summary>
    /// <param name="deaCriteriaItem"></param>
    /// <param name="msg"></param>
    /// <returns></returns>
    public bool CreateCodeDiseaseCriteriaItem(CodeDiseaseCriteriaItem deaCriteriaItem, ref string msg)
    {
        if (deaCriteriaItem == null)
        {
            msg = "请勿提交空数据!";
            return false;
        }

        if (_codeDiseaseCriteriaItemRepository.Any(deaCriteriaItem))
        {
            msg = "数据已存在,请勿重复添加";
            return false;
        }

        _codeDiseaseCriteriaItemRepository.Insert(deaCriteriaItem);
        return true;
    }

    /// <summary>
    /// 更新疾病审核条件包含的项目
    /// </summary>
    /// <param name="diseaseCriteriaItem"></param>
    /// <param name="msg"></param>
    /// <returns></returns>
    public bool UpdateCodeDiseaseCriteriaItem(UpdateDiseaseCriteriaItem diseaseCriteriaItem, ref string msg)
    {
        if (diseaseCriteriaItem.OldCriteriaItem == null ||
            diseaseCriteriaItem.NewCriteriaItem == null)
        {
            msg = "请勿提交空数据!";
            return false;
        }

        //先删除旧的项目
        if (diseaseCriteriaItem.OldCriteriaItem != null)
            _codeDiseaseCriteriaItemRepository.Delete(diseaseCriteriaItem.OldCriteriaItem);

        //保存新的项目
        _codeDiseaseCriteriaItemRepository.Insert(diseaseCriteriaItem.NewCriteriaItem);
        return true;
    }

    /// <summary>
    /// 删除疾病审核条件包含的项目
    /// </summary>
    /// <param name="items">项目</param>
    /// <param name="msg"></param>
    /// <returns></returns>
    public bool DeleteCodeDiseaseCriteriaItem(List<CodeDiseaseCriteriaItem> items, ref string msg)
    {
        return _codeDiseaseCriteriaItemRepository.Delete(items);
    }

    /// <summary>
    /// 获取疾病审核条件包含的项目
    /// </summary>
    /// <param name="diseaseCode">疾病代码</param>
    /// <returns></returns>
    public DiseaseCriteriaItemInfo[] ReadCodeDiseaseCriteriaItem(string diseaseCode)
    {
        var itemDict = _cacheRepository.DictCodeItem();

        return _diseaseMgtRepository.QueryDiseaseCriteriaItem(diseaseCode)
            .Select(x => new DiseaseCriteriaItemInfo
            {
                DiseaseCode = x.DiseaseCode.SelectAll(),
                ItemName = itemDict.TryGetValue(x.ItemCode ?? "", out var item) ? item.ItemName : ""
            })
            .ToArray();
    }

    #endregion

    #region 疾病判断计算式

    /// <summary>
    /// 疾病判断计算式列表
    /// </summary>
    /// <returns></returns>
    public object ReadDiseaseExpressionList()
    {
        return _codeDiseaseExpressionRepository.FindAll()
            .Select(x => new
            {
                x.ExpCode,
                x.ExpName
            })
            .ToArray();
    }

    /// <summary>
    /// 新增疾病判断计算式
    /// </summary>
    /// <param name="deaExp"></param>
    /// <param name="msg"></param>
    /// <returns></returns>
    public bool CreateCodeDiseaseExpression(CodeDiseaseExpression deaExp, ref string msg)
    {
        if (string.IsNullOrEmpty(deaExp.ExpText) || deaExp.Items.Count <= 0)
        {
            msg = "项目列表/公式内容不能为空!";
            return false;
        }

        if (_codeDiseaseExpressionRepository.Any(deaExp))
        {
            msg = $"公式{deaExp.ExpCode}已存在,请勿重复添加!";
            return false;
        }

        var deaExpItem = new List<CodeDiseaseExpressionItem>();
        foreach (var item in deaExp.Items)
        {
            deaExpItem.Add(new CodeDiseaseExpressionItem
            {
                ExpCode = deaExp.ExpCode,
                ItemCode = item.ItemCode,
                ValueType = item.ValueType
            });
        }

        _codeDiseaseExpressionItemRepository.Insert(deaExpItem);
        _codeDiseaseExpressionRepository.Insert(deaExp);
        return true;
    }

    /// <summary>
    /// 更新疾病判断计算式
    /// </summary>
    /// <param name="deaExp"></param>
    /// <param name="msg"></param>
    /// <returns></returns>
    public bool UpdateCodeDiseaseExpression(CodeDiseaseExpression deaExp, ref string msg)
    {
        if (!_codeDiseaseExpressionRepository.Any(deaExp))
        {
            msg = $"公式{deaExp.ExpCode}不存在!";
            return false;
        }

        var deaExpItem = new List<CodeDiseaseExpressionItem>();
        foreach (var item in deaExp.Items)
        {
            deaExpItem.Add(new CodeDiseaseExpressionItem
            {
                ExpCode = deaExp.ExpCode,
                ItemCode = item.ItemCode,
                ValueType = item.ValueType
            });
        }

        //先删除公式的项目列表,在进行添加
        _codeDiseaseExpressionItemRepository.Delete(x => x.ExpCode == deaExp.ExpCode);
        _codeDiseaseExpressionItemRepository.Insert(deaExpItem);
        _codeDiseaseExpressionRepository.Update(deaExp);
        return true;
    }

    /// <summary>
    /// 删除疾病判断计算式
    /// </summary>
    /// <param name="expCode">公式代码</param>
    /// <param name="msg"></param>
    /// <returns></returns>
    public bool DeleteCodeDiseaseExpression(string expCode, ref string msg)
    {
        _dataTranRepository.ExecTran(() =>
        {
            _codeDiseaseExpressionItemRepository.Delete(x => x.ExpCode == expCode);
            _codeDiseaseExpressionRepository.Delete(x => x.ExpCode == expCode);
        });
        return true;
    }

    /// <summary>
    /// 获取疾病判断计算式
    /// </summary>
    /// <param name="expCode">公式代码</param>
    /// <returns></returns>
    public CodeDiseaseExpression ReadCodeDiseaseExpression(string expCode)
    {
        if (string.IsNullOrEmpty(expCode))
        {
            return null;
        }

        var codeDeaExp = _codeDiseaseExpressionRepository.First(x => x.ExpCode == expCode);
        if (codeDeaExp != null)
        {
            codeDeaExp.Items = _codeDiseaseExpressionItemRepository.FindAll(x => x.ExpCode == expCode)
                               .Select(x => new ExpItem
                               {
                                   ItemCode = x.ItemCode,
                                   ValueType = x.ValueType,
                               }).ToList();
            codeDeaExp.ItemText = string.Join(",", codeDeaExp.Items.Select(x => x.ItemCode).ToArray());
        }
        return codeDeaExp;
    }
    #endregion

    #region 疾病包含关系对应 MapDiseaseDisease
    /// <summary>
    /// 获取科室疾病用于疾病包含关系中的父疾病
    /// </summary>
    /// <returns></returns>
    public List<ClsDisease> GetParentDeptDiseases()
    {
        return _diseaseMgtRepository.QueryDeptDisease()
            .Select((a, b) => new ClsDisease
            {
                ClsName = b.ClsName,
                DiseaseCode = a.DiseaseCode,
                DiseaseName = a.DiseaseName
            })
            .ToList();
    }

    /// <summary>
    /// 获取科室疾病用于疾病包含关系中的子疾病
    /// </summary>
    /// <param name="diseaseCode"></param>
    /// <returns></returns>
    public List<ClsDisease> GetChildDeptDiseases(string diseaseCode)
    {
        //所有疾病
        var deptDiseases = _diseaseMgtRepository
            .QueryDeptDisease()
            .Select((a, b) => new ClsDisease
            {
                DiseaseCode = a.DiseaseCode,
                DiseaseName = a.DiseaseName,
                ClsName = b.ClsName
            })
            .Where(a => a.DiseaseCode != diseaseCode)
            .ToList();

        //已对应疾病
        var mapDiseaseDiseases = _diseaseMgtRepository
            .QueryMapDiseaseDisease(diseaseCode)
            .Select(a => a.ChildDiseaseCode)
            .ToList();

        //移除已对应
        deptDiseases.RemoveAll(a => mapDiseaseDiseases.Contains(a.DiseaseCode));

        return deptDiseases;
    }

    /// <summary>
    /// 获取疾病包含关系对应
    /// </summary>
    /// <param name="diseaseCode"></param>
    /// <returns></returns>
    public List<ClsDisease> QueryMapDiseaseDisease(string diseaseCode = "")
    {
        ISugarQueryable<MapDiseaseDisease, CodeDisease, CodeItemCls> sugarQueryable = null;
        if (string.IsNullOrEmpty(diseaseCode))
            sugarQueryable = _diseaseMgtRepository.QueryMapDiseaseDisease();
        else
            sugarQueryable = _diseaseMgtRepository.QueryMapDiseaseDisease(diseaseCode);

        return sugarQueryable
                .Select((a, b, c) => new ClsDisease
                {
                    DiseaseCode = b.DiseaseCode,
                    DiseaseName = b.DiseaseName,
                    ClsName = c.ClsName
                })
                .ToList();
    }

    /// <summary>
    /// 删除父疾病
    /// </summary>
    /// <param name="deaCodes"></param>
    /// <returns></returns>
    public bool DeleteParentDisease(string[] deaCodes)
    {
        return _mapDiseaseDiseaseRepository.Delete(x => SqlFunc.ContainsArray(deaCodes, x.ParentDiseaseCode));
    }

    /// <summary>
    /// 新增疾病包含关系对应
    /// </summary>
    /// <param name="MapDiseaseDiseases"></param>
    /// <param name="msg"></param>
    /// <returns></returns>
    public bool CreateMapDiseaseDisease(List<MapDiseaseDisease> MapDiseaseDiseases, ref string msg)
    {
        string[] query = _mapDiseaseDiseaseRepository.FindInKeys(MapDiseaseDiseases)
            .Select(x => $"'{x.ParentDiseaseCode}'-'{x.ChildDiseaseCode}'")
            .ToArray();

        if (query.Length > 0)
        {
            msg = $"添加失败：[{string.Join("], [", query)}] 记录已存在";
            return false;
        }

        _mapDiseaseDiseaseRepository.Insert(MapDiseaseDiseases);
        return true;
    }

    /// <summary>
    /// 删除疾病包含关系对应
    /// </summary>
    /// <param name="MapDiseaseDiseases"></param>
    /// <param name="msg"></param>
    /// <returns></returns>
    public bool DeleteMapDiseaseDisease(List<MapDiseaseDisease> MapDiseaseDiseases, ref string msg)
    {
        return _mapDiseaseDiseaseRepository.Delete(MapDiseaseDiseases);
    }
    #endregion

    #region 疾病分类信息 CodeDiseaseCls
    /// <summary>
    /// 新增疾病分类信息
    /// </summary>
    /// <param name="codeDiseaseCls"></param>
    /// <param name="msg"></param>
    /// <returns></returns>
    public bool CreateCodeDiseaseCls(CodeDiseaseCls codeDiseaseCls, ref string msg)
    {
        codeDiseaseCls.DiseaseClsName = codeDiseaseCls.DiseaseClsName.Trim();
        if (_codeDiseaseClsRepository.Any(x => x.DiseaseClsName == codeDiseaseCls.DiseaseClsName))
        {
            msg = $"[{codeDiseaseCls.DiseaseClsName}]疾病分类信息已存在";
            return false;
        }

        // 兼容旧数据，如果编码存在，则重新生成编码
        codeDiseaseCls.DiseaseClsCode = string.Empty;
        while (IsGenerCode())
        {
            codeDiseaseCls.DiseaseClsCode = _noGeneration.NextDiseaseClsNo();
        }

        _codeDiseaseClsRepository.Insert(codeDiseaseCls);
        return true;

        bool IsGenerCode()
        {
            if (codeDiseaseCls.DiseaseClsCode.IsNullOrEmpty()) return true;

            return _codeDiseaseClsRepository.Any(x => x.DiseaseClsCode == codeDiseaseCls.DiseaseClsCode);
        }
    }

    /// <summary>
    /// 更新疾病分类信息
    /// </summary>
    /// <param name="codeDiseaseCls"></param>
    /// <param name="msg"></param>
    /// <returns></returns>
    public bool UpdateCodeDiseaseCls(CodeDiseaseCls codeDiseaseCls, ref string msg)
    {
        if (!_codeDiseaseClsRepository.Any(codeDiseaseCls))
        {
            msg = $"编码'{codeDiseaseCls.DiseaseClsCode}'的信息不存在";
            return false;
        }

        return _codeDiseaseClsRepository.Update(codeDiseaseCls);
    }

    /// <summary>
    /// 删除疾病分类信息
    /// </summary>
    /// <param name="diseaseClsCode"></param>
    /// <param name="msg"></param>
    /// <returns></returns>
    public bool DeleteCodeDiseaseCls(string[] diseaseClsCode, ref string msg)
    {
        _dataTranRepository.ExecTran(() =>
        {
            //删除对应关系
            _mapDiseaseClsDiseaseRepository.Delete(x => SqlFunc.ContainsArray(diseaseClsCode, x.DiseaseClsCode));
            _mapDiseaseClsCombRepository.Delete(x => SqlFunc.ContainsArray(diseaseClsCode, x.DiseaseClsCode));
            _codeDiseaseClsRepository.DeleteByIds(diseaseClsCode);
        });
        return true;
    }

    /// <summary>
    /// 获取疾病分类信息
    /// </summary>
    /// <returns></returns>
    public object ReadCodeDiseaseCls()
    {
        return _codeDiseaseClsRepository.FindAll().OrderByDescending(x => x.DiseaseClsCode).ToList();
    }

    /// <summary>
    /// 保存疾病分类对应组合内容
    /// </summary>
    /// <param name="data">数据信息</param>
    /// <returns></returns>
    public bool BatchSaveMapDiseaseClsComb(List<MapDiseaseClsComb> data)
    {
        data.NotNullAndEmpty("数据不能为空");

        var diseaseClsCodes = data.Select(x => x.DiseaseClsCode).Distinct().ToArray();
        _mapDiseaseClsCombRepository.Delete(x => SqlFunc.ContainsArray(diseaseClsCodes, x.DiseaseClsCode));
        _mapDiseaseClsCombRepository.Insert(data);

        return true;
    }

    /// <summary>
    /// 删除疾病分类对应组合内容
    /// </summary>
    /// <param name="diseaseClsCode">疾病分类代码</param>
    /// <param name="combCodes">对应组合代码集合</param>
    /// <returns></returns>
    public bool BatchRemoveMapDiseaseClsComb(string diseaseClsCode, string[] combCodes)
    {
        return _mapDiseaseClsCombRepository.Delete(x => x.DiseaseClsCode == diseaseClsCode && SqlFunc.ContainsArray(combCodes, x.CombCode));
    }

    /// <summary>
    /// 查询疾病分类对应组合内容
    /// </summary>
    /// <param name="query">查询条件</param>
    /// <returns></returns>
    public List<MapDiseaseClsComb> GetMapDiseaseClsCombs(MapDiseaseClsCombQuery query)
    {
        query.NotNullAndEmpty("查询条件不能为空");

        return _diseaseMgtRepository.QueryMapDiseaseClsComb(query.DiseaseClsCode)
            .WhereIF(!query.Keyword.IsNullOrEmpty(), (mapDisClsComb, disCls, comb) => disCls.DiseaseClsName.Contains(query.Keyword) || comb.CombName.Contains(query.Keyword))
            .Select((mapDisClsComb, disCls, comb, cls) => new MapDiseaseClsComb
            {
                DiseaseClsCode = mapDisClsComb.DiseaseClsCode.SelectAll(),
                DiseaseClsName = disCls.DiseaseClsName,
                CombName = comb.CombName,
                ClsCode = cls.ClsCode,
                ClsName = cls.ClsName,
            })
            .ToMyPageList(query);
    }

    /// <summary>
    /// 获取带有统计疾病数的组合信息
    /// </summary>
    /// <param name="query"></param>
    /// <returns></returns>
    public List<MatchDiseaseClsCombDto> GetCombWithDiseCounts(MatchDiseaseClsCombQuery query)
    {
        query.NotNullAndEmpty("查询条件不能为空");

        var combs = _diseaseMgtRepository.QueryCodeItemComb()
            .WhereIF(!query.Keyword.IsNullOrEmpty(), (comb, cls) => comb.CombCode.Contains(query.Keyword) || comb.CombName.Contains(query.Keyword))
            .Select((comb, cls) => new MatchDiseaseClsCombDto
            {
                CombCode = comb.CombCode,
                CombName = comb.CombName,
                ClsCode = cls.ClsCode,
                ClsName = SqlFunc.IsNull(cls.ClsName, "(无项目分类)")
            })
            .ToMyPageList(query);

        if (combs.IsNullOrEmpty()) return combs;

        var combCode = combs.Count == 1 ? combs.First().CombCode : string.Empty;
        var sugDiseCombs = _diseaseMgtRepository.GetSuggDiseaseCombs(query.KeywordOfDise, combCode);
        combs.ForEach(x =>
        {
            x.Diseases = sugDiseCombs.FirstOrDefault(y => y.CombCode == x.CombCode)?.Diseases ?? new();
        });

        return combs.OrderByDescending(x => x.Count).ToList();
    }

    #endregion

    #region 疾病分类对应疾病 MapDiseaseClsDisease
    public List<ClsDisease> GetDeptDisease4DiseaseCls(string diseaseClsCode)
    {
        //所有疾病
        var deptDiseases = _diseaseMgtRepository
            .QueryDeptDisease()
            .Select((a, b) => new ClsDisease
            {
                DiseaseCode = a.DiseaseCode,
                DiseaseName = a.DiseaseName,
                ClsCode = b.ClsCode,
                ClsName = SqlFunc.IsNull(b.ClsName, "(无项目分类)")
            })
            .ToList();

        //已对应的疾病
        var mapDiseaseClsDiseases = _diseaseMgtRepository
            .QueryMapDiseaseClsDisease(diseaseClsCode)
            .Select(a => a.DiseaseCode)
            .ToList();

        //移除已对应
        deptDiseases.RemoveAll(a => mapDiseaseClsDiseases.Contains(a.DiseaseCode));

        return deptDiseases;
    }

    /// <summary>
    /// 获取疾病分类对应疾病
    /// </summary>
    /// <param name="diseaseClsCode"></param>
    /// <returns></returns>
    public List<ClsDisease> QueryMapDiseaseClsDisease(string diseaseClsCode)
    {
        var mapDiseaseClsDiseases = _diseaseMgtRepository.QueryMapDiseaseClsDisease(diseaseClsCode)
            .Select((a, b, c) => new
            {
                a.DiseaseClsCode,
                a.DiseaseCode,
                b.DiseaseName,
                c.ClsName
            })
            .ToList()
            .GroupBy(a => a.DiseaseClsCode);

        var deptDiseases = new List<ClsDisease>();

        foreach (var item in mapDiseaseClsDiseases)
        {
            deptDiseases.AddRange(item.Select(a => new ClsDisease
            {
                ClsName = a.ClsName,
                DiseaseName = a.DiseaseName,
                DiseaseCode = a.DiseaseCode
            }));
        }

        return deptDiseases;
    }

    /// <summary>
    /// 新增疾病分类对应疾病
    /// </summary>
    /// <param name="mapDiseaseClsDiseases"></param>
    /// <param name="msg"></param>
    /// <returns></returns>
    public bool CreateMapDiseaseClsDisease(List<MapDiseaseClsDisease> mapDiseaseClsDiseases, ref string msg)
    {
        string[] query = _mapDiseaseClsDiseaseRepository.FindInKeys(mapDiseaseClsDiseases)
            .Select(x => $"'{x.DiseaseClsCode}'-'{x.DiseaseCode}'")
            .ToArray();

        if (query.Length > 0)
        {
            msg = $"添加失败：[{string.Join("], [", query)}] 记录已存在";
            return false;
        }

        _mapDiseaseClsDiseaseRepository.Insert(mapDiseaseClsDiseases);

        return true;
    }

    /// <summary>
    /// 删除疾病分类对应疾病
    /// </summary>
    /// <param name="mapDiseaseClsDiseases"></param>
    /// <param name="msg"></param>
    /// <returns></returns>
    public bool DeleteMapDiseaseClsDisease(List<MapDiseaseClsDisease> mapDiseaseClsDiseases, ref string msg)
    {
        return _mapDiseaseClsDiseaseRepository.Delete(mapDiseaseClsDiseases);
    }
    #endregion

    #region 疾病-重大阳性
    /// <summary>
    /// 保存疾病-重大阳性对应关系
    /// </summary>
    /// <param name="data"></param>
    /// <returns></returns>
    public bool BatchSaveMapDiseaseMajorPositive(List<MapDiseaseMajorPositive> data)
    {
        CheckHelper.NotNullAndEmpty(data, "内容不能为空!");

        _mapDiseaseMajorPositiveRepository.Delete(x => SqlFunc.ContainsArray(data.Select(y => y.DiseaseCode).ToArray(), x.DiseaseCode) && SqlFunc.ContainsArray(data.Select(y => y.PositiveCode).ToArray(), x.PositiveCode));
        _mapDiseaseMajorPositiveRepository.BulkCopy(data);
        CacheHelper.Remove("MajorPositiveDictionary");

        return true;
    }

    /// <summary>
    /// 疾病-重大阳性对应关系列表查询
    /// </summary>
    /// <param name="query"></param>
    /// <param name="totalNumber"></param>
    /// <param name="totalPage"></param>
    /// <returns></returns>
    public List<MapDiseaseMajorPositiveDto> GetMapDiseaseMajorPositives(MapDiseaseMajorPositiveQuery query, ref int totalNumber, ref int totalPage)
    {
        CheckHelper.NotNullAndEmpty(query, "查询条件不能为空!");

        return _diseaseMgtRepository.QueryMapDiseaseMajorPositive(query.DiseaseCode)
            .WhereIF(!query.Keyword.IsNullOrEmpty(), (map, disease, majorPositive) => majorPositive.PositiveCode.Contains(query.Keyword) || majorPositive.PositiveName.Contains(query.Keyword))
            .Select((map, disease, majorPositive) => new MapDiseaseMajorPositiveDto
            {
                PositiveCode = majorPositive.PositiveCode.SelectAll(),
                DiseaseCode = disease.DiseaseCode,
                DiseaseName = disease.DiseaseName,
            })
            .ToMyPageList(query.PageNumber, query.PageSize, ref totalNumber, ref totalPage);
    }

    /// <summary>
    /// 删除疾病-重大阳性对应关系
    /// </summary>
    /// <param name="data"></param>
    /// <returns></returns>
    public bool BatchRemoveMapKeywordMajorPositive(List<MapDiseaseMajorPositive> data)
    {
        CheckHelper.NotNullAndEmpty(data, "内容不能为空!");

        _mapDiseaseMajorPositiveRepository.Delete(x => SqlFunc.ContainsArray(data.Select(y => y.DiseaseCode).ToArray(), x.DiseaseCode) && SqlFunc.ContainsArray(data.Select(y => y.PositiveCode).ToArray(), x.PositiveCode));

        return true;
    }

    #endregion
    #region 疾病统计年龄段 CodeDiseaseStatAgeRange
    /// <summary>
    /// 新增疾病统计年龄段
    /// </summary>
    /// <param name="codeDiseaseStatAgeRange"></param>
    /// <returns></returns>
    public bool CreateCodeDiseaseStatAgeRange(CodeDiseaseStatAgeRange codeDiseaseStatAgeRange)
    {
        if (_codeDiseaseStatAgeRangeRepository.Any(x => x.RangeCode == codeDiseaseStatAgeRange.RangeCode))
            throw new BusinessException($"编码'{codeDiseaseStatAgeRange.RangeCode}'的信息已存在");
        _codeDiseaseStatAgeRangeRepository.Insert(codeDiseaseStatAgeRange);
        return true;
    }
    /// <summary>
    /// 更新疾病统计年龄段
    /// </summary>
    /// <param name="codeDiseaseStatAgeRange"></param>
    /// <returns></returns>
    public bool UpdateCodeDiseaseStatAgeRange(CodeDiseaseStatAgeRange codeDiseaseStatAgeRange)
    {
        if (!_codeDiseaseStatAgeRangeRepository.Any(x => x.RangeCode == codeDiseaseStatAgeRange.RangeCode))
            throw new BusinessException($"编码'{codeDiseaseStatAgeRange.RangeCode}'的信息已存在");
        _codeDiseaseStatAgeRangeRepository.Update(codeDiseaseStatAgeRange);
        return true;
    }
    /// <summary>
    /// 删除疾病统计年龄段
    /// </summary>
    /// <param name="codeDiseaseStatAgeRanges"></param>
    /// <returns></returns>
    public bool DeleteCodeDiseaseStatAgeRange(List<CodeDiseaseStatAgeRange> codeDiseaseStatAgeRanges)
    {
        _codeDiseaseStatAgeRangeRepository.Delete(codeDiseaseStatAgeRanges);
        return true;
    }
    /// <summary>
    /// 获取疾病统计年龄段
    /// </summary>
    /// <returns></returns>
    public List<CodeDiseaseStatAgeRange> ReadCodeDiseaseStatAgeRange()
    {
        return _codeDiseaseStatAgeRangeRepository.FindAll().ToList();
    }
    #endregion
}