﻿using System.Collections.Generic;

namespace Peis.Utility.Cache;

/*
public class DataAdapter
{
    public List<ProviderConfig> Configs { get; set; } = new List<ProviderConfig>();

    public void AddConfig(ProviderConfig config)
    {
        Configs.Add(config);
    }

    public void Fill(MemoryCacheAdapter cache)
    {
        foreach (var config in Configs)
        {
            cache.Set(config.Key, config.Refresher.Refresh(), config.Time);
        }
    }

    public void Fill(AppCache cache)
    {
        foreach (var config in Configs)
        {
            var data = new CacheData(config.Refresher.Refresh(), config.Refresher, config.Time);
            cache.Set(config.Key, data);
        }
    }
}
*/