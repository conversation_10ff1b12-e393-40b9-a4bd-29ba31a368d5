﻿using Peis.Model.Other.PeEnum;

namespace Peis.Model.DTO.DataQuery
{
    /// <summary>
    /// 费用清单
    /// </summary>
    public class ExpenseData
    {
        /// <summary>
        /// 姓名
        /// </summary>
        public string Name { get; set; }

        /// <summary>
        /// 性别
        /// </summary>
        public Sex? Sex { get; set; }

        /// <summary>
        /// 年龄
        /// </summary>
        public int? Age { get; set; }

        /// <summary>
        /// 体检号
        /// </summary>
        public string RegNo { get; set; }

        /// <summary>
        /// 原始单价
        /// </summary>
        public decimal OriginalPrice { get; set; }

        /// <summary>
        /// 计价金额
        /// </summary>
        public decimal Price { get; set; }

        /// <summary>
        /// 个人缴费
        /// </summary>
        public decimal SelfPayPrice { get; set; }

        /// <summary>
        /// 团体缴费
        /// </summary>
        public decimal CompanyPayPrice { get; set; }

        /// <summary>
        /// 预约平台
        /// </summary>
        public decimal PlatformPayPrice { get; set; }

        /// <summary>
        ///费用列表
        /// </summary>
        public ItemFeeCls[] ItemFeeCls { get; set; }
    }

    /// <summary>
    /// 费用列表
    /// </summary>
    public class ItemFeeCls
    {
        /// <summary>
        /// 项目数据
        /// </summary>
        public Item[] Items { get; set; }

        /// <summary>
        /// 原始单价
        /// </summary>
        public decimal OriginalPrice { get; set; }

        /// <summary>
        /// 计价金额
        /// </summary>
        public decimal Price { get; set; }

        /// <summary>
        /// 个人缴费
        /// </summary>
        public decimal SelfPayPrice { get; set; }

        /// <summary>
        /// 团体缴费
        /// </summary>
        public decimal CompanyPayPrice { get; set; }

        /// <summary>
        /// 预约平台
        /// </summary>
        public decimal PlatformPayPrice { get; set; }
    }

    /// <summary>
    /// 费用
    /// </summary>
    public class Item
    {
        /// <summary>
        /// 类别
        /// </summary>
        public string FeeClsName { get; set; }

        /// <summary>
        /// 体检项目
        /// </summary>
        public string CombName { get; set; }

        /// <summary>
        /// 原始单价
        /// </summary>
        public decimal OriginalPrice { get; set; }

        /// <summary>
        /// 计价金额
        /// </summary>
        public decimal Price { get; set; }

        /// <summary>
        /// 个人缴费
        /// </summary>
        public decimal SelfPayPrice { get; set; }

        /// <summary>
        /// 团体缴费
        /// </summary>
        public decimal CompanyPayPrice { get; set; }

        /// <summary>
        /// 预约平台
        /// </summary>
        public decimal PlatformPayPrice { get; set; }
    }


}
