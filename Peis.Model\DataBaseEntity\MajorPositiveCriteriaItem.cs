﻿namespace Peis.Model.DataBaseEntity;

///<summary>
/// 重大阳性审核条件项目表
///</summary>
[SugarTable(TableName = nameof(MajorPositiveCriteriaItem), TableDescription = "重大阳性审核条件项目表")]
[SugarIndex($"idx_{nameof(MajorPositiveCriteriaItem)}", nameof(PositiveCode), OrderByType.Asc, nameof(ItemCode), OrderByType.Asc, nameof(Value), OrderByType.Asc)]
public class MajorPositiveCriteriaItem
{
    /// <summary>
    /// 重大阳性码
    /// </summary>        
    [SugarColumn(IsNullable = false, IsPrimaryKey = true, Length = 8)]
    [Required(ErrorMessage = "重大阳性码不能为空")]
    public string PositiveCode { get; set; }

    /// <summary>
    /// 项目码
    /// </summary>        
    [SugarColumn(IsNullable = false, IsPrimaryKey = true, Length = 6)]
    [Required(ErrorMessage = "项目码不能为空")]
    public string ItemCode { get; set; }

    /// <summary>
    /// 条件的值
    /// </summary>        
    [SugarColumn(IsNullable = false, IsPrimaryKey = true, Length = 255)]
    [Required(ErrorMessage = "条件的值不能为空")]
    public string Value { get; set; }

    /// <summary>
    /// 操作符
    /// </summary>        
    [SugarColumn(IsNullable = false)]
    public int Operator { get; set; }

    /// <summary>
    /// 数据类型
    /// </summary>        
    [SugarColumn(IsNullable = false)]
    public int ValueType { get; set; }
}