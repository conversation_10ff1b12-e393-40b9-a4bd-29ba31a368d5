﻿using Peis.Utility.Helper;
using System.IO;

namespace Peis.Model.Constant;

/// <summary>
/// 常量类-URL相关
/// </summary>
public static class ConstantUrl
{
    /// <summary>
    /// 静态文件主机地址
    /// </summary>
    /// <returns>string</returns>
    public static string FileHostUrl
    {
        get
        {
            var url = Appsettings.GetSectionValue(ResxCommon.ConfigFileServiceUrl);
            url = url.EndsWith("/") ? url : $"{url}/";
            return url;
        }
    }

    /// <summary>
    /// 报告服务主机地址
    /// </summary>
    /// <returns>string</returns>
    public static string ReportHostUrl
    {
        get
        {
            var url = Appsettings.GetSectionValue(ResxCommon.ConfigReportServiceUrl);
            url = url.EndsWith("/") ? url : $"{url}/";
            return url;
        }
    }

    /// <summary>
    /// 报告服务导出PDF地址
    /// </summary>
    /// <returns>string</returns>
    public static string ExportToPdfUrl => $"{ReportHostUrl}{ResxCommon.ExportToPdfUrl}";

    /// <summary>
    /// 主机目录
    /// </summary>
    public static string AppDomainBaseDirectory = AppDomain.CurrentDomain.BaseDirectory;

    /// <summary>
    /// 医院公章图片路径
    /// </summary>
    public static string HospitalStampPath = Path.Combine(AppDomainBaseDirectory, ResxCommon.FolderWebHost, "Common", "HospitalStamp.png");
}
