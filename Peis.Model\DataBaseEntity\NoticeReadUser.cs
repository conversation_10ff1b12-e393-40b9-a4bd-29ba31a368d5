﻿using SqlSugar;
using System;

namespace Peis.Model.DataBaseEntity
{
    ///<summary>
    ///公告已读用户表
    ///</summary>
    [SugarTable("NoticeReadUser")]
    public class NoticeReadUser
    {
        /// <summary>
        /// 公告Id
        /// </summary>        
        [SugarColumn(IsNullable = false, IsPrimaryKey = true)]
        public long NoticeId { get; set; }

        /// <summary>
        /// 用户code
        /// </summary>        
        [SugarColumn(IsNullable = false, IsPrimaryKey = true, Length = 10)]
        public string OperCode { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>        
        [SugarColumn(IsNullable = false)]
        public DateTime CreateTime { get; set; }
    }
}