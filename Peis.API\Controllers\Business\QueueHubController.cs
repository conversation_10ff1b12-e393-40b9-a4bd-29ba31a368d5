﻿using Microsoft.AspNetCore.Mvc;
using Peis.Model.DataBaseEntity;
using Peis.Model.DTO.QuestionHub;
using Peis.Service.IService;
using Peis.Utility.Helper;
using Peis.Utility.PeUser;
using System.Collections.Generic;

namespace Peis.API.Controllers.Business
{
    /// <summary>
    /// 问题中心
    /// </summary>
    [Route("api/[controller]")]
    [ApiController]
    public class QueueHubController : BaseApiController
    {
        private readonly IQuestionHubService _questionHubService;
        private readonly IHttpContextUser _httpContextUser;
        public QueueHubController(IQuestionHubService questionHubService,
            IHttpContextUser httpContextUser)
        {
            _questionHubService = questionHubService;
            _httpContextUser = httpContextUser;
        }

        /// <summary>
        /// 是否提出过问题
        /// </summary>
        /// <param name="questionerCode">咨询医生代码</param>
        /// <param name="combCode">组合代码</param>
        /// <returns></returns>
        [HttpPost("IsExistQueue")]
        public IActionResult IsExistQueue(string questionerCode, string combCode)
        {
            result.ReturnData = _questionHubService.IsExistQueue(questionerCode, combCode);

            return Ok(result);
        }

        /// <summary>
        /// 提出问题
        /// </summary>
        /// <param name="question">问题</param>
        /// <returns></returns>
        [HttpPost("Question")]
        public IActionResult Question(Question question)
        {
            var msg = string.Empty;

            result.Success = _questionHubService.Question(question, ref msg);

            result.ReturnMsg = msg;

            return Ok(result);
        }

        /// <summary>
        /// 获取主检本人的咨询列表
        /// </summary>
        /// <param name="questionerCode">主检医生代码</param>
        /// <returns></returns>
        [HttpPost("ReadQuestionListByCheck")]
        [ProducesResponseType(typeof(QuestionAndReply[]), 200)]
        public IActionResult ReadQuestionListByCheck(string questionerCode)
        {
            result.ReturnData = _questionHubService.ReadQuestionListByCheck(questionerCode);

            return Ok(result);
        }

        /// <summary>
        /// 获取医生本人的待答复列表
        /// </summary>
        /// <param name="replyerName">答复医生名称</param>
        /// <returns></returns>
        [HttpPost("GetPeQuestionToDoctors")]
        [ProducesResponseType(typeof(List<PeQuestionToDoctor>), 200)]
        public IActionResult GetPeQuestionToDoctors(string replyerName)
        {
            result.ReturnData = _questionHubService.GetPeQuestionToDoctors(replyerName);

            return Ok(result);
        }

        /// <summary>
        /// 答复问题
        /// </summary>
        /// <param name="peQuestionToDoctors">医生问题单</param>
        /// <returns></returns>
        [HttpPost("ReplyQuestion")]
        public IActionResult ReplyQuestion(List<PeQuestionToDoctor> peQuestionToDoctors)
        {
            var msg = string.Empty;
            peQuestionToDoctors.BatchUpdate(x => x.HospCode = _httpContextUser.HospCode);
            result.Success = _questionHubService.ReplyQuestion(peQuestionToDoctors, ref msg);

            return Ok(result);
        }

        /// <summary>
        /// 获取医生已答复且主检未解锁（已锁）的问题列表
        /// </summary>
        /// <param name="operatorCode">主检医生代码</param>
        /// <returns></returns>
        [HttpPost("GetPeQuestionlocks")]
        [ProducesResponseType(typeof(List<PeQuestionToDoctor>), 200)]
        public IActionResult GetPeQuestionLockeds(string operatorCode)
        {
            result.ReturnData = _questionHubService.GetPeQuestionLockeds(operatorCode);

            return Ok(result);
        }

        /// <summary>
        /// 主检医生解锁问题
        /// </summary>
        /// <param name="questionId">问题Id</param>
        /// <returns></returns>
        [HttpPost("UnlockQuestion")]
        public IActionResult UnlockQuestion(int questionId)
        {
            var msg = string.Empty;

            result.Success = _questionHubService.UnlockQuestion(questionId, ref msg);

            return Ok(result);
        }
    }
}