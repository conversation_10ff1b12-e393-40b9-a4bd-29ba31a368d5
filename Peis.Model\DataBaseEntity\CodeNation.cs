﻿using SqlSugar;

namespace Peis.Model.DataBaseEntity
{
    ///<summary>
    ///民族信息
    ///</summary>
    [SugarTable("CodeNation")]
    public class CodeNation
    {
        /// <summary>
        /// 民族代码
        /// </summary>        
        [SugarColumn(IsNullable = false, IsPrimaryKey = true, Length = 10)]
        public string NatCode { get; set; }

        /// <summary>
        /// 民族名称
        /// </summary>        
        [SugarColumn(IsNullable = false, Length = 50)]
        public string NatName { get; set; }

        /// <summary>
        /// 拼音简码
        /// </summary>        
        [SugarColumn(IsNullable = true, Length = 10)]
        public string PinYinCode { get; set; }

        /// <summary>
        /// 五笔简码
        /// </summary>        
        [SugarColumn(IsNullable = true, Length = 10)]
        public string WuBiCode { get; set; }
    }
}