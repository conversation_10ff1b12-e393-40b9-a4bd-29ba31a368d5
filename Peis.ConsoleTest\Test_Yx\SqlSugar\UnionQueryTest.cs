﻿using Newtonsoft.Json;
using Peis.Model.DataBaseEntity;
using Peis.Model.Other.PeEnum;
using Peis.Utility.Helper;
using SqlSugar;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;

namespace Peis.ConsoleTest.Test_Yx.SqlSugar
{
    internal class UnionQueryTest
    {
        public static void Invoke()
        {
            var result = UnionSearch(x => new SysParameter
            {
                ParameterCode = x.ParameterCode,
                ParameterName = x.ParameterName
            },
            x => x.ParameterCode).ToArray();

            Console.WriteLine(JsonConvert.SerializeObject(result, new JsonSerializerSettings { Formatting = Formatting.Indented }));

            //var result2 = UnionSearch(x => (dynamic)new //SysParameter
            //{
            //    ParameterCode = x.ParameterCode,
            //    ParameterName = x.ParameterName
            //}).ToDataTablePage(2, 1);

            //Console.WriteLine(JsonConvert.SerializeObject(result2, new JsonSerializerSettings { Formatting = Formatting.Indented }));
        }

        private static SqlSugarClient DB()
        {
            var _db = new SqlSugarClient(new ConnectionConfig()
            {
                ConnectionString = Appsettings.GetSectionValue("ConnectionString:Entities"),
                DbType = DbType.SqlServer,
                IsAutoCloseConnection = true,//开启自动释放模式
                InitKeyType = InitKeyType.Attribute,//从特性读取主键和自增列信息
            });

            //Print sql
            _db.Aop.OnLogExecuting = (sql, pars) =>
            {
                Console.WriteLine(sql + "\r\n" + _db.Utilities.SerializeObject(pars.ToDictionary(it => it.ParameterName, it => it.Value)));
                Console.WriteLine();
            };

            return _db;
        }

        private static ISugarQueryable<TResult> UnionSearch<TResult>(
            Expression<Func<CodeSystemParameter, TResult>> selectColumns,
            Expression<Func<TResult, object>> orderBy)
            where TResult : class, new()
        {
            var query = DB().Queryable<CodeSystemParameter>();
            var queryables = new List<ISugarQueryable<TResult>>();

            if (true)
                queryables.Add(query.Clone().Where(x => x.ParameterCode == ParamComm.ChargeType.ToString()).Select(selectColumns));

            if (true)
                queryables.Add(query.Clone().Where(x => x.ParameterName.Contains("启用导检")).Select(selectColumns));

            switch (queryables.Count)
            {
                case 0:
                    throw new ArgumentNullException("参数至少有一个");
                case 1:
                    return queryables[0].OrderBy(orderBy, OrderByType.Desc);
                default:
                    return DB().UnionAll(queryables).OrderBy(orderBy, OrderByType.Desc);
            }
        }

        class SysParameter
        {
            public string ParameterCode { get; set; }
            public string ParameterName { get; set; }
        }
    }
}
