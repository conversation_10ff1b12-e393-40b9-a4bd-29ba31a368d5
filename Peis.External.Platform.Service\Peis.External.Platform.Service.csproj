﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net6.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
    <GenerateDocumentationFile>True</GenerateDocumentationFile>
	<Platforms>AnyCPU;x86;x64;ARM64</Platforms>
  </PropertyGroup>

  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|AnyCPU'">
    <NoWarn>1701;1702;CS1591;CS8603;CS8600;CS8602</NoWarn>
  </PropertyGroup>

  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|AnyCPU'">
    <NoWarn>1701;1702;CS1591;CS8603;CS8600;CS8602</NoWarn>
  </PropertyGroup>

  <ItemGroup>
    <Folder Include="WebServices\" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\Peis.Quartz.UI\Peis.Quartz.UI.csproj" />
    <ProjectReference Include="..\Peis.Repository\Peis.Repository.csproj" />
    <ProjectReference Include="..\Peis.Service\Peis.Service.csproj" />
  </ItemGroup>

</Project>
