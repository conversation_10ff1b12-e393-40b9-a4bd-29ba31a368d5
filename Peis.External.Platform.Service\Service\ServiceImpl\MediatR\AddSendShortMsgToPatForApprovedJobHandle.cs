﻿using MediatR;
using Peis.External.Platform.Service.Service.IService.Job;

namespace Peis.External.Platform.Service.Service.ServiceImpl.MediatR;

public class AddSendShortMsgToPatForApprovedJobHandle : IRequestHandler<AddSendShortMsgToPatForApprovedJobHandle.Data>
{
    private readonly ISendShortMsgToPatForApprovedJob _quartzTaskService;

    public AddSendShortMsgToPatForApprovedJobHandle(ISendShortMsgToPatForApprovedJob quartzTaskService)
    {
        _quartzTaskService = quartzTaskService;
    }

    public Task Handle(Data notification, CancellationToken cancellationToken)
    {
        _quartzTaskService.AddJob(notification.RegNo);
        return Task.CompletedTask;
    }

    public record Data(string RegNo) : IRequest;
}
