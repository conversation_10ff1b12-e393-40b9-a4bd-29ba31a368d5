﻿using Peis.Model.Other.PeEnum;
using Peis.Repository.IRepository;
using Peis.Utility.PeUser;

namespace Peis.External.Platform.Service.Repository.RepositoryImpl
{
    public class ShenShanRegisterRepository : IShenShanRegisterRepository
    {

        private readonly ISqlSugarClient _db;
        private readonly ISplitTable _splitTable;
        private readonly IRegisterRepository _registerRepository;

        public ShenShanRegisterRepository(
            ISqlSugarClient db,
            ISplitTable splitTable,
            IRegisterRepository registerRepository)
        {
            _db = db;
            _splitTable = splitTable;
            _registerRepository = registerRepository;
        }

        /// <summary>
        /// 获取订单的组合
        /// </summary>
        /// <param name="regNo">体检号</param>
        /// <param name="regCombIds">登记组合Id</param>
        /// <returns></returns> 
        public ISugarQueryable<PeRegister, PeRegisterComb, CodeItemComb, MapCodeItemCombHisOrderItem> ReadRegCombs(string regNo, params long[] regCombIds)
        {
            var peRegisterComb = _splitTable.GetSplitSugarQueryable<PeRegisterComb>(regNo);

            return _db.Queryable<PeRegister>()
                .InnerJoin(peRegisterComb, (reg, regComb) => regComb.RegNo == reg.RegNo)
                .InnerJoin<CodeItemComb>((reg, regComb, comb) => regComb.CombCode == comb.CombCode)
                .InnerJoin<MapCodeItemCombHisOrderItem>((reg, regComb, comb, mapCombHisOrderItem) => mapCombHisOrderItem.CombCode == comb.CombCode)
                .Where((reg, regComb) => reg.RegNo == regNo && SqlFunc.ContainsArray(regCombIds, regComb.Id));
        }

        /// <summary>
        /// 体检检验申请查询
        /// </summary>
        /// <param name="regNo"></param>
        /// <returns></returns>
        public ISugarQueryable<PeRegister, ExtHisApplyComb, PeSample> QueryLisApplyInfo(string regNo)
        {
            var extHisApplyComb = _splitTable.GetSplitSugarQueryable<ExtHisApplyComb>(regNo);

            return _db.Queryable<PeRegister>()
                .InnerJoin(extHisApplyComb, (reg, applyComb) => applyComb.RegNo == reg.RegNo)
                .InnerJoin<PeSample>((reg, applyComb, sample) => sample.RegNo == reg.RegNo && sample.CombCode == applyComb.CombCode);
        }

        /// <summary>
        /// 体检检查申请查询
        /// </summary>
        /// <param name="regNo"></param>
        /// <returns></returns>
        public ISugarQueryable<PeRegister, ExtHisApplyComb> QueryExamApplyInfo(string regNo)
        {
            var extHisApplyComb = _splitTable.GetSplitSugarQueryable<ExtHisApplyComb>(regNo);

            return _db.Queryable<PeRegister>()
                .InnerJoin(extHisApplyComb, (reg, applyComb) => applyComb.RegNo == reg.RegNo);
        }

        /// <summary>
        /// 获取组合列表
        /// </summary>
        /// <param name="regNo"></param>
        /// <returns></returns>
        public ISugarQueryable<PeRecordComb> GetPeRecordComb(string regNo)
        {
            return _splitTable.GetSplitSugarQueryable<PeRecordComb>(regNo);
        }

        /// <summary>
        /// 获取诊疗卡号
        /// </summary>
        /// <param name="regNo"></param>
        /// <returns></returns>
        public string GetHisCard(string regNo)
        {
            var hisOrderIndex = _db.Queryable<ExtHisOrderIndex>().Where(x => x.RegNo == regNo).First();
            if(hisOrderIndex != null)
            {
                return hisOrderIndex.HisCard;
            }
            return string.Empty;
        }

        /// <summary>
        /// 获取支付金额
        /// </summary>
        /// <param name="regNo"></param>
        /// <returns></returns>
        public decimal GetHisPrice(string regNo)
        {
            var billCombs = _splitTable.GetSplitSugarQueryable<PeRegisterComb>(regNo)
                .Where(x=> x.RegNo == regNo && x.PayStatus != PayStatus.冲销);
            return billCombs.Sum(x => x.Price);
        }

        /// <summary>
        /// 获取采血机条码信息
        /// </summary>
        /// <param name="regNo"></param>
        /// <returns></returns>
        public ISugarQueryable<PeRegister, PeRegisterComb, PeSample, CodeBarcodeType, CodeSample> ReadSampleDetailWithSpecimenInfo(string regNo)
        {
            var peRegisterComb = _registerRepository.ReadRegisterCombs(regNo);
            return _db.Queryable<PeRegister>()
                .InnerJoin(peRegisterComb, (reg, regComb) => regComb.RegNo == reg.RegNo)
                .InnerJoin<PeSample>((reg, regComb, comb) => regComb.CombCode == comb.CombCode)
                .InnerJoin<CodeBarcodeType>((reg, regComb, comb, barcode) => comb.BarcodeType == barcode.Barcode && comb.RegNo == reg.RegNo)
                .InnerJoin<CodeSample>((reg, regComb, comb, barcode, samp) => barcode.SampCode == samp.SampCode)
                .Where((reg, regComb, comb, barcode, samp) => reg.RegNo == regNo && (regComb.PayStatus == PayStatus.收费 || regComb.IsPayBySelf == false) && samp.SampName.Contains("血"));
        }

        /// <summary>
        /// 获取条码列表
        /// </summary>
        /// <param name="regNo"></param>
        /// <returns></returns>
        public ISugarQueryable<PeSample> GetSampleInfo(string regNo)
        {
            return _db.Queryable<PeSample>().Where(x => x.RegNo == regNo);
        }

        /// <summary>
        /// 获取收费日结信息
        /// </summary>
        /// <returns></returns>
        /// <exception cref="NotImplementedException"></exception>
        public ISugarQueryable<ExtHisOrderIndex, ExtHisBill> ReadHisBillList()
        {
            return _db.Queryable<ExtHisOrderIndex>()
                .InnerJoin<ExtHisBill>((order, bill) => order.RegNo == bill.RegNo);
        }

        /// <summary>
        /// 获取个人收费日结组合数据
        /// </summary>
        /// <param name="beginTime"></param>
        /// <param name="endTime"></param>
        /// <returns></returns>
        public ISugarQueryable<ExtHisOrderIndex, ExtHisBill, ExtHisBillComb, PeRegisterCluster> GetPersonalSettlementComb(DateTime beginTime,DateTime endTime)
        {
            var combs = _splitTable.GetTableOrDefault<ExtHisBillComb>(beginTime, endTime);
            return _db.Queryable<ExtHisOrderIndex>()
                .InnerJoin<ExtHisBill>((a, b) => a.RegNo == b.RegNo)
                .InnerJoin(combs,(a,b,c)=>a.RegNo==c.RegNo&&b.InvoiceId==c.InvoiceId)
                .LeftJoin<PeRegisterCluster>((a, b, c,d) =>a.RegNo==d.RegNo);
        }

        public ISugarQueryable<ExtHisOrderIndex, PeRegister> GetPlaformNoticeQueue()
        {
            return _db.Queryable<ExtHisOrderIndex>()
            .InnerJoin<PeRegister>((order, reg) => order.RegNo == reg.RegNo)
                .Where((order, reg) => order.IsEmrNoticed == false)
                .Where((order, reg) => reg.IsActive == true)
                .Where((order, reg) => !SqlFunc.IsNullOrEmpty(order.HisCard));
        }
    }
}