﻿using Peis.Model.Other.PeEnum;

namespace Peis.Model.DTO.DataQuery
{
    /// <summary>
    /// 检验数据查询
    /// </summary>
    public class InspectionData
    {
        /// <summary>
        /// 体检号
        /// </summary>
        public string RegNo { get; set; }

        /// <summary>
        /// 姓名
        /// </summary>
        public string Name { get; set; }

        /// <summary>
        /// 性别
        /// </summary>
        public Sex? Sex { get; set; }

        /// <summary>
        /// 年龄
        /// </summary>
        public string Age { get; set; }

        /// <summary>
        /// 单位
        /// </summary>
        public string CompanyName { get; set; }

        /// <summary>
        /// 套餐
        /// </summary>

        public string ClusName { get; set; }

        /// <summary>
        /// 状态
        /// </summary>
        public PeStatus PeStatus { get; set; }

        /// <summary>
        /// 体检项目
        /// </summary>
        public PeItem[] PeItems { get; set; }

        /// <summary>
        /// 检验项目
        /// </summary>
        public InspectItem[] InspectItems { get; set; }
    }

    /// <summary>
    /// 体检项目
    /// </summary>
    public class PeItem
    {
        /// <summary>
        /// 组合代码
        /// </summary>
        public string CombCode { get; set; }

        /// <summary>
        /// 组合名称
        /// </summary>
        public string CombName { get; set; }

        /// <summary>
        /// 项目代码
        /// </summary>
        public string ItemCode { get; set; }

        /// <summary>
        /// 项目名称
        /// </summary>
        public string ItemName { get; set; }

        /// <summary>
        /// 项目结果
        /// </summary>
        public string ItemResult { get; set; }

        /// <summary>
        /// 下限
        /// </summary>
        public string LowerLimit { get; set; }

        /// <summary>
        /// 上限
        /// </summary>
        public string UpperLimit { get; set; }
    }

    /// <summary>
    /// 检验项目
    /// </summary>
    public class InspectItem
    {
        /// <summary>
        /// 项目代码
        /// </summary>
        public string ItemCode { get; set; }

        /// <summary>
        /// 项目名称
        /// </summary>
        public string ItemName { get; set; }

        /// <summary>
        /// 项目结果
        /// </summary>
        public string ItemResult { get; set; }

        /// <summary>
        /// 下限
        /// </summary>
        public string LowerLimit { get; set; }

        /// <summary>
        /// 上限
        /// </summary>
        public string UpperLimit { get; set; }
    }
}
