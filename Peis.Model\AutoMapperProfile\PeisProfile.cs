﻿using Peis.Model.DTO.BasicCode;
using Peis.Model.DTO.CompanyData;
using Peis.Model.DTO.Disease;
using Peis.Model.DTO.DiseaseMgt;
using Peis.Model.DTO.MajorPositive;
using Peis.Model.DTO.ReportConclusionNew;
using Peis.Model.DTO.ReportDataModel;

namespace Peis.Model.AutoMapperProfile
{
    public class PeisProfile : Profile
    {
        public PeisProfile()
        {
            CreateMap<PeRegister, RecordResultPerson>();
            CreateMap<PeRegister, ReportConPatient>();
            CreateMap<PeRegister, RegisterPatient>();
            CreateMap<RecordItem, ItemRangeLimit>();

            CreateMap<CodeCompany, CodeCompanyDto>();
            CreateMap<CodeCompanyDto, CodeCompany>();

            CreateMap<CodeCompanyTimes, CodeCompanyTimesDto>();
            CreateMap<CodeCompanyTimes, CodeCompanyTimes>();

            CreateMap<CodeReportDataModel, ReportDataModel>().ReverseMap();
            CreateMap<ReportDataModelResultSet, CodeReportDataModelResultSet>()
                .ForMember(dest => dest.ModelCode, opt => opt.Ignore())
                .ReverseMap();

            CreateMap<CodeFollowUpResultTemplate, CodeFollowUpResultTemplateDto>();
            CreateMap<CodeFollowUpResultTemplateDto, CodeFollowUpResultTemplate>();

            CreateMap<MajorPositivePartWithKeywordDto, MapMajorPositivePartWithKeyowrd>();
            CreateMap<MajorPositivePartDto, MapMajorPositivePartWithKeyowrd>();
            CreateMap<MajorPositiveKeywordDto, MajorPositiveKeyword>();
            CreateMap<MajorPositiveCriteriaItemDto, MajorPositiveCriteriaItem>();
            CreateMap<MajorPositiveCriteriaItem, MajorPositiveCriteriaItemDto>();
            CreateMap<MapDiseaseClsCombDto, MapDiseaseClsComb>();
            CreateMap<MapDiseaseClsComb, MapDiseaseClsCombDto>();
        }
    }
}
