using Peis.Model.DTO.Disease;
using Peis.Model.DTO.DoctorStation;
using Peis.Model.Other.PeEnum;
using Peis.Repository.IRepository;
using Peis.Service.IService;
using Peis.Service.IService.Helper;
using Peis.Utility.Flee;
using System.Data;
using System.Text.RegularExpressions;

namespace Peis.Service.Service
{
    public class DiseaseService : IDiseaseService
    {
        private readonly ISqlSugarClient _db;
        private readonly INoGeneration _noGeneration;
        private readonly ICacheRepository _cacheRepository;
        private readonly static Regex _normalCombRegex = new("未见|正常");

        public DiseaseService(
            ISqlSugarClient db,
            INoGeneration noGeneration,
            ICacheRepository cacheRepository)
        {
            _db = db;
            _noGeneration = noGeneration;
            _cacheRepository = cacheRepository;
        }

        /// <summary>
        /// 获取疾病
        /// </summary>
        /// <param name="calcItemTags">参与计算的项目结果</param>
        /// <param name="items">项目信息</param>
        /// <param name="fullItemTags">完整的项目结果，用于计算疾病公式条件引用</param>
        /// <returns></returns>
        public List<CombTag> GetDisease(List<ItemTag> calcItemTags, ItemRangeLimit[] items, List<ItemTag> fullItemTags)
        {
            if (calcItemTags.Count == 0)
                return new();

            var itemDict = items.ToDictionary(x => x.ItemCode);
            var itemTagDict = fullItemTags.ToDictionary(x => (long)x.Id);

            calcItemTags.BatchUpdate(x => x.AbnormalType = AbnormalType.正常);

            // 计算疾病
            var itemResultDeas = GetDiseaseByItemResult(calcItemTags); // 获取项目结果绑定的疾病
            var diseaseCriDeas = GetDiseaseByCriteria(calcItemTags, itemDict, itemTagDict); // 获取满足疾病审核条件的疾病
            var diseaseExpDeas = GetDiseaseByDiseaseExp(calcItemTags, itemDict, itemTagDict); // 获取满足疾病表达式的疾病
            var outsideRangeDeas = GetDiseaseByOutsideRange(calcItemTags, itemDict); // 获取超出参考范围的疾病
            var keywordDeas = GetDiseaseByKeyword(calcItemTags); // 获取匹配疾病或词条关键字的疾病

            var newCombTags = itemResultDeas
                .Union(diseaseCriDeas)
                .Union(diseaseExpDeas)
                .Union(outsideRangeDeas)
                .Union(keywordDeas)
                .ToList();
            {
                // 修正属于正常类型的“疾病”
                if (_normalCombRegex.IsMatch(combTag.Tag))
                {
                    combTag.AbnormalType = AbnormalType.正常;
                    continue;
                }

                // 赋值异常类型给项目标签
                combTag.BindItemTags
                    .ForEach(itemTagId =>
                    {
                        if (itemTagDict.ContainsKey(itemTagId))
                            itemTagDict[itemTagId].AbnormalType = AbnormalType.异常;
                    });
            }

            return ProcessDiseaseResults(newCombTags, itemTagDict);
        }
        /// <summary>
        /// 获取疾病（接收外部检验用）
        /// </summary>
        /// <param name="calcItemTags"></param>
        /// <param name="items"></param>
        /// <param name="fullItemTags"></param>
        /// <returns></returns>
        public List<CombTag> GetDiseaseForExtLisReport(List<ItemTag> calcItemTags, ItemRangeLimit[] items, List<ItemTag> fullItemTags)
        {
            if (calcItemTags.Count == 0)
                return new();

            var itemDict = items.ToDictionary(x => x.ItemCode);
            var itemTagDict = fullItemTags.ToDictionary(x => (long)x.Id);

            calcItemTags.BatchUpdate(x => x.AbnormalType = AbnormalType.正常);

            // 计算疾病
            var diseaseCriDeas = GetDiseaseByCriteria(calcItemTags, itemDict, itemTagDict);   // 获取满足疾病审核条件的疾病
            var diseaseExpDeas = GetDiseaseByDiseaseExp(calcItemTags, itemDict, itemTagDict); // 获取满足疾病表达式的疾病
            var outsideRangeDeas = GetDiseaseByOutsideRange(calcItemTags, itemDict);            // 获取超出参考范围的疾病

            var newCombTags = diseaseCriDeas
                .Union(diseaseExpDeas)
                .Union(outsideRangeDeas)
                .ToList();
            {
                // 修正属于正常类型的“疾病”
                if (_normalCombRegex.IsMatch(combTag.Tag))
                {
                    combTag.AbnormalType = AbnormalType.正常;
                    continue;
                }

                // 赋值异常类型给项目标签
                combTag.BindItemTags
                    .ForEach(itemTagId =>
                    {
                        if (itemTagDict.ContainsKey(itemTagId))
                            itemTagDict[itemTagId].AbnormalType = AbnormalType.异常;
                    });
            }

            return ProcessDiseaseResults(newCombTags, itemTagDict);
        }
        /// <summary>
        /// 合并疾病（合并小结、疾病包含关系）
        /// </summary>
        /// <param name="items"></param>
        /// <param name="fullItemTags"></param>
        /// <param name="combTags"></param>
        /// <returns></returns>
        public List<CombTag> GetDistinctDisease(List<CombTag> combTags, ItemRangeLimit[] items, List<ItemTag> fullItemTags)
        {
            if (combTags.Count < 2)
                return combTags;

            // 合并相同小结内容的Tag
            foreach (var combTagGroup in combTags.GroupBy(x => x.Tag).Where(g => g.Count() > 1))
            {
                var firstCombTag = combTags.First(x => combTagGroup.Contains(x));

                firstCombTag.IsCustom = false;
                firstCombTag.BindItemTags = combTagGroup.SelectMany(x => x.BindItemTags).Distinct().ToList();

                if (string.IsNullOrEmpty(firstCombTag.DiseaseCode))
                {
                    var diseaseCombTag = combTagGroup.FirstOrDefault(x => !string.IsNullOrEmpty(x.DiseaseCode));
                    if (diseaseCombTag != null)
                    {
                        firstCombTag.DiseaseCode = diseaseCombTag.DiseaseCode;
                        firstCombTag.DiseaseName = diseaseCombTag.DiseaseName;
                    }
                }
                combTags.RemoveAll(combTagGroup.Where(x => !x.Equals(firstCombTag)));
            }

            // 合并相同疾病的Tag
            var itemDict = items.ToDictionary(x => x.ItemCode);
            var itemTagDict = fullItemTags.ToDictionary(x => (long)x.Id);
            foreach (var combTagGroup in combTags.Where(x => !string.IsNullOrEmpty(x.DiseaseCode)).GroupBy(x => x.DiseaseCode).Where(g => g.Count() > 1))
            {
                var firstCombTag = combTags.First(x => combTagGroup.Contains(x));
                firstCombTag.IsCustom = false;
                firstCombTag.BindItemTags = combTagGroup.SelectMany(x => x.BindItemTags).Distinct().ToList();

                FomatDiseaseShowMetricsText(firstCombTag, itemDict, itemTagDict);
                combTags.RemoveAll(combTagGroup.Where(x => !x.Equals(firstCombTag)));
            }

            // 疾病包含关系
            var mapDiseaseDiseases = _db.Queryable<MapDiseaseDisease>()
                .Where(x => SqlFunc.ContainsArray(combTags.Select(x => x.DiseaseCode).ToArray(), x.ParentDiseaseCode))
                .ToArray();
            foreach (var diseaseMapper in mapDiseaseDiseases.GroupBy(x => x.ParentDiseaseCode))
            {
                var mainCombTag = combTags.FirstOrDefault(x => x.DiseaseCode == diseaseMapper.Key);
                var subCombTags = combTags.Join(diseaseMapper, combTag => combTag.DiseaseCode, map => map.ChildDiseaseCode, (combTag, map) => combTag).ToArray();
                if (subCombTags.Length > 0)
                {
                    mainCombTag.BindItemTags = mainCombTag.BindItemTags.Union(subCombTags.SelectMany(x => x.BindItemTags)).Distinct().ToList();

                    FomatDiseaseShowMetricsText(mainCombTag, itemDict, itemTagDict);
                    combTags.RemoveAll(subCombTags);
                }
            }

            return combTags;
        }
        /// <summary>
        /// 血压异常小结特殊处理（血压：收缩压/舒张压mmHg疾病名）
        /// </summary>
        /// <param name="combTags"></param>
        /// <param name="items"></param>
        /// <param name="fullItemTags"></param>
        public void BloodPressureDiseaseHandle(List<CombTag> combTags, ItemRangeLimit[] items, List<ItemTag> fullItemTags)
        {
            var sbpItemTag = fullItemTags.Where(x => x.ItemCode == "5531").FirstOrDefault();// 收缩压
            var dbpItemTag = fullItemTags.Where(x => x.ItemCode == "5532").FirstOrDefault();// 舒张压

            if (sbpItemTag is null || dbpItemTag is null)
                return;
            if (!sbpItemTag.Tag.TryParseDouble(out _) || !dbpItemTag.Tag.TryParseDouble(out _))
                return;

            var bpCombTags = combTags.Where(x => x.BindItemTags.Contains((long)sbpItemTag.Id) || x.BindItemTags.Contains((long)dbpItemTag.Id));
            foreach (var combTag in bpCombTags)
            {
                var nonBpItemIds = combTag.BindItemTags.Except(new[] { (long)sbpItemTag.Id, (long)dbpItemTag.Id }).ToArray();
                if (nonBpItemIds.Length == 0)
                {
                    combTag.Tag = $"血压：{sbpItemTag.Tag}/{dbpItemTag.Tag}mmHg{combTag.DiseaseName}";
                    continue;
                }

                var itemGroups = nonBpItemIds.Select(id => fullItemTags.First(x => x.Id == id)).GroupBy(x => x.ItemCode);
                var nonBpResults = itemGroups
                    .Select(g => $"{items.First(x => x.ItemCode == g.Key).ItemName}{string.Join('|', g.Select(x => x.Tag))}")
                    .ToArray();

                combTag.Tag = $"血压：{sbpItemTag.Tag}/{dbpItemTag.Tag}mmHg，{string.Join("，", nonBpResults)}：{combTag.DiseaseName}";
            }
        }

        #region 本地私有方法

        /// <summary>
        /// 处理疾病结果的通用逻辑
        /// </summary>
        /// <param name="combTags">疾病组合标签列表</param>
        /// <param name="itemTagDict">项目标签字典</param>
        /// <returns>处理后的疾病组合标签列表</returns>
        private List<CombTag> ProcessDiseaseResults(List<CombTag> combTags, Dictionary<long, ItemTag> itemTagDict)
        {
            foreach (var combTag in combTags)
            {
                // 修正属于正常类型的"疾病"
                if (_normalCombRegex.IsMatch(combTag.Tag))
                {
                    combTag.AbnormalType = AbnormalType.正常;
                    continue;
                }

                // 赋值异常类型给项目标签
                combTag.BindItemTags
                    .ForEach(itemTagId =>
                    {
                        if (itemTagDict.ContainsKey(itemTagId))
                            itemTagDict[itemTagId].AbnormalType = AbnormalType.异常;
                    });
            }

            return combTags;
        }
        // 生成小结
        /// <summary>
        /// 获取疾病（项目结果）
        /// </summary>
        /// <param name="calcItemTags"></param>
        /// <returns></returns>
        private List<CombTag> GetDiseaseByItemResult(List<ItemTag> calcItemTags)
        {
            var newCombTags = new List<CombTag>();// 小结标签容器

            var itemTags = calcItemTags.Where(x => x.ResultId > 0).ToArray();// 获取项目结果模板的项目标签
            if (itemTags.Length == 0)
                return newCombTags;

            var dbItemResults = _db.Queryable<CodeItemResult>()
                .Where(res => SqlFunc.ContainsArray(itemTags.Select(sq => sq.ResultId).ToArray(), res.ResultId))
                .ToArray();

            foreach (var itemTag in itemTags)
            {
                var itemResult = dbItemResults.Where(x => x.ResultId == itemTag.ResultId).FirstOrDefault();
                if (itemResult == null)
                    continue;

                if (!itemResult.DiseaseCode.IsNullOrEmpty()
                    && _cacheRepository.DictDisease().TryGetValue(itemResult.DiseaseCode, out var disease))
                {
                    // 有绑定疾病
                    newCombTags.Add(new CombTag
                    {
                        Id = _noGeneration.NextSnowflakeId(),
                        Tag = disease.DiseaseName,
                        DiseaseCode = disease.DiseaseCode,
                        DiseaseName = disease.DiseaseName,
                        AbnormalType = itemResult.AbnormalType,
                        IsCustom = false,
                        BindItemTags = new List<long> { (long)itemTag.Id }
                    });
                }
                else if (itemResult.AbnormalType != AbnormalType.正常)
                {
                    // 绑定的疾病失效或无绑定的、属于非正常结果类型的
                    newCombTags.Add(new CombTag
                    {
                        Id = _noGeneration.NextSnowflakeId(),
                        Tag = itemTag.Tag,
                        AbnormalType = AbnormalType.异常,
                        IsCustom = false,
                        BindItemTags = new List<long> { (long)itemTag.Id }
                    });
                }
            }

            return newCombTags;
        }
        /// <summary>
        /// 获取疾病（疾病审核条件）
        /// </summary>
        /// <param name="calcItemTags"></param>
        /// <param name="itemTagDict"></param>
        /// <returns></returns>
        private List<CombTag> GetDiseaseByCriteria(List<ItemTag> calcItemTags, Dictionary<string, ItemRangeLimit> itemDict, Dictionary<long, ItemTag> itemTagDict)
        {
            #region 获取疾病维护数据
            //查询疾病审核条件的项目
            var deaCriteriaItems = _db.Queryable<CodeDiseaseCriteriaItem>()
                .Where(x => SqlFunc.Subqueryable<CodeDiseaseCriteriaItem>()
                                   .Where(sq => SqlFunc.ContainsArray(calcItemTags.Select(x => x.ItemCode).ToArray(), sq.ItemCode))
                                   .Where(sq => sq.DiseaseCode == x.DiseaseCode)
                                   .Any())
                .Select(x => new
                {
                    x.DiseaseCode,
                    x.ItemCode,
                    Operator = (ExpOperator)x.Operator,
                    x.Value,
                    ValueType = (ExpValueType)x.ValueType
                })
                .ToArray()// Surgar
                .GroupBy(x => x.DiseaseCode)// Linq
                .ToArray();
            if (deaCriteriaItems.Length == 0)
                return new();

            //查询疾病审核条件主表
            var deaCriterias = _db
                .Queryable<CodeDisease>()
                .InnerJoin<CodeDiseaseCriteria>((dea, cri) => cri.DiseaseCode == dea.DiseaseCode)
                .Where((dea, cri) => SqlFunc.ContainsArray(deaCriteriaItems.Select(x => x.Key).ToArray(), dea.DiseaseCode))
                .Select((dea, cri) => new
                {
                    dea.DiseaseCode,
                    dea.DiseaseName,
                    Logic = (ExpLogic)cri.Logic
                })
                .ToArray();
            if (deaCriterias.Length == 0)
                return new();

            var diseases = deaCriterias
                .Where(dea => dea.Logic == ExpLogic.And || dea.Logic == ExpLogic.Or)
                .Join(deaCriteriaItems, dea => dea.DiseaseCode, expItem => expItem.Key, (dea, exp) => new
                {
                    dea.DiseaseCode,
                    dea.DiseaseName,
                    dea.Logic,
                    DiseaseExps = exp.ToArray()
                })
                .ToArray();
            #endregion 获取疾病维护数据

            var newCombTags = new List<CombTag>();// 小结标签容器
            var recItems = itemTagDict.Values.GroupBy(x => x.ItemCode).ToDictionary(x => x.Key, x => x.ToArray());
            foreach (var dea in diseases)
            {
                var combTagBindItemTags = new List<long>();
                bool shouldSkipDisease = false;

                foreach (var deaExp in dea.DiseaseExps)
                {
                    recItems.TryGetValue(deaExp.ItemCode, out ItemTag[] expValues);
                    if (expValues == null || expValues.Length == 0)
                    {
                        if (dea.Logic == ExpLogic.And)// 逻辑为And时条件不能缺少
                        {
                            shouldSkipDisease = true;
                            break;
                        }
                        continue;
                    }

                    var filterItemTagIds = expValues.Where(GetItemTagFilter(deaExp.Value, deaExp.ValueType, deaExp.Operator))
                        .Select(x => (long)x.Id)
                        .ToList();
                    if (dea.Logic == ExpLogic.And && filterItemTagIds.Count == 0)// 逻辑为And时,只要有任何一个不通过即失败
                    {
                        shouldSkipDisease = true;
                        break;
                    }
                    combTagBindItemTags.AddRange(filterItemTagIds);
                }

                if (shouldSkipDisease)
                    continue;

                if (combTagBindItemTags.Count == 0)
                    continue;
                /*
                 * Item|Results
                 * ——|————
                 * A   |A1 A2
                 * B   |B1 B2
                 * 假设新结果是：A2 B2
                 * 可以由组合计算新疾病：(A2 B1) (A2 B2) (B2 A1) (B2 A2)
                 * 但是不能是：(A1 B1)，因为组合没有包含新结果A2或B2，这说明之前一定计算过
                 */
                if (!calcItemTags.Any(x => combTagBindItemTags.Contains((long)x.Id)))// 不存在新结果标签，跳过重复的疾病添加
                    continue;

                var combTag = new CombTag
                {
                    Id = _noGeneration.NextSnowflakeId(),
                    Tag = dea.DiseaseName,
                    DiseaseCode = dea.DiseaseCode,
                    DiseaseName = dea.DiseaseName,
                    AbnormalType = AbnormalType.异常,
                    BindItemTags = combTagBindItemTags.Distinct().ToList()
                };

                FomatDiseaseShowMetricsText(combTag, itemDict, itemTagDict);
                newCombTags.Add(combTag);
            }

            return newCombTags;

            #region 本地函数
            static Func<ItemTag, bool> GetItemTagFilter(string expValue, ExpValueType valueType, ExpOperator expOperator)
            {
                if (!Enum.IsDefined(valueType))
                    return x => false;

                //ExpValueType.Number
                if (valueType == ExpValueType.Number)
                {
                    if (!expValue.TryParseDouble(out var numbValue))
                        return x => false;

                    return expOperator switch
                    {
                        ExpOperator.GreaterThan => x => x.Tag.TryParseDouble(out var value) && value > numbValue,
                        ExpOperator.LessThan => x => x.Tag.TryParseDouble(out var value) && value < numbValue,
                        ExpOperator.Equal => x => x.Tag.TryParseDouble(out var value) && value == numbValue,
                        ExpOperator.GreaterThanOrEqual => x => x.Tag.TryParseDouble(out var value) && value >= numbValue,
                        ExpOperator.LessThanOrEqual => x => x.Tag.TryParseDouble(out var value) && value <= numbValue,
                        ExpOperator.NotEqual => x => x.Tag.TryParseDouble(out var value) && value != numbValue,
                        _ => x => false
                    };
                }

                //ExpValueType.String
                return expOperator switch
                {
                    ExpOperator.Equal => x => x.Tag == expValue,
                    ExpOperator.NotEqual => x => x.Tag != expValue,
                    ExpOperator.Contains => x => x.Tag.Contains(expValue),
                    _ => x => false
                };
            }
            #endregion
        }
        /// <summary>
        /// 获取疾病（疾病表达式）
        /// </summary>
        /// <param name="calcItemTags"></param>
        /// <param name="itemTagDict"></param>
        /// <returns></returns>
        private List<CombTag> GetDiseaseByDiseaseExp(List<ItemTag> calcItemTags, Dictionary<string, ItemRangeLimit> itemDict, Dictionary<long, ItemTag> itemTagDict)
        {
            var deaDic = new Dictionary<string, List<ItemTag>>();
            var newCombTags = new List<CombTag>();

            #region 1.获取符合条件的疾病表达式
            var expArray = _db.Queryable<CodeDiseaseExpressionItem>()
                .Where(x => SqlFunc.ContainsArray(calcItemTags.Select(x => x.ItemCode).ToArray(), x.ItemCode))
                .Select(x => x.ExpCode).ToArray();

            var deaExps = _db.Queryable<CodeDiseaseExpression>()
                .InnerJoin<CodeDiseaseExpressionItem>((deaExp, deaExpItem) => deaExpItem.ExpCode == deaExp.ExpCode)
                .Where(deaExp => SqlFunc.ContainsArray(expArray, deaExp.ExpCode))
                .Select((deaExp, deaExpItem) => new
                {
                    deaExp.ExpCode,
                    deaExp.ExpText,
                    deaExp.DiseaseCode,
                    deaExpItem.ItemCode,
                    deaExpItem.ValueType
                })
                .ToList();

            if (deaExps.Count == 0)
                return new();

            //表达式分组后的数据
            var deaExpsGroup = deaExps.GroupBy(x => new { x.ExpCode, x.ExpText, x.DiseaseCode }).Select(x => new
            {
                x.Key.ExpCode,
                x.Key.ExpText,
                x.Key.DiseaseCode,
                ItemArray = x.ToList().Select(x => x.ItemCode).ToArray()
            }).ToList();
            #endregion

            #region 2.循环疾病表达式
            var variablesDic = new Dictionary<string, object>();
            foreach (var deaExp in deaExpsGroup)
            {
                //过滤不在表达式中的项目
                var filterItem = itemTagDict.Values.Where(x => deaExp.ItemArray.Contains(x.ItemCode)).ToList();
                var groupItem = filterItem.GroupBy(item => item.ItemCode).Select(group => group.ToList()).ToList();
                //获取所有可能的组合方式
                var groups = CartesianProduct(groupItem);

                try
                {
                    #region 验证表达式
                    foreach (var itemTags in groups)
                    {
                        variablesDic.Clear();

                        foreach (var item in itemTags)
                        {
                            if (GetValueType(item.ItemCode) == Model.Other.PeEnum.ValueType.数值)
                            {
                                variablesDic.Add("v" + item.ItemCode, decimal.Parse(item.Tag));
                                variablesDic.Add("u" + item.ItemCode, decimal.Parse(item.UpperLimit));
                                variablesDic.Add("l" + item.ItemCode, decimal.Parse(item.LowerLimit));
                            }
                            else
                                variablesDic.Add("v" + item.ItemCode, item.Tag);
                        }

                        var resultFlag = FleeExpressionContext.CalculateExpression(variablesDic, deaExp.ExpText);
                        if (resultFlag)
                        {
                            deaDic.Add(deaExp.DiseaseCode, filterItem);
                            break;
                        }
                    }
                    #endregion
                }
                catch (Exception)
                {
                    continue;
                }
            }

            if (deaDic.Count == 0)
                return new();

            #endregion

            #region 3.查出疾病组成小结
            var deaList = _db.Queryable<CodeDisease>()
                .Where(x => SqlFunc.ContainsArray(deaDic.Keys.ToArray(), x.DiseaseCode))
                .Select(x => new
                {
                    x.DiseaseCode,
                    x.DiseaseName
                })
                .ToArray();

            foreach (var disease in deaList)
            {
                var newCombTag = new CombTag
                {
                    Id = _noGeneration.NextSnowflakeId(),
                    Tag = disease.DiseaseName,
                    DiseaseCode = disease.DiseaseCode,
                    DiseaseName = disease.DiseaseName,
                    AbnormalType = AbnormalType.异常,
                    BindItemTags = deaDic[disease.DiseaseCode].Select(x => Convert.ToInt64(x.Id)).ToList()
                };

                FomatDiseaseShowMetricsText(newCombTag, itemDict, itemTagDict);
                newCombTags.Add(newCombTag);
            }
            ;
            #endregion

            return newCombTags;

            #region 获取项目类型
            Model.Other.PeEnum.ValueType GetValueType(string itemCode)
            {
                return deaExps.Where(x => x.ItemCode == itemCode).Select(x => x.ValueType).First();
            }
            #endregion

            #region 内部递归
            List<List<ItemTag>> CartesianProduct(List<List<ItemTag>> lists)
            {
                if (lists.Count == 0)
                {
                    return new List<List<ItemTag>>();
                }

                // 如果当前只有一组列表，那么直接将每个元素作为一个列表返回。
                if (lists.Count == 1)
                {
                    return lists[0].Select(x => new List<ItemTag> { x }).ToList();
                }

                // 否则，将当前列表中的第一个列表取出来，把剩下的列表再次传入自身进行排列组合。
                var head = lists[0];
                var tail = lists.Skip(1).ToList();
                var tailCombinations = CartesianProduct(tail);

                // 对于第一个列表中的每个元素，分别与排列组合的结果列表进行连接得到新的列表。
                var result = new List<List<ItemTag>>();

                foreach (var item in head)
                {
                    foreach (var tailCombination in tailCombinations)
                    {
                        //判断tailCombination 中是否存在任意一个元素满足条件，也就是是否存在具有相同 itemcode 的元素
                        //如果不存在，则生成新列表并加入最终结果列表中。
                        if (!tailCombination.Any(x => x.ItemCode == item.ItemCode))
                        {
                            result.Add(new List<ItemTag> { item }.Concat(tailCombination).ToList());
                        }
                    }
                }

                return result;
            }
            #endregion
        }
        /// <summary>
        /// 获取疾病（参考范围）
        /// </summary>
        /// <param name="itemDict"></param>
        /// <param name="calcItemTags"></param>
        /// <returns></returns>
        private List<CombTag> GetDiseaseByOutsideRange(List<ItemTag> calcItemTags, Dictionary<string, ItemRangeLimit> itemDict)
        {
            var combTags = new List<CombTag>();
            foreach (var itemTag in calcItemTags)
            {
                if (!itemTag.Tag.TryParseDouble(out var itemValue))
                    continue;
                if (!itemDict.TryGetValue(itemTag.ItemCode, out var itemInfo))
                    continue;

                // 危急范围
                // 暂无

                // 普通范围
                if (!ReferenceRange.TryParse(itemInfo.LowerLimit, itemInfo.UpperLimit, out var range))
                    continue;
                var (combTagValue, hint) = range.CompareTo(itemValue) switch
                {
                    -1 => ($"{itemInfo.ItemName}低{itemTag.Tag} {itemInfo.Unit}", "↓"),
                    1 => ($"{itemInfo.ItemName}高{itemTag.Tag} {itemInfo.Unit}", "↑"),
                    _ => (string.Empty,
                    string.Empty)
                };
                if (combTagValue != string.Empty)
                {
                    // 尝试匹配疾病，如果没有匹配到则不设置疾病代码和名称
                    var diseaseMatch = _cacheRepository.DictDisease().Values
                        .FirstOrDefault(x => combTagValue.Contains(x.DiseaseName));

                    combTags.Add(new CombTag
                    {
                        Id = _noGeneration.NextSnowflakeId(),
                        Tag = combTagValue,
                        DiseaseCode = diseaseMatch?.DiseaseCode,
                        DiseaseName = diseaseMatch?.DiseaseName,
                        AbnormalType = AbnormalType.异常,
                        IsCustom = false,
                        BindItemTags = new() { (long)itemTag.Id }
                    });
                    itemTag.Hint = hint;
                }
            }

            return combTags;
        }
        /// <summary>
        /// 获取疾病（疾病、词条）
        /// </summary>
        /// <param name="calcItemTags"></param>
        /// <returns></returns>
        private List<CombTag> GetDiseaseByKeyword(List<ItemTag> calcItemTags)
        {
            var combTags = new List<CombTag>();
            foreach (var itemTag in calcItemTags)
            {
                foreach (var disease in _cacheRepository.DictDisease().Values)
                {
                    // 先从疾病词条中匹配，若匹配便返回
                    var deaEntryText = disease.DiseaseEntries
                        .Where(entryText => Regex.IsMatch(itemTag.Tag, $"(?<!未见(?:明显|明确)?|未发现|无(?:明显)?|未闻及(?:病理性)?){Regex.Escape(entryText)}"))
                        .FirstOrDefault();
                    if (deaEntryText != null)
                    {
                        combTags.Add(NewCombTag(itemTag, disease.DiseaseCode, deaEntryText));
                        continue;
                    }

                    // 从疾病名中匹配
                    if (Regex.IsMatch(itemTag.Tag, $"(?<!未见(?:明显|明确)?|未发现|无(?:明显)?|未闻及(?:病理性)?){Regex.Escape(disease.DiseaseName)}"))
                        combTags.Add(NewCombTag(itemTag, disease.DiseaseCode, deaEntryText));
                }
            }

            return combTags;

            CombTag NewCombTag(ItemTag itemTag, string diseaseCode, string diseaseName)
            {
                return new CombTag
                {
                    Id = _noGeneration.NextSnowflakeId(),
                    Tag = itemTag.Tag,
                    DiseaseCode = diseaseCode, // 匹配疾病
                    DiseaseName = diseaseName,
                    AbnormalType = AbnormalType.异常,
                    IsCustom = false,
                    BindItemTags = new() { (long)itemTag.Id }
                };
            }
        }

        // 小结格式
        /// <summary>
        /// 疾病显示指标数值（不包括偏高偏低的格式）
        /// </summary>
        /// <remarks>
        /// 如：<br />
        /// "{指标}{数值} {单位}:{疾病}"<br />
        /// "{指标1}{数值} {单位}，{指标2}{数值} {单位}:{疾病}"
        /// </remarks>
        /// <param name="combTag"></param>
        /// <param name="itemDict"></param>
        /// <param name="itemTagDict"></param>
        /// <returns></returns>
        private static void FomatDiseaseShowMetricsText(CombTag combTag, Dictionary<string, ItemRangeLimit> itemDict, Dictionary<long, ItemTag> itemTagDict)
        {
            // 结果都为数值
            if (combTag.BindItemTags.All(x => itemTagDict[x].Tag.IsDouble()))
            {
                var itemNameResults = new List<string>();
                foreach (var itemTagId in combTag.BindItemTags)
                {
                    var itemTag = itemTagDict[itemTagId];
                    var codeItem = itemDict[itemTag.ItemCode];

                    if (string.IsNullOrWhiteSpace(codeItem.Unit))
                        itemNameResults.Add($"{codeItem.ItemName}{itemTag.Tag}");
                    else
                        itemNameResults.Add($"{codeItem.ItemName}{itemTag.Tag} {codeItem.Unit}");
                }

                combTag.Tag = $"{string.Join("，", itemNameResults)}：{combTag.DiseaseName}";
            }

            return;
        }
        #endregion
    }
}
