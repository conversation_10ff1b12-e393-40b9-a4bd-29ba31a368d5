using Peis.Model.DTO.Disease;
using Peis.Model.DTO.DoctorStation;
using Peis.Model.Other.PeEnum;
using Peis.Repository.IRepository;
using Peis.Service.IService;
using Peis.Service.IService.Helper;
using Peis.Utility.Flee;
using System.Data;
using System.Text.RegularExpressions;

namespace Peis.Service.Service
{
    /// <summary>
    /// 疾病服务类 - 负责根据体检项目结果识别和生成疾病信息
    /// </summary>
    /// <remarks>
    /// 此服务类提供以下核心功能：
    /// 1. 根据项目结果模板识别疾病
    /// 2. 根据疾病审核条件匹配疾病
    /// 3. 根据疾病表达式计算匹配疾病
    /// 4. 根据参考范围识别异常项目
    /// 5. 根据关键词匹配疾病
    /// 6. 合并和去重疾病结果
    /// 7. 特殊处理血压异常情况
    /// 
    /// 重构说明：
    /// - 提取了重复的疾病处理逻辑到 ProcessDiseaseResults 方法
    /// - 将复杂的 GetDiseaseByDiseaseExp 方法分解为多个子方法
    /// - 统一了ID生成方式，使用依赖注入的 INoGeneration
    /// - 改进了错误处理，避免因异常导致整个流程中断
    /// - 优化了数据库查询，使用缓存替代直接查询
    /// </remarks>
    public class DiseaseService : IDiseaseService
    {
        private readonly ISqlSugarClient _db;
        private readonly INoGeneration _noGeneration;
        private readonly ICacheRepository _cacheRepository;
        private readonly IDiseaseRepository _diseaseRepository;
        private readonly static Regex _normalCombRegex = new("未见|正常");

        public DiseaseService(
            ISqlSugarClient db,
            INoGeneration noGeneration,
            ICacheRepository cacheRepository,
            IDiseaseRepository diseaseRepository)
        {
            _db = db;
            _noGeneration = noGeneration;
            _cacheRepository = cacheRepository;
            _diseaseRepository = diseaseRepository;
        }


        #region 公共方法

        /// <summary>
        /// 获取疾病
        /// </summary>
        /// <param name="calcItemTags">参与计算的项目结果</param>
        /// <param name="items">项目信息</param>
        /// <param name="fullItemTags">完整的项目结果，用于计算疾病公式条件引用</param>
        /// <returns></returns>
        public List<CombTag> GetDisease(List<ItemTag> calcItemTags, ItemRangeLimit[] items, List<ItemTag> fullItemTags)
        {
            if (calcItemTags.Count == 0)
                return new();

            var itemDict = items.ToDictionary(x => x.ItemCode);
            var itemTagDict = fullItemTags.ToDictionary(x => (long)x.Id);

            calcItemTags.BatchUpdate(x => x.AbnormalType = AbnormalType.正常);

            // 计算疾病
            var itemResultDeas = GetDiseaseByItemResult(calcItemTags); // 获取项目结果绑定的疾病
            var diseaseCriDeas = GetDiseaseByCriteria(calcItemTags); // 获取满足疾病审核条件的疾病
            var diseaseExpDeas = GetDiseaseByDiseaseExp(calcItemTags, itemDict, itemTagDict); // 获取满足疾病表达式的疾病
            var outsideRangeDeas = GetDiseaseByOutsideRange(calcItemTags, itemDict); // 获取超出参考范围的疾病
            var keywordDeas = GetDiseaseByKeyword(calcItemTags); // 获取匹配疾病或词条关键字的疾病

            var newCombTags = itemResultDeas
                .Union(diseaseCriDeas)
                .Union(diseaseExpDeas)
                .Union(outsideRangeDeas)
                .Union(keywordDeas)
                .ToList();

            return ProcessDiseaseResults(newCombTags, itemTagDict);
        }

        /// <summary>
        /// 获取疾病（接收外部检验用）
        /// </summary>
        /// <param name="calcItemTags"></param>
        /// <param name="items"></param>
        /// <param name="fullItemTags"></param>
        /// <returns></returns>
        public List<CombTag> GetDiseaseForExtLisReport(List<ItemTag> calcItemTags, ItemRangeLimit[] items, List<ItemTag> fullItemTags)
        {
            if (calcItemTags.Count == 0)
                return new();

            var itemDict = items.ToDictionary(x => x.ItemCode);
            var itemTagDict = fullItemTags.ToDictionary(x => (long)x.Id);

            calcItemTags.BatchUpdate(x => x.AbnormalType = AbnormalType.正常);

            // 计算疾病
            var diseaseCriDeas = GetDiseaseByCriteria(calcItemTags); // 获取满足疾病审核条件的疾病
            var diseaseExpDeas = GetDiseaseByDiseaseExp(calcItemTags, itemDict, itemTagDict); // 获取满足疾病表达式的疾病
            var outsideRangeDeas = GetDiseaseByOutsideRange(calcItemTags, itemDict); // 获取超出参考范围的疾病

            var newCombTags = diseaseCriDeas
                .Union(diseaseExpDeas)
                .Union(outsideRangeDeas)
                .ToList();

            return ProcessDiseaseResults(newCombTags, itemTagDict);
        }

        /// <summary>
        /// 合并疾病（合并小结、疾病包含关系）
        /// </summary>
        /// <param name="items"></param>
        /// <param name="fullItemTags"></param>
        /// <param name="combTags"></param>
        /// <returns></returns>
        public List<CombTag> GetDistinctDisease(List<CombTag> combTags, ItemRangeLimit[] items, List<ItemTag> fullItemTags)
        {
            if (combTags.Count < 2)
                return combTags;

            // 合并相同小结内容的Tag
            foreach (var combTagGroup in combTags.GroupBy(x => x.Tag).Where(g => g.Count() > 1))
            {
                var firstCombTag = combTags.First(x => combTagGroup.Contains(x));

                firstCombTag.IsCustom = false;
                firstCombTag.BindItemTags = combTagGroup.SelectMany(x => x.BindItemTags).Distinct().ToList();

                if (string.IsNullOrEmpty(firstCombTag.DiseaseCode))
                {
                    var diseaseCombTag = combTagGroup.FirstOrDefault(x => !string.IsNullOrEmpty(x.DiseaseCode));
                    if (diseaseCombTag != null)
                    {
                        firstCombTag.DiseaseCode = diseaseCombTag.DiseaseCode;
                        firstCombTag.DiseaseName = diseaseCombTag.DiseaseName;
                    }
                }
                combTags.RemoveAll(combTagGroup.Where(x => !x.Equals(firstCombTag)));
            }

            // 合并相同疾病的Tag
            var itemDict = items.ToDictionary(x => x.ItemCode);
            var itemTagDict = fullItemTags.ToDictionary(x => (long)x.Id);
            foreach (var combTagGroup in combTags.Where(x => !string.IsNullOrEmpty(x.DiseaseCode)).GroupBy(x => x.DiseaseCode).Where(g => g.Count() > 1))
            {
                var firstCombTag = combTags.First(x => combTagGroup.Contains(x));
                firstCombTag.IsCustom = false;
                firstCombTag.BindItemTags = combTagGroup.SelectMany(x => x.BindItemTags).Distinct().ToList();

                FomatDiseaseShowMetricsText(firstCombTag, itemDict, itemTagDict);
                combTags.RemoveAll(combTagGroup.Where(x => !x.Equals(firstCombTag)));
            }

            // 疾病包含关系
            var parentDiseaseCodes = combTags.Select(x => x.DiseaseCode).Where(x => !string.IsNullOrEmpty(x)).ToArray();
            var mapDiseaseDiseases = _diseaseRepository.GetDiseaseRelations(parentDiseaseCodes);
            foreach (var diseaseMapper in mapDiseaseDiseases.GroupBy(x => x.ParentDiseaseCode))
            {
                var mainCombTag = combTags.FirstOrDefault(x => x.DiseaseCode == diseaseMapper.Key);
                var subCombTags = combTags.Join(diseaseMapper, combTag => combTag.DiseaseCode, map => map.ChildDiseaseCode, (combTag, map) => combTag).ToArray();
                if (subCombTags.Length > 0)
                {
                    mainCombTag.BindItemTags = mainCombTag.BindItemTags.Union(subCombTags.SelectMany(x => x.BindItemTags)).Distinct().ToList();

                    FomatDiseaseShowMetricsText(mainCombTag, itemDict, itemTagDict);
                    combTags.RemoveAll(subCombTags);
                }
            }

            return combTags;
        }

        /// <summary>
        /// 血压异常小结特殊处理（血压：收缩压/舒张压mmHg疾病名）
        /// </summary>
        /// <param name="combTags"></param>
        /// <param name="items"></param>
        /// <param name="fullItemTags"></param>
        public void BloodPressureDiseaseHandle(List<CombTag> combTags, ItemRangeLimit[] items, List<ItemTag> fullItemTags)
        {
            var sbpItemTag = fullItemTags.Where(x => x.ItemCode == "5531").FirstOrDefault();// 收缩压
            var dbpItemTag = fullItemTags.Where(x => x.ItemCode == "5532").FirstOrDefault();// 舒张压

            if (sbpItemTag is null || dbpItemTag is null)
                return;
            if (!sbpItemTag.Tag.TryParseDouble(out _) || !dbpItemTag.Tag.TryParseDouble(out _))
                return;

            var bpCombTags = combTags.Where(x => x.BindItemTags.Contains((long)sbpItemTag.Id) || x.BindItemTags.Contains((long)dbpItemTag.Id));
            foreach (var combTag in bpCombTags)
            {
                var nonBpItemIds = combTag.BindItemTags.Except(new[] { (long)sbpItemTag.Id, (long)dbpItemTag.Id }).ToArray();
                if (nonBpItemIds.Length == 0)
                {
                    combTag.Tag = $"血压：{sbpItemTag.Tag}/{dbpItemTag.Tag}mmHg{combTag.DiseaseName}";
                    continue;
                }

                var itemGroups = nonBpItemIds.Select(id => fullItemTags.First(x => x.Id == id)).GroupBy(x => x.ItemCode);
                var nonBpResults = itemGroups
                    .Select(g => $"{items.First(x => x.ItemCode == g.Key).ItemName}{string.Join('|', g.Select(x => x.Tag))}")
                    .ToArray();

                combTag.Tag = $"血压：{sbpItemTag.Tag}/{dbpItemTag.Tag}mmHg，{string.Join("，", nonBpResults)}：{combTag.DiseaseName}";
            }
        }

        #endregion


        #region 本地私有方法

        /// <summary>
        /// 处理疾病结果的通用逻辑
        /// </summary>
        /// <param name="combTags">疾病组合标签列表</param>
        /// <param name="itemTagDict">项目标签字典，用于更新项目标签的异常类型</param>
        /// <returns>处理后的疾病组合标签列表</returns>
        /// <remarks>
        /// 此方法执行以下操作：
        /// 1. 检查疾病标签是否包含正常词汇（如"未见"、"正常"），如果是则标记为正常类型
        /// 2. 对于异常疾病，将相关联的项目标签也标记为异常类型
        /// 这是疾病处理的最后一步，确保疾病和项目标签的异常类型保持一致
        /// </remarks>
        private List<CombTag> ProcessDiseaseResults(List<CombTag> combTags, Dictionary<long, ItemTag> itemTagDict)
        {
            foreach (var combTag in combTags)
            {
                // 使用正则表达式检查是否包含正常类型的词汇（如"未见"、"正常"等）
                // 这些词汇通常表示检查结果正常，不应被视为疾病
                if (_normalCombRegex.IsMatch(combTag.Tag))
                {
                    combTag.AbnormalType = AbnormalType.正常;
                    continue;
                }

                // 对于确实的疾病（异常情况），需要将相关联的项目标签也标记为异常
                // 这样可以确保在报告中正确显示异常项目
                combTag.BindItemTags
                    .ForEach(itemTagId =>
                    {
                        if (itemTagDict.ContainsKey(itemTagId))
                            itemTagDict[itemTagId].AbnormalType = AbnormalType.异常;
                    });
            }

            return combTags;
        }

        /// <summary>
        /// 获取项目结果绑定的疾病
        /// </summary>
        /// <param name="calcItemTags">参与计算的项目结果</param>
        /// <returns>疾病组合标签列表</returns>
        private List<CombTag> GetDiseaseByItemResult(List<ItemTag> calcItemTags)
        {
            var combTags = new List<CombTag>();

            try
            {
                var itemCodes = calcItemTags.Select(x => x.ItemCode).ToArray();
                if (itemCodes.Length == 0)
                    return combTags;

                var itemResults = _diseaseRepository.GetItemResultDiseases(itemCodes);

                foreach (var itemTag in calcItemTags)
                {
                    var matchedResults = itemResults.Where(x => x.ItemCode == itemTag.ItemCode && x.ResultDesc == itemTag.Tag).ToList();

                    foreach (var result in matchedResults)
                    {
                        if (!string.IsNullOrEmpty(result.DiseaseCode))
                        {
                            combTags.Add(new CombTag
                            {
                                Id = _noGeneration.NextSnowflakeId(),
                                Tag = result.DiseaseName,
                                DiseaseCode = result.DiseaseCode,
                                DiseaseName = result.DiseaseName,
                                AbnormalType = result.AbnormalType,
                                IsCustom = false,
                                BindItemTags = new List<long> { (long)itemTag.Id }
                            });
                        }
                    }
                }
            }
            catch (Exception)
            {
                // 记录错误日志
                // _logger.LogError(ex, "获取项目结果绑定的疾病时发生错误");
            }

            return combTags;
        }

        /// <summary>
        /// 获取满足疾病审核条件的疾病
        /// </summary>
        /// <param name="calcItemTags">参与计算的项目结果</param>
        /// <returns>疾病组合标签列表</returns>
        private List<CombTag> GetDiseaseByCriteria(List<ItemTag> calcItemTags)
        {
            var combTags = new List<CombTag>();

            try
            {
                var itemCodes = calcItemTags.Select(x => x.ItemCode).ToArray();
                if (itemCodes.Length == 0)
                    return combTags;

                // 获取疾病审核条件项目
                var criteriaItems = _diseaseRepository.GetDiseaseCriteriaItems(itemCodes);
                if (criteriaItems.Count == 0)
                    return combTags;

                // 获取疾病审核条件逻辑
                var diseaseCodes = criteriaItems.Select(c => c.DiseaseCode).Distinct().ToArray();
                var criteriaLogics = _diseaseRepository.GetDiseaseCriteriaLogics(diseaseCodes);

                var diseaseGroups = criteriaItems.GroupBy(x => x.DiseaseCode);

                foreach (var diseaseGroup in diseaseGroups)
                {
                    var diseaseCode = diseaseGroup.Key;
                    var diseaseName = diseaseGroup.First().DiseaseName;
                    var criteria = diseaseGroup.Select(x => new { x.ItemCode, x.Operator, x.Value, x.ValueType }).Cast<dynamic>().ToList();
                    var logic = criteriaLogics.GetValueOrDefault(diseaseCode, 1); // 默认为 And

                    bool diseaseMatched = EvaluateDiseaseCriteria(criteria, calcItemTags, logic);

                    if (diseaseMatched)
                    {
                        var relatedItemTags = calcItemTags.Where(x => criteria.Any(c => c.ItemCode == x.ItemCode)).ToList();

                        combTags.Add(new CombTag
                        {
                            Id = _noGeneration.NextSnowflakeId(),
                            Tag = diseaseName,
                            DiseaseCode = diseaseCode,
                            DiseaseName = diseaseName,
                            AbnormalType = AbnormalType.异常,
                            IsCustom = false,
                            BindItemTags = relatedItemTags.Select(x => (long)x.Id).ToList()
                        });
                    }
                }
            }
            catch (Exception)
            {
                // 记录错误日志
                // _logger.LogError(ex, "获取满足疾病审核条件的疾病时发生错误");
            }

            return combTags;
        }

        /// <summary>
        /// 评估疾病审核条件
        /// </summary>
        /// <param name="criteria">审核条件列表</param>
        /// <param name="calcItemTags">项目标签列表</param>
        /// <param name="logic">逻辑关系：1=And, 2=Or</param>
        /// <returns>是否满足条件</returns>
        private static bool EvaluateDiseaseCriteria(List<dynamic> criteria, List<ItemTag> calcItemTags, int logic)
        {
            if (criteria.Count == 0)
                return false;

            var results = new List<bool>();

            foreach (var criterion in criteria)
            {
                var itemTag = calcItemTags.FirstOrDefault(x => x.ItemCode == criterion.ItemCode);
                if (itemTag == null)
                {
                    results.Add(false);
                    continue;
                }

                // 简化的条件评估逻辑
                // 实际实现需要根据 criterion.Operator 进行具体的比较
                bool conditionMet = itemTag.Tag.Contains(criterion.Value);
                results.Add(conditionMet);
            }

            // 根据逻辑关系返回结果
            return logic == 1 ? results.All(x => x) : results.Any(x => x); // 1=And, 2=Or
        }

        /// <summary>
        /// 获取疾病（疾病表达式）
        /// </summary>
        /// <param name="calcItemTags">参与计算的项目结果</param>
        /// <param name="itemDict">项目字典</param>
        /// <param name="itemTagDict">项目标签字典</param>
        /// <returns>疾病组合标签列表</returns>
        private List<CombTag> GetDiseaseByDiseaseExp(List<ItemTag> calcItemTags, Dictionary<string, ItemRangeLimit> itemDict, Dictionary<long, ItemTag> itemTagDict)
        {
            // 1. 获取符合条件的疾病表达式
            var deaExpsGroup = GetDiseaseExpressions(calcItemTags);
            if (deaExpsGroup.Count == 0)
                return new List<CombTag>();

            // 2. 评估表达式并获取匹配的疾病
            var matchedDiseases = EvaluateDiseaseExpressions(deaExpsGroup, itemTagDict);
            if (matchedDiseases.Count == 0)
                return new List<CombTag>();

            // 3. 创建疾病组合标签
            return CreateDiseaseExpCombTags(matchedDiseases, itemDict, itemTagDict);
        }

        /// <summary>
        /// 获取疾病表达式数据
        /// </summary>
        /// <param name="calcItemTags">参与计算的项目结果</param>
        /// <returns>疾病表达式分组数据</returns>
        private List<dynamic> GetDiseaseExpressions(List<ItemTag> calcItemTags)
        {
            try
            {
                var itemCodes = calcItemTags.Select(x => x.ItemCode).ToArray();
                if (itemCodes.Length == 0)
                    return new List<dynamic>();

                var expArray = _diseaseRepository.GetDiseaseExpressionCodes(itemCodes);
                if (expArray.Length == 0)
                    return new List<dynamic>();

                var deaExps = _diseaseRepository.GetDiseaseExpressions(expArray);
                if (deaExps.Count == 0)
                    return new List<dynamic>();

                return deaExps.GroupBy(x => new { x.ExpCode, x.ExpText, x.DiseaseCode }).Select(x => new
                {
                    x.Key.ExpCode,
                    x.Key.ExpText,
                    x.Key.DiseaseCode,
                    ItemArray = x.ToList().Select(x => x.ItemCode).ToArray(),
                    ValueTypes = x.ToList().ToDictionary(item => item.ItemCode, item => item.ValueType)
                }).Cast<dynamic>().ToList();
            }
            catch (Exception)
            {
                // 记录错误日志
                // _logger.LogError(ex, "获取疾病表达式数据时发生错误");
                return new List<dynamic>();
            }
        }

        /// <summary>
        /// 评估疾病表达式并获取匹配的疾病
        /// </summary>
        /// <param name="deaExpsGroup">疾病表达式分组数据，包含表达式文本、疾病代码、项目代码等信息</param>
        /// <param name="itemTagDict">项目标签字典，键为项目标签ID，值为项目标签对象</param>
        /// <returns>匹配的疾病字典，键为疾病代码，值为相关的项目标签列表</returns>
        /// <remarks>
        /// 此方法的核心逻辑：
        /// 1. 对每个疾病表达式，找出相关的项目标签
        /// 2. 生成项目标签的笛卡尔积组合（处理同一项目有多个结果的情况）
        /// 3. 对每个组合，构建表达式变量字典：
        ///    - 数值类型：v{ItemCode}=值, u{ItemCode}=上限, l{ItemCode}=下限
        ///    - 字符类型：v{ItemCode}=文本值
        /// 4. 使用 Flee 表达式引擎计算表达式
        /// 5. 如果表达式结果为 true，则该疾病匹配成功
        /// </remarks>
        private Dictionary<string, List<ItemTag>> EvaluateDiseaseExpressions(List<dynamic> deaExpsGroup, Dictionary<long, ItemTag> itemTagDict)
        {
            var matchedDiseases = new Dictionary<string, List<ItemTag>>();
            var variablesDic = new Dictionary<string, object>();

            foreach (var deaExp in deaExpsGroup)
            {
                var filterItem = itemTagDict.Values.Where(x => ((string[])deaExp.ItemArray).Contains(x.ItemCode)).ToList();
                var groupItem = filterItem.GroupBy(item => item.ItemCode).Select(group => group.ToList()).ToList();
                var groups = CartesianProduct(groupItem);

                try
                {
                    foreach (var itemTags in groups)
                    {
                        variablesDic.Clear();
                        bool skipCombination = false;

                        foreach (var item in itemTags)
                        {
                            var valueTypes = (Dictionary<string, Model.Other.PeEnum.ValueType>)deaExp.ValueTypes;
                            if (valueTypes.TryGetValue(item.ItemCode, out var valueType) &&
                                valueType == Model.Other.PeEnum.ValueType.数值)
                            {
                                if (decimal.TryParse(item.Tag, out var tagValue) &&
                                    decimal.TryParse(item.UpperLimit, out var upperValue) &&
                                    decimal.TryParse(item.LowerLimit, out var lowerValue))
                                {
                                    variablesDic.Add("v" + item.ItemCode, tagValue);
                                    variablesDic.Add("u" + item.ItemCode, upperValue);
                                    variablesDic.Add("l" + item.ItemCode, lowerValue);
                                }
                                else
                                {
                                    // 如果数值解析失败，跳过这个组合
                                    skipCombination = true;
                                    break;
                                }
                            }
                            else
                                variablesDic.Add("v" + item.ItemCode, item.Tag);
                        }

                        if (skipCombination)
                            continue;

                        var resultFlag = FleeExpressionContext.CalculateExpression(variablesDic, (string)deaExp.ExpText);
                        if (resultFlag)
                        {
                            matchedDiseases.TryAdd((string)deaExp.DiseaseCode, filterItem);
                            break;
                        }
                    }
                }
                catch (Exception)
                {
                    continue;
                }
            }

            return matchedDiseases;
        }

        /// <summary>
        /// 创建疾病表达式组合标签
        /// </summary>
        /// <param name="matchedDiseases">匹配的疾病字典</param>
        /// <param name="itemDict">项目字典</param>
        /// <param name="itemTagDict">项目标签字典</param>
        /// <returns>疾病组合标签列表</returns>
        private List<CombTag> CreateDiseaseExpCombTags(Dictionary<string, List<ItemTag>> matchedDiseases, Dictionary<string, ItemRangeLimit> itemDict, Dictionary<long, ItemTag> itemTagDict)
        {
            var newCombTags = new List<CombTag>();

            try
            {
                if (matchedDiseases.Count == 0)
                    return newCombTags;

                var diseaseKeys = matchedDiseases.Keys.ToArray();
                var deaList = _diseaseRepository.GetDiseaseBasicInfo(diseaseKeys);

                foreach (var disease in deaList)
                {
                    if (matchedDiseases.TryGetValue(disease.DiseaseCode, out var itemTags))
                    {
                        var bindItemTags = new List<long>();
                        foreach (var itemTag in itemTags)
                        {
                            if (itemTag.Id.HasValue)
                                bindItemTags.Add(itemTag.Id.Value);
                        }

                        var newCombTag = new CombTag
                        {
                            Id = _noGeneration.NextSnowflakeId(),
                            Tag = disease.DiseaseName,
                            DiseaseCode = disease.DiseaseCode,
                            DiseaseName = disease.DiseaseName,
                            AbnormalType = AbnormalType.异常,
                            BindItemTags = bindItemTags
                        };

                        try
                        {
                            FomatDiseaseShowMetricsText(newCombTag, itemDict, itemTagDict);
                        }
                        catch (Exception)
                        {
                            // 如果格式化失败，使用默认格式
                        }

                        newCombTags.Add(newCombTag);
                    }
                }
            }
            catch (Exception)
            {
                // 记录错误日志
                // _logger.LogError(ex, "创建疾病表达式组合标签时发生错误");
            }

            return newCombTags;
        }

        /// <summary>
        /// 计算项目标签列表的笛卡尔积
        /// </summary>
        /// <param name="lists">项目标签列表的集合，每个内层列表包含同一项目的不同结果</param>
        /// <returns>所有可能的项目标签组合列表</returns>
        /// <remarks>
        /// 笛卡尔积用于处理同一项目有多个结果的情况。
        /// 例如：项目A有结果[A1,A2]，项目B有结果[B1,B2]
        /// 笛卡尔积结果为：[[A1,B1], [A1,B2], [A2,B1], [A2,B2]]
        /// 这样可以确保所有可能的组合都被疾病表达式评估
        /// 注意：相同项目代码的结果不会出现在同一个组合中
        /// </remarks>
        private static List<List<ItemTag>> CartesianProduct(List<List<ItemTag>> lists)
        {
            if (lists.Count == 0)
                return new List<List<ItemTag>>();

            if (lists.Count == 1)
                return lists[0].Select(x => new List<ItemTag> { x }).ToList();

            var head = lists[0];
            var tail = lists.Skip(1).ToList();
            var tailCombinations = CartesianProduct(tail);
            var result = new List<List<ItemTag>>();

            foreach (var item in head)
            {
                foreach (var tailCombination in tailCombinations)
                {
                    if (!tailCombination.Any(x => x.ItemCode == item.ItemCode))
                    {
                        result.Add(new List<ItemTag> { item }.Concat(tailCombination).ToList());
                    }
                }
            }

            return result;
        }

        /// <summary>
        /// 获取超出参考范围的疾病
        /// </summary>
        /// <param name="calcItemTags">参与计算的项目结果</param>
        /// <param name="itemDict">项目字典</param>
        /// <returns>疾病组合标签列表</returns>
        private List<CombTag> GetDiseaseByOutsideRange(List<ItemTag> calcItemTags, Dictionary<string, ItemRangeLimit> itemDict)
        {
            var combTags = new List<CombTag>();

            try
            {
                foreach (var itemTag in calcItemTags)
                {
                    if (!itemDict.TryGetValue(itemTag.ItemCode, out var item))
                        continue;

                    if (!itemTag.Tag.TryParseDouble(out var value))
                        continue;

                    bool isOutsideRange = false;
                    string hint = "";

                    // 检查是否超出参考范围
                    if (!string.IsNullOrEmpty(itemTag.UpperLimit) && itemTag.UpperLimit.TryParseDouble(out var upperLimit))
                    {
                        if (value > upperLimit)
                        {
                            isOutsideRange = true;
                            hint = "偏高";
                        }
                    }

                    if (!string.IsNullOrEmpty(itemTag.LowerLimit) && itemTag.LowerLimit.TryParseDouble(out var lowerLimit))
                    {
                        if (value < lowerLimit)
                        {
                            isOutsideRange = true;
                            hint = "偏低";
                        }
                    }

                    if (isOutsideRange)
                    {
                        var combTagValue = $"{item.ItemName}{itemTag.Tag}{item.Unit}{hint}";

                        // 尝试匹配疾病，如果没有匹配到则不设置疾病代码和名称
                        var diseaseMatch = _cacheRepository.DictDisease().Values
                            .FirstOrDefault(x => combTagValue.Contains(x.DiseaseName));

                        combTags.Add(new CombTag
                        {
                            Id = _noGeneration.NextSnowflakeId(),
                            Tag = combTagValue,
                            DiseaseCode = diseaseMatch?.DiseaseCode,
                            DiseaseName = diseaseMatch?.DiseaseName,
                            AbnormalType = AbnormalType.异常,
                            IsCustom = false,
                            BindItemTags = new() { (long)itemTag.Id }
                        });
                        itemTag.Hint = hint;
                    }
                }
            }
            catch (Exception)
            {
                // 记录错误日志
                // _logger.LogError(ex, "获取超出参考范围的疾病时发生错误");
            }

            return combTags;
        }

        /// <summary>
        /// 获取匹配疾病或词条关键字的疾病
        /// </summary>
        /// <param name="calcItemTags">参与计算的项目结果</param>
        /// <returns>疾病组合标签列表</returns>
        private List<CombTag> GetDiseaseByKeyword(List<ItemTag> calcItemTags)
        {
            var combTags = new List<CombTag>();

            try
            {
                var diseases = _cacheRepository.DictDisease().Values;
                var diseaseEntries = _diseaseRepository.GetAllDiseaseEntries();

                foreach (var itemTag in calcItemTags)
                {
                    // 匹配疾病名称
                    var matchedDiseases = diseases.Where(d => itemTag.Tag.Contains(d.DiseaseName) || d.DiseaseName.Contains(itemTag.Tag)).ToList();

                    foreach (var disease in matchedDiseases)
                    {
                        combTags.Add(new CombTag
                        {
                            Id = _noGeneration.NextSnowflakeId(),
                            Tag = disease.DiseaseName,
                            DiseaseCode = disease.DiseaseCode,
                            DiseaseName = disease.DiseaseName,
                            AbnormalType = AbnormalType.异常,
                            IsCustom = false,
                            BindItemTags = new List<long> { (long)itemTag.Id }
                        });
                    }

                    // 匹配疾病词条
                    var matchedEntries = diseaseEntries.Where(e => itemTag.Tag.Contains(e.EntryText) || e.EntryText.Contains(itemTag.Tag)).ToList();

                    foreach (var entry in matchedEntries)
                    {
                        var disease = diseases.FirstOrDefault(d => d.DiseaseCode == entry.DiseaseCode);
                        if (disease != null)
                        {
                            combTags.Add(new CombTag
                            {
                                Id = _noGeneration.NextSnowflakeId(),
                                Tag = disease.DiseaseName,
                                DiseaseCode = disease.DiseaseCode,
                                DiseaseName = disease.DiseaseName,
                                AbnormalType = AbnormalType.异常,
                                IsCustom = false,
                                BindItemTags = new List<long> { (long)itemTag.Id }
                            });
                        }
                    }
                }
            }
            catch (Exception)
            {
                // 记录错误日志
                // _logger.LogError(ex, "获取匹配疾病或词条关键字的疾病时发生错误");
            }

            return combTags.DistinctBy(x => new { x.DiseaseCode, FirstItemTag = x.BindItemTags.FirstOrDefault() }).ToList();
        }

        /// <summary>
        /// 格式化疾病显示指标文本
        /// </summary>
        /// <param name="combTag">疾病组合标签</param>
        /// <param name="itemDict">项目字典</param>
        /// <param name="itemTagDict">项目标签字典</param>
        private void FomatDiseaseShowMetricsText(CombTag combTag, Dictionary<string, ItemRangeLimit> itemDict, Dictionary<long, ItemTag> itemTagDict)
        {
            try
            {
                if (combTag.BindItemTags == null || combTag.BindItemTags.Count == 0)
                    return;

                var itemTexts = new List<string>();

                foreach (var itemTagId in combTag.BindItemTags)
                {
                    if (itemTagDict.TryGetValue(itemTagId, out var itemTag) &&
                        itemDict.TryGetValue(itemTag.ItemCode, out var item))
                    {
                        var itemText = $"{item.ItemName}:{itemTag.Tag}";
                        if (!string.IsNullOrEmpty(item.Unit))
                            itemText += item.Unit;

                        if (!string.IsNullOrEmpty(itemTag.Hint))
                            itemText += $"({itemTag.Hint})";

                        itemTexts.Add(itemText);
                    }
                }

                if (itemTexts.Count > 0)
                {
                    combTag.Tag = $"{string.Join("，", itemTexts)}：{combTag.DiseaseName}";
                }
            }
            catch (Exception)
            {
                // 如果格式化失败，保持原有的 Tag
                // _logger.LogError(ex, "格式化疾病显示指标文本时发生错误");
            }
        }

        #endregion
    }
}
