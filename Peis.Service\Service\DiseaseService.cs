﻿using Peis.Model.DTO.Disease;
using Peis.Model.DTO.DoctorStation;
using Peis.Model.Other.PeEnum;
using Peis.Repository.Cache;
using Peis.Repository.IRepository;
using Peis.Service.IService;
using Peis.Service.IService.Helper;
using Peis.Utility.Flee;
using System.Data;
using System.Text.RegularExpressions;

namespace Peis.Service.Service
{
    /// <summary>
    /// 疾病服务类 - 负责根据体检项目结果识别和生成疾病信息
    /// </summary>
    /// <remarks>
    /// 此服务类提供以下核心功能：
    /// 1. 根据项目结果模板识别疾病
    /// 2. 根据疾病审核条件匹配疾病
    /// 3. 根据疾病表达式计算匹配疾病
    /// 4. 根据参考范围识别异常项目
    /// 5. 根据关键词匹配疾病
    /// 6. 合并和去重疾病结果
    /// 7. 特殊处理血压异常情况
    /// 
    /// 重构说明：
    /// - 提取了重复的疾病处理逻辑到 ProcessDiseaseResults 方法
    /// - 将复杂的 GetDiseaseByDiseaseExp 方法分解为多个子方法
    /// - 统一了ID生成方式，使用依赖注入的 INoGeneration
    /// - 改进了错误处理，避免因异常导致整个流程中断
    /// - 优化了数据库查询，使用缓存替代直接查询
    /// </remarks>
    public class DiseaseService : IDiseaseService
    {
        private readonly ISqlSugarClient _db;
        private readonly INoGeneration _noGeneration;
        private readonly ICacheRepository _cacheRepository;
        private readonly IDiseaseRepository _diseaseRepository;
        private readonly ISimpleCache _simpleCache;
        private readonly static Regex _normalCombRegex = new("未见|正常");

        public DiseaseService(
            ISqlSugarClient db,
            INoGeneration noGeneration,
            ICacheRepository cacheRepository,
            IDiseaseRepository diseaseRepository,
            ISimpleCache simpleCache)
        {
            _db = db;
            _noGeneration = noGeneration;
            _cacheRepository = cacheRepository;
            _diseaseRepository = diseaseRepository;
            _simpleCache = simpleCache;
        }


        #region 公共方法

        /// <summary>
        /// 获取疾病
        /// </summary>
        /// <param name="calcItemTags">参与计算的项目结果</param>
        /// <param name="items">项目信息</param>
        /// <param name="fullItemTags">完整的项目结果，用于计算疾病公式条件引用</param>
        /// <returns></returns>
        public List<CombTag> GetDisease(List<ItemTag> calcItemTags, ItemRangeLimit[] items, List<ItemTag> fullItemTags)
        {
            var list = _simpleCache.GetOrCreate<List<CodeDisease>>(nameof(CodeDisease));

            if (calcItemTags.Count == 0)
                return new();

            var itemDict = items.ToDictionary(x => x.ItemCode);
            var itemTagDict = fullItemTags.ToDictionary(x => (long)x.Id);

            calcItemTags.BatchUpdate(x => x.AbnormalType = AbnormalType.正常);

            // 计算疾病
            var itemResultDeas = GetDiseaseByItemResult(calcItemTags); // 获取项目结果绑定的疾病
            var diseaseCriDeas = GetDiseaseByCriteria(calcItemTags); // 获取满足疾病审核条件的疾病
            var diseaseExpDeas = GetDiseaseByDiseaseExp(calcItemTags, itemDict, itemTagDict); // 获取满足疾病表达式的疾病
            var outsideRangeDeas = GetDiseaseByOutsideRange(calcItemTags, itemDict); // 获取超出参考范围的疾病
            var keywordDeas = GetDiseaseByKeyword(calcItemTags); // 获取匹配疾病或词条关键字的疾病

            var newCombTags = itemResultDeas
                .Union(diseaseCriDeas)
                .Union(diseaseExpDeas)
                .Union(outsideRangeDeas)
                .Union(keywordDeas)
                .ToList();

            return ProcessDiseaseResults(newCombTags, itemTagDict);
        }

        /// <summary>
        /// 获取疾病（接收外部检验用）
        /// </summary>
        /// <param name="calcItemTags"></param>
        /// <param name="items"></param>
        /// <param name="fullItemTags"></param>
        /// <returns></returns>
        public List<CombTag> GetDiseaseForExtLisReport(List<ItemTag> calcItemTags, ItemRangeLimit[] items, List<ItemTag> fullItemTags)
        {
            if (calcItemTags.Count == 0)
                return new();

            var itemDict = items.ToDictionary(x => x.ItemCode);
            var itemTagDict = fullItemTags.ToDictionary(x => (long)x.Id);

            calcItemTags.BatchUpdate(x => x.AbnormalType = AbnormalType.正常);

            // 计算疾病
            var diseaseCriDeas = GetDiseaseByCriteria(calcItemTags); // 获取满足疾病审核条件的疾病
            var diseaseExpDeas = GetDiseaseByDiseaseExp(calcItemTags, itemDict, itemTagDict); // 获取满足疾病表达式的疾病
            var outsideRangeDeas = GetDiseaseByOutsideRange(calcItemTags, itemDict); // 获取超出参考范围的疾病

            var newCombTags = diseaseCriDeas
                .Union(diseaseExpDeas)
                .Union(outsideRangeDeas)
                .ToList();

            return ProcessDiseaseResults(newCombTags, itemTagDict);
        }

        /// <summary>
        /// 合并疾病（合并小结、疾病包含关系）
        /// </summary>
        /// <param name="items"></param>
        /// <param name="fullItemTags"></param>
        /// <param name="combTags"></param>
        /// <returns></returns>
        public List<CombTag> GetDistinctDisease(List<CombTag> combTags, ItemRangeLimit[] items, List<ItemTag> fullItemTags)
        {
            if (combTags.Count < 2)
                return combTags;

            var itemDict = items.ToDictionary(x => x.ItemCode);
            var itemTagDict = fullItemTags.ToDictionary(x => (long)x.Id);

            // 合并相同小结内容的Tag
            MergeSameTagContent(combTags);

            // 合并相同疾病的Tag
            MergeSameDiseaseCode(combTags, itemDict, itemTagDict);

            // 处理疾病包含关系
            ProcessDiseaseHierarchy(combTags, itemDict, itemTagDict);

            return combTags;
        }

        /// <summary>
        /// 合并相同小结内容的标签
        /// </summary>
        /// <param name="combTags">疾病组合标签列表</param>
        private static void MergeSameTagContent(List<CombTag> combTags)
        {
            var duplicateTagGroups = combTags.GroupBy(x => x.Tag).Where(g => g.Count() > 1);

            foreach (var combTagGroup in duplicateTagGroups)
            {
                var firstCombTag = combTags.First(x => combTagGroup.Contains(x));
                MergeTagGroup(firstCombTag, combTagGroup);
                RemoveDuplicateTags(combTags, combTagGroup, firstCombTag);
            }
        }

        /// <summary>
        /// 合并标签组的属性
        /// </summary>
        /// <param name="firstCombTag">第一个标签（保留的标签）</param>
        /// <param name="combTagGroup">标签组</param>
        private static void MergeTagGroup(CombTag firstCombTag, IGrouping<string, CombTag> combTagGroup)
        {
            firstCombTag.IsCustom = false;
            firstCombTag.BindItemTags = combTagGroup.SelectMany(x => x.BindItemTags).Distinct().ToList();

            // 如果第一个标签没有疾病代码，尝试从组中找一个有疾病代码的
            if (string.IsNullOrEmpty(firstCombTag.DiseaseCode))
            {
                var diseaseCombTag = combTagGroup.FirstOrDefault(x => !string.IsNullOrEmpty(x.DiseaseCode));
                if (diseaseCombTag != null)
                {
                    firstCombTag.DiseaseCode = diseaseCombTag.DiseaseCode;
                    firstCombTag.DiseaseName = diseaseCombTag.DiseaseName;
                }
            }
        }

        /// <summary>
        /// 移除重复的标签
        /// </summary>
        /// <param name="combTags">疾病组合标签列表</param>
        /// <param name="combTagGroup">标签组</param>
        /// <param name="firstCombTag">保留的第一个标签</param>
        private static void RemoveDuplicateTags(List<CombTag> combTags, IGrouping<string, CombTag> combTagGroup, CombTag firstCombTag)
        {
            var tagsToRemove = combTagGroup.Where(x => !x.Equals(firstCombTag)).ToList();
            combTags.RemoveAll(tagsToRemove.Contains);
        }

        /// <summary>
        /// 合并相同疾病代码的标签
        /// </summary>
        /// <param name="combTags">疾病组合标签列表</param>
        /// <param name="itemDict">项目字典</param>
        /// <param name="itemTagDict">项目标签字典</param>
        private void MergeSameDiseaseCode(List<CombTag> combTags, Dictionary<string, ItemRangeLimit> itemDict, Dictionary<long, ItemTag> itemTagDict)
        {
            var sameDiseaseGroups = combTags
                .Where(x => !string.IsNullOrEmpty(x.DiseaseCode))
                .GroupBy(x => x.DiseaseCode)
                .Where(g => g.Count() > 1);

            foreach (var combTagGroup in sameDiseaseGroups)
            {
                var firstCombTag = combTags.First(x => combTagGroup.Contains(x));
                firstCombTag.IsCustom = false;
                firstCombTag.BindItemTags = combTagGroup.SelectMany(x => x.BindItemTags).Distinct().ToList();

                FomatDiseaseShowMetricsText(firstCombTag, itemDict, itemTagDict);

                var tagsToRemove = combTagGroup.Where(x => !x.Equals(firstCombTag)).ToList();
                combTags.RemoveAll(tagsToRemove.Contains);
            }
        }

        /// <summary>
        /// 处理疾病包含关系
        /// </summary>
        /// <param name="combTags">疾病组合标签列表</param>
        /// <param name="itemDict">项目字典</param>
        /// <param name="itemTagDict">项目标签字典</param>
        private void ProcessDiseaseHierarchy(List<CombTag> combTags, Dictionary<string, ItemRangeLimit> itemDict, Dictionary<long, ItemTag> itemTagDict)
        {
            var parentDiseaseCodes = combTags.Select(x => x.DiseaseCode).Where(x => !string.IsNullOrEmpty(x)).ToArray();
            if (parentDiseaseCodes.Length == 0)
                return;

            var mapDiseaseDiseases = _diseaseRepository.GetDiseaseRelations(parentDiseaseCodes);
            var diseaseMapperGroups = mapDiseaseDiseases.GroupBy(x => x.ParentDiseaseCode);

            foreach (var diseaseMapper in diseaseMapperGroups)
            {
                ProcessParentChildDiseaseRelation(combTags, diseaseMapper, itemDict, itemTagDict);
            }
        }

        /// <summary>
        /// 处理父子疾病关系
        /// </summary>
        /// <param name="combTags">疾病组合标签列表</param>
        /// <param name="diseaseMapper">疾病映射组</param>
        /// <param name="itemDict">项目字典</param>
        /// <param name="itemTagDict">项目标签字典</param>
        private void ProcessParentChildDiseaseRelation(List<CombTag> combTags, IGrouping<string, MapDiseaseDisease> diseaseMapper, Dictionary<string, ItemRangeLimit> itemDict, Dictionary<long, ItemTag> itemTagDict)
        {
            var mainCombTag = combTags.FirstOrDefault(x => x.DiseaseCode == diseaseMapper.Key);
            if (mainCombTag == null)
                return;

            var subCombTags = combTags.Join(diseaseMapper,
                combTag => combTag.DiseaseCode,
                map => map.ChildDiseaseCode,
                (combTag, map) => combTag).ToArray();

            if (subCombTags.Length > 0)
            {
                mainCombTag.BindItemTags = mainCombTag.BindItemTags
                    .Union(subCombTags.SelectMany(x => x.BindItemTags))
                    .Distinct()
                    .ToList();

                FomatDiseaseShowMetricsText(mainCombTag, itemDict, itemTagDict);
                combTags.RemoveAll(subCombTags.Contains);
            }
        }

        /// <summary>
        /// 血压异常小结特殊处理（血压：收缩压/舒张压mmHg疾病名）
        /// </summary>
        /// <param name="combTags">疾病组合标签列表</param>
        /// <param name="items">项目信息数组</param>
        /// <param name="fullItemTags">完整的项目标签列表</param>
        public void BloodPressureDiseaseHandle(List<CombTag> combTags, ItemRangeLimit[] items, List<ItemTag> fullItemTags)
        {
            var bloodPressureData = GetBloodPressureData(fullItemTags);
            if (bloodPressureData == null)
                return;

            var bpCombTags = GetBloodPressureRelatedCombTags(combTags, bloodPressureData.Value);

            foreach (var combTag in bpCombTags)
            {
                FormatBloodPressureCombTag(combTag, bloodPressureData.Value, items, fullItemTags);
            }
        }

        /// <summary>
        /// 获取血压数据
        /// </summary>
        /// <param name="fullItemTags">完整的项目标签列表</param>
        /// <returns>血压数据，如果数据无效则返回null</returns>
        private static (ItemTag SbpItemTag, ItemTag DbpItemTag)? GetBloodPressureData(List<ItemTag> fullItemTags)
        {
            var sbpItemTag = fullItemTags.FirstOrDefault(x => x.ItemCode == "5531"); // 收缩压
            var dbpItemTag = fullItemTags.FirstOrDefault(x => x.ItemCode == "5532"); // 舒张压

            if (sbpItemTag == null || dbpItemTag == null)
                return null;

            if (!sbpItemTag.Tag.TryParseDouble(out _) || !dbpItemTag.Tag.TryParseDouble(out _))
                return null;

            return (sbpItemTag, dbpItemTag);
        }

        /// <summary>
        /// 获取与血压相关的疾病组合标签
        /// </summary>
        /// <param name="combTags">疾病组合标签列表</param>
        /// <param name="bloodPressureData">血压数据</param>
        /// <returns>与血压相关的疾病组合标签</returns>
        private static IEnumerable<CombTag> GetBloodPressureRelatedCombTags(List<CombTag> combTags, (ItemTag SbpItemTag, ItemTag DbpItemTag) bloodPressureData)
        {
            var sbpId = (long)bloodPressureData.SbpItemTag.Id;
            var dbpId = (long)bloodPressureData.DbpItemTag.Id;

            return combTags.Where(x => x.BindItemTags.Contains(sbpId) || x.BindItemTags.Contains(dbpId));
        }

        /// <summary>
        /// 格式化血压疾病组合标签
        /// </summary>
        /// <param name="combTag">疾病组合标签</param>
        /// <param name="bloodPressureData">血压数据</param>
        /// <param name="items">项目信息数组</param>
        /// <param name="fullItemTags">完整的项目标签列表</param>
        private static void FormatBloodPressureCombTag(CombTag combTag, (ItemTag SbpItemTag, ItemTag DbpItemTag) bloodPressureData, ItemRangeLimit[] items, List<ItemTag> fullItemTags)
        {
            var sbpId = (long)bloodPressureData.SbpItemTag.Id;
            var dbpId = (long)bloodPressureData.DbpItemTag.Id;
            var nonBpItemIds = combTag.BindItemTags.Except(new[] { sbpId, dbpId }).ToArray();

            var baseBloodPressureText = $"血压：{bloodPressureData.SbpItemTag.Tag}/{bloodPressureData.DbpItemTag.Tag}mmHg";

            if (nonBpItemIds.Length == 0)
            {
                combTag.Tag = $"{baseBloodPressureText}{combTag.DiseaseName}";
            }
            else
            {
                var nonBpResults = BuildNonBloodPressureResults(nonBpItemIds, items, fullItemTags);
                combTag.Tag = $"{baseBloodPressureText}，{string.Join("，", nonBpResults)}：{combTag.DiseaseName}";
            }
        }

        /// <summary>
        /// 构建非血压项目的结果文本
        /// </summary>
        /// <param name="nonBpItemIds">非血压项目ID数组</param>
        /// <param name="items">项目信息数组</param>
        /// <param name="fullItemTags">完整的项目标签列表</param>
        /// <returns>非血压项目结果文本数组</returns>
        private static string[] BuildNonBloodPressureResults(long[] nonBpItemIds, ItemRangeLimit[] items, List<ItemTag> fullItemTags)
        {
            var itemGroups = nonBpItemIds
                .Select(id => fullItemTags.First(x => x.Id == id))
                .GroupBy(x => x.ItemCode);

            return itemGroups
                .Select(g => $"{items.First(x => x.ItemCode == g.Key).ItemName}{string.Join('|', g.Select(x => x.Tag))}")
                .ToArray();
        }

        #endregion


        #region 本地私有方法

        /// <summary>
        /// 处理疾病结果的通用逻辑
        /// </summary>
        /// <param name="combTags">疾病组合标签列表</param>
        /// <param name="itemTagDict">项目标签字典，用于更新项目标签的异常类型</param>
        /// <returns>处理后的疾病组合标签列表</returns>
        /// <remarks>
        /// 此方法执行以下操作：
        /// 1. 检查疾病标签是否包含正常词汇（如"未见"、"正常"），如果是则标记为正常类型
        /// 2. 对于异常疾病，将相关联的项目标签也标记为异常类型
        /// 这是疾病处理的最后一步，确保疾病和项目标签的异常类型保持一致
        /// </remarks>
        private static List<CombTag> ProcessDiseaseResults(List<CombTag> combTags, Dictionary<long, ItemTag> itemTagDict)
        {
            foreach (var combTag in combTags)
            {
                // 使用正则表达式检查是否包含正常类型的词汇（如"未见"、"正常"等）
                // 这些词汇通常表示检查结果正常，不应被视为疾病
                if (_normalCombRegex.IsMatch(combTag.Tag))
                {
                    combTag.AbnormalType = AbnormalType.正常;
                    continue;
                }

                // 对于确实的疾病（异常情况），需要将相关联的项目标签也标记为异常
                // 这样可以确保在报告中正确显示异常项目
                combTag.BindItemTags
                    .ForEach(itemTagId =>
                    {
                        if (itemTagDict.ContainsKey(itemTagId))
                            itemTagDict[itemTagId].AbnormalType = AbnormalType.异常;
                    });
            }

            return combTags;
        }

        /// <summary>
        /// 获取项目结果绑定的疾病
        /// </summary>
        /// <param name="calcItemTags">参与计算的项目结果</param>
        /// <returns>疾病组合标签列表</returns>
        private List<CombTag> GetDiseaseByItemResult(List<ItemTag> calcItemTags)
        {
            var combTags = new List<CombTag>();

            try
            {
                var itemCodes = calcItemTags.Select(x => x.ItemCode).ToArray();
                if (itemCodes.Length == 0)
                    return combTags;

                var itemResults = _diseaseRepository.GetItemResultDiseases(itemCodes);

                foreach (var itemTag in calcItemTags)
                {
                    var matchedResults = itemResults.Where(x => x.ItemCode == itemTag.ItemCode && x.ResultDesc == itemTag.Tag).ToList();

                    foreach (var result in matchedResults)
                    {
                        if (!string.IsNullOrEmpty(result.DiseaseCode))
                        {
                            combTags.Add(new CombTag
                            {
                                Id = _noGeneration.NextSnowflakeId(),
                                Tag = result.DiseaseName,
                                DiseaseCode = result.DiseaseCode,
                                DiseaseName = result.DiseaseName,
                                AbnormalType = result.AbnormalType,
                                IsCustom = false,
                                BindItemTags = new List<long> { (long)itemTag.Id }
                            });
                        }
                    }
                }
            }
            catch (Exception)
            {
                // 记录错误日志
                // _logger.LogError(ex, "获取项目结果绑定的疾病时发生错误");
                throw;
            }

            return combTags;
        }

        /// <summary>
        /// 获取满足疾病审核条件的疾病
        /// </summary>
        /// <param name="calcItemTags">参与计算的项目结果</param>
        /// <returns>疾病组合标签列表</returns>
        private List<CombTag> GetDiseaseByCriteria(List<ItemTag> calcItemTags)
        {
            var combTags = new List<CombTag>();

            try
            {
                var itemCodes = calcItemTags.Select(x => x.ItemCode).ToArray();
                if (itemCodes.Length == 0)
                    return combTags;

                // 获取疾病审核条件项目
                var criteriaItems = _diseaseRepository.GetDiseaseCriteriaItems(itemCodes);
                if (criteriaItems.Count == 0)
                    return combTags;

                // 获取疾病审核条件逻辑
                var diseaseCodes = criteriaItems.Select(c => c.DiseaseCode).Distinct().ToArray();
                var criteriaLogics = _diseaseRepository.GetDiseaseCriteriaLogics(diseaseCodes);

                var diseaseGroups = criteriaItems.GroupBy(x => x.DiseaseCode);

                foreach (var diseaseGroup in diseaseGroups)
                {
                    var diseaseCode = diseaseGroup.Key;
                    var diseaseName = diseaseGroup.First().DiseaseName;
                    var criteria = diseaseGroup.Select(x => new { x.ItemCode, x.Operator, x.Value, x.ValueType }).Cast<dynamic>().ToList();
                    var logic = criteriaLogics.GetValueOrDefault(diseaseCode, 1); // 默认为 And

                    bool diseaseMatched = EvaluateDiseaseCriteria(criteria, calcItemTags, logic);

                    if (diseaseMatched)
                    {
                        var relatedItemTags = calcItemTags.Where(x => criteria.Any(c => c.ItemCode == x.ItemCode)).ToList();

                        combTags.Add(new CombTag
                        {
                            Id = _noGeneration.NextSnowflakeId(),
                            Tag = diseaseName,
                            DiseaseCode = diseaseCode,
                            DiseaseName = diseaseName,
                            AbnormalType = AbnormalType.异常,
                            IsCustom = false,
                            BindItemTags = relatedItemTags.Select(x => (long)x.Id).ToList()
                        });
                    }
                }
            }
            catch (Exception)
            {
                // 记录错误日志
                // _logger.LogError(ex, "获取满足疾病审核条件的疾病时发生错误");
                throw;
            }

            return combTags;
        }

        /// <summary>
        /// 评估疾病审核条件
        /// </summary>
        /// <param name="criteria">审核条件列表</param>
        /// <param name="calcItemTags">项目标签列表</param>
        /// <param name="logic">逻辑关系：1=And, 2=Or</param>
        /// <returns>是否满足条件</returns>
        private static bool EvaluateDiseaseCriteria(List<dynamic> criteria, List<ItemTag> calcItemTags, int logic)
        {
            if (criteria.Count == 0)
                return false;

            var results = new List<bool>();

            foreach (var criterion in criteria)
            {
                var itemTag = calcItemTags.FirstOrDefault(x => x.ItemCode == criterion.ItemCode);
                if (itemTag == null)
                {
                    results.Add(false);
                    continue;
                }

                // 简化的条件评估逻辑
                // 实际实现需要根据 criterion.Operator 进行具体的比较
                bool conditionMet = itemTag.Tag.Contains(criterion.Value);
                results.Add(conditionMet);
            }

            // 根据逻辑关系返回结果
            return logic == 1 ? results.All(x => x) : results.Any(x => x); // 1=And, 2=Or
        }

        /// <summary>
        /// 获取疾病（疾病表达式）
        /// </summary>
        /// <param name="calcItemTags">参与计算的项目结果</param>
        /// <param name="itemDict">项目字典</param>
        /// <param name="itemTagDict">项目标签字典</param>
        /// <returns>疾病组合标签列表</returns>
        private List<CombTag> GetDiseaseByDiseaseExp(List<ItemTag> calcItemTags, Dictionary<string, ItemRangeLimit> itemDict, Dictionary<long, ItemTag> itemTagDict)
        {
            // 1. 获取符合条件的疾病表达式
            var deaExpsGroup = GetDiseaseExpressions(calcItemTags);
            if (deaExpsGroup.Count == 0)
                return new List<CombTag>();

            // 2. 评估表达式并获取匹配的疾病
            var matchedDiseases = EvaluateDiseaseExpressions(deaExpsGroup, itemTagDict);
            if (matchedDiseases.Count == 0)
                return new List<CombTag>();

            // 3. 创建疾病组合标签
            return CreateDiseaseExpCombTags(matchedDiseases, itemDict, itemTagDict);
        }

        /// <summary>
        /// 获取疾病表达式数据
        /// </summary>
        /// <param name="calcItemTags">参与计算的项目结果</param>
        /// <returns>疾病表达式分组数据</returns>
        private List<dynamic> GetDiseaseExpressions(List<ItemTag> calcItemTags)
        {
            try
            {
                var itemCodes = calcItemTags.Select(x => x.ItemCode).ToArray();
                if (itemCodes.Length == 0)
                    return new List<dynamic>();

                var expArray = _diseaseRepository.GetDiseaseExpressionCodes(itemCodes);
                if (expArray.Length == 0)
                    return new List<dynamic>();

                var deaExps = _diseaseRepository.GetDiseaseExpressions(expArray);
                if (deaExps.Count == 0)
                    return new List<dynamic>();

                return deaExps.GroupBy(x => new { x.ExpCode, x.ExpText, x.DiseaseCode }).Select(x => new
                {
                    x.Key.ExpCode,
                    x.Key.ExpText,
                    x.Key.DiseaseCode,
                    ItemArray = x.ToList().Select(x => x.ItemCode).ToArray(),
                    ValueTypes = x.ToList().ToDictionary(item => item.ItemCode, item => item.ValueType)
                }).Cast<dynamic>().ToList();
            }
            catch (Exception)
            {
                throw;
            }
        }

        /// <summary>
        /// 评估疾病表达式并获取匹配的疾病
        /// </summary>
        /// <param name="deaExpsGroup">疾病表达式分组数据，包含表达式文本、疾病代码、项目代码等信息</param>
        /// <param name="itemTagDict">项目标签字典，键为项目标签ID，值为项目标签对象</param>
        /// <returns>匹配的疾病字典，键为疾病代码，值为相关的项目标签列表</returns>
        /// <remarks>
        /// 此方法的核心逻辑：
        /// 1. 对每个疾病表达式，找出相关的项目标签
        /// 2. 生成项目标签的笛卡尔积组合（处理同一项目有多个结果的情况）
        /// 3. 对每个组合，构建表达式变量字典：
        ///    - 数值类型：v{ItemCode}=值, u{ItemCode}=上限, l{ItemCode}=下限
        ///    - 字符类型：v{ItemCode}=文本值
        /// 4. 使用 Flee 表达式引擎计算表达式
        /// 5. 如果表达式结果为 true，则该疾病匹配成功
        /// </remarks>
        private Dictionary<string, List<ItemTag>> EvaluateDiseaseExpressions(List<dynamic> deaExpsGroup, Dictionary<long, ItemTag> itemTagDict)
        {
            var matchedDiseases = new Dictionary<string, List<ItemTag>>();

            foreach (var deaExp in deaExpsGroup)
            {
                try
                {
                    var filterItem = GetFilteredItemTags(deaExp, itemTagDict);
                    if (filterItem.Count == 0)
                        continue;

                    var groups = GenerateItemTagCombinations(filterItem);
                    var diseaseMatched = EvaluateExpressionForGroups(deaExp, groups);

                    if (diseaseMatched)
                    {
                        matchedDiseases.TryAdd((string)deaExp.DiseaseCode, filterItem);
                    }
                }
                catch (Exception)
                {
                    // 记录错误日志并继续处理下一个表达式
                    // _logger.LogError(ex, "评估疾病表达式时发生错误: {DiseaseCode}", deaExp.DiseaseCode);
                    continue;
                }
            }

            return matchedDiseases;
        }

        /// <summary>
        /// 获取与疾病表达式相关的项目标签
        /// </summary>
        /// <param name="deaExp">疾病表达式</param>
        /// <param name="itemTagDict">项目标签字典</param>
        /// <returns>过滤后的项目标签列表</returns>
        private static List<ItemTag> GetFilteredItemTags(dynamic deaExp, Dictionary<long, ItemTag> itemTagDict)
        {
            var itemArray = (string[])deaExp.ItemArray;
            return itemTagDict.Values.Where(x => itemArray.Contains(x.ItemCode)).ToList();
        }

        /// <summary>
        /// 生成项目标签的笛卡尔积组合
        /// </summary>
        /// <param name="filterItem">过滤后的项目标签</param>
        /// <returns>项目标签组合列表</returns>
        private static List<List<ItemTag>> GenerateItemTagCombinations(List<ItemTag> filterItem)
        {
            var groupItem = filterItem.GroupBy(item => item.ItemCode).Select(group => group.ToList()).ToList();
            return CartesianProduct(groupItem);
        }

        /// <summary>
        /// 为项目标签组合评估表达式
        /// </summary>
        /// <param name="deaExp">疾病表达式</param>
        /// <param name="groups">项目标签组合</param>
        /// <returns>是否有任何组合匹配表达式</returns>
        private static bool EvaluateExpressionForGroups(dynamic deaExp, List<List<ItemTag>> groups)
        {
            var variablesDic = new Dictionary<string, object>();

            foreach (var itemTags in groups)
            {
                variablesDic.Clear();

                if (BuildExpressionVariables(deaExp, itemTags, variablesDic))
                {
                    var resultFlag = FleeExpressionContext.CalculateExpression(variablesDic, (string)deaExp.ExpText);
                    if (resultFlag)
                    {
                        return true;
                    }
                }
            }

            return false;
        }

        /// <summary>
        /// 构建表达式变量字典
        /// </summary>
        /// <param name="deaExp">疾病表达式</param>
        /// <param name="itemTags">项目标签列表</param>
        /// <param name="variablesDic">变量字典</param>
        /// <returns>是否成功构建变量字典</returns>
        private static bool BuildExpressionVariables(dynamic deaExp, List<ItemTag> itemTags, Dictionary<string, object> variablesDic)
        {
            var valueTypes = (Dictionary<string, Model.Other.PeEnum.ValueType>)deaExp.ValueTypes;

            foreach (var item in itemTags)
            {
                if (valueTypes.TryGetValue(item.ItemCode, out var valueType) &&
                    valueType == Model.Other.PeEnum.ValueType.数值)
                {
                    if (!TryAddNumericVariables(item, variablesDic))
                    {
                        return false; // 数值解析失败
                    }
                }
                else
                {
                    variablesDic.Add("v" + item.ItemCode, item.Tag);
                }
            }

            return true;
        }

        /// <summary>
        /// 尝试添加数值类型的变量
        /// </summary>
        /// <param name="item">项目标签</param>
        /// <param name="variablesDic">变量字典</param>
        /// <returns>是否成功添加</returns>
        private static bool TryAddNumericVariables(ItemTag item, Dictionary<string, object> variablesDic)
        {
            if (decimal.TryParse(item.Tag, out var tagValue) &&
                decimal.TryParse(item.UpperLimit, out var upperValue) &&
                decimal.TryParse(item.LowerLimit, out var lowerValue))
            {
                variablesDic.Add("v" + item.ItemCode, tagValue);
                variablesDic.Add("u" + item.ItemCode, upperValue);
                variablesDic.Add("l" + item.ItemCode, lowerValue);
                return true;
            }

            return false;
        }

        /// <summary>
        /// 创建疾病表达式组合标签
        /// </summary>
        /// <param name="matchedDiseases">匹配的疾病字典</param>
        /// <param name="itemDict">项目字典</param>
        /// <param name="itemTagDict">项目标签字典</param>
        /// <returns>疾病组合标签列表</returns>
        private List<CombTag> CreateDiseaseExpCombTags(Dictionary<string, List<ItemTag>> matchedDiseases, Dictionary<string, ItemRangeLimit> itemDict, Dictionary<long, ItemTag> itemTagDict)
        {
            var newCombTags = new List<CombTag>();

            try
            {
                if (matchedDiseases.Count == 0)
                    return newCombTags;

                var diseaseKeys = matchedDiseases.Keys.ToArray();
                var deaList = _diseaseRepository.GetDiseaseBasicInfo(diseaseKeys);

                foreach (var disease in deaList)
                {
                    if (matchedDiseases.TryGetValue(disease.DiseaseCode, out var itemTags))
                    {
                        var bindItemTags = new List<long>();
                        foreach (var itemTag in itemTags)
                        {
                            if (itemTag.Id.HasValue)
                                bindItemTags.Add(itemTag.Id.Value);
                        }

                        var newCombTag = new CombTag
                        {
                            Id = _noGeneration.NextSnowflakeId(),
                            Tag = disease.DiseaseName,
                            DiseaseCode = disease.DiseaseCode,
                            DiseaseName = disease.DiseaseName,
                            AbnormalType = AbnormalType.异常,
                            BindItemTags = bindItemTags
                        };

                        try
                        {
                            FomatDiseaseShowMetricsText(newCombTag, itemDict, itemTagDict);
                        }
                        catch (Exception)
                        {
                            // 如果格式化失败，使用默认格式
                        }

                        newCombTags.Add(newCombTag);
                    }
                }
            }
            catch //(Exception)
            {
                // 记录错误日志
                // _logger.LogError(ex, "创建疾病表达式组合标签时发生错误");
                throw;
            }

            return newCombTags;
        }

        /// <summary>
        /// 计算项目标签列表的笛卡尔积
        /// </summary>
        /// <param name="lists">项目标签列表的集合，每个内层列表包含同一项目的不同结果</param>
        /// <returns>所有可能的项目标签组合列表</returns>
        /// <remarks>
        /// 笛卡尔积用于处理同一项目有多个结果的情况。
        /// 例如：项目A有结果[A1,A2]，项目B有结果[B1,B2]
        /// 笛卡尔积结果为：[[A1,B1], [A1,B2], [A2,B1], [A2,B2]]
        /// 这样可以确保所有可能的组合都被疾病表达式评估
        /// 注意：相同项目代码的结果不会出现在同一个组合中
        /// </remarks>
        private static List<List<ItemTag>> CartesianProduct(List<List<ItemTag>> lists)
        {
            if (lists.Count == 0)
                return new List<List<ItemTag>>();

            if (lists.Count == 1)
                return lists[0].Select(x => new List<ItemTag> { x }).ToList();

            var head = lists[0];
            var tail = lists.Skip(1).ToList();
            var tailCombinations = CartesianProduct(tail);
            var result = new List<List<ItemTag>>();

            foreach (var item in head)
            {
                foreach (var tailCombination in tailCombinations)
                {
                    if (!tailCombination.Any(x => x.ItemCode == item.ItemCode))
                    {
                        result.Add(new List<ItemTag> { item }.Concat(tailCombination).ToList());
                    }
                }
            }

            return result;
        }

        /// <summary>
        /// 获取超出参考范围的疾病
        /// </summary>
        /// <param name="calcItemTags">参与计算的项目结果</param>
        /// <param name="itemDict">项目字典</param>
        /// <returns>疾病组合标签列表</returns>
        private List<CombTag> GetDiseaseByOutsideRange(List<ItemTag> calcItemTags, Dictionary<string, ItemRangeLimit> itemDict)
        {
            var combTags = new List<CombTag>();

            try
            {
                foreach (var itemTag in calcItemTags)
                {
                    var outsideRangeResult = ProcessItemTagForOutsideRange(itemTag, itemDict);
                    if (outsideRangeResult != null)
                    {
                        combTags.Add(outsideRangeResult);
                    }
                }
            }
            catch //(Exception)
            {
                // 记录错误日志
                // _logger.LogError(ex, "获取超出参考范围的疾病时发生错误");
                throw;
            }

            return combTags;
        }

        /// <summary>
        /// 处理单个项目标签的超出范围检查
        /// </summary>
        /// <param name="itemTag">项目标签</param>
        /// <param name="itemDict">项目字典</param>
        /// <returns>如果超出范围则返回疾病组合标签，否则返回null</returns>
        private CombTag ProcessItemTagForOutsideRange(ItemTag itemTag, Dictionary<string, ItemRangeLimit> itemDict)
        {
            if (!itemDict.TryGetValue(itemTag.ItemCode, out var item))
                return null;

            if (!itemTag.Tag.TryParseDouble(out var value))
                return null;

            var rangeCheckResult = CheckValueRange(itemTag, value);
            if (!rangeCheckResult.IsOutsideRange)
                return null;

            var combTagValue = BuildCombTagValue(item, itemTag, rangeCheckResult.Hint);
            var diseaseMatch = FindMatchingDisease(combTagValue);

            itemTag.Hint = rangeCheckResult.Hint;

            return CreateOutsideRangeCombTag(combTagValue, diseaseMatch, itemTag);
        }

        /// <summary>
        /// 检查数值是否超出参考范围
        /// </summary>
        /// <param name="itemTag">项目标签</param>
        /// <param name="value">数值</param>
        /// <returns>范围检查结果</returns>
        private static (bool IsOutsideRange, string Hint) CheckValueRange(ItemTag itemTag, double value)
        {
            // 检查上限
            if (!string.IsNullOrEmpty(itemTag.UpperLimit) &&
                itemTag.UpperLimit.TryParseDouble(out var upperLimit) &&
                value > upperLimit)
            {
                return (true, "偏高");
            }

            // 检查下限
            if (!string.IsNullOrEmpty(itemTag.LowerLimit) &&
                itemTag.LowerLimit.TryParseDouble(out var lowerLimit) &&
                value < lowerLimit)
            {
                return (true, "偏低");
            }

            return (false, string.Empty);
        }

        /// <summary>
        /// 构建组合标签值
        /// </summary>
        /// <param name="item">项目信息</param>
        /// <param name="itemTag">项目标签</param>
        /// <param name="hint">提示信息</param>
        /// <returns>组合标签值</returns>
        private static string BuildCombTagValue(ItemRangeLimit item, ItemTag itemTag, string hint)
        {
            return $"{item.ItemName}{itemTag.Tag}{item.Unit}{hint}";
        }

        /// <summary>
        /// 查找匹配的疾病
        /// </summary>
        /// <param name="combTagValue">组合标签值</param>
        /// <returns>匹配的疾病，如果没有匹配则返回null</returns>
        private Model.DTO.ReportConclusionNew.Disease FindMatchingDisease(string combTagValue)
        {
            return _cacheRepository.DictDisease().Values
                .FirstOrDefault(x => combTagValue.Contains(x.DiseaseName));
        }

        /// <summary>
        /// 创建超出范围的疾病组合标签
        /// </summary>
        /// <param name="combTagValue">组合标签值</param>
        /// <param name="diseaseMatch">匹配的疾病</param>
        /// <param name="itemTag">项目标签</param>
        /// <returns>疾病组合标签</returns>
        private CombTag CreateOutsideRangeCombTag(string combTagValue, Model.DTO.ReportConclusionNew.Disease diseaseMatch, ItemTag itemTag)
        {
            return new CombTag
            {
                Id = _noGeneration.NextSnowflakeId(),
                Tag = combTagValue,
                DiseaseCode = diseaseMatch?.DiseaseCode,
                DiseaseName = diseaseMatch?.DiseaseName,
                AbnormalType = AbnormalType.异常,
                IsCustom = false,
                BindItemTags = new() { (long)itemTag.Id }
            };
        }

        /// <summary>
        /// 获取匹配疾病或词条关键字的疾病
        /// </summary>
        /// <param name="calcItemTags">参与计算的项目结果</param>
        /// <returns>疾病组合标签列表</returns>
        private List<CombTag> GetDiseaseByKeyword(List<ItemTag> calcItemTags)
        {
            var combTags = new List<CombTag>();

            try
            {
                var diseases = _cacheRepository.DictDisease().Values;
                var diseaseEntries = _diseaseRepository.GetAllDiseaseEntries();

                foreach (var itemTag in calcItemTags)
                {
                    // 匹配疾病名称
                    var matchedDiseases = diseases.Where(d => itemTag.Tag.Contains(d.DiseaseName) || d.DiseaseName.Contains(itemTag.Tag)).ToList();

                    foreach (var disease in matchedDiseases)
                    {
                        combTags.Add(new CombTag
                        {
                            Id = _noGeneration.NextSnowflakeId(),
                            Tag = disease.DiseaseName,
                            DiseaseCode = disease.DiseaseCode,
                            DiseaseName = disease.DiseaseName,
                            AbnormalType = AbnormalType.异常,
                            IsCustom = false,
                            BindItemTags = new List<long> { (long)itemTag.Id }
                        });
                    }

                    // 匹配疾病词条
                    var matchedEntries = diseaseEntries.Where(e => itemTag.Tag.Contains(e.EntryText) || e.EntryText.Contains(itemTag.Tag)).ToList();

                    foreach (var entry in matchedEntries)
                    {
                        var disease = diseases.FirstOrDefault(d => d.DiseaseCode == entry.DiseaseCode);
                        if (disease != null)
                        {
                            combTags.Add(new CombTag
                            {
                                Id = _noGeneration.NextSnowflakeId(),
                                Tag = disease.DiseaseName,
                                DiseaseCode = disease.DiseaseCode,
                                DiseaseName = disease.DiseaseName,
                                AbnormalType = AbnormalType.异常,
                                IsCustom = false,
                                BindItemTags = new List<long> { (long)itemTag.Id }
                            });
                        }
                    }
                }
            }
            catch (Exception)
            {
                // 记录错误日志
                // _logger.LogError(ex, "获取匹配疾病或词条关键字的疾病时发生错误");
            }

            return combTags.DistinctBy(x => new { x.DiseaseCode, FirstItemTag = x.BindItemTags.FirstOrDefault() }).ToList();
        }

        /// <summary>
        /// 格式化疾病显示指标文本
        /// </summary>
        /// <param name="combTag">疾病组合标签</param>
        /// <param name="itemDict">项目字典</param>
        /// <param name="itemTagDict">项目标签字典</param>
        private static void FomatDiseaseShowMetricsText(CombTag combTag, Dictionary<string, ItemRangeLimit> itemDict, Dictionary<long, ItemTag> itemTagDict)
        {
            try
            {
                if (combTag.BindItemTags == null || combTag.BindItemTags.Count == 0)
                    return;

                var itemTexts = new List<string>();

                foreach (var itemTagId in combTag.BindItemTags)
                {
                    if (itemTagDict.TryGetValue(itemTagId, out var itemTag) &&
                        itemDict.TryGetValue(itemTag.ItemCode, out var item))
                    {
                        var itemText = $"{item.ItemName}:{itemTag.Tag}";
                        if (!string.IsNullOrEmpty(item.Unit))
                            itemText += item.Unit;

                        if (!string.IsNullOrEmpty(itemTag.Hint))
                            itemText += $"({itemTag.Hint})";

                        itemTexts.Add(itemText);
                    }
                }

                if (itemTexts.Count > 0)
                {
                    combTag.Tag = $"{string.Join("，", itemTexts)}：{combTag.DiseaseName}";
                }
            }
            catch (Exception)
            {
                // 如果格式化失败，保持原有的 Tag
                // _logger.LogError(ex, "格式化疾病显示指标文本时发生错误");
                throw;
            }
        }

        #endregion
    }
}
