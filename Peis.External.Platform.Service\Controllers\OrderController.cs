﻿using Microsoft.AspNetCore.Mvc;
using Peis.External.Platform.Service.Service.IService.His;
using Peis.Model.DTO;

namespace Peis.External.Platform.Service.Controllers
{
    [Route("api/Shen<PERSON>han/[controller]")]
    [ApiController]
    public class OrderController : BaseApiController
    {
        private readonly IHisBillService _billService;
        private readonly IShenShanRegisterRepository _shenShanRegisterRepository;

        public OrderController(IHisBillService billService,
            IShenShanRegisterRepository shenShanRegisterRepository)
        {
            _billService = billService; ;
            _shenShanRegisterRepository = shenShanRegisterRepository;
        }

        /// <summary>
        /// 获取HIS索引号
        /// </summary>
        /// <param name="regNo"></param>
        /// <returns></returns>
        [HttpPost("GetHisPatientIndex")]
        public IActionResult GetHisPatientIndex([FromQuery] string regNo)
        {
            string msg = string.Empty;
            var hisCard = _shenShanRegisterRepository.GetHisCard(regNo);
            result.ReturnData = _billService.GetHisIndex(hisCard);
            return Ok(result);
        }

        /// <summary>
        /// 根据门诊卡号获取病人信息
        /// </summary>
        /// <param name="hisCard"></param>
        /// <returns></returns>
        [HttpPost("GetHistoryArchivesByHisCard")]
        [ProducesResponseType(typeof(HistoryArchives), 200)]

        public IActionResult GetHistoryArchivesByHisCard([FromQuery] string hisCard)
        {
            string msg = string.Empty;
            result.ReturnData = _billService.GetHistoryArchivesByHisCard(hisCard);
            return Ok(result);
        }
    }
}
