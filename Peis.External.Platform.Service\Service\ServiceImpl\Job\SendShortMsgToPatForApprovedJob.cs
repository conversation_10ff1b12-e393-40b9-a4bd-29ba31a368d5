﻿using Peis.Quartz.UI.BaseService;
using Peis.Quartz.UI.Consts;
using Peis.Quartz.UI.Enum;
using Peis.Quartz.UI.Model;
using Peis.Quartz.UI.Service;
using Peis.Quartz.UI.Tools;
using Peis.External.Platform.Service.Service.IService.Job;
using Peis.Model.Other.PeEnum;
using Peis.Repository.IRepository;
using Peis.Service.IService.ExternalSystem;
using CronHelper = Peis.Quartz.UI.Tools.CronHelper;

namespace Peis.External.Platform.Service.Service.ServiceImpl.Job;

/// <summary>
/// 报告审核24小时后发送短信通知体检者
/// </summary>
public class SendShortMsgToPatForApprovedJob : IJobService, ISendShortMsgToPatForApprovedJob
{
    const string TASKNAME = nameof(SendShortMsgToPatForApprovedJob);

    readonly IExternalSystemShortMessageService _shortMessageService;
    readonly IRegisterRepository _registerRepository;
    readonly IQuartzHandle _quartzHandle;
    readonly IQuartzService _quartzService;

    public SendShortMsgToPatForApprovedJob(IExternalSystemShortMessageService shortMessageService, IRegisterRepository registerRepository, IQuartzHandle quartzHandle, IQuartzService quartzService)
    {
        _shortMessageService = shortMessageService;
        _registerRepository = registerRepository;
        _quartzHandle = quartzHandle;
        _quartzService = quartzService;
    }

    public string ExecuteService(string regNo)
    {
        var reg = _registerRepository.ReadRegister(regNo).First();
        if (reg.IsNullOrEmpty())
            return "未找到患者信息！";

        if (reg.PeStatus != PeStatus.已审核)
            return "未审核！";

        if (reg.IsCompanyCheck)
            return "团检不需要！";

        string msg;
        var flag = _shortMessageService.SendShortMsgMB01(reg.RegNo, out msg);
        if (!flag) return msg;

        // 删除一次性任务
        RemoveJob(regNo).Wait();
        return msg;
    }

    /// <summary>
    /// 添加一次性任务
    /// </summary>
    /// <param name="regNo"></param>
    public async Task AddJob(string regNo)
    {
        var reg = _registerRepository.ReadRegister(regNo).First();
        if (reg.IsNullOrEmpty() || reg.PeStatus != PeStatus.已审核 || reg.IsCompanyCheck) return; // 团检不需要

        string taskName = $"{TASKNAME}_{regNo}";
        var nowTime = DateTime.Now;
        var taskModel = new QuartzTask
        {
            TaskName = taskName,
            TaskType = QuartzTaskType.Dll,
            GroupName = QuartzUIStatic.GroupOfOneTime,
            ApiParameter = regNo,
            DllClassName = TASKNAME,
            CreatedTime = nowTime,
            UpdatedTime = nowTime,
            Describe = "一次性：报告审核24小时后发送短信通知体检者",
            Interval = CronHelper.ConvertTimeToCron(nowTime.AddHours(24)),
            Status = QuartzTaskState.开启,
        };

        await _quartzHandle.AddJob(taskModel);
    }

    /// <summary>
    /// 删除一次性任务
    /// </summary>
    /// <param name="regNo"></param>
    public async Task RemoveJob(string regNo)
    {
        try
        {
            string taskName = $"{TASKNAME}_{regNo}";
            var taskModel = (await _quartzService
                .GetJobs(x => x.TaskName.Equals(taskName) && x.GroupName.Equals(QuartzUIStatic.GroupOfOneTime)))
                .FirstOrDefault();
            if (!taskModel.IsNullOrEmpty())
                await _quartzHandle.Remove(taskModel);
        }
        catch { }
    }
}
