﻿namespace Peis.Model.DataBaseEntity.External;

///<summary>
/// 基础数据：His收费项目(来源系统对接数据提取)
///</summary>
[SugarTable(nameof(CodeHisChargeItem), TableDescription = "His收费项目(来源系统对接数据提取)")]
[SugarIndex("index_CodeHisChargeItem_CNName", nameof(ChageItemCNName), OrderByType.Asc)]
public class CodeHisChargeItem
{
    /// <summary>
    /// 数据PId
    /// </summary>        
    [SugarColumn(IsPrimaryKey = true, Length = 64)]
    public string PId { get; set; }

    /// <summary>
    /// 标准版本PId
    /// </summary>        
    [SugarColumn(IsNullable = true, Length = 40)]
    public string StandardVersionPId { get; set; }

    /// <summary>
    /// 版本号
    /// </summary>    
    public int VersionNumber { get; set; }

    /// <summary>
    /// 收费项目Id
    /// </summary>        
    [SugarColumn(Length = 64)]
    public string ChargeItemId { get; set; }

    /// <summary>
    /// 收费项目Code
    /// </summary>        
    [SugarColumn(Length = 64)]
    public string ChargeItemCode { get; set; }

    /// <summary>
    /// 收费项目中文名称
    /// </summary>        
    [SugarColumn(ColumnDataType = "nvarchar(256)")]
    public string ChageItemCNName { get; set; }

    /// <summary>
    /// 收费项目英文名称
    /// </summary>        
    [SugarColumn(IsNullable = true, Length = 128)]
    public string ChageItemENName { get; set; }

    /// <summary>
    /// 项目备注
    /// </summary>
    [SugarColumn(IsNullable = true, ColumnDataType = "nvarchar(256)")]
    public string ChageItemDesc { get; set; }

    /// <summary>
    /// 收费项目规格
    /// </summary>
    [SugarColumn(IsNullable = true, ColumnDataType = "nvarchar(128)")]
    public string ItemSpec { get; set; }

    /// <summary>
    /// 收费单位
    /// </summary>
    [SugarColumn(IsNullable = true, ColumnDataType = "nvarchar(64)")]
    public string ItemUnit { get; set; }

    /// <summary>
    /// 基准单价
    /// </summary>
    [SugarColumn(Length = 12, DecimalDigits = 4)]
    public decimal BasePrice { get; set; }

    /// <summary>
    /// 物价编码
    /// </summary>        
    [SugarColumn(IsNullable = true, Length = 64)]
    public string ItemPriceCode { get; set; }

    /// <summary>
    /// 医保项目编码
    /// </summary>        
    [SugarColumn(IsNullable = true, Length = 64)]
    public string ItemMiCode { get; set; }

    /// <summary>
    /// 医保项目名称
    /// </summary>
    [SugarColumn(IsNullable = true, ColumnDataType = "nvarchar(128)")]
    public string ItemMiName { get; set; }

    /// <summary>
    /// 收费类型Id
    /// </summary>
    [SugarColumn(IsNullable = true, Length = 64)]
    public string ChargeTypeId { get; set; }

    /// <summary>
    /// 收费类型名称
    /// </summary>
    [SugarColumn(IsNullable = true, ColumnDataType = "nvarchar(128)")]
    public string ChargeTypeName { get; set; }

    /// <summary>
    /// 核算分类Id(门诊住院共用)
    /// </summary>
    [SugarColumn(IsNullable = true, Length = 64)]
    public string AccttypeId { get; set; }

    /// <summary>
    /// 核算分类名称
    /// </summary>
    [SugarColumn(IsNullable = true, ColumnDataType = "nvarchar(64)")]
    public string AccttypeName { get; set; }

    /// <summary>
    /// 描述
    /// </summary>
    [SugarColumn(IsNullable = true, ColumnDataType = "nvarchar(256)")]
    public string Description { get; set; }

    /// <summary>
    /// 是否可用(Y:可用 N:不可用)
    /// </summary>
    [SugarColumn(IsNullable = false, Length = 1)]
    public string IsAvailable { get; set; }

    /// <summary>
    /// 是否删除(Y:是 N:否)
    /// </summary>
    [SugarColumn(IsNullable = false, Length = 1)]
    public string IsDeleted { get; set; }

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreatedTime { get; set; }

    /// <summary>
    /// 修改时间
    /// </summary>        
    [SugarColumn(IsNullable = true)]
    public DateTime? UpdatedTime { get; set; }
}