﻿using Peis.Model.Other.PeEnum;
using Peis.Model.TableFilter;
using SqlSugar;
using System;

namespace Peis.Model.DataBaseEntity
{
    ///<summary>
    ///发票入库
    ///</summary>
    [SugarTable("FeeInvoiceWarehouse")]
    public class FeeInvoiceWarehouse: IHospCodeFilter
    {
        /// <summary>
        /// Id
        /// </summary>        
        [SugarColumn(IsNullable = false, IsPrimaryKey = true, IsIdentity = true)]
        public int Id { get; set; }

        /// <summary>
        /// 发票前缀
        /// </summary>        
        [SugarColumn(IsNullable = false, Length = 2)]
        public string Prefix { get; set; }

        /// <summary>
        /// 发票起始号
        /// </summary>        
        [SugarColumn(IsNullable = false)]
        public int StartNo { get; set; }

        /// <summary>
        /// 发票结束号
        /// </summary>        
        [SugarColumn(IsNullable = false)]
        public int EndNo { get; set; }

        /// <summary>
        /// 采购员
        /// </summary>        
        [SugarColumn(IsNullable = false, Length = 10)]
        public string Buyer { get; set; }

        /// <summary>
        /// 采购时间
        /// </summary>        
        [SugarColumn(IsNullable = false)]
        public DateTime BuyDate { get; set; }

        /// <summary>
        /// 录入系统员
        /// </summary>        
        [SugarColumn(IsNullable = false, Length = 10)]
        public string ImportOperator { get; set; }

        /// <summary>
        /// 录入系统时间
        /// </summary>        
        [SugarColumn(IsNullable = false)]
        public DateTime ImportTime { get; set; }

        /// <summary>
        /// 生效日期
        /// </summary>        
        [SugarColumn(IsNullable = false)]
        public DateTime EffectiveDate { get; set; }

        /// <summary>
        /// 失效日期
        /// </summary>        
        [SugarColumn(IsNullable = false)]
        public DateTime ExpiryDate { get; set; }

        /// <summary>
        /// 发票状态(1 未用、2 在用、3 用完)
        /// </summary>        
        [SugarColumn(IsNullable = false)]
        public InvoiceStatus Status { get; set; }

        /// <summary>
        /// 院区编码
        /// </summary>        
        [SugarColumn(IsNullable = false, Length = 1)]
        public string HospCode { get; set; }
    }
}