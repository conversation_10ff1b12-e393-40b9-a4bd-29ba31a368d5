﻿using SqlSugar;

namespace Peis.Model.DataBaseEntity
{
    ///<summary>
    ///疾病分类对应疾病
    ///</summary>
    [SugarTable("MapDiseaseClsDisease")]
    public class MapDiseaseClsDisease
    {
        /// <summary>
        /// 疾病分类代码
        /// </summary>        
        [SugarColumn(IsNullable = false, IsPrimaryKey = true, Length = 6)]
        public string DiseaseClsCode { get; set; }

        /// <summary>
        /// 疾病代码
        /// </summary>        
        [SugarColumn(IsNullable = false, IsPrimaryKey = true, Length = 8)]
        public string DiseaseCode { get; set; }
    }
}