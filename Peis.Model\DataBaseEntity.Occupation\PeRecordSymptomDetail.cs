﻿namespace Peis.Model.DataBaseEntity.Occupation;

/// <summary>
/// 症状询问结果表明细
/// </summary>
public class PeRecordSymptomDetail
{
    /// <summary>
    /// 体检号
    /// </summary>
    [SugarColumn(Length = 12, IsPrimaryKey = true)]
    public string RegNo { set; get; }
    /// <summary>
    /// 症状代码
    /// </summary>
    [SugarColumn(Length = 50, IsPrimaryKey = true)]
    public string SymptomCode { set; get; }
    /// <summary>
    /// 结果
    /// </summary>
    [SugarColumn(Length = 50, IsNullable = true)]
    public string Result { set; get; }
}
