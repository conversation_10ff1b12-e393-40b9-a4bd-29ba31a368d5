﻿using Microsoft.Extensions.Logging;
using Peis.External.Platform.Service.Service.IService.BaiHui;
using Peis.Model.DTO.External.Report;
using Peis.Repository.IRepository;
using Peis.Service.IService;
using Peis.Service.IService.ExternalSystem;

namespace Peis.External.Platform.Service.Service.ServiceImpl.Report;

/// <summary>
/// 报告文件接口服务
/// </summary>
public class ExternalSystemReportFileService : IExternalSystemReportFileService
{
    readonly ISplitTableRepository<PeReportFile> _peReportFileRepository;
    readonly IReportFileRepository _reportFileRepository;
    readonly IRegisterRepository _registerRepository;
    readonly IBHBasicService _bhBasicService;
    readonly ILogger<ExternalSystemReportFileService> _logger;
    readonly ISystemParameterService _systemParameterService;

    public ExternalSystemReportFileService(
        ISplitTableRepository<PeReportFile> peReportFileRepository,
        IReportFileRepository reportFileRepository,
        IRegisterRepository registerRepository,
        IBHBasicService bhBasicService,
        ILogger<ExternalSystemReportFileService> logger,
        ISystemParameterService systemParameterService)
    {
        _peReportFileRepository = peReportFileRepository;
        _reportFileRepository = reportFileRepository;
        _registerRepository = registerRepository;
        _bhBasicService = bhBasicService;
        _logger = logger;
        _systemParameterService = systemParameterService;  
    }

    /// <summary>
    /// 同步总检报告至平台
    /// </summary>
    /// <param name="regNo">体检号，为空时，全部同步</param>
    /// <param name="msg">消息</param>
    /// <returns>bool</returns>
    public bool SyncPeReport(string regNo, out string msg)
    {
        var defaultExamReportWx = _systemParameterService.DefaultExamReportWx;
        int count = 0;
        var reportFiles = _reportFileRepository
            .QueryPeReportFiles(regNo)
            .Where(x => x.ReportType.Equals(defaultExamReportWx))
            .Where(x => x.SyncPEReportTimes.Equals(0))
            .Where(a => a.IsWithStamp)
            .ToList();
        foreach (var report in reportFiles)
        {
            var reg = _registerRepository.ReadRegister(report.RegNo).First();
            if (reg.IsNullOrEmpty())
                continue;

            var flag = SynchPEReportInfo(new PeReport(reg, report), out _);
            if (flag)
                count++;
        }

        msg = string.Format("共获取未同步报告{0}份，同步成功{1}份！", reportFiles.Count, count);
        return true;
    }

    #region 本地私有方法

    /// <summary>
    /// 体检报告同步至平台
    /// </summary>
    /// <param name="syncReport">体检号</param>
    /// <param name="msg">消息</param>
    /// <returns>bool</returns>
    bool SynchPEReportInfo(PeReport syncReport, out string msg)
    {
        try
        {
            // 同步平台
            syncReport.NotNullAndEmpty(nameof(syncReport));
            var bhRequestMsg = new BHRequestMsg(ResxExternal.BHApiSynchPEReportInfo, syncReport.ToJson(), false);
            var syncRes = _bhBasicService.PostMsgTransfer<string>(bhRequestMsg);
            var resData = JsonHelper.ToObject<ReportResData>(syncRes.SyncResult);
            if (resData.IsNullOrEmpty() || !resData.Data.IsSuccess)
            {
                if (!resData.IsNullOrEmpty())
                    _logger.LogError(resData.Data.Note);

                msg = "总检报告同步失败！";
                return false;
            }

            // 修改文件同步次数
            var reportFile = _reportFileRepository.QueryPeReportFiles(syncReport.PeSeqNo)
                .First(x => x.Id.Equals(syncReport.PeReportFileId));
            if (reportFile.IsNullOrEmpty())
            {
                msg = ResxCommon.Success;
                return true;
            }

            reportFile.SetSyncPEReportTimes();
            _peReportFileRepository.SplitTableUpdate(reportFile);
            msg = ResxCommon.Success;
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogException(ex);
            msg = ResxCommon.Fail;
            return false;
        }
    }

    #endregion
}
