﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Peis.Model.DataBaseEntity;
using Peis.Model.DTO;
using Peis.Model.Other;
using Peis.Repository.Repository.TransactionAttribute;
using Peis.Service.IService;
using Peis.Service.IService.Helper;
using Peis.Utility.CustomAttribute;
using Peis.Utility.Helper;
using Peis.Utility.PeUser;
using System.Collections.Generic;

namespace Peis.API.Controllers.PermissionSetting
{
    /// <summary>
    /// 用户角色权限
    /// </summary>
    [Route("api/[controller]")]
    [ApiController]
    public class UserManageController : BaseApiController
    {
        private readonly ISystemSettingService _systemSettingService;
        private readonly IPermissionService _permissionService;
        private readonly IHttpContextUser _httpContextUser;
        private readonly INoGeneration _noGeneration;

        public UserManageController(
            ISystemSettingService systemSettingService,
            IPermissionService permissionService,
            IHttpContextUser httpContextUser,
            INoGeneration noGeneration)
        {
            _systemSettingService = systemSettingService;
            _permissionService = permissionService;
            _httpContextUser = httpContextUser;
            _noGeneration = noGeneration;
        }

        #region 用户
        /// <summary>
        /// 获取院区列表
        /// </summary>
        /// <returns></returns>
        [HttpPost("GetHospitalInfo")]
        [AllowAnonymous]
        public IActionResult GetHospitalInfo()
        {
            result.ReturnData = _permissionService.GetHospitalInfo();
            return Ok(result);
        }

        /// <summary>
        /// 前端登录
        /// </summary>
        /// <param name="user"></param>
        /// <returns></returns>
        [HttpPost("Login")]
        [AllowAnonymous]
        public IActionResult Login([FromBody] UserInfo user)
        {
            var errMsg = "";            
            result.ReturnData = _permissionService.Login(user,ref errMsg);
            if (result.ReturnData.IsNullOrEmpty())
            {
                result.Success   = false;
                result.ReturnMsg = errMsg;
            }

            return Ok(result);
        }

        /// <summary>
        /// 刷新token
        /// </summary>
        /// <param name="token">原token</param>
        /// <returns></returns>
        [HttpPost("RefreshToken/{token}")]
        [AllowAnonymous]
        public IActionResult RefreshToken([Regex(ErrorMessage = "[token]-不能为空！")]string token)
        {
            result.ReturnData = _permissionService.RefreshToken(token, out var success,out var msg
                );
            result.Success = success;
            result.ReturnMsg = msg;

            return Ok(result);
        }

        /// <summary>
        /// 修改用户密码
        /// </summary>
        /// <param name="operatorCode">用户账号</param>
        /// <param name="oldPassword">旧密码</param>
        /// <param name="newPassword">新密码</param>
        /// <returns></returns>
        [HttpPost("ChangeUserPassword")]
        public IActionResult ChangeUserPassword([FromQuery] string operatorCode, [FromQuery] string oldPassword, [FromQuery] string newPassword)
        {
            var errMsg       = "";
            result.Success   = _permissionService.ChangeUserPassword(operatorCode, oldPassword, newPassword, ref errMsg);
            result.ReturnMsg = errMsg;
            return Ok(result);
        }

        /// <summary>
        /// 获取用户菜单权限树
        /// </summary>
        /// <param name="operatorCode"></param>
        /// <returns></returns>
        [HttpPost("GetUserMenus")]
        public IActionResult GetUserMenus([FromQuery] string operatorCode)
        {
            var menu = _permissionService.GetUserMenus(operatorCode);
            result.ReturnData = menu;
            return Ok(result);
        }

        /// <summary>
        /// 获取操作员折扣
        /// </summary>
        /// <param name="operatorCode"></param>
        /// <param name="password"></param>
        /// <response code="200">新建时返回体检号，修改时返回true/false</response>
        /// <returns></returns>
        [HttpPost("GetOperatorDiscount")]
        [ProducesResponseType(typeof(SysOperator), 200)]
        public IActionResult GetOperatorDiscount([FromQuery] string operatorCode, [FromQuery] string password)
        {
            var errMsg = "";
            var user = new UserInfo() { OperatorCode = operatorCode, Password = password, HospCode = _httpContextUser.HospCode };
            var codeOper = _permissionService.VerifyAccount(user, ref errMsg);
            if (codeOper == null)
            {
                result.Success = false;
                result.ReturnMsg = errMsg;
                return Ok(result);
            }
            codeOper.Password = "";
            result.ReturnData = codeOper;
            return Ok(result);
        }
        #endregion

        #region 维护操作员
        /// <summary>
        /// 获取操作员列表
        /// </summary>
        /// <returns></returns>
        [HttpPost("GetAllOperator")]
        [ProducesResponseType(typeof(SysOperatorPlus[]), 200)]
        public IActionResult GetAllOperator()
        {
            var operList = _systemSettingService.GetAllOperator();
            result.ReturnData = operList;
            return Ok(result);
        }

        /// <summary>
        /// 添加操作员信息
        /// </summary>
        /// <param name="roleCode"></param>
        /// <param name="sysOperator"></param>
        /// <returns></returns>
        [HttpPost("AddOperator")]
        [UnitOfWork]
        public IActionResult AddOperator([FromQuery] string roleCode, [FromBody] SysOperator sysOperator)
        {
            string errMsg = string.Empty;
            string[] roles = roleCode.Split(',');
            var codeOper = _systemSettingService.AddOperator(roles, sysOperator, ref errMsg);
            if (codeOper == null)
            {
                result.Success = false;
                result.ReturnMsg = errMsg;
                return Ok(result);
            }
            result.ReturnData = codeOper;
            return Ok(result);
        }

        /// <summary>
        /// 修改操作员信息
        /// </summary>
        /// <param name="roleCode"></param>
        /// <param name="sysOperator"></param>
        /// <returns></returns>
        [HttpPost("UpdateOperator")]
        [UnitOfWork]

        public IActionResult UpdateOperator([FromQuery] string roleCode, [FromBody] SysOperator sysOperator)
        {
            string[] roles = roleCode.Split(',');
            var codeOper   = _systemSettingService.UpdateOperator(roles, sysOperator);
            if (codeOper == null)
            {
                result.Success = false;
                result.ReturnMsg = "修改信息失败";
                return Ok(result);
            }
            result.ReturnData = codeOper;
            return Ok(result);
        }

        /// <summary>
        /// 根据id集合批量删除操作员
        /// </summary>
        /// <param name="operatorCode">示例：["operator_code","operator_code"]</param>
        /// <returns></returns>
        [HttpPost("DeleteOperators")]
        [UnitOfWork]
        public IActionResult DeleteOperators([FromQuery] string operatorCode)
        {
            string[] oper = operatorCode.Split(',');
            bool isDelete = _systemSettingService.DeleteOperators(oper);
            if (!isDelete)
            {
                result.Success = false;
                result.ReturnMsg = "数据已被删除,勿重复操作!";
                return Ok(result);
            }
            return Ok(result);
        }

        /// <summary>
        /// 根据操作员编码获取有哪些角色
        /// </summary>
        /// <param name="operatorCode"></param>
        /// <returns></returns>
        [HttpPost("GetUserRole")]
        public IActionResult GetUserRole([FromQuery] string operatorCode)
        {
            result.ReturnData = _systemSettingService.GetUserRole(operatorCode);
            return Ok(result);
        }
        #endregion

        #region 维护角色
        /// <summary>
        /// 获取角色列表
        /// </summary>
        /// <returns></returns>
        [HttpPost("GetAllRole")]
        public IActionResult GetAllRole()
        {
            result.ReturnData = _systemSettingService.GetAllRole();
            return Ok(result);
        }

        /// <summary>
        /// 获取角色权限树
        /// </summary>
        /// <param name="roleCode"></param>
        /// <returns></returns>
        [HttpPost("GetRoleMenu")]
        public IActionResult GetRoleMenu([FromQuery] string roleCode)
        {
            result.ReturnData = _systemSettingService.GetRoleMenu(roleCode);
            return Ok(result);
        }

        /// <summary>
        /// 设置角色权限
        /// </summary>
        /// <param name="roleCode"></param>
        /// <param name="sysRoleFuncs"></param>
        /// <returns></returns>
        [HttpPost("SetRolePermissions")]
        public IActionResult SetRolePermissions([FromQuery] string roleCode, [FromBody] List<SysRoleFunc> sysRoleFuncs)
        {
            _systemSettingService.SetRolePermissions(roleCode, sysRoleFuncs);
            return Ok(result);
        }

        /// <summary>
        /// 添加角色信息
        /// </summary>
        /// <param name="sysRole"></param>
        /// <returns></returns>
        [HttpPost("AddRole")]
        public IActionResult AddRole([FromBody] SysRole sysRole)
        {
            string errMsg    = string.Empty;
            sysRole.HospCode = _httpContextUser.HospCode;
            sysRole.RoleCode = _noGeneration.NextRoleNo(_httpContextUser.HospCode);
            var codeRole     = _systemSettingService.AddRole(sysRole, ref errMsg);
            if (codeRole == null)
            {
                result.Success = false;
                result.ReturnMsg = errMsg;
                return Ok(result);
            }
            result.ReturnData = codeRole;
            return Ok(result);
        }

        /// <summary>
        /// 修改角色信息
        /// </summary>
        /// <param name="sysRole"></param>
        /// <returns></returns>
        [HttpPost("UpdateRole")]
        public IActionResult UpdateRole([FromBody] SysRole sysRole)
        {
            var codeRole = _systemSettingService.UpdateRole(sysRole);
            if (codeRole == null)
            {
                result.Success = false;
                result.ReturnMsg = "修改角色失败";
                return Ok(result);
            }
            result.ReturnData = codeRole;
            return Ok(result);
        }

        /// <summary>
        /// 根据id集合批量删除角色信息
        /// </summary>
        /// <param name="roleCode">示例：["role_code","role_code"]</param>
        /// <returns></returns>
        [HttpPost("DeleteRoles")]
        public IActionResult DeleteRoles([FromQuery] string roleCode)
        {
            string[] roles = roleCode.Split(',');
            bool isDelete  = _systemSettingService.DeleteRoles(roles);
            if (!isDelete)
            {
                result.Success = false;
                result.ReturnMsg = "数据已被删除,勿重复操作!";
                return Ok(result);
            }
            return Ok(result);
        }
        #endregion
    }
}
