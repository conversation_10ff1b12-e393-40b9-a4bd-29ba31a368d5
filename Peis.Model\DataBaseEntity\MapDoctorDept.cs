﻿using SqlSugar;

namespace Peis.Model.DataBaseEntity
{
    ///<summary>
    ///医生科室关联表(医生跨科关系)
    ///</summary>
    [SugarTable("MapDoctorDept")]
    public class MapDoctorDept
    {
        /// <summary>
        /// 医生代码(操作员编码)
        /// </summary>        
        [SugarColumn(IsNullable = false, IsPrimaryKey = true, Length = 10)]
        public string OperatorCode { get; set; }

        /// <summary>
        /// 科室编码
        /// </summary>        
        [SugarColumn(IsNullable = false, IsPrimaryKey = true, Length = 6)]
        public string DeptCode { get; set; }
    }
}