﻿using SqlSugar;

namespace Peis.Model.DataBaseEntity
{
    ///<summary>
    ///套餐与组合互斥信息
    ///</summary>
    [SugarTable("CodeClusMutexComb")]
    public class CodeClusMutexComb
    {
        /// <summary>
        /// 套餐编码
        /// </summary>        
        [SugarColumn(IsNullable = false, IsPrimaryKey = true, Length = 6)]
        public string ClusCode { get; set; }

        /// <summary>
        /// 组合编码
        /// </summary>        
        [SugarColumn(IsNullable = false, IsPrimaryKey = true, Length = 8)]
        public string CombCode { get; set; }
    }
}