﻿using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Peis.Model.DataBaseEntity;
using Peis.Model.DTO;
using Peis.Model.DTO.CriticalValue;
using Peis.Model.DTO.Disease;
using Peis.Model.DTO.DoctorStation;
using Peis.Model.DTO.External.Report;
using Peis.Model.DTO.Record;
using Peis.Model.DTO.ReportGraphicText;
using Peis.Model.Other.Input;
using Peis.Model.Other.Input.DoctorStation;
using Peis.Service.IService;
using Peis.Service.IService.ExternalSystem;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Peis.API.Controllers.Business
{
    /// <summary>
    /// 结果录入/医生工作站
    /// </summary>
    [Route("api/[controller]")]
    [ApiController]
    public class RecordController : BaseApiController
    {
        private readonly IRecordService _recordService;
        private readonly ICriticalValueService _criticalValueService;
        private readonly IExternalSystemReportService _externalSystemReportService;
        private readonly IReportLogService _reportLogService;

        public RecordController(
            IRecordService recordService,
            ICriticalValueService criticalValueService,
            IExternalSystemReportService externalSystemReportService,
            IReportLogService reportLogService)
        {
            _recordService = recordService;
            _criticalValueService = criticalValueService;
            _externalSystemReportService = externalSystemReportService;
            _reportLogService = reportLogService;
        }

        #region pacs图像处理
        /// <summary>
        /// 获取pacs科室信息
        /// </summary>
        /// <returns>科室信息（系统参数）</returns>
        [HttpPost("GetPacsDept")]
        [ProducesResponseType(typeof(CodeSystemParameter), 200)]
        public IActionResult GetPacsDept()
        {
            var msg = string.Empty;

            result.ReturnData = _recordService.GetPacsDept();
            result.ReturnMsg = msg;
            return Ok(result);
        }

        /// <summary>
        /// 获取pacs组合
        /// </summary>
        /// <param name="regNo">体检号</param>
        /// <param name="deptCode">科室代码</param>
        /// <returns>体检者的pacs组合信息</returns>
        [HttpPost("GetPacsComb")]
        [ProducesResponseType(typeof(List<PacsComb>), 200)]
        public IActionResult GetPacsComb(string regNo, string deptCode)
        {
            var msg = string.Empty;

            result.ReturnData = _recordService.GetPacsComb(regNo, deptCode);
            result.ReturnMsg = msg;
            return Ok(result);
        }

        /// <summary>
        /// 保存采集的pacs图像
        /// </summary>
        /// <param name="files"></param>
        /// <param name="operatorCode">采集人代码</param>
        /// <param name="regNo">体检号</param>
        /// <param name="deptCode">科室代码</param>
        /// <param name="regCombId">登记组合id（传空代表按体检者采集的）</param>
        /// <returns>图像路径列表</returns>
        [HttpPost("SaveCollectedPacsImage")]
        [ProducesResponseType(typeof(List<string>), 200)]
        public IActionResult SaveCollectedPacsImage([FromForm(Name = "files")] IFormFileCollection files, [FromQuery] string operatorCode, [FromQuery] string regNo, [FromQuery] string deptCode, [FromQuery] long? regCombId)
        {
            var msg = string.Empty;

            result.ReturnData = _recordService.SaveCollectedPacsImage(files, operatorCode, regNo, deptCode, regCombId, ref msg);
            result.ReturnMsg = msg;
            return Ok(result);
        }

        /// <summary>
        /// 获取采集的pacs图像
        /// </summary>
        /// <param name="regNo">体检号</param>
        /// <param name="deptCode">科室代码</param>
        /// <param name="regCombId">登记组合id（传空代表获取所有采集的）</param>
        /// <returns>图像采集记录列表</returns>
        [HttpPost("GetCollectedPacsImage")]
        [ProducesResponseType(typeof(PeImageCollect[]), 200)]
        public IActionResult GetCollectedPacsImage(string regNo, string deptCode, long? regCombId)
        {
            result.ReturnData = _recordService.GetCollectedPacsImage(regNo, deptCode, regCombId);
            return Ok(result);
        }

        /// <summary>
        /// 获取结果图像
        /// </summary>
        /// <param name="regNo"></param>
        /// <param name="regCombId">登记组合Id不能为空</param>
        /// <returns></returns>
        [HttpPost("GetRecordImage")]
        public IActionResult GetRecordImage(string regNo, long regCombId)
        {
            result.ReturnData = _recordService.GetRecordImage(regNo, regCombId);
            return Ok(result);
        }

        /// <summary>
        /// 获取所有结果图像
        /// </summary>
        /// <param name="regNo"></param>
        /// <returns></returns>
        [HttpPost("GetAllRecordImage")]
        [ProducesResponseType(typeof(List<ReportGraphicTextFileInfo>), 200)]
        public IActionResult GetAllRecordImage(string regNo)
        {
            result.ReturnData = _recordService.GetReportGraphicTextList(regNo);
            return Ok(result);
        }

        /// <summary>
        /// 删除采集的pacs图像
        /// </summary>
        /// <param name="operatorCode">操作员代码</param>
        /// <param name="regNo">体检号</param>
        /// <param name="collectIds">采集Id</param>
        /// <returns></returns>
        [HttpPost("DeleteCollectedPacsImage")]
        public IActionResult DeleteCollectedPacsImage(string operatorCode, string regNo, long[] collectIds)
        {
            var msg = string.Empty;

            result.Success = _recordService.DeleteCollectedPacsImage(operatorCode, regNo, collectIds, ref msg);
            return Ok(result);
        }

        /// <summary>
        /// 选中采集的pacs图像指定组合
        /// </summary>
        /// <param name="operatorCode">操作员代码</param>
        /// <param name="regNo">体检号</param>
        /// <param name="regCombId">登记组合id</param>
        /// <param name="collectIds">采集id</param>
        /// <returns></returns>
        [HttpPost("CheckCollectedPacsImage4Comb")]
        public IActionResult CheckCollectedPacsImage4Comb(string operatorCode, string regNo, long regCombId, long[] collectIds)
        {
            var msg = string.Empty;

            result.Success = _recordService.CheckCollectedPacsImage4Comb(operatorCode, regNo, regCombId, collectIds, ref msg);
            result.ReturnMsg = msg;
            return Ok(result);
        }

        /// <summary>
        /// 组合取消选中采集的pacs图像
        /// </summary>
        /// <param name="operatorCode">操作员代码</param>
        /// <param name="collectIds">图像采集id</param>
        /// <returns></returns>
        [HttpPost("UncheckCollectedPacsImage4Comb")]
        public IActionResult UncheckCollectedPacsImage4Comb(string operatorCode, long[] collectIds)
        {
            var msg = string.Empty;

            result.Success = _recordService.UncheckCollectedPacsImage4Comb(operatorCode, collectIds, ref msg);
            result.ReturnMsg = msg;

            return Ok(result);
        }

        /// <summary>
        /// 选中组合中的采集的pacs图像用于报告打印
        /// </summary>
        /// <param name="operatorCode">操作员代码</param>
        /// <param name="collectIds">图像采集id</param>
        /// <returns></returns>
        [HttpPost("CheckPacsImage2Print")]
        public IActionResult CheckPacsImage2Print(string operatorCode, long[] collectIds)
        {
            var msg = string.Empty;

            result.Success = _recordService.CheckPacsImage2Print(operatorCode, collectIds, ref msg);
            result.ReturnMsg = msg;

            return Ok(result);
        }

        /// <summary>
        /// 取消选中组合中的采集的pacs图像用于报告打印
        /// </summary>
        /// <param name="operatorCode">操作员代码</param>
        /// <param name="recImageIds">图像记录id</param>
        /// <returns></returns>
        [HttpPost("UncheckPacsImage2Print")]
        public IActionResult UncheckPacsImage2Print(string operatorCode, long[] recImageIds)
        {
            var msg = string.Empty;

            result.Success = _recordService.UncheckPacsImage2Print(operatorCode, recImageIds, ref msg);
            result.ReturnMsg = msg;

            return Ok(result);
        }

        /// <summary>
        /// 获取用于报告打印的pacs图像
        /// </summary>
        /// <param name="regNo">体检号</param>
        /// <param name="regCombId">登记组合id</param>
        /// <returns>图像路径列表</returns>
        [HttpPost("GetPacsImage2Print")]
        [ProducesResponseType(typeof(List<string>), 200)]
        public IActionResult GetPacsImage2Print(string regNo, long regCombId)
        {
            result.ReturnData = _recordService.GetPacsImage2Print(regNo, regCombId);
            return Ok(result);
        }
        #endregion

        #region 按医生过滤
        /// <summary>
        /// 录入结果人员列表(医生工作站)
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        [HttpPost("GetPatientListByDoctor")]
        [ProducesResponseType(typeof(RecordResultPerson[]), 200)]
        public IActionResult GetPatientListByDoctor([FromBody] RecordResultPersonQuery query)
        {
            result.ReturnData = _recordService.RecordResultPerson(true, query);
            return Ok(result);
        }

        /// <summary>
        /// 获取项目导航(医生工作站)
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        [HttpPost("GetExamItemsByDoctor")]
        [ProducesResponseType(typeof(CombNavigation), 200)]
        public IActionResult GetExamItemsByDoctor([FromBody] ItemNavigateQuery query)
        {
            string msg = string.Empty;
            result.ReturnData = _recordService.GetCombNavigation(true, query, ref msg);
            result.ReturnMsg = msg;
            return Ok(result);
        }
        #endregion

        /// <summary>
        /// 手动接受检验、检查结果
        /// </summary>
        /// <param name="regNo"></param>
        /// <returns></returns>
        [HttpPost("ManuallyAcceptResults")]
        public IActionResult ManuallyAcceptResults([FromQuery] string regNo)
        {
            _externalSystemReportService.SyncReport(regNo);
            return Ok(result);
        }

        /// <summary>
        /// 录入结果人员列表(结果录入)
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        [HttpPost("GetPatientList")]
        [ProducesResponseType(typeof(RecordResultPerson[]), 200)]
        public IActionResult GetPatientList([FromBody] RecordResultPersonQuery query)
        {
            result.ReturnData = _recordService.RecordResultPerson(false, query);
            return Ok(result);
        }

        /// <summary>
        /// 获取检查组合导航列表（医生工作站、结果录入）
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        [HttpPost("GetExamCombList")]
        public IActionResult GetExamCombList([FromBody] ExamCombQuery query)
        {
            result.ReturnData = _recordService.GetExamCombList(query);
            return Ok(result);
        }

        /// <summary>
        /// 获取项目导航(结果录入)
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        [HttpPost("GetExamItems")]
        [ProducesResponseType(typeof(CombNavigation), 200)]
        public IActionResult GetExamItems([FromBody] ItemNavigateQuery query)
        {
            string msg = string.Empty;
            result.ReturnData = _recordService.GetCombNavigation(false, query, ref msg);
            result.ReturnMsg = msg;
            return Ok(result);
        }

        /// <summary>
        /// 获取简易项目结果(主检用到)
        /// </summary>
        /// <param name="regNo"></param>
        /// <returns></returns>
        [HttpPost("GetSimpleItemResult")]
        [ProducesResponseType(typeof(AllCombAndItem[]), 200)]
        public IActionResult GetSimpleItemResult([FromQuery] string regNo)
        {
            string msg = string.Empty;
            result.ReturnData = _recordService.GetSimpleItemResult(regNo, ref msg);
            result.ReturnMsg = msg;
            return Ok(result);
        }

        /// <summary>
        /// 获取历史报告
        /// </summary>
        /// <param name="regNo">体检号</param>
        /// <returns></returns>
        [HttpPost("GetHistoryReport")]
        [ProducesResponseType(typeof(HistoryReport), 200)]
        public IActionResult GetHistoryReport(string regNo)
        {
            result.ReturnData = _recordService.GetHistoryReport(regNo);
            return Ok(result);
        }

        /// <summary>
        /// 获取组合项目结果记录
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        [HttpPost("ReadRecordComb")]
        [ProducesResponseType(typeof(RecordCombData), 200)]
        public IActionResult ReadRecordComb([FromBody] RecordCombQuery query)
        {
            result.ReturnData = _recordService.ReadRecordComb(query);
            return Ok(result);
        }

        /// <summary>
        /// 保存组合项目结果
        /// </summary>
        /// <returns></returns>
        [HttpPost("SaveRecordComb")]
        public IActionResult SaveRecordComb([FromBody] RecordCombData record)
        {
            _recordService.SaveRecordComb(record);
            return Ok(result);
        }

        /// <summary>
        /// 删除组合项目结果记录
        /// </summary>
        /// <param name="record"></param>
        /// <returns></returns>
        [HttpPost("DeleteRecordComb")]
        public IActionResult DeleteRecordComb([FromBody] DelRecordComb record)
        {
            _recordService.DeleteRecordCombAndImage(record);
            return Ok(result);
        }

        /// <summary>
        /// 按组合获取定义的项目结果及其疾病，用于医生工作站录入项目结果时选择
        /// </summary>
        /// <param name="combCode">组合代码</param>
        /// <returns></returns>
        [HttpPost("GetDefaultItemResultByComb")]
        [ProducesResponseType(typeof(List<DefaultItemResultDisease>), 200)]
        public IActionResult GetDefaultItemResultByComb(string combCode)
        {
            result.ReturnData = _recordService.GetDefaultItemResultByComb(combCode);
            return Ok(result);
        }

        /// <summary>
        /// 按项目获取定义的项目结果及其疾病，用于医生工作站录入项目结果时选择
        /// </summary>
        /// <param name="itemCode">项目代码</param>
        /// <returns></returns>
        [HttpPost("GetDefaultItemResultByItem")]
        [ProducesResponseType(typeof(DefaultItemResultDisease), 200)]
        public IActionResult GetDefaultItemResultByItem(string itemCode)
        {
            result.ReturnData = _recordService.GetDefaultItemResultByItem(itemCode);
            return Ok(result);
        }

        /// <summary>
        /// 获取项目历史结果
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        [HttpPost("GetItemHistoryResult")]
        [ProducesResponseType(typeof(ItemHistoryResult[]), 200)]
        public IActionResult GetItemHistoryResult([FromBody] ItemHistoryQuery query)
        {
            result.ReturnData = _recordService.GetItemHistoryResult(query);
            return Ok(result);
        }

        /// <summary>
        /// 编辑项目标签
        /// </summary>
        /// <param name="editItem"></param>
        /// <returns></returns>
        [HttpPost("EditItemTag")]
        [ProducesResponseType(typeof(EditItemTagCallback[]), 200)]
        public IActionResult EditItemTag([FromBody] EditItemTag editItem)
        {
            result.ReturnData = _recordService.EditItemTag(editItem);
            return Ok(result);
        }

        /// <summary>
        /// 弃检组合
        /// </summary>
        /// <param name="abandonComb"></param>
        /// <returns></returns>
        [HttpPost("AbandonComb")]
        public IActionResult AbandonComb([FromBody] AbandonCombData abandonComb)
        {
            string msg = string.Empty;
            result.Success = _recordService.AbandonComb(abandonComb, ref msg);
            result.ReturnMsg = msg;
            return Ok(result);
        }

        #region 电话联系记录
        /// <summary>
        /// 获取电话联系记录
        /// </summary>
        /// <param name="regNo">体检号:{"regNo":"220924000001"}</param>
        /// <returns></returns>
        [HttpPost("ReadContactRecord")]
        [ProducesResponseType(typeof(RecordContact[]), 200)]
        public IActionResult ReadContactRecord([FromQuery] string regNo)
        {
            result.ReturnData = _recordService.ReadContactRecord(regNo);
            return Ok(result);
        }

        /// <summary>
        /// 新增电话联系记录
        /// </summary>
        /// <param name="record">Id不需要传</param>
        /// <returns></returns>
        [HttpPost("CreateContactRecord")]
        public IActionResult CreateContactRecord([FromBody] RecordContact record)
        {
            string msg = string.Empty;
            result.Success = _recordService.CreateContactRecord(record, ref msg);
            result.ReturnData = msg;
            return Ok(result);
        }

        /// <summary>
        /// 删除电话联系记录
        /// </summary>
        /// <param name="id">{"id":1}</param>
        /// <returns></returns>
        [HttpPost("DeleteContactRecord")]
        public IActionResult DeleteContactRecord([FromQuery] int id)
        {
            result.Success = _recordService.DeleteContactRecord(id);
            return Ok(result);
        }

        #endregion

        #region 获取打印图例
        /// <summary>
        /// 获取打印图例
        /// </summary>
        /// <param name="regNo">{"regNo":"220924000001"}</param>
        /// <returns></returns>
        [HttpPost("ReadPrintImage")]
        [ProducesResponseType(typeof(PrintImage[]), 200)]
        public IActionResult ReadPrintImage([FromQuery] string regNo)
        {
            result.ReturnData = _recordService.ReadPrintImage(regNo);
            return Ok(result);
        }
        #endregion

        /// <summary>
        /// 确认危急值
        /// </summary>
        /// <param name="apply"></param>
        /// <returns></returns>
        [HttpPost("ConfirmCriticalValue")]
        public async Task<IActionResult> ConfirmCriticalValue([FromBody] ConfirmCriticalValue apply)
        {
            result.Success = await _criticalValueService.ConfirmCriticalValue(apply);
            return Ok(result);
        }

        /// <summary>
        /// 锁定危急值
        /// </summary>
        /// <param name="Id"></param>
        /// <returns></returns>
        [HttpPost("LockCriticalValue")]
        public async Task<IActionResult> LockCriticalValue([FromQuery] long Id)
        {
            
            result.Success = await _criticalValueService.LockCriticalValue(Id);
            return Ok(result);
        }

        /// <summary>
        /// 解锁危急值
        /// </summary>
        /// <param name="Id"></param>
        /// <returns></returns>
        [HttpPost("UnLockCriticalValue")]
        public async Task<IActionResult> UnLockCriticalValue([FromQuery] long Id)
        {

            result.Success = await _criticalValueService.UnLockCriticalValue(Id);
            return Ok(result);
        }

        #region 结果接收
        /// <summary>
        /// 获取接收结果日志列表
        /// </summary>
        /// <param name="regNo"></param>
        /// <returns></returns>
        [HttpPost("GetReportRecieveLog")]
        [ProducesResponseType(typeof(List<ExtRecieveLogGroup>), 200)]
        public IActionResult GetReportRecieveLog([FromQuery] string regNo)
        {
            List<ExtRecieveLogGroup> data = new();
            var msg = string.Empty;
            result.Success = _reportLogService.GetReportRecieveLog(regNo, ref data);
            result.ReturnData = data;
            result.ReturnMsg = msg;
            return Ok(result);
        }

        /// <summary>
        /// 手动接收结果
        /// </summary>
        /// <param name="type"></param>
        /// <param name="requestNumber"></param>
        /// <returns></returns>
        [HttpPost("RecieveExtReport")]
        public IActionResult RecieveExtReport([FromQuery] string type, [FromQuery] string requestNumber)
        {
            result.Success = _reportLogService.RecieveExtReport(type, requestNumber);
            return Ok(result);
        }

        #endregion

    }
}
