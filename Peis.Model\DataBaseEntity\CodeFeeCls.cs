﻿using SqlSugar;

namespace Peis.Model.DataBaseEntity
{
    ///<summary>
    ///费用分类信息（收费类代码）
    ///</summary>
    [SugarTable("CodeFeeCls")]
    public class CodeFeeCls
    {
        /// <summary>
        /// 分类代码
        /// </summary>        
        [SugarColumn(IsNullable = false, IsPrimaryKey = true, Length = 10)]
        public string FeeClsCode { get; set; }

        /// <summary>
        /// 分类名称
        /// </summary>        
        [SugarColumn(IsNullable = false, Length = 40)]
        public string FeeClsName { get; set; }
    }
}