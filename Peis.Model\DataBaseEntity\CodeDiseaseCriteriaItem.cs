﻿using SqlSugar;

namespace Peis.Model.DataBaseEntity
{
    ///<summary>
    ///疾病审核条件项目表
    ///</summary>
    [SugarTable("CodeDiseaseCriteriaItem")]
    public class CodeDiseaseCriteriaItem
    {
        /// <summary>
        /// 疾病码
        /// </summary>        
        [SugarColumn(IsNullable = false, IsPrimaryKey = true, Length = 8)]
        public string DiseaseCode { get; set; }

        /// <summary>
        /// 项目码
        /// </summary>        
        [SugarColumn(IsNullable = false, IsPrimaryKey = true, Length = 6)]
        public string ItemCode { get; set; }

        /// <summary>
        /// 条件的值
        /// </summary>        
        [SugarColumn(IsNullable = false, IsPrimaryKey = true, Length = 255)]
        public string Value { get; set; }

        /// <summary>
        /// 操作符
        /// </summary>        
        [SugarColumn(IsNullable = false)]
        public int Operator { get; set; }

        /// <summary>
        /// 数据类型
        /// </summary>        
        [SugarColumn(IsNullable = false)]
        public int ValueType { get; set; }
    }
}