﻿namespace Peis.Model.DataBaseEntity.Occupation
{
    /// <summary>
    /// 引用组合关联表
    /// </summary>
    [SugarTable]
    public class PeRegisterQuoteComb
    {
        /// <summary>
        /// 体检号
        /// </summary>
        [SugarColumn(IsPrimaryKey = true, Length = 12)]
        public string RegNo { get; set; }
        /// <summary>
        /// 引用组合ID
        /// </summary>
        [SugarColumn(IsPrimaryKey = true)]
        public long RegCombId { get; set; }
    }
}
