﻿<Project Sdk="Microsoft.NET.Sdk">

	<PropertyGroup>
		<TargetFramework>net6.0</TargetFramework>
		<ImplicitUsings>enable</ImplicitUsings>
		<GenerateDocumentationFile>True</GenerateDocumentationFile>
		<Platforms>AnyCPU;x86;x64;ARM64</Platforms>
	</PropertyGroup>

	<ItemGroup>
		<PackageReference Include="Swashbuckle.AspNetCore" Version="6.5.0" />
		<PackageReference Include="Swashbuckle.AspNetCore.Filters" Version="7.0.12" />
	</ItemGroup>

	<ItemGroup>
		<ProjectReference Include="..\Peis.Model\Peis.Model.csproj" />
		<ProjectReference Include="..\Peis.Repository\Peis.Repository.csproj" />
		<ProjectReference Include="..\Peis.Service\Peis.Service.csproj" />
	</ItemGroup>

</Project>
