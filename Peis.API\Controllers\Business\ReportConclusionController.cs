﻿using MediatR;
using Microsoft.AspNetCore.Mvc;
using Peis.External.Platform.Service.Service.ServiceImpl.MediatR;
using Peis.Model.DataBaseEntity;
using Peis.Model.DTO.CriticalValue;
using Peis.Model.DTO.MajorPositive;
using Peis.Model.DTO.ReportConclusion;
using Peis.Model.Other.Input.ReportConclusion;
using Peis.Model.Other.PeEnum;
using Peis.Service.IService;
using Peis.Service.Service.MediatR;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Peis.API.Controllers.Business
{
    /// <summary>
    /// 报告结论
    /// </summary>
    [Route("api/[controller]")]
    [ApiController]
    public class ReportConclusionController : BaseApiController
    {
        private readonly IReportConclusionService _reportConclusionService;
        private readonly ICriticalValueService _criticalValueService;
        private readonly IMajorPositiveService _majorPositiveService;
        private readonly IMediator _mediator;

        public ReportConclusionController(
            IReportConclusionService reportConclusionService,
            ICriticalValueService criticalValueService,
            IMajorPositiveService majorPositiveService,
            IMediator mediator)
        {
            _reportConclusionService = reportConclusionService;
            _criticalValueService = criticalValueService;
            _majorPositiveService = majorPositiveService;
            _mediator = mediator;
        }

        /// <summary>
        /// 获取报告结论
        /// </summary>
        /// <param name="regNo">体检号</param>
        /// <param name="checkAudit">主检界面/审核界面(0/1)</param>
        /// <returns></returns>
        [HttpPost("GetReportConclution")]
        [ProducesResponseType(typeof(ReportConclusion), 200)]
        public IActionResult GetReportConclusion([FromQuery] string regNo, [FromQuery] CheckAudit checkAudit)
        {
            var reportConclusion = new ReportConclusion();
            var msg = string.Empty;
            result.Success = _reportConclusionService.GetReportConclusion(regNo, checkAudit, ref reportConclusion, ref msg);
            result.ReturnMsg = msg;
            result.ReturnData = reportConclusion;
            return Ok(result);
        }

        /// <summary>
        /// 仅读取报告结论
        /// </summary>
        /// <param name="regNo"></param>
        /// <returns></returns>
        [HttpPost("ReadonlyReportConclusion")]
        [ProducesResponseType(typeof(ReportConclusion), 200)]
        public IActionResult ReadonlyReportConclusion([FromQuery] string regNo)
        {
            result.ReturnData = _reportConclusionService.ReadonlyReportConclusion(regNo);
            result.Success = result.ReturnData != null;
            return Ok(result);
        }

        /// <summary>
        /// 生成综述建议
        /// </summary>
        /// <param name="regNo">体检号</param>
        /// <returns></returns>
        [HttpPost("GenerateSummarySuggestions")]
        [ProducesResponseType(typeof(SummarySuggestions), 200)]
        public IActionResult GenerateSummarySuggestions(string regNo)
        {
            result.ReturnData = _reportConclusionService.GenerateSummarySuggestions(regNo);
            return Ok(result);
        }

        /// <summary>
        /// 保存报告结论
        /// </summary>
        /// <param name="reportConclusion"></param>
        /// <returns></returns>
        [HttpPost("SaveReportConclusion")]
        public IActionResult SaveReportConclusion(ReportConclusion reportConclusion)
        {
            var msg = string.Empty;
            result.Success = _reportConclusionService.SaveReportConclusion(reportConclusion, ref msg);
            result.ReturnMsg = msg;
            return Ok(result);
        }

        /// <summary>
        /// 取消报告结论
        /// </summary>
        /// <param name="regNo"></param>
        /// <returns></returns>
        [HttpPost("CancelReportConclusion")]
        public IActionResult CancelReportConclusion(string regNo)
        {
            var msg = string.Empty;
            result.Success = _reportConclusionService.CancelReportConclusion(regNo, ref msg);
            result.ReturnMsg = msg;
            return Ok(result);
        }

        /// <summary>
        /// 手动生成正常建议
        /// </summary>
        /// <returns></returns>
        [HttpPost("AddNormalSuggestions")]
        public IActionResult AddNormalSuggestions()
        {
            result.ReturnData = _reportConclusionService.AddNormalSuggestions();
            return Ok(result);
        }

        #region 审核/取消
        /// <summary>
        /// 审核报告结论
        /// </summary>
        /// <param name="auditInfo">审核信息</param>
        /// <returns></returns>
        [HttpPost("AuditReportConclusion")]
        public IActionResult AuditReportConclusion(AuditInfo auditInfo)
        {
            var msg = string.Empty;
            result.Success = _reportConclusionService.AuditReportConclusion(auditInfo, ref msg);
            result.ReturnMsg = msg;

            if (result.Success)
                AuditReportOtherBussHandle(auditInfo.RegNo);

            return Ok(result);
        }

        /// <summary>
        /// (新)审核报告结论
        /// </summary>
        /// <param name="reportConclusion"></param>
        /// <returns></returns>
        [HttpPost("NewAuditReportConclusion")]
        public IActionResult NewAuditReportConclusion(ReportConclusion reportConclusion)
        {
            var msg = string.Empty;
            result.Success = _reportConclusionService.NewAuditReportConclusion(reportConclusion, ref msg);
            result.ReturnMsg = msg;

            if (result.Success)
                AuditReportOtherBussHandle(reportConclusion.PatientInfo.RegNo);

            return Ok(result);
        }

        /// <summary>
        /// 审核报告后其他业务处理
        /// </summary>
        /// <param name="regNo"></param>
        void AuditReportOtherBussHandle(string regNo)
        {
            if (regNo.IsNullOrEmpty()) return;

            _mediator.Send(new SavePeReportFileByRegNoHandle.Data(regNo));
            _mediator.Send(new AddSendShortMsgToPatForApprovedJobHandle.Data(regNo));
        }

        /// <summary>
        /// 取消审核报告结论
        /// </summary>
        /// <param name="auditInfo">审核信息</param>
        /// <returns></returns>
        [HttpPost("CancelAuditReportConclusion")]
        public IActionResult CancelAuditReportConclusion(AuditInfo auditInfo)
        {
            var msg = string.Empty;
            result.Success = _reportConclusionService.CancelAuditReportConclusion(auditInfo, ref msg);
            result.ReturnMsg = msg;

            if (result.Success)
            {
                _mediator.Send(new RemovePeReportFileByRegNoHandle.Data(auditInfo.RegNo));
                _mediator.Send(new RemoveSendShortMsgToPatForApprovedJobHandle.Data(auditInfo.RegNo));
            }

            return Ok(result);
        }
        #endregion

        /// <summary>
        /// 获取已录入结果的组合，用于综述
        /// </summary>
        /// <param name="regNo"></param>
        /// <returns></returns>
        [HttpPost("GetRecordCombs")]
        public IActionResult GetRecordCombs(string regNo)
        {
            var msg = string.Empty;
            result.ReturnData = _reportConclusionService.GetRecordCombs(regNo, ref msg);
            result.ReturnMsg = msg;
            return Ok(result);
        }

        /// <summary>
        /// 编辑小结标签
        /// </summary>
        /// <param name="sumTagEdit">编辑小结标签</param>
        /// <returns></returns>
        [HttpPost("EditSumTag")]
        [ProducesResponseType(typeof(SumTagEditBack), 200)]
        public IActionResult EditSumTag([FromBody] SumTagEdit sumTagEdit)
        {
            result.ReturnData = _reportConclusionService.EditSumTag(sumTagEdit);
            return Ok(result);
        }

        /// <summary>
        /// 获取建议
        /// </summary>
        /// <param name="diseaseCode">疾病代码</param>
        /// <returns></returns>
        [HttpPost("GetSuggestion")]
        [ProducesResponseType(typeof(string), 200)]
        public IActionResult GetSuggestion(string diseaseCode)
        {
            result.ReturnData = _reportConclusionService.GetSuggestion(diseaseCode);
            return Ok(result);
        }

        /// <summary>
        /// 关键词查询疾病建议，用于添加新建议
        /// </summary>
        /// <param name="keyword">关键词</param>
        /// <param name="queryType">查询类型:1-疾病名称，2-建议内容，其他-疾病名称、建议内容</param>
        /// <returns></returns>
        [HttpPost("QueryDiseaseSuggestions")]
        [ProducesResponseType(typeof(List<DiseaseSuggestion>), 200)]
        public IActionResult QueryDiseaseSuggestions(string keyword, int queryType)
        {
            result.ReturnData = _reportConclusionService.QueryDiseaseSuggestions(keyword, queryType);
            return Ok(result);
        }

        /// <summary>
        /// 优先分配主检
        /// </summary>
        /// <param name="regNo">体检号</param>
        /// <param name="operatorCode">操作员代码</param>
        /// <returns></returns>
        [HttpPost("PriorityAllocation")]
        public IActionResult PriorityAllocation(string regNo, string operatorCode)
        {
            var msg = string.Empty;
            result.Success = _reportConclusionService.PriorityAllocation(regNo, operatorCode, ref msg);
            result.ReturnMsg = msg;
            return Ok(result);
        }

        #region 主检列表
        /// <summary>
        /// 获取主检的人员列表（主检分配）
        /// </summary>
        /// <param name="queryPatientList"></param>
        /// <returns></returns>
        [HttpPost("GetPatientList4Allocate")]
        [ProducesResponseType(typeof(List<PatientInfo>), 200)]
        public IActionResult GetPatientList4Allocate(QueryPatientList queryPatientList)
        {
            result.ReturnData = _reportConclusionService.GetPatientList4Allocate(queryPatientList);
            return Ok(result);
        }

        /// <summary>
        /// 获取主检的人员列表（主检不分配）/审核的人员列表
        /// </summary>
        /// <param name="queryPatientList"></param>
        /// <returns></returns>
        [HttpPost("GetPatientList4NotAllocate")]
        [ProducesResponseType(typeof(List<PatientInfo>), 200)]
        public IActionResult GetPatientList4NotAllocate(QueryPatientList queryPatientList)
        {
            result.ReturnData = _reportConclusionService.GetPatientList4NotAllocate(queryPatientList);
            return Ok(result);
        }

        /// <summary>
        /// 查询已检的人员列表（主检分配）
        /// </summary>
        /// <param name="regNo">体检号</param>
        /// <param name="beginActiveDate">体检开始日期</param>
        /// <param name="endActiveDate">体检结束日期</param>
        /// <returns></returns>
        [HttpPost("QueryCheckedPatientList")]
        [ProducesResponseType(typeof(List<PatientInfo>), 200)]
        public IActionResult QueryCheckedPatientList(string regNo, DateTime? beginActiveDate, DateTime? endActiveDate)
        {
            List<PatientInfo> patientInfos = new();
            var msg = string.Empty;
            result.Success = _reportConclusionService.QueryCheckedPatientList(regNo, beginActiveDate, endActiveDate, ref patientInfos, ref msg);
            result.ReturnMsg = msg;
            result.ReturnData = patientInfos;
            return Ok(result);
        }
        #endregion

        /// <summary>
        /// 主检(不分配)----查询
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        [HttpPost("ReadCheckPerson")]
        [ProducesResponseType(typeof(CheckPerson), 200)]
        public IActionResult ReadCheckPerson([FromBody] CheckPersonQuery query)
        {
            result.ReturnData = _reportConclusionService.ReadCheckPerson(query);
            return Ok(result);
        }

        /// <summary>
        /// 撤回主检
        /// </summary>
        /// <param name="returnChecked">撤回主检</param>
        /// <returns></returns>
        [HttpPost("ReturnChecked")]
        public IActionResult ReturnChecked(ReturnChecked returnChecked)
        {
            var msg = string.Empty;
            result.Success = _reportConclusionService.ReturnChecked(returnChecked, ref msg);
            result.ReturnMsg = msg;
            return Ok(result);
        }

        /// <summary>
        /// 获取撤回主检记录（主检回复）
        /// </summary>
        /// <param name="operatorCode">审核医生</param>
        /// <returns></returns>
        [HttpPost("GetReturnChecked")]
        [ProducesResponseType(typeof(List<PeReportExtReturnChecked>), 200)]
        public IActionResult GetReturnChecked(string operatorCode)
        {
            result.ReturnData = _reportConclusionService.GetReturnChecked(operatorCode);
            return Ok(result);
        }

        /// <summary>
        /// 获取撤回主检记录（主检未答复）
        /// </summary>
        /// <param name="replyerCode">答复医生代码（主检医生）</param>
        /// <returns></returns>
        [HttpPost("GetNotReplyReturn")]
        [ProducesResponseType(typeof(List<PeReportExtReturnChecked>), 200)]
        public IActionResult GetNotReplyReturn(string replyerCode)
        {
            var peReportExtReturnCheckeds = new List<PeReportExtReturnChecked>();
            var msg = string.Empty;
            result.Success = _reportConclusionService.GetNotReplyReturn(replyerCode, ref peReportExtReturnCheckeds, ref msg);
            result.ReturnData = peReportExtReturnCheckeds;
            result.ReturnMsg = msg;
            return Ok(result);
        }

        /// <summary>
        /// 答复撤回主检
        /// </summary>
        /// <param name="peReportExtReturnCheckeds">主检问题单</param>
        /// <returns></returns>
        [HttpPost("ReplyReturn")]
        public IActionResult ReplyReturn(List<PeReportExtReturnChecked> peReportExtReturnCheckeds)
        {
            var msg = string.Empty;
            result.Success = _reportConclusionService.ReplyReturn(peReportExtReturnCheckeds, ref msg);
            result.ReturnMsg = msg;
            return Ok(result);
        }

        /// <summary>
        /// 回复危急值（主检和审核权限者用）
        /// </summary>
        /// <param name="reply"></param>
        /// <returns></returns>
        [HttpPost("ReplyCriticalValue")]
        public async Task<IActionResult> ReplyCriticalValue([FromBody] ReplyCriticalValue reply)
        {
            result.Success = await _criticalValueService.ReplyCriticalValue(reply);
            return Ok(result);
        }

        /// <summary>
        /// 重大阳性结果审核
        /// </summary>
        /// <param name="regNo">体检号</param>
        /// <returns></returns>
        [HttpPost("PositiveResultReview")]
        [ProducesResponseType(typeof(PositiveData), 200)]
        public IActionResult PositiveResultReview([FromQuery] string regNo)
        {
            var positiveData = new PositiveData();
            if (!_majorPositiveService.PositiveResultReview(regNo, ref positiveData))
            {
                result.Success = false;
                result.ReturnMsg = "获取数据失败!";
                return Ok(result);
            }

            result.ReturnData = positiveData;
            return Ok(result);
        }

        /// <summary>
        /// 保存重大阳性结果
        /// </summary>
        /// <param name="positiveResult"></param>
        /// <returns></returns>
        [HttpPost("SavePositiveResult")]
        public IActionResult SavePositiveResult([FromBody] PositiveResult positiveResult)
        {
            string msg = string.Empty;
            if (!_majorPositiveService.SavePositiveResult(positiveResult, ref msg))
            {
                result.Success = false;
                result.ReturnMsg = msg;
                return Ok(result);
            }

            result.ReturnMsg = "保存成功";
            return Ok(result);
        }
    }
}