﻿namespace Peis.Model.DataBaseEntity;

///<summary>
///重大阳性关键字
///</summary>
[SugarTable(TableName = nameof(MajorPositiveKeyword), TableDescription = "重大阳性关键字")]
[SugarIndex($"idx_{nameof(MajorPositiveKeyword)}", nameof(KeywordCode), OrderByType.Asc)]
public class MajorPositiveKeyword
{
    /// <summary>
    /// 关键字代码
    /// </summary>        
    [SugarColumn(IsNullable = false, IsPrimaryKey = true, Length = 6)]
    public string KeywordCode { get; set; }

    /// <summary>
    /// 关键字名称
    /// </summary>        
    [SugarColumn(IsNullable = false, Length = 400)]
    [Required(ErrorMessage = "关键字名称不能为空")]
    public string KeywordName { get; set; }
}