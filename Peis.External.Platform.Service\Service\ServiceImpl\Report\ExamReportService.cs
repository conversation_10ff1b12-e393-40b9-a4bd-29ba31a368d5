﻿using DocumentFormat.OpenXml.Spreadsheet;
using Peis.Quartz.UI.Tools;
using Peis.External.Platform.Service.Service.IService.Report;
using Peis.Model.DTO.External.Report;
using Peis.Model.DTO.MajorPositive;
using Peis.Model.DTO.ReportGraphicText;
using Peis.Model.Other.PeEnum;
using Peis.Repository.IRepository;
using Peis.Service.IService;
using Peis.Utility.PeUser;
using System.Globalization;
using System.Net;
using System.Text.RegularExpressions;

namespace Peis.External.Platform.Service.Service.ServiceImpl.Report
{
    public class ExamReportService : IExamReportService
    {
        private readonly ISqlSugarClient _db;
        private readonly ISplitTable _splitTable;
        private readonly IDataTranRepository _dataTranRepository;
        private readonly IRegisterRepository _registerRepository;
        private readonly IRecordNewService _recordService;
        private readonly IDataRepository<PeRegister> _peRegisterRepository;
        private readonly IRecordRepository _recordRepository;
        private readonly IDataRepository<ExtExamReportLog> _reportLogRepository;
        private readonly IHttpContextUser _httpContextUser;
        private readonly ILogBusinessNewService _logBusinessNewService;

        public ExamReportService(ISqlSugarClient db,
            ISplitTable splitTable,
            IDataTranRepository dataTranRepository,
            IRegisterRepository registerRepository,
            IRecordNewService recordService,
            IDataRepository<ExtExamReportLog> extExamReportLogRepository,
            IDataRepository<PeRegister> peRegisterRepository,
            IRecordRepository recordRepository,
            IHttpContextUser httpContextUser,
            ILogBusinessNewService logBusinessNewService)
        {
            _db = db;
            _splitTable = splitTable;
            _dataTranRepository = dataTranRepository;
            _registerRepository = registerRepository;
            _recordService = recordService;
            _reportLogRepository = extExamReportLogRepository;
            _peRegisterRepository = peRegisterRepository;
            _recordRepository = recordRepository;
            _httpContextUser = httpContextUser;
            _logBusinessNewService = logBusinessNewService;
        }

        public void AcceptReport(string xml, ref string respData)
        {
            var examReport = XmlHelper.Deserialize<ExamReport>(xml);
            var register = _registerRepository.ReadRegisterNoHosp(examReport.PE_NO).First();
            if (register == null)
            {
                respData = XmlHelper.Serialize(ReportResult.Bad("体检系统不存在该患者"));
                return;
            }
            else if (register.PeStatus > PeStatus.已检完)
            {
                respData = XmlHelper.Serialize(ReportResult.Bad("当前体检已经审核或者打印报告，不能修改项目结果"));
                return;
            }
            _httpContextUser.SetHospCode(register.HospCode);
            // 获取上一次的接收内容，如果有对比内容避免重复接收
            if (TryGetLastAcceptLog(examReport, out var lastLog) && (lastLog.ReportXML == xml))
            {
                respData = XmlHelper.Serialize(ReportResult.Ok());
                return;
            }

            var acceptLog = LogAcceptBeginning(examReport, xml, lastLog);
            try
            {
                AcceptReport(register, examReport);
            }
            catch (BusinessException bEx)
            {
                respData = XmlHelper.Serialize(ReportResult.Bad(bEx.Message));
                return;
            }
            catch (Exception)
            {
                respData = XmlHelper.Serialize(ReportResult.Bad("服务内部发生异常"));
                return;
            }

            LogAcceptSucceed(acceptLog);
            respData = XmlHelper.Serialize(ReportResult.Ok());
            return;
        }

        public void AcceptExamCritical(string xml, ref string respData)
        {
            throw new NotImplementedException();
        }

        public void ReacceptReport(string applyNo)
        {
            var acceptLog = _reportLogRepository.First(x => x.ApplyNo == applyNo);
            if (acceptLog == null)
                throw new BusinessException($"接收记录获取失败：{applyNo}");

            var examReport = XmlHelper.Deserialize<ExamReport>(acceptLog.ReportXML);
            var register = _registerRepository.ReadRegisterNoHosp(examReport.PE_NO).First() ?? throw new BusinessException("订单不存在");

            if (register.PeStatus > PeStatus.已检完)
                throw new BusinessException("订单已经审核或者打印报告，不能修改项目结果");

            AcceptReport(register, examReport);
            LogAcceptSucceed(acceptLog);
        }

        #region 本地方法
        private PeRecordComb NewRecordComb(PeRegisterComb regComb, ExamReport report)
        {
            Regex regex = new Regex("<!--.+-->");
            return new PeRecordComb
            {
                Id           = regComb.Id,
                RegNo        = regComb.RegNo,
                RegCombId    = regComb.Id,
                CombCode     = regComb.CombCode,
                CombResult   = regex.Replace(WebUtility.HtmlDecode(report.EXAM_RESULT),string.Empty).Trim(),
                ClsCode      = regComb.ClsCode,
                ExamDeptCode = regComb.ExamDeptCode,
                OperName     = string.Empty,
                DoctorName   = report.REPORT_DR_NAME,
                IsError      = false,
                OperTime     = DateTime.Now,
                ExamTime     = DateTime.ParseExact(report.REPORT_DATE, "yyyyMMddHHmmss", CultureInfo.InvariantCulture),
                RegisterTime = regComb.RegisterTime
            };
        }
        private PeRecordCombTag NewRecordCombTag(PeRecordComb recComb)
        {
            return new PeRecordCombTag
            {
                Id           = SnowFlakeSingle.Instance.NextId(),
                RecCombId    = recComb.Id,
                CombCode     = recComb.CombCode,
                CombTag      = recComb.CombResult,
                IsCustom     = false,
                BindItemTags = string.Empty,
                RegisterTime = recComb.RegisterTime
            };
        }
        private PeRecordItem NewRecordItem(PeRecordComb recComb, ExamReport report)
        {
            Regex regex = new Regex("<!--.+-->");
            var item = _db.Queryable<MapItemComb>()
                    .InnerJoin<CodeItem>((map, item) => map.ItemCode == item.ItemCode)
                    .Where((map, item) => map.CombCode == recComb.CombCode)
                    .Select((map, item) => item)
                    .First();

            return new PeRecordItem
            {
                Id           = SnowFlakeSingle.Instance.NextId(),
                RecCombId    = recComb.Id,
                CombCode     = recComb.CombCode,
                ItemCode     = item.ItemCode,
                ItemName     = item.ItemName,
                SortIndex    = item.SortIndex,
                ItemResult   = regex.Replace(WebUtility.HtmlDecode(report.EXAM_DESCR),string.Empty).Trim(),
                ResultType   = item.ValueType,
                Unit         = string.Empty,
                LowerLimit   = string.Empty,
                UpperLimit   = string.Empty,
                Hint         = string.Empty,
                RegisterTime = recComb.RegisterTime
            };
        }
        private PeRecordItemTag NewRecordItemTag(PeRecordItem recItem)
        {
            return new PeRecordItemTag
            {
                Id             = SnowFlakeSingle.Instance.NextId(),
                RecItemId      = recItem.Id,
                BindResultId   = 0,
                ItemCode       = recItem.ItemCode,
                ItemTag        = recItem.ItemResult,
                IsCalcResul    = false,
                CalcItemTagIds = string.Empty,
                RegisterTime   = recItem.RegisterTime
            };
        }
        private PeReportGraphicTextFile NewRecordPartImages(PeRecordComb recComb, ExamReport report)
        {
            if (string.IsNullOrWhiteSpace(report.REPORT_URL) || !report.REPORT_URL.EndsWith("pdf", StringComparison.OrdinalIgnoreCase))
                return null;

            return new PeReportGraphicTextFile
            {
                Id           = recComb.Id,
                RegNo        = recComb.RegNo,
                RegisterTime = recComb.RegisterTime,
                RegCombId    = recComb.RegCombId,
                CombCode     = recComb.CombCode,
                ReportUrl    = report.REPORT_URL,
                Status       = EnumReportGraphicTextStatus.ToGener,
                CreatorCode  = "root",
                CreatorName  = "root",
                CreatedTime  = DateTime.Now
            };
        }
        private void SaveRecord(PeRegisterComb regComb, ExamReport examReport)
        {
            var recComb    = NewRecordComb(regComb, examReport);
            var recCombTag = NewRecordCombTag(recComb);
            var recItem    = NewRecordItem(recComb, examReport);
            var recItemTag = NewRecordItemTag(recItem);
            var recPDF     = NewRecordPartImages(recComb, examReport);

            _dataTranRepository.ExecTran(() =>
            {
                // 删除旧记录
                _recordService.DeleteRecordCombAndImage(new() { RegNo = recComb.RegNo, RegCombId = recComb.RegCombId },out var recordComb,out var recordItems,false);
                if (recordComb == null)
                {
                    _logBusinessNewService.RecordNewLog(recComb.RegNo, recComb.CombCode, logOper: string.IsNullOrEmpty(_httpContextUser.UserName) ? "自动接收" : _httpContextUser.UserName);
                }
                else
                {
                    _logBusinessNewService.RecordModifyLog(recordComb, recComb, recordItems, new PeRecordItem[] { recItem }, logOper: string.IsNullOrEmpty(_httpContextUser.UserName) ? "自动接收" : _httpContextUser.UserName);
                }
                // 保存新记录
                _db.Insertable(recComb).SplitTable().ExecuteCommand();
                _db.Insertable(recCombTag).SplitTable().ExecuteCommand();
                _db.Insertable(recItem).SplitTable().ExecuteCommand();
                _db.Insertable(recItemTag).SplitTable().ExecuteCommand();
                _logBusinessNewService.RecordNewLog(recComb.RegNo, recComb.CombCode, logOper: string.IsNullOrEmpty(_httpContextUser.UserName) ? "自动接收" : _httpContextUser.UserName);
                _logBusinessNewService.SaveLogs();
                if (recPDF != null)
                    _db.Insertable(recPDF).SplitTable().ExecuteCommand();
            });

            try 
            {
                var recCombs = new List<PeRecordItem> { recItem }.Select(x => new RecComb
                {
                    RegNo = recComb.RegNo,
                    RecCombId = recComb.Id,
                    CombCode = recComb.CombCode,
                    CombName = regComb.CombName,
                    ItemCode = x.ItemCode,
                    ItemName = x.ItemName,
                    ItemResult = x.ItemResult,
                    ResultType = x.ResultType,
                    LowerLimit = x.LowerLimit,
                    UpperLimit = x.UpperLimit,
                    OperCode = _httpContextUser.UserId,
                }).ToList();
                QuartzTaskHelper.AddJobAsync<IMajorPositiveService>(nameof(IMajorPositiveService.SaveMajorPositiveWhileSaveRecord), recCombs).ConfigureAwait(false);
            }
            catch { }
        }
        private void AcceptReport(PeRegister register, ExamReport examReport)
        {
            // 体检号+"US"的是彩超合并报告
            if (examReport.ELECTR_REQUISITION_NO.Equals($"{examReport.PE_NO}US", StringComparison.OrdinalIgnoreCase))
            {
                var orderCodes = examReport.EXAM_ITEM_CODE.Split(',').ToArray();
                var applyQuery = _splitTable.GetTableOrDefault<ExtHisApplyComb>(register.RegisterTime);
                var regCombs = _splitTable.GetTableOrDefault<PeRegisterComb>(register.RegisterTime)
                    .InnerJoin(applyQuery, (regComb, apply) => regComb.Id == apply.RegCombId)
                    .Where((regComb, apply) => regComb.RegNo == register.RegNo && SqlFunc.ContainsArray(orderCodes, apply.HisOrderCode))
                    .OrderBy((regComb, apply) => regComb.CombSortIndex)
                    .ToArray();
                if (regCombs.Length == 0)
                    throw new BusinessException("找不到对应组合");

                SaveRecord(regCombs.First(), examReport);

                examReport.EXAM_RESULT = "已合并发报告";
                examReport.EXAM_DESCR  = "已合并发报告";
                examReport.REPORT_URL  = string.Empty;
                foreach (var regComb in regCombs.Skip(1))
                {
                    SaveRecord(regComb, examReport);
                }
            }
            else
            {
                var applyQuery = _splitTable.GetTableOrDefault<ExtHisApplyComb>(register.RegisterTime);
                var regComb = _splitTable.GetTableOrDefault<PeRegisterComb>(register.RegisterTime)
                    .InnerJoin(applyQuery, (regComb, apply) => regComb.Id == apply.RegCombId)
                    .Where((regComb, apply) => regComb.RegNo == register.RegNo && apply.HisOrderCode == examReport.EXAM_ITEM_CODE)
                    .First();
                if (regComb == null)
                {
                    examReport.EXAM_ITEM_CODE = examReport.EXAM_ITEM_CODE.Substring(0, examReport.EXAM_ITEM_CODE.Length - 1);
                    regComb = _splitTable.GetTableOrDefault<PeRegisterComb>(register.RegisterTime)
                    .InnerJoin(applyQuery, (regComb, apply) => regComb.Id == apply.RegCombId)
                    .Where((regComb, apply) => regComb.RegNo == register.RegNo && apply.HisOrderCode == examReport.EXAM_ITEM_CODE)
                    .First();
                    if (regComb == null)
                        throw new BusinessException("找不到对应组合");
                }
                SaveRecord(regComb, examReport);
            }

            UpdatePeStatus(examReport.PE_NO);
        }

        private bool TryGetLastAcceptLog(ExamReport examReport, out ExtExamReportLog log)
        {
            log = _reportLogRepository.FindInSingleKey(examReport.ELECTR_REQUISITION_NO);
            return log != null;
        }
        private ExtExamReportLog LogAcceptBeginning(ExamReport examReport, string xml, ExtExamReportLog lastLog)
        {
            ExtExamReportLog log;
            if (lastLog == null)
            {
                log = new ExtExamReportLog()
                {
                    RegNo      = examReport.PE_NO,
                    ApplyNo    = examReport.ELECTR_REQUISITION_NO,
                    CombName   = examReport.EXAM_ITEM_NAME,
                    ReportXML  = xml,
                    CreateTime = DateTime.Now,
                    LastTime   = DateTime.Now,
                    Success    = false
                };
                _reportLogRepository.Insert(log);
            }
            else
            {
                log           = lastLog;
                log.ReportXML = xml;
                log.LastTime  = DateTime.Now;
                log.Success   = false;
                _reportLogRepository.Update(log);
            }
            return log;
        }
        private void LogAcceptSucceed(ExtExamReportLog log)
        {
            log.Success = true;
            _reportLogRepository.Update(log);
        }

        /// <summary>
        /// 更新体检状态
        /// </summary>
        /// <param name="regNo"></param>
        /// <returns></returns>
        private bool UpdatePeStatus(string regNo)
        {
            var register = _registerRepository.ReadRegister(regNo).First();
            if (register == null)
                return false;

            //查出所有体检的组合
            var peRecordComb = _recordRepository.ReadCombsInfo(regNo)
                                .Select((regComb, recComb) => new
                                {
                                    regComb.CombName,
                                    recComb.DoctorName
                                }).ToList();
            //查已录入结果的组合总数
            var hasResult = peRecordComb.Where(x => !string.IsNullOrEmpty(x.DoctorName)).Count();

            //没有医生有值  未检查
            if (hasResult == 0)
                register.PeStatus = PeStatus.未检查;

            //有一个有医生值 正在检查
            else if (hasResult < peRecordComb.Count())
                register.PeStatus = PeStatus.正在检查;

            //所有医生都有值 已检完
            else
                register.PeStatus = PeStatus.已检完;

            _peRegisterRepository.Update(register);
            return true;
        }
        #endregion
    }
}
