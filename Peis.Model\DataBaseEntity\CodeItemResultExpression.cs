﻿using SqlSugar;

namespace Peis.Model.DataBaseEntity
{
    ///<summary>
    ///项目结果公式信息
    ///</summary>
    [SugarTable("CodeItemResultExpression")]
    public class CodeItemResultExpression
    {
        /// <summary>
        /// 项目代码
        /// </summary>        
        [SugarColumn(IsNullable = false, IsPrimaryKey = true, Length = 6)]
        public string ItemCode { get; set; }

        /// <summary>
        /// 项目结果公式
        /// </summary>        
        [SugarColumn(IsNullable = false, Length = 100)]
        public string Expression { get; set; }

        /// <summary>
        /// 项目结果公式展示
        /// </summary>        
        [SugarColumn(IsNullable = false, Length = 400)]
        public string ExpressionShow { get; set; }
    }
}