﻿using Peis.Model.DTO.DoctorStation;
using System.Collections.Generic;

namespace Peis.Model.DTO.CriticalValue
{
    /// <summary>
    /// 确认危急值的项目
    /// </summary>
    public class ConfirmCriticalValueItem
    {
        /// <summary>
        /// 项目代码
        /// </summary>
        public string ItemCode { get; set; }

        /// <summary>
        /// 项目名称
        /// </summary>
        public string ItemName { get; set; }

        /// <summary>
        /// 项目结果标签
        /// </summary>
        public List<ItemTag> ItemTags { get; set; }

        /// <summary>
        /// 结果下限
        /// </summary>
        public string LowerLimit { get; set; }

        /// <summary>
        /// 结果上限
        /// </summary>
        public string UpperLimit { get; set; }
    }
}
