﻿using SqlSugar;

namespace Peis.Model.DataBaseEntity
{
    ///<summary>
    ///影像对应
    ///</summary>
    [SugarTable("MapItemRisPart")]
    public class MapItemRisPart
    {
        /// <summary>
        /// 体检项目代码
        /// </summary>        
        [SugarColumn(IsNullable = false, IsPrimaryKey = true, Length = 6)]
        public string ItemCode { get; set; }

        /// <summary>
        /// 体检项目名称
        /// </summary>        
        [SugarColumn(IsNullable = false, Length = 200)]
        public string ItemName { get; set; }

        /// <summary>
        /// 影像分类代码
        /// </summary>        
        [SugarColumn(IsNullable = false, Length = 10)]
        public string RisClsCode { get; set; }

        /// <summary>
        /// 影像分类名称
        /// </summary>        
        [SugarColumn(IsNullable = false, Length = 200)]
        public string RisClsName { get; set; }

        /// <summary>
        /// 影像部位代码
        /// </summary>        
        [SugarColumn(IsNullable = false, Length = 10)]
        public string PartCode { get; set; }

        /// <summary>
        /// 影像部位名称
        /// </summary>        
        [SugarColumn(IsNullable = false, Length = 200)]
        public string PartName { get; set; }
    }
}