﻿namespace Peis.Model.DataBaseEntity.Occupation
{
    /// <summary>
    /// 地区代码表
    /// </summary>
    [SugarTable]
    public class CodeOccupationalAddress
    {
        /// <summary>
        /// 地区编码
        /// </summary>
        [SugarColumn(Length = 10, IsPrimaryKey = true)]
        public string AddressCode { get; set; }
        /// <summary>
        /// 地区简称
        /// </summary>
        [SugarColumn(Length = 50, IsNullable = false)]
        public string AddressShortName { get; set; }
        /// <summary>
        /// 地区全称
        /// </summary>
        [SugarColumn(Length = 100, IsNullable = false)]
        public string AddressFullName { get; set; }

        /// <summary>
        /// 父级代码
        /// </summary>
        [SugarColumn(Length = 10, IsNullable = true)]
        public string ParentCode { get; set; }

        /// <summary>
        /// 子级
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public List<CodeOccupationalAddress> Children { get; set; }
    }
}
