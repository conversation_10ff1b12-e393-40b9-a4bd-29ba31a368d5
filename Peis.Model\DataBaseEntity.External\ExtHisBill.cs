﻿using Peis.Model.DTO.External.His;
using Peis.Model.Other.PeEnum;
using SqlSugar;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Peis.Model.DataBaseEntity.External
{
    /// <summary>
    /// HIS订单支付表
    /// </summary>
    [SugarTable("ExtHisBill", "HIS订单支付表")]
    public class ExtHisBill
    {
        /// <summary>
        /// 创建时间
        /// </summary>
        [SugarColumn(IsNullable = false, IsPrimaryKey = true)]
        public DateTime CreateTime { get; set; }

        /// <summary>
        /// 体检号
        /// </summary>
        [SugarColumn(IsNullable = false, IsPrimaryKey = true, Length = 12)]
        public string RegNo { get; set; }

        /// <summary>
        /// 姓名
        /// </summary>
        [SugarColumn(IsNullable = false, Length = 50)]
        public string Name { get; set; }

        /// <summary>
        /// 档案卡号
        /// </summary>
        [SugarColumn(IsNullable = false, Length = 12)]
        public string PatCode { get; set; }

        /// <summary>
        /// 金额
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public decimal Price { get; set; }

        /// <summary>
        /// 支付状态
        /// </summary>
        [SugarColumn(IsNullable = false)]
        public PayStatus PayStatus { get; set; }

        /// <summary>
        /// 支付时间
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public DateTime? ChargeDate { get; set; }

        /// <summary>
        /// 发票ID
        /// </summary>
        [SugarColumn(IsNullable = true, Length = 20)]
        public string InvoiceId { get; set; }

        /// <summary>
        /// 发票号
        /// </summary>
        [SugarColumn(IsNullable = true, Length = 20)]
        public string InvoiceNo { get; set; }

        /// <summary>
        /// 支付类型 
        /// </summary>
        [SugarColumn(IsNullable = false, Length = 1)]
        public FeeType FeeType { get; set; }

        /// <summary>
        /// 原发票ID
        /// </summary>        
        [SugarColumn(IsNullable = true, Length = 20)]
        public string OriginalInvoiceId { get; set; }

        /// <summary>
        /// 收费人员
        /// </summary>
        [SugarColumn(IsNullable = true, Length = 20)]
        public string FeeOperator { get; set; }

    }
}
