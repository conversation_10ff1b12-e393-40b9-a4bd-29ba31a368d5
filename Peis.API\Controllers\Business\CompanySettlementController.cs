﻿using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Peis.Model.DataBaseEntity.External;
using Peis.Model.DTO.CompanySettlement;
using Peis.Service.IService.ExternalSystem;
using Peis.Service.Service;
using Peis.Service.Service.MediatR;
using Peis.Utility.CustomAttribute;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Peis.API.Controllers.Business
{
    /// <summary>
    /// 单位结算
    /// </summary>
    [Route("api/[controller]")]
    [ApiController]
    public class CompanySettlementController : BaseApiController
    {
        private readonly IExternalSystemCompanySettlementService _externalSystemCompanyService;
        private readonly IMediator _mediator;
        public CompanySettlementController(IExternalSystemCompanySettlementService externalSystemCompanyService, IMediator mediator)
        {
            _externalSystemCompanyService = externalSystemCompanyService;
            _mediator = mediator;
        }

        /// <summary>
        /// 获取待结算人员信息
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        [HttpPost("QueryPersonListForSettlement")]
        [ProducesResponseType(typeof(List<CompanyPaymentListPerson>), 200)]
        public IActionResult QueryPersonListForSettlement([FromBody] QuerySettlementList query)
        {
            result.Success = true;
            result.ReturnData = _externalSystemCompanyService.QueryPersonListForSettlement(query);
            return Ok(result);
        }

        /// <summary>
        /// 生成结算数据
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        [HttpPost("CreateCompanySettlement")]
        public IActionResult CreateCompanySettlement([FromBody] QueryCreateCompanySettlement query)
        {
            result.Success = _externalSystemCompanyService.CreateCompanySettlement(query);
            return Ok(result);
        }

        /// <summary>
        /// 删除结算数据
        /// </summary>
        /// <param name="seqNo"></param>
        /// <returns></returns>
        [HttpPost("DeleteCompanySettlement")]
        public IActionResult DeleteCompanySettlement([FromQuery] string seqNo)
        {
            result.Success = _externalSystemCompanyService.DeleteCompanySettlement(seqNo);
            return Ok(result);
        }

        /// <summary>
        /// 获取结算记录
        /// </summary>
        /// <param name="companyCode"></param>
        /// <param name="companyTimes"></param>
        /// <returns></returns>
        [HttpPost("GetCompanySettlement")]
        [ProducesResponseType(typeof(List<CompanySettlement>), 200)]
        public IActionResult GetCompanySettlement([FromQuery] string companyCode, [FromQuery] int companyTimes)
        {
            result.Success = true;
            result.ReturnData = _externalSystemCompanyService.GetCompanySettlement(companyCode, companyTimes);
            return Ok(result);
        }

        /// <summary>
        /// 审核结算信息 产生HIS结算数据
        /// </summary>
        /// <param name="seqNo"></param>
        /// <returns></returns>
        [HttpPost("AuditCompanySettlement")]
        public async Task<IActionResult> AuditCompanySettlementAsync([FromQuery] string seqNo)
        {
            result.Success = await _externalSystemCompanyService.AuditCompanySettlement(seqNo);
            return Ok(result);
        }
        /// <summary>
        /// 更新结算记录的发票抬头 社会信用代码 发票号
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        [HttpPost("UpdateCompanySettlement")]
        [ProducesResponseType(typeof(UpdateCompanySettlementParam), 200)]
        public IActionResult UpdateCompanySettlement([FromBody] UpdateCompanySettlementParam param)
        {
            result.Success = _externalSystemCompanyService.UpdateCompanySettlement(param);
            return Ok(result);
        }

        /// <summary>
        /// 结算费用明细报表
        /// </summary>
        /// <param name="seqNo"></param>
        /// <returns></returns>
        [HttpPost("QueryCompanySettlementDetail")]
        [ProducesResponseType(typeof(CompanySettlementReport), 200)]
        public IActionResult QueryCompanySettlementDetail([FromQuery] string seqNo)
        {
            result.Success = true;
            result.ReturnData = _externalSystemCompanyService.QueryCompanySettlementDetail(seqNo);
            return Ok(result);
        }

        /// <summary>
        /// 退费申请
        /// </summary>
        /// <param name="seqNo"></param>
        /// <returns></returns>
        [HttpPost("RefundCompanySettlement")]
        public IActionResult RefundCompanySettlement([FromQuery] string seqNo)
        {
            result.Success = _externalSystemCompanyService.RefundCompanySettlement(seqNo);
            return Ok(result);
        }

        /// <summary>
        /// 撤销退费申请
        /// </summary>
        /// <param name="seqNo"></param>
        /// <returns></returns>
        [HttpPost("CancelRefundCompanySettlement")]
        public IActionResult CancelRefundCompanySettlement([FromQuery] string seqNo)
        {
            result.Success = _externalSystemCompanyService.CancelRefundCompanySettlement(seqNo);
            return Ok(result);
        }

        /// <summary>
        /// 费用结算打印单数据源
        /// </summary>
        /// <param name="seqNo"></param>
        /// <returns></returns>
        [HttpGet("GetExaminationReportSource")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [AllowAnonymous]
        public ActionResult<SettlementFormDatasource> GetExaminationReportSource([FromQuery] string seqNo)
        {
            return _externalSystemCompanyService.GetSettlementFormDatasource(seqNo);
        }

        /// <summary>
        /// 结算费用明细报表(按人员)
        /// </summary>
        /// <param name="seqNo"></param>
        /// <returns></returns>
        [HttpPost("QueryCompanySettlementDetailByPerson")]
        [ProducesResponseType(typeof(CompanySettlementReportByPerson), 200)]
        public IActionResult QueryCompanySettlementDetailByPerson([FromQuery] string seqNo)
        {
            result.Success = true;
            result.ReturnData = _externalSystemCompanyService.QueryCompanySettlementDetailByPerson(seqNo);
            return Ok(result);
        }

        /// <summary>
        /// 取消审核结算记录
        /// </summary>
        /// <param name="seqNo"></param>
        /// <returns></returns>
        [HttpPost("CancelAuditCompanySettlement")]
        public IActionResult CancelAuditCompanySettlement([FromQuery] string seqNo)
        {
            result.Success = _externalSystemCompanyService.CancelAuditCompanySettlement(seqNo);
            return Ok(result);
        }

        /// <summary>
        /// 按结算时间获取单位结算记录
        /// </summary>
        /// <param name="beginTime"></param>
        /// <param name="endTime"></param>
        /// <returns></returns>
        [HttpPost("QueryCompanySettlementByChargeTime")]
        [ProducesResponseType(typeof(List<CompanySettlement>), 200)]
        public IActionResult QueryCompanySettlementByChargeTime([FromQuery] DateTime beginTime, [FromQuery] DateTime endTime)
        {
            result.ReturnData = _externalSystemCompanyService.QueryCompanySettlementByChargeTime(beginTime, endTime);
            return Ok(result);
        }

        /// <summary>
        /// 获取弃检人员及其弃检项目金额
        /// </summary>
        /// <param name="seqNo"></param>
        /// <returns></returns>
        [HttpPost("ReadSettlementPersonAbandomCombs")]
        [ProducesResponseType(typeof(SettlementPersonAbandomCombs[]), 200)]
        public IActionResult ReadSettlementPersonAbandomCombs([FromQuery] string seqNo)
        {
            result.ReturnData = _externalSystemCompanyService.ReadSettlementPersonAbandomCombs(seqNo);
            result.Success = true;
            return Ok(result);
        }

        /// <summary>
        /// 重置团体收费对应组合
        /// </summary>
        /// <param name="companyCode"></param>
        /// <param name="companyTimes"></param>
        /// <returns></returns>
        [HttpPost("UpdateCompanyChargeItem")]
        public IActionResult UpdateCompanyChargeItem([FromQuery] string companyCode, [FromQuery] int companyTimes)
        {
            _externalSystemCompanyService.UpdateCompanyChargeItem(companyCode, companyTimes);
            result.Success = true;
            return Ok(result);
        }

        /// <summary>
        /// 同步团体结算订单信息
        /// </summary>
        /// <param name="BillSeqNo">结算号</param>
        /// <returns></returns>
        [HttpGet("SyncCompanyOrder/{BillSeqNo}")]
        [AllowAnonymous]
        public IActionResult SyncCompanyOrder(string BillSeqNo)
        {
            _mediator.Publish(new SyncCompanySettlementOrder.Data(BillSeqNo));
            return Ok(result);
        }
    }
}
