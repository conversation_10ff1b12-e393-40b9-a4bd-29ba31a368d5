﻿using System.Reflection;
using Microsoft.Extensions.Options;
using Microsoft.OpenApi.Models;
using Peis.External.API.Options.DataBase;
using Peis.External.Wx.Service;
using Peis.Utility.Helper;
using Peis.Utility.Middleware;
using Peis.Utility.PeUser;
using Peis.Utility.SqlSugarExtension;
using SqlSugar;
using Volo.Abp;
using Volo.Abp.AspNetCore;
using Volo.Abp.Autofac;
using Volo.Abp.Modularity;

namespace Peis.External.API
{
    [DependsOn(typeof(AbpAspNetCoreModule))]
    [DependsOn(typeof(AbpAutofacModule))]
    [DependsOn(typeof(PeisExternalWxModule))]
    public class PeisExternalApiModule : AbpModule
    {
        public override void ConfigureServices(ServiceConfigurationContext context)
        {
            var services = context.Services;
            var configuration = services.GetConfiguration();

            services.AddMemoryCache();
            services.AddControllers();
            services.AddEndpointsApiExplorer();

            #region 配置跨域
            services.AddCors(opt =>
            {
                opt.AddPolicy("Cors", builder =>
                {
                    builder
                       //.WithOrigins(Appsettings.GetSectionArraryValue("AppSettings:CorsIPs"))
                       .SetIsOriginAllowed(t => true)
                       .AllowAnyMethod()
                       .AllowAnyHeader()
                       .AllowCredentials();
                });
            });
            #endregion

            #region 配置swagger操作文档
            services.AddSwaggerGen(opt =>
            {
                opt.SwaggerDoc("v1", new OpenApiInfo { Title = "API", Version = "v1" });
                var entryXmlPath = Path.Combine(AppContext.BaseDirectory, $"{Assembly.GetExecutingAssembly().GetName().Name}.xml");
                if (File.Exists(entryXmlPath))
                    opt.IncludeXmlComments(entryXmlPath, true);

                var serviceXmlPath = Path.Combine(AppContext.BaseDirectory, $"Peis.External.Wx.Service.xml");
                if (File.Exists(serviceXmlPath))
                    opt.IncludeXmlComments(serviceXmlPath, true);
            });
            #endregion

            #region Sqlsugar
            services.Configure<PeisDataBaseOptions>(configuration.GetSection(PeisDataBaseOptions.Key));
            services.AddScoped<ISqlSugarClient>(sp =>
            {
                var peisOptions = sp.GetService<IOptionsSnapshot<PeisDataBaseOptions>>()!.Value;
                var _db = new SqlSugarClient(new ConnectionConfig()
                {
                    ConnectionString = peisOptions.ConnectionString,
                    DbType = DbType.SqlServer,
                    IsAutoCloseConnection = true,//开启自动释放模式
                    InitKeyType = InitKeyType.Attribute //从特性读取主键和自增列信息
                });

                _db.CurrentConnectionConfig.ConfigureExternalServices = new ConfigureExternalServices()
                {
                    SplitTableService = new yyyySplitTableService()
                };

                if (peisOptions.PrintSQL)
                    _db.Aop.OnLogExecuting = (sql, pars) =>
                    {
                        Console.WriteLine(sql + "\r\n" + _db.Utilities.SerializeObject(pars.ToDictionary(it => it.ParameterName, it => it.Value)));
                        Console.WriteLine();
                    };

                return _db;
            });
            #endregion

            services.AddSingleton<IHttpContextAccessor, HttpContextAccessor>();
            services.AddScoped<IHttpContextUser, HttpContextUserByWeChat>();
        }

        public override void OnApplicationInitialization(ApplicationInitializationContext context)
        {
            var app = context.GetApplicationBuilder();
            var env = context.GetEnvironment();

            if (env.IsDevelopment())
            {
                app.UseDeveloperExceptionPage();
            }

            #region 使用跨域
            app.UseCors("Cors");
            #endregion

            #region 使用swagger
            if (Appsettings.GetSectionBooleanValue("AppSettings:SwaggerIsEnabled"))
            {
                app.UseSwagger();
                app.UseSwaggerUI(option =>
                {
                    option.SwaggerEndpoint("/swagger/v1/swagger.json", "API V1");
                    option.RoutePrefix = "swagger";
                    option.DocumentTitle = "WeChat API";
                });
            }
            #endregion

            app.UseRouting();

            // 解密请求
            if (Appsettings.GetSectionBooleanValue("Middleware:UseRequestDecrypt"))
                app.UseRequestDecryptWeChatMiddleware();

            // Serilog日志上下文
            app.UseRequestLogContextMiddleware();
            app.UseReqResLoggerMiddleware();

            // 异常处理
            if (Appsettings.GetSectionBooleanValue("Middleware:UseExceptionHandling"))
                app.UseExceptionHandlingMiddleware();

            app.UseEndpoints(endpoints =>
            {
                endpoints.MapControllers();
            });
        }
    }
}
