﻿using SqlSugar;

namespace Peis.Model.DataBaseEntity
{
    ///<summary>
    ///院区代码信息
    ///</summary>
    [SugarTable("CodeHospital")]
    public class CodeHospital
    {
        /// <summary>
        /// 院区编码
        /// </summary>        
        [SugarColumn(IsNullable = false, IsPrimaryKey = true, Length = 1)]
        public string HospCode { get; set; }

        /// <summary>
        /// 院区名称
        /// </summary>        
        [SugarColumn(IsNullable = false, Length = 100)]
        public string HospName { get; set; }

        /// <summary>
        /// 院区简称（暂时用于报表前缀作过滤）
        /// </summary>
        [SugarColumn(IsNullable = false, Length = 100)]
        public string HospShortName { get; set; }

        /// <summary>
        /// 备注
        /// </summary>        
        [SugarColumn(IsNullable = true, Length = 250)]
        public string Note { get; set; }
    }
}