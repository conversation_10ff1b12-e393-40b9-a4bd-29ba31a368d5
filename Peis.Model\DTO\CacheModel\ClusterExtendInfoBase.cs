﻿using Peis.Model.DTO.Register;
using Peis.Model.Other.PeEnum;

namespace Peis.Model.DTO.CacheModel;

public class ClusterExtendInfoBase
{
    /// <summary>
    /// 套餐代码
    /// </summary>
    public string CLusCode { get; set; }
    /// <summary>
    /// 套餐名称
    /// </summary>
    public string ClusterName { get; set; }
    /// <summary>
    /// 金额
    /// </summary>
    public decimal Price { get; set; }
    /// <summary>
    /// 性别
    /// </summary>
    public Sex Sex { get; set; }
    /// <summary>
    /// 是否职业病套餐
    /// </summary>
    public bool IsOccupation { get; set; }
    /// <summary>
    /// 危害因素代码
    /// </summary>
    [SugarColumn(IsJson =true)]
    public List<string> Hazards { get; set; }
    /// <summary>
    /// 在岗状态
    /// </summary>
    public string JobStatus { get; set; }
    /// <summary>
    /// 套餐绑定组合
    /// </summary>
    public CandidateClusterBindComb[] ClusCombs { get; set; }
}

/// <summary>
/// 普通套餐扩展信息
/// </summary>
public class CommonClusterExtendInfo: ClusterExtendInfoBase
{
    /// <summary>
    /// 套餐互斥组合
    /// </summary>
    public string[] MutexCombs { get; set; }
}

/// <summary>
/// 单位套餐扩展信息
/// </summary>
public class CompanyClusterExtendInfo:ClusterExtendInfoBase
{
}


