﻿using Microsoft.AspNetCore.Mvc;
using Peis.Model.DataBaseEntity.Occupation;
using Peis.Model.DTO.Occupation.Record;
using Peis.Service.IService.Occupation.Record;

namespace Peis.API.Controllers.Occupation
{
    [Route("api/Occupation/[controller]")]
    [ApiController]
    public class RecordController : BaseApiController
    {
        private readonly IOccupationalRecordService _resultService;

        public RecordController(IOccupationalRecordService resultService)
        {
            _resultService = resultService;
        }

        /// <summary>
        /// 计算纯音测听结论
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost("CalculateAuditoryResult")]
        [ProducesResponseType(typeof(PeRecordAuditoryResult), 200)]
        public IActionResult CalculateAuditoryResult([FromBody] AuditoryResultDto input)
        {
            result.ReturnData = _resultService.CalculateAuditoryResult(input);
            return Ok(result);
        }

        /// <summary>
        /// 保存纯音测听结论
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost("SaveAuditoryResult")]
        public IActionResult SaveAuditoryResult([FromBody] PeRecordAuditoryResult input)
        {
            _resultService.SaveAuditoryResult(input);
            result.Success = true;
            return Ok(result);
        }

        /// <summary>   
        /// 读取听力测试结果
        /// </summary>
        [HttpPost("ReadAuditoryResult")]
        [ProducesResponseType(typeof(PeRecordAuditoryResult), 200)]
        public IActionResult ReadAuditoryResult([FromQuery] string regNo)
        {
            result.ReturnData = _resultService.ReadAuditoryResult(regNo);
            result.Success = true;
            return Ok(result);
        }

        /// <summary>   
        /// 读取听力测试结果
        /// </summary>
        [HttpPost("DeleteAuditoryResult")]
        public IActionResult DeleteAuditoryResult([FromQuery] string regNo)
        {
            result.Success = _resultService.DeleteAuditoryResult(regNo);
            return Ok(result);
        }

        /// <summary>
        /// 保存吸烟史结果
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost("SaveSmokingHistory")]
        public IActionResult SaveSmokingHistory([FromBody] PeRecordGeneralConsultation input)
        {
            _resultService.SaveSmokingHistory(input);
            result.Success = true;
            return Ok(result);
        }

        /// <summary>
        /// 读取吸烟史结果
        /// </summary>
        /// <param name="regNo"></param>
        /// <returns></returns>
        [HttpPost("ReadSmokingHistory")]
        [ProducesResponseType(typeof(PeRecordGeneralConsultation), 200)]
        public IActionResult ReadSmokingHistory([FromQuery] string regNo)
        {
            result.ReturnData = _resultService.ReadSmokingHistory(regNo);
            result.Success = true;
            return Ok(result);
        }

        /// <summary>
        /// 删除吸烟史结果
        /// </summary>
        /// <param name="regNo"></param>
        /// <returns></returns>
        [HttpPost("DeleteSmokingHistory")]
        public IActionResult DeleteSmokingHistory([FromQuery] string regNo)
        {
            result.Success = _resultService.DeleteSmokingHistory(regNo);
            return Ok(result);
        }

        /// <summary>
        /// 保存症状询问数据
        /// </summary>
        /// <param name="symptoms"></param>
        /// <returns></returns>
        [HttpPost("SaveSymptomResults")]
        public IActionResult SaveSymptomResults([FromBody] SymptomRecord symptoms)
        {
            result.Success = _resultService.SaveSymptomResults(symptoms);
            return Ok(result);
        }

        /// <summary>
        /// 获取症状询问数据
        /// </summary>
        /// <param name="regNo"></param>
        /// <returns></returns>
        [HttpPost("GetSymptomResults")]
        [ProducesResponseType(typeof(SymptomRecord), 200)]
        public IActionResult GetSymptomResults([FromQuery] string regNo)
        {
            result.Success = true;
            result.ReturnData = _resultService.GetSymptomResults(regNo);
            return Ok(result);
        }

        /// <summary>
        /// 保存职业病问诊结果
        /// </summary>
        /// <param name="occupationalConsultation"></param>
        /// <returns></returns>
        [HttpPost("SaveOccupationalConsultation")]
        public IActionResult SaveOccupationalConsultation([FromBody] OccupationalConsultationDto occupationalConsultation)
        {
            result.Success = _resultService.SaveOccupationalConsultation(occupationalConsultation);
            return Ok(result);
        }

        /// <summary>
        /// 获取职业病问诊结果
        /// </summary>
        /// <param name="regNo"></param>
        /// <returns></returns>
        [HttpPost("GetOccupationalConsultation")]
        [ProducesResponseType(typeof(OccupationalConsultationDto), 200)]
        public IActionResult GetOccupationalConsultation([FromQuery] string regNo)
        {
            result.Success = true;
            result.ReturnData = _resultService.GetOccupationalConsultation(regNo);
            return Ok(result);
        }
    }

}
