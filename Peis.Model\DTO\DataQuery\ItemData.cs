﻿namespace Peis.Model.DTO.DataQuery
{
    /// <summary>
    /// 项目情况
    /// </summary>
    public class ItemData
    {
        /// <summary>
        /// 套餐名称
        /// </summary>
        public string ClusName { get; set; }

        /// <summary>
        /// 项目分类
        /// </summary>
        public ItemCls[] ItemCls { get; set; }

        /// <summary>
        /// 加项的项目
        /// </summary>
        public CombData[] AddItem { get; set; }

        /// <summary>
        /// 未检的项目
        /// </summary>
        public CombData[] NoCheckItems { get; set; }

        /// <summary>
        /// 拒检的项目
        /// </summary>
        public CombData[] AbandonItems { get; set; }

        /// <summary>
        /// 单位支付的项目
        /// </summary>
        public CombData[] CompanyPayItems { get; set; }

        /// <summary>
        /// 预约平台支付的项目
        /// </summary>
        public CombData[] PlatformPayItems { get; set; }

        /// <summary>
        /// 个人支付的项目
        /// </summary>
        public CombData[] SelfPayItems { get; set; }
    }

    /// <summary>
    /// 项目分类
    /// </summary>
    public class ItemCls
    {
        /// <summary>
        /// 项目分类代码
        /// </summary>
        public string ClsCode { get; set; }

        /// <summary>
        /// 项目分类名称
        /// </summary>
        public string ClsName { get; set; }
    }

    /// <summary>
    /// 组合
    /// </summary>
    public class CombData
    {
        /// <summary>
        /// 组合代码
        /// </summary>
        public string CombCode { get; set; }

        /// <summary>
        /// 组合名称
        /// </summary>
        public string CombName { get; set; }
    }
}
