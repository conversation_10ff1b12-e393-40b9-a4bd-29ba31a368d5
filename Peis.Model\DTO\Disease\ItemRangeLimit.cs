﻿namespace Peis.Model.DTO.Disease
{
    public class ItemRangeLimit
    {
        /// <summary>
        /// 项目代码
        /// </summary>
        public string ItemCode { get; set; }

        /// <summary>
        /// 项目名称
        /// </summary>
        public string ItemName { get; set; }

        /// <summary>
        /// 项目结果类型
        /// </summary>
        public Other.PeEnum.ValueType ResultType { get; set; }

        /// <summary>
        /// 结果单位
        /// </summary>
        public string Unit { get; set; }

        /// <summary>
        /// 结果下限
        /// </summary>
        public string LowerLimit { get; set; }

        /// <summary>
        /// 结果上限
        /// </summary>
        public string UpperLimit { get; set; }

        /// <summary>
        /// 危急下限
        /// </summary>
        public string DangerLowerLimit { get; set; }

        /// <summary>
        /// 危急上限
        /// </summary>
        public string DangerUpperLimit { get; set; }
    }
}
