﻿using Peis.Model.DataBaseEntity;
using Peis.Model.DataBaseEntity.Occupation;
using Peis.Model.DTO.CacheModel;
using Peis.Model.DTO.ReportConclusionNew;
using Peis.Model.Other.PeEnum;
using System;
using System.Collections.Generic;
using System.Reflection;

namespace Peis.Repository.IRepository
{
    public interface ICacheRepository
    {
        /// <summary>
        /// 获取套餐字典
        /// </summary>
        /// <returns></returns>
        Dictionary<string, CommonClusterExtendInfo> DictCluster();

        /// <summary>
        /// 获取组合字典
        /// </summary>
        /// <returns></returns>
        Dictionary<string, CombExtendInfo> DictComb();
      
        /// <summary>
        /// 获取团体套餐字典
        /// </summary>
        /// <returns></returns>
        Dictionary<string, CompanyClusterExtendInfo> DictCompanyCluster();

        /// <summary>
        /// 获取条码类型字典
        /// </summary>
        /// <returns></returns>
        Dictionary<string, CodeBarcodeType> DictBarcode();

        /// <summary>
        /// 获取危害因素字典
        /// </summary>
        /// <returns></returns>
        Dictionary<string,HazardFacotrExtendInfo> DictHazardFactor();

        /// <summary>
        /// 获取职业病项目代码字典
        /// </summary>
        /// <returns></returns>
        Dictionary<string, string> DictOccupationItemCode();

        /// <summary>
        /// 获取职业病项目单位字典
        /// </summary>
        /// <returns></returns>
        Dictionary<string, string> DictOccupationItemUnit();

        /// <summary>
        /// 获取症状询问对应字典
        /// </summary>
        /// <returns></returns>
        Dictionary<string, string> DictSymptomMap();

        /// <summary>
        /// 获取项目限定字典
        /// </summary>
        /// <returns></returns>
        Dictionary<Sex, Dictionary<string, CodeOccupationalItemLimit>> DictItemLimit();

        Dictionary<string, List<MapCompanyClusterCombTestTube>> DictCompanyClusterTestTube();

        Dictionary<string, string> DictOccupationJob();

        /// <summary>
        /// 获取疾病及词条字典
        /// </summary>
        /// <returns></returns>
        Dictionary<string, Disease> DictDisease();

        /// <summary>
        /// 症状询问字典
        /// </summary>
        /// <returns></returns>
        Dictionary<string, string> DictSymptom();
        Dictionary<Type, PropertyInfo[]> DictMaintainCode();

        Dictionary<string,Dictionary<string,string>> DictAddressRelation();

        /// <summary>
        /// 重大阳性
        /// </summary>
        /// <returns></returns>
        Dictionary<string, MajorPositive> DictMajorPositive();
        /// <summary>
        /// 在岗状态
        /// </summary>
        /// <returns></returns>
        Dictionary<string, string> DictJobStatus();

        /// <summary>
        /// 单位信息
        /// </summary>
        /// <returns></returns>
        Dictionary<string, CodeCompany> DictCompany();
        /// <summary>
        /// 项目信息
        /// </summary>
        /// <returns></returns>
        Dictionary<string, CodeItem> DictCodeItem();
        /// <summary>
        /// 职业病项目信息
        /// </summary>
        /// <returns></returns>
        Dictionary<string, string> DictCodeOccupationalItem();

        /// <summary>
        /// 获取岗位字典
        /// </summary>
        /// <returns></returns>
        Dictionary<string, CodeJob> DictJob();

        /// <summary>
        /// 获取单位部门字典
        /// </summary>
        /// <returns></returns>
        Dictionary<string, CodeCompanyDepartment> DictCompanyDepartment();

        /// <summary>
        /// 获取科室字典
        /// </summary>
        /// <returns></returns>
        Dictionary<string, CodeDepartment> DictDepartment();

        /// <summary>
        /// 获取项目分类字典
        /// </summary>
        /// <returns></returns>
        Dictionary<string, CodeItemCls> DictItemCls();

        /// <summary>
        /// 获取样本字典
        /// </summary>
        /// <returns></returns>
        Dictionary<string, CodeSample> DictSample();
    }
}
