﻿using System.Text.Json.Serialization;
using Peis.Model.Other.PeEnum;
using Peis.Model.TableFilter;

namespace Peis.Model.DataBaseEntity
{
    ///<summary>
    ///体检套餐
    ///</summary>
    [SugarTable("CodeCluster")]
    public class CodeCluster : IHospCodeFilter
    {
        /// <summary>
        /// 套餐代码
        /// </summary>        
        [SugarColumn(IsNullable = false, IsPrimaryKey = true, Length = 6)]
        public string ClusCode { get; set; }

        /// <summary>
        /// 套餐名称
        /// </summary>        
        [SugarColumn(IsNullable = false, Length = 200)]
        public string ClusName { get; set; }

        /// <summary>
        /// 单价
        /// </summary>        
        [SugarColumn(IsNullable = false)]
        public decimal Price { get; set; }

        /// <summary>
        /// 使用性别（0 通用 1 男  2 女）
        /// </summary>
        [SugarColumn(IsNullable = false)]
        public Sex Sex { get; set; }

        /// <summary>
        /// 体检分类
        /// </summary>        
        [SugarColumn(IsNullable = true)]
        public PeCls PeCls { get; set; }

        /// <summary>
        /// 申请单样式
        /// </summary>        
        [SugarColumn(IsNullable = false, Length = 32)]
        public string GuidanceType { get; set; }

        /// <summary>
        /// 报告单样式
        /// </summary>        
        [SugarColumn(IsNullable = false, Length = 32)]
        public string ReportType { get; set; }

        /// <summary>
        /// 注意事项
        /// </summary>        
        [SugarColumn(IsNullable = true, Length = 255)]
        public string Attention { get; set; }

        /// <summary>
        /// 体检须知
        /// </summary>        
        [SugarColumn(IsNullable = true, Length = 255)]
        public string Note { get; set; }

        /// <summary>
        /// 年龄下限
        /// </summary>        
        [SugarColumn(IsNullable = true)]
        public int LowerAgeLimit { get; set; }

        /// <summary>
        /// 年龄上限
        /// </summary>        
        [SugarColumn(IsNullable = true)]
        public int UpperAgeLimit { get; set; }

        /// <summary>
        /// 打印健康证
        /// </summary>        
        [SugarColumn(IsNullable = false)]
        public bool PrintJKZ { get; set; }

        /// <summary>
        /// 启用标识
        /// </summary>        
        [SugarColumn(IsNullable = false)]
        public bool IsEnabled { get; set; }

        /// <summary>
        /// 微信显示套餐
        /// </summary>        
        [SugarColumn(IsNullable = false)]
        public bool WechatShow { get; set; }

        /// <summary>
        /// 显示顺序
        /// </summary>        
        [SugarColumn(IsNullable = false)]
        public int SortIndex { get; set; }

        /// <summary>
        /// 拼音码
        /// </summary>        
        [SugarColumn(IsNullable = true, Length = 20)]
        public string PinYinCode { get; set; }

        /// <summary>
        /// 五笔码
        /// </summary>        
        [SugarColumn(IsNullable = true, Length = 20)]
        public string WuBiCode { get; set; }

        /// <summary>
        /// 院区编码
        /// </summary>        
        [SugarColumn(IsNullable = false, Length = 1)]
        public string HospCode { get; set; }

        /// <summary>
        /// 是否职业
        /// </summary>
        [SugarColumn(IsNullable = false, DefaultValue = "0")]
        public bool IsOccupation { get; set; }

        /// <summary>
        /// 危害因素列表
        /// </summary>
        [SugarColumn(IsNullable = true, IsJson = true, Length = 255)]
        public List<string> HazardFactors { get; set; }

        /// <summary>
        /// 在岗状态
        /// </summary>
        [SugarColumn(IsNullable = true, Length = 10)]
        public string JobStatus { get; set; }

        #region Ext
        /// <summary>
        /// 组合对应
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public List<MapClusterComb> MapClusterCombs { get; set; }

        /// <summary>
        /// 互斥组合
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public List<CodeClusMutexComb> ClusMutexCombs { get; set; }

        /// <summary>
        /// 组合代码
        /// </summary>        
        [SugarColumn(IsIgnore = true)]
        [JsonIgnore]
        public string CombCode { get; set; }

        /// <summary>
        /// 组合名称
        /// </summary>        
        [SugarColumn(IsIgnore = true)]
        [JsonIgnore]
        public string CombName { get; set; }

        /// <summary>
        /// 原始单价
        /// </summary>        
        [SugarColumn(IsIgnore = true)]
        [JsonIgnore]
        public decimal CombOriginalPrice { get; set; }

        /// <summary>
        /// 单价
        /// </summary>        
        [SugarColumn(IsIgnore = true)]
        [JsonIgnore]
        public decimal CombPrice { get; set; }

        /// <summary>
        /// 折扣率
        /// </summary>        
        [SugarColumn(IsIgnore = true)]
        [JsonIgnore]
        public decimal CombDiscount { get; set; }

        /// <summary>
        /// 危害因素名称
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public string HazardNames { get; set; }

        /// <summary>
        /// 在岗状态名称
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public string JobStatusName { get; set; }
        #endregion
    }
}