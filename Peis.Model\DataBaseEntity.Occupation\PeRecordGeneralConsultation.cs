﻿using Peis.Model.Other.PeEnum;
using Peis.Model.Other.PeEnum.Occupation;

namespace Peis.Model.DataBaseEntity.Occupation;
[SugarTable]
public class PeRecordGeneralConsultation
{
    /// <summary>
    /// 体检号
    /// </summary>
    [SugarColumn(IsPrimaryKey = true, Length = 12)]
    public string RegNo { get; set; }

    #region 月经史
    /// <summary>
    /// 月经史-初潮年龄
    /// </summary>
    [SugarColumn(IsNullable = true, Length = 10)]
    public int? MenarcheAge { get; set; }
    /// <summary>
    /// 月经史-经期
    /// </summary>
    [SugarColumn(IsNullable = true, Length = 10)]
    public string MenstrualPeriod { get; set; }
    /// <summary>
    /// 月经史-周期
    /// </summary>
    [SugarColumn(IsNullable = true)]
    public string MenstrualCycle { get; set; }
    /// <summary>
    /// 月经史-停经年龄
    /// </summary>
    [SugarColumn(IsNullable = true)]
    public int? MenopauseAge { get; set; }
    /// <summary>
    /// 月经史-是否经期
    /// </summary>
    [SugarColumn(IsNullable = true)]
    public BoolType? IsMenstrualPeriod { get; set; }
    #endregion
    #region 生育史
    /// <summary>
    /// 生育史-子女人数
    /// </summary>
    [SugarColumn(IsNullable = true)]
    public int? ChildQuantity { get; set; }
    /// <summary>
    /// 生育史-流产次数
    /// </summary>
    [SugarColumn(IsNullable = true)]
    public int? AbortiveTimes { get; set; }
    /// <summary>
    /// 生育史-早产次数
    /// </summary>
    [SugarColumn(IsNullable = true)]
    public int? PrematureTimes { get; set; }
    /// <summary>
    /// 生育史-死产次数
    /// </summary>
    [SugarColumn(IsNullable = true)]
    public string StillbirthTimes { get; set; }
    /// <summary>
    /// 生育史-畸胎次数
    /// </summary>
    [SugarColumn(IsNullable = true)]
    public int? TeratomaTimes { get; set; }
    /// <summary>
    /// 生育史-子女健康状况
    /// </summary>
    [SugarColumn(IsNullable = true, Length = 250)]
    public string ChildrenHealthStatus { get; set; }
    #endregion
    #region 婚姻史
    /// <summary>
    /// 婚姻史-婚姻状况
    /// </summary>
    [SugarColumn(IsNullable = true)]
    public MarryStatus? MarryStatus { get; set; }
    /// <summary>
    /// 婚姻史-结婚日期  
    /// </summary>
    [SugarColumn(IsNullable = true)]
    public DateTime? MarrageDate { get; set; }
    /// <summary>
    /// 配偶接触放射线情况
    /// </summary>
    [SugarColumn(IsNullable = true, Length = 100)]
    public string Spouse { get; set; }
    /// <summary>
    /// 配偶职业及健康状况
    /// </summary>
    [SugarColumn(IsNullable = true, Length = 100)]
    public string SpouseHealthStatus { get; set; }
    #endregion
    #region 吸烟史
    /// <summary>
    /// 烟酒史-吸烟情况 对照字典3.26
    /// </summary>
    [SugarColumn(IsNullable = true)]
    public SmokingStatus SmokingStatus { get; set; }
    /// <summary>
    /// 平均每天吸烟量-支
    /// </summary>
    [SugarColumn(IsNullable = true)]
    public int SmokingCountPerDays { get; set; }
    /// <summary>
    /// 吸烟史-年
    /// </summary>
    [SugarColumn(IsNullable = true)]
    public int SmokingYears { get; set; }
    /// <summary>
    /// 吸烟史-月
    /// </summary>
    [SugarColumn(IsNullable = true)]
    public int SmokingMonths { get; set; }
    #endregion
    #region 饮酒史
    /// <summary>
    /// 饮酒情况
    /// </summary>
    [SugarColumn(IsNullable = true)]
    public DrinkingStatus DrinkingStatus { get; set; }
    /// <summary>
    /// 饮酒每天毫升数
    /// </summary>
    [SugarColumn(IsNullable = true)]
    public int DrinkingMillilitersPerDays { get; set; }
    /// <summary>
    /// 饮酒年数
    /// </summary>
    [SugarColumn(IsNullable = true)]
    public int DrinkingHistoryYear { get; set; }
    #endregion
    /// <summary>
    /// 家族史
    /// </summary>
    [SugarColumn(IsNullable = true, Length = 500)]
    public string FamilyHistory { get; set; }
    /// <summary>
    /// 个人史
    /// </summary>
    [SugarColumn(IsNullable = true, Length = 500)]
    public string PersonalHistory { get; set; }
    /// <summary>
    /// 其他情况
    /// </summary>
    [SugarColumn(IsNullable = true, Length = 250)]
    public string OtherSituations { get; set; }
    /// <summary>
    /// 检查日期
    /// </summary>
    [SugarColumn(IsNullable = false)]
    public DateTime ExamTime { get; set; }
    /// <summary>
    /// 检查医生
    /// </summary>
    [SugarColumn(IsNullable = false,Length = 50)]
    public string DoctorName { get; set; }
}
