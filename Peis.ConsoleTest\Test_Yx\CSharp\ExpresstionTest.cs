﻿using Peis.Utility.Helper;
using System;
using System.Linq;
using System.Linq.Expressions;

namespace Peis.ConsoleTest.Test_Yx.CSharp
{
    class Product
    {
        public string Name { get; set; }
        public decimal Price { get; set; }
        public bool OnSale { get; set; }
    }

    public class ExpresstionTest
    {
        public static void Invoke()
        {
            var products = new Product[]
            {
                new() { Name = "苹果", Price = 11.50M, OnSale = true },
                new() { Name = "西瓜", Price = 20.50M, OnSale = true },
                new() { Name = "香蕉", Price = 5.50M, OnSale = true }
            };

            Expression<Func<Product, bool>> isSalling = x => x.OnSale;
            Expression<Func<Product, bool>> isApple = x => x.Name == "苹果";

            var result = products
                .Where(isSalling.Compile())
                .Where(isApple.Compile())
                .ToArray();

            Console.WriteLine(result.ToJson());
        }
    }
}
