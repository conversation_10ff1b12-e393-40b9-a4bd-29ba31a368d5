﻿using Peis.Model.Other.PeEnum;
using Peis.Model.TableFilter;

namespace Peis.Model.DataBaseEntity
{
    ///<summary>
    ///危急值消息表
    ///</summary>
    [SugarTable("CriticalValueMsg")]
    public class CriticalValueMsg: IHospCodeFilter
    {
        /// <summary>
        /// 危急值Id 
        /// </summary>        
        [SugarColumn(IsNullable = false, IsPrimaryKey = true)]
        public long Id { get; set; }

        /// <summary>
        /// 体检号
        /// </summary>        
        [SugarColumn(IsNullable = false, Length = 12)]
        public string RegNo { get; set; }

        /// <summary>
        /// 1 危急值 2 重大阳性
        /// </summary>        
        [SugarColumn(IsNullable = false)]
        public CriticalValueType Type { get; set; }

        /// <summary>
        /// 生成人
        /// </summary>        
        [SugarColumn(IsNullable = false, Length = 10)]
        public string Creator { get; set; }

        /// <summary>
        /// 生成时间
        /// </summary>        
        [SugarColumn(IsNullable = false)]
        public DateTime CreateTime { get; set; }

        /// <summary>
        /// 确认人
        /// </summary>        
        [SugarColumn(IsNullable = false, Length = 10)]
        public string Confirmer { get; set; }

        /// <summary>
        /// 确认时间
        /// </summary>        
        [SugarColumn(IsNullable = false)]
        public DateTime ConfirmTime { get; set; }

        /// <summary>
        /// 回复内容
        /// </summary>        
        [SugarColumn(IsNullable = true, Length = 150)]
        public string ReplyContent { get; set; }

        /// <summary>
        /// 回复人
        /// </summary>        
        [SugarColumn(IsNullable = true, Length = 10)]
        public string ReplyPerson { get; set; }

        /// <summary>
        /// 回复时间
        /// </summary>        
        [SugarColumn(IsNullable = true)]
        public DateTime? ReplyTime { get; set; }

        /// <summary>
        /// 绑定随访ID
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public long? FollowUpId { get; set; }

        /// <summary>
        /// 院区编码
        /// </summary>        
        [SugarColumn(IsNullable = false, Length = 1)]
        public string HospCode { get; set; }
    }
}