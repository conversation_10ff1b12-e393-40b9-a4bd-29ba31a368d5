﻿namespace Peis.Model.DataBaseEntity
{
    ///<summary>
    ///项目分类信息
    ///</summary>
    [SugarTable("CodeItemCls")]
    public class CodeItemCls
    {
        /// <summary>
        /// 项目分类代码
        /// </summary>        
        [SugarColumn(IsNullable = false, IsPrimaryKey = true, Length = 6)]
        public string ClsCode { get; set; }

        /// <summary>
        /// 项目分类名称
        /// </summary>        
        [SugarColumn(IsNullable = false, Length = 30)]
        public string ClsName { get; set; }

        /// <summary>
        /// 显示顺序
        /// </summary>        
        [SugarColumn(IsNullable = false)]
        public int SortIndex { get; set; }

        /// <summary>
        /// 拼音码
        /// </summary>        
        [SugarColumn(IsNullable = true, Length = 30)]
        public string PinYinCode { get; set; }

        /// <summary>
        /// 五笔码
        /// </summary>        
        [SugarColumn(IsNullable = true, Length = 30)]
        public string WuBiCode { get; set; }

        /// <summary>
        /// 按组合打印次数
        /// </summary>
        [SugarColumn(IsNullable = false)]
        public int CombPrintTimes { get; set; }

        /// <summary>
        /// 按组合打印-合并标签备注
        /// </summary>
        [SugarColumn(IsNullable = true, Length = 255)]
        public string CombLabelAlias { get; set; }

        /// <summary>
        /// 按分类打印次数
        /// </summary>
        [SugarColumn(IsNullable = false)]
        public int ClsPrintTimes { get; set; }

        /// <summary>
        /// 按分类打印-分类标签备注
        /// </summary>
        [SugarColumn(IsNullable = true, Length = 255)]
        public string ClsLabelAlias { get; set; }
    }
}