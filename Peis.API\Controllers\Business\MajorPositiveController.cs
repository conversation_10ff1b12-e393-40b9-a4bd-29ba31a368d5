﻿using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Peis.Model.DataBaseEntity;
using Peis.Model.DTO.MajorPositive;
using Peis.Model.Other.Input;
using Peis.Repository.Repository.TransactionAttribute;
using Peis.Service.IService;
using Peis.Utility.Helper;
using System.Collections.Generic;

namespace Peis.API.Controllers.Business
{
    /// <summary>
    /// 重大阳性
    /// </summary>
    [Route("api/[controller]")]
    [ApiController]
    public class MajorPositiveController:BaseApiController
    {
        private readonly IMajorPositiveService _majorPositiveService;

        public MajorPositiveController(IMajorPositiveService majorPositiveService)
        {
            _majorPositiveService = majorPositiveService;
        }

        #region 重大阳性列表
        /// <summary>
        /// /CU_MajorPositive/Create 新增重大阳性信息
        /// /CU_MajorPositive/Update 更新重大阳性信息
        /// </summary>
        /// <param name="type"></param>
        /// <param name="major"></param>
        /// <returns></returns>
        [HttpPost("CU_MajorPositive/{type}")]
        [UnitOfWork]
        public IActionResult CU_MajorPositive([FromRoute] string type, [FromBody] MajorPositive major)
        {
            string msg = string.Empty;

            switch (type.ToLower())
            {
                case "create":
                    result.Success = _majorPositiveService.CreateMajorPositive(major);
                    break;
                case "update":
                    result.Success = _majorPositiveService.UpdateMajorPositive(major);
                    break;
                default:
                    return new BadRequestResult();
            }
            result.ReturnMsg = msg;

            return Ok(result);
        }

        /// <summary>
        /// /RD_MajorPositive/Read   获取重大阳性信息
        /// /RD_MajorPositive/Delete 删除重大阳性信息
        /// </summary>
        /// <param name="type"></param>
        /// <param name="majors">重大阳性代码数组['01','02']</param>
        /// <returns></returns>
        [HttpPost("RD_MajorPositive/{type}")]
        public IActionResult RD_MajorPositive([FromRoute] string type, [FromBody] string[] majors)
        {
            string msg = string.Empty;

            switch (type.ToLower())
            {
                case "read":
                    result.ReturnData = _majorPositiveService.ReadMajorPositive();
                    break;
                case "delete":
                    result.Success = _majorPositiveService.DeleteMajorPositive(majors);
                    break;
                default:
                    return new BadRequestResult();
            }

            return Ok(result);
        }

        /// <summary>
        /// 保存重大阳性部位、关键字的对应关系信息
        /// </summary>
        /// <param name="data">重大阳性部位、关键字的对应关系信息</param>
        /// <returns></returns>
        [HttpPost("SaveMapPartWithKeywordMajorPositive")]
        [ProducesResponseType(typeof(bool), 200)]
        [UnitOfWork]
        public IActionResult SaveMapPartWithKeywordMajorPositive(List<MajorPositivePartWithKeywordDto> data)
        {
            result.Success = _majorPositiveService.SaveMapPartWithKeywordMajorPositive(data);
            return Ok(result);
        }

        /// <summary>
        /// 根据重大阳性代码获取部位与关键字关系信息
        /// </summary>
        /// <param name="positiveCode">重大阳性代码</param>
        /// <returns></returns>
        [HttpGet("GetMapPartWithKeywordMajorPositives/{positiveCode}")]
        [ProducesResponseType(typeof(List<MajorPositivePartWithKeywordDto>), 200)]
        public IActionResult GetMapPartWithKeywordMajorPositives(string positiveCode)
        {
            result.ReturnData = _majorPositiveService.GetMapPartWithKeywordMajorPositives(positiveCode);
            return Ok(result);
        }
        #endregion

        #region 重大阳性关键字
        /// <summary>
        /// 保存重大阳性关键字
        /// </summary>
        /// <param name="data"> 重大阳性关键字实体</param>
        /// <returns></returns>
        [HttpPost("SaveMajorPositiveKeyword")]
        [ProducesResponseType(typeof(bool), 200)]
        [UnitOfWork]
        public IActionResult SaveMajorPositiveKeyword(MajorPositiveKeyword data)
        {
            var code = _majorPositiveService.SaveMajorPositiveKeyword(data);
            result.Success = !code.IsNullOrEmpty();
            result.ReturnMsg = code;
            return Ok(result);
        }

        /// <summary>
        /// 获取重大阳性关键字列表
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        [HttpPost("GetMajorPositiveKeywords")]
        [ProducesResponseType(typeof(List<MajorPositiveKeyword>), 200)]
        public IActionResult GetMajorPositiveKeywords(MajorPositiveKeywordQuery query)
        {
            int totalNumber = 0;
            int totalPage = 0;
            result.ReturnData = _majorPositiveService.GetMajorPositiveKeywords(query, ref totalNumber, ref totalPage);
            result.TotalNumber = totalNumber;
            result.TotalPage = totalPage;
            return Ok(result);
        }

        /// <summary>
        /// 删除重大阳性关键字
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        [HttpPost("BatchRemoveMajorPositiveKeyword")]
        [ProducesResponseType(typeof(bool), 200)]
        [UnitOfWork]
        public IActionResult BatchRemoveMajorPositiveKeyword(MajorPositiveKeywordQuery query)
        {
            result.Success = _majorPositiveService.BatchRemoveMajorPositiveKeyword(query.KeywordCodes);
            return Ok(result);
        }
        #endregion

        #region 重大阳性部位
        /// <summary>
        /// 保存重大阳性部位
        /// </summary>
        /// <param name="data"> 重大阳性部位实体</param>
        /// <returns></returns>
        [HttpPost("SaveMajorPositivePart")]
        [ProducesResponseType(typeof(bool), 200)]
        [UnitOfWork]
        public IActionResult SaveMajorPositivePart(MajorPositivePart data)
        {
            var code = _majorPositiveService.SaveMajorPositivePart(data);
            result.Success = !code.IsNullOrEmpty();
            result.ReturnMsg = code;
            return Ok(result);
        }

        /// <summary>
        /// 获取重大阳性部位字列表
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        [HttpPost("GetMajorPositiveParts")]
        [ProducesResponseType(typeof(List<MajorPositivePart>), 200)]
        public IActionResult GetMajorPositiveParts(MajorPositivePartQuery query)
        {
            int totalNumber = 0;
            int totalPage = 0;
            result.ReturnData = _majorPositiveService.GetMajorPositiveParts(query, ref totalNumber, ref totalPage);
            result.TotalNumber = totalNumber;
            result.TotalPage = totalPage;
            return Ok(result);
        }

        /// <summary>
        /// 删除重大阳性部位
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        [HttpPost("BatchRemoveMajorPositivePart")]
        [ProducesResponseType(typeof(bool), 200)]
        [UnitOfWork]
        public IActionResult BatchRemoveMajorPositivePart(MajorPositivePartQuery query)
        {
            result.Success = _majorPositiveService.BatchRemoveMajorPositivePart(query.PartCodes);
            return Ok(result);
        }
        #endregion

        #region 重大阳性计算式
        /// <summary>
        /// SaveMajorPositiveExpression 保存重大阳性判断计算式
        /// </summary>
        /// <param name="majorExp">重大阳性判断计算式</param>
        /// <returns></returns>
        [HttpPost("SaveMajorPositiveExpression")]
        [UnitOfWork]
        public IActionResult SaveMajorPositiveExpression([FromBody] SaveMajorExpression majorExp)
        {
            string msg = string.Empty;
            result.Success = _majorPositiveService.SaveMajorPositiveExpression(majorExp);
            result.ReturnMsg = msg;

            return Ok(result);
        }

        /// <summary>
        /// /RD_MajorPositiveExpression/Read   获取重大阳性判断计算式
        /// /RD_MajorPositiveExpression/Delete 删除重大阳性判断计算式
        /// </summary>
        /// <param name="type">操作</param>
        /// <param name="positiveCode">重大阳性代码</param>
        /// <returns></returns>
        [HttpPost("RD_MajorPositiveExpression/{type}")]
        public IActionResult RD_MajorPositiveExpression([FromRoute] string type, [FromQuery] string positiveCode)
        {
            string msg = string.Empty;

            switch (type.ToLower())
            {
                case "read":
                    result.ReturnData = _majorPositiveService.ReadMajorPositiveExpression(positiveCode);
                    break;
                case "delete":
                    result.Success = _majorPositiveService.DeleteMajorPositiveExpression(positiveCode);
                    break;
                default:
                    return new BadRequestResult();
            }

            return Ok(result);
        }
        #endregion

        #region 重大阳性审核条件、项目
        /// <summary>
        /// 保存重大阳性审核条件
        /// </summary>
        /// <param name="data"> 重大阳性审核条件实体</param>
        /// <returns></returns>
        [HttpPost("SaveMajorPositiveCriteria")]
        [ProducesResponseType(typeof(bool), 200)]
        [UnitOfWork]
        public IActionResult SaveMajorPositiveCriteria(MajorPositiveCriteria data)
        {
            var code = _majorPositiveService.SaveMajorPositiveCriteria(data);
            result.Success = !code.IsNullOrEmpty();
            result.ReturnMsg = code;
            return Ok(result);
        }

        /// <summary>
        /// 获取重大阳性审核条件
        /// </summary>
        /// <param name="positiveCode">重大阳性代码</param>
        /// <returns></returns>
        [HttpGet("GetMajorPositiveCriteria/{positiveCode}")]
        [ProducesResponseType(typeof(List<MajorPositivePart>), 200)]
        public IActionResult GetMajorPositiveCriteria(string positiveCode)
        {
            result.ReturnData = _majorPositiveService.GetMajorPositiveCriteria(positiveCode);
            return Ok(result);
        }

        /// <summary>
        /// 删除重大阳性审核条件
        /// </summary>
        /// <param name="positiveCodes"> 重大阳性代码数组 </param>
        /// <returns></returns>
        [HttpPost("BatchRemoveMajorPositiveCriteria")]
        [ProducesResponseType(typeof(bool), 200)]
        [UnitOfWork]
        public IActionResult BatchRemoveMajorPositiveCriteria(string[] positiveCodes)
        {
            result.Success = _majorPositiveService.BatchRemoveMajorPositiveCriteria(positiveCodes);
            return Ok(result);
        }


        /// <summary>
        /// 保存重大阳性审核项目
        /// </summary>
        /// <param name="data"> 重大阳性审核项目实体</param>
        /// <returns></returns>
        [HttpPost("SaveMajorPositiveCriteriaItem")]
        [ProducesResponseType(typeof(bool), 200)]
        [UnitOfWork]
        public IActionResult SaveMajorPositiveCriteriaItem(MajorPositiveCriteriaItem data)
        {
            result.Success = _majorPositiveService.SaveMajorPositiveCriteriaItem(data);
            return Ok(result);
        }

        /// <summary>
        /// 获取重大阳性审核项目列表
        /// </summary>
        /// <param name="query">查询条件</param>
        /// <returns></returns>
        [HttpPost("GetMajorPositiveCriteriaItems")]
        [ProducesResponseType(typeof(List<MajorPositiveCriteriaItemDto>), 200)]
        public IActionResult GetMajorPositiveCriteriaItems(MajorPositiveCriteriaItemQuery query)
        {
            int totalNumber = 0;
            int totalPage = 0;
            result.ReturnData = _majorPositiveService.GetMajorPositiveCriteriaItems(query, ref totalNumber, ref totalPage);
            result.TotalNumber = totalNumber;
            result.TotalPage = totalPage;
            return Ok(result);
        }

        /// <summary>
        /// 删除重大阳性审核条件
        /// </summary>
        /// <param name="data"> 删除列表内容 </param>
        /// <returns></returns>
        [HttpPost("BatchMajorPositiveCriteriaItem")]
        [ProducesResponseType(typeof(bool), 200)]
        [UnitOfWork]
        public IActionResult BatchMajorPositiveCriteriaItem(List<MajorPositiveCriteriaItemDto> data)
        {
            result.Success = _majorPositiveService.BatchMajorPositiveCriteriaItem(data);
            return Ok(result);
        }

        #endregion

        /// <summary>
        /// 手动添加重大阳性结果
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost("ManualSaveMajorPositive")]

        public IActionResult ManualSaveMajorPositive([FromBody] MajorPositiveDetailInput input)
        {
            result.Success = _majorPositiveService.ManualSaveMajorPositive(input);
            return Ok(result);
        }

        /// <summary>
        /// 重大阳性结果查询
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        [HttpPost("QueryMajorPositiveList")]
        [ProducesDefaultResponseType(typeof(List<MajorPositiveStatistics>))]
        public IActionResult QueryMajorPositiveList([FromBody] MajorPositiveListQuery query)
        {
            result.ReturnData = _majorPositiveService.QueryMajorPositiveList(query);
            return Ok(result);
        }

        /// <summary>
        /// 确认重大阳性结果
        /// </summary>
        /// <param name="majorPositiveId"></param>
        /// <returns></returns>
        [HttpPost("ConfirmMajorPositive")]
        public IActionResult ConfirmMajorPositive([FromQuery]long majorPositiveId)
        {
            result.ReturnData = _majorPositiveService.ConfirmMajorPositive(majorPositiveId);
            return Ok(result);
        }

        /// <summary>
        /// 撤销确认重大阳性结果
        /// </summary>
        /// <param name="majorPositiveId"></param>
        /// <returns></returns>
        [HttpPost("CancelConfirmMajorPositive")]
        public IActionResult CancelConfirmMajorPositive([FromQuery] long majorPositiveId)
        {
            result.ReturnData = _majorPositiveService.CancelConfirmMajorPositive(majorPositiveId);
            return Ok(result);
        }

        /// <summary>
        /// 批量删除重大阳性结果
        /// </summary>
        /// <param name="majorPositiveIds"></param>
        /// <returns></returns>
        [HttpPost("BatchDeleteMajorPositive")]
        [UnitOfWork]
        public IActionResult BatchDeleteMajorPositive([FromBody] long[] majorPositiveIds)
        {
            result.ReturnData = _majorPositiveService.BatchDeleteMajorPositive(majorPositiveIds);
            return Ok(result);
        }

        /// <summary>
        /// 恢复重大阳性结果
        /// </summary>
        /// <param name="majorPositiveId"></param>
        /// <returns></returns>
        [HttpPost("RestoreMajorPositive")]
        public IActionResult RestoreMajorPositive([FromQuery] long majorPositiveId)
        {
            result.ReturnData = _majorPositiveService.RestoreMajorPositive(majorPositiveId);
            return Ok(result);
        }

        /// <summary>
        /// 重大阳性删除记录查询
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        [HttpPost("QueryDeleteMajorPositiveList")]
        [ProducesDefaultResponseType(typeof(List<MajorPositiveStatistics>))]
        public IActionResult QueryDeleteMajorPositiveList([FromBody] MajorPositiveListQuery query)
        {
            result.ReturnData = _majorPositiveService.QueryMajorPositiveList(query,true);
            return Ok(result);
        }

        /// <summary>
        /// 获取重大阳性待办列表
        /// </summary>
        /// <param name="query">查询条件</param>
        /// <returns></returns>
        [HttpPost("GetMajorPositiveDetailToDos")]
        [ProducesResponseType(typeof(List<MajorPositiveDetail>), StatusCodes.Status200OK)]
        public IActionResult GetMajorPositiveDetailToDos(MajorPositiveDetailToDoQuery query)
        {
            result.ReturnData = _majorPositiveService.GetMajorPositiveDetailToDos(query);
            result.TotalNumber = query.TotalNumber;
            result.TotalPage = query.TotalPage;
            return Ok(result);
        }
    }
}
