﻿namespace Peis.Model.DataBaseEntity.Occupation
{
    /// <summary>
    /// 项目计量单位编码（仅定量）
    /// </summary>
    [SugarTable]
    public class CodeOccupationalItemUnit
    {
        /// <summary>
        /// 计量单位代码
        /// </summary>
        [SugarColumn(Length = 4, IsPrimaryKey = true)]
        public string UnitCode { get; set; }
        /// <summary>
        /// 计量单位名称
        /// </summary> 
        [SugarColumn(Length =20,IsNullable =false)]
        public string UnitName { get; set; }
    }
}
