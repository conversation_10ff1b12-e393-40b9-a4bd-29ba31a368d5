﻿using Peis.Model.TableFilter;
using SqlSugar;

namespace Peis.Model.DataBaseEntity
{
    ///<summary>
    ///单位分类代码
    ///</summary>
    [SugarTable("CodeCompanyCls")]
    public class CodeCompanyCls: IHospCodeFilter
    {
        /// <summary>
        /// 单位分类代码
        /// </summary>        
        [SugarColumn(IsNullable = false, IsPrimaryKey = true, Length = 10)]
        public string CompanyClsCode { get; set; }

        /// <summary>
        /// 单位分类名称
        /// </summary>        
        [SugarColumn(IsNullable = false, Length = 50)]
        public string CompanyClsName { get; set; }

        /// <summary>
        /// 院区编码
        /// </summary>        
        [SugarColumn(IsNullable = false, Length = 1)]
        public string HospCode { get; set; }
    }
}