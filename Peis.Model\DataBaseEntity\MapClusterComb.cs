﻿namespace Peis.Model.DataBaseEntity
{
    ///<summary>
    ///套餐-组合对应
    ///</summary>
    [SugarTable("MapClusterComb")]
    public class MapClusterComb
    {
        /// <summary>
        /// 套餐代码
        /// </summary>        
        [SugarColumn(IsNullable = false, IsPrimaryKey = true, Length = 6)]
        public string ClusCode { get; set; }

        /// <summary>
        /// 组合代码
        /// </summary>        
        [SugarColumn(IsNullable = false, IsPrimaryKey = true, Length = 8)]
        public string CombCode { get; set; }

        /// <summary>
        /// 原始单价
        /// </summary>        
        [SugarColumn(IsNullable = false, Length = 12, DecimalDigits = 2)]
        public decimal OriginalPrice { get; set; }
        /// <summary>
        /// 单价
        /// </summary>        
        [SugarColumn(IsNullable = false, Length = 12, DecimalDigits = 2)]
        public decimal Price { get; set; }

        /// <summary>
        /// 折扣率
        /// </summary>        
        [SugarColumn(IsNullable = false, Length = 12, DecimalDigits = 2)]
        public decimal Discount { get; set; }

        #region Ext
        /// <summary>
        /// 组合名称
        /// </summary>        
        [SugarColumn(IsIgnore = true)]
        public string CombName { get; set; }
        #endregion
    }
}