﻿using Peis.Model.DataBaseEntity.Occupation;
using Peis.Model.Other.PeEnum;
using Peis.Model.ReportDataSource.ExaminationReport;
using System.Linq;
using System.Text.Json.Serialization;

namespace Peis.Model.DTO.CompanyStatistics
{
    /// <summary>
    /// 单位体检报告数据
    /// </summary>
    public class CompanyPeReportData
    {
        /// <summary>
        /// 受检单位
        /// </summary>
        public string CompanyName { get; set; }

        /// <summary>
        /// 总人数
        /// </summary>
        public int TotalNum { get; set; }

        /// <summary>
        /// 男
        /// </summary>
        public int MaleNum { get; set; }

        /// <summary>
        /// 女
        /// </summary>
        public int FemaleNum { get; set; }

        /// <summary>
        /// 未完成
        /// </summary>
        public int UnfinishNum { get; set; }

        /// <summary>
        /// 单位体检报告
        /// </summary>
        public CompanyPeReport[] Report { get; set; }
    }

    /// <summary>
    /// 单位体检报告
    /// </summary>
    public class CompanyPeReport
    {
        /// <summary>
        /// 体检号
        /// </summary>
        public string RegNo { get; set; }

        /// <summary>
        /// 姓名
        /// </summary>
        public string Name { get; set; }

        /// <summary>
        /// 性别
        /// </summary>
        public Sex? Sex { get; set; }

        /// <summary>
        /// 性别名称
        /// </summary>
        public string SexName => Sex.HasValue ? Sex.ToString() : string.Empty;

        /// <summary>
        /// 年龄
        /// </summary>
        public int? Age { get; set; }

        /// <summary>
        /// 手机号
        /// </summary>
        public string Tel { get; set; }

        /// <summary>
        /// 证件号
        /// </summary>
        public string CardNo { get; set; }

        /// <summary>
        /// 体检状态
        /// </summary>
        public PeStatus PeStatus { get; set; }

        /// <summary>
        /// 体检状态-名称
        /// </summary>
        public string PeStatusName => PeStatus.ToString();

        /// <summary>
        /// 体检日期
        /// </summary>
        public DateTime? ActiveTime { get; set; }

        /// <summary>
        /// 金额
        /// </summary>
        public decimal TotalPrice { get; set; }

        /// <summary>
        /// 单位名称
        /// </summary>
        public string CompanyName { get; set; }

        /// <summary>
        /// 危害因素
        /// </summary>
        [JsonIgnore]
        public List<PeRegisterOccupationHazard> Hazards { get; set; }

        /// <summary>
        /// 危害因素内容
        /// </summary>
        public string HazardNames => PeRegisterOccupationHazard.ToHazardousNames((Hazards ?? new()).Select(x => x.HazardousName));

        /// <summary>
        /// 岗位状态/体检类别
        /// </summary>
        public string JobStatusName { get; set; }

        /// <summary>
        /// 工号
        /// </summary>
        public string JobId { get; set; }

        /// <summary>
        /// 工种
        /// </summary>
        public string JobName { get; set; }

        /// <summary>
        /// 综述/结果
        /// </summary>
        public string Summary { get; set; }

        /// <summary>
        /// 总检结论
        /// </summary>
        public string MaxConclusion => OccupationalReportInfo.GetMaxConclusion(this.Hazards);

        /// <summary>
        /// 结论
        /// </summary>
        public string Conclusion { get; set; }

        /// <summary>
        /// 职业病处理意见
        /// </summary>
        public string OccupationalAdvice { get; set; }

        /// <summary>
        /// 建议
        /// </summary>
        public string Suggestion { get; set; }

        /// <summary>
        /// 审核时间/总检日期
        /// </summary>
        public DateTime? AuditTime { get; set; }
    }
}
