﻿using Peis.Model.Other.PeEnum;

namespace Peis.Model.DataBaseEntity.Occupation
{
    /// <summary>
    ///职业病婚姻状况映射表
    /// </summary>
    [SugarTable]
    public class MapOccupationalMarryStatus
    {
        /// <summary>
        /// 职业代码
        /// </summary>
        [SugarColumn(Length = 2, IsPrimaryKey = true)]
        public string OccupationalCode { get; set; }
        /// <summary>
        /// 系统内代码
        /// </summary>
        [SugarColumn( IsPrimaryKey = true)]
        public MarryStatus PeCode { get; set; }
    }
}
