﻿using Peis.Model.Other.PeEnum.Occupation;

namespace Peis.Model.DataBaseEntity.Occupation
{
    /// <summary>
    /// 重点监测项目
    /// </summary>
    [SugarTable]
    public class CodeOccupationalImportantMonitorItem
    {
        /// <summary>
        /// 编码
        /// </summary>
        [SugarColumn(IsPrimaryKey = true)]
        public ImportItemKey Key { get; set; }
        /// <summary>
        /// 体检系统项目代码
        /// </summary>
        [SugarColumn(Length = 10, IsNullable = false)]
        public string ItemCode { get; set; }
        /// <summary>
        /// 结果代码
        /// </summary>
        [SugarColumn(Length = 10, IsNullable = true)]
        public string ResultCode { get; set; }
        /// <summary>
        /// 参考范围上限
        /// </summary>
        [SugarColumn(Length = 10, IsNullable = true)]
        public string UpperLimit { get; set; }
        /// <summary>
        /// 参考范围下限
        /// </summary>
        [SugarColumn(Length = 10, IsNullable = true)]
        public string LowerLimit { get; set; }
        /// <summary>
        /// 项目单位代码
        /// </summary>
        [SugarColumn(Length = 10, IsNullable = true)]
        public string UnitCode { get; set; }
        /// <summary>
        /// 项目单位名称
        /// </summary>
        [SugarColumn(Length = 20, IsNullable = true)]
        public string UnitName { get; set; }

        /// <summary>
        /// 名称
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public string KeyName => Key.ToString();
    }
}
