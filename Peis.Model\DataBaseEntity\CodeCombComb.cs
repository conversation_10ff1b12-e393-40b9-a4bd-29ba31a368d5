﻿using SqlSugar;

namespace Peis.Model.DataBaseEntity
{
    ///<summary>
    ///体检自动添加组合对应信息
    ///</summary>
    [SugarTable("CodeCombComb")]
    public class CodeCombComb
    {
        /// <summary>
        /// 组合名称
        /// </summary>        
        [SugarColumn(IsNullable = false, IsPrimaryKey = true, Length = 50)]
        public string CombCode { get; set; }

        /// <summary>
        /// 对应组合名称
        /// </summary>        
        [SugarColumn(IsNullable = false, IsPrimaryKey = true, Length = 50)]
        public string AppendCombCode { get; set; }

        /// <summary>
        /// 数量
        /// </summary>        
        [SugarColumn(IsNullable = false)]
        public int Count { get; set; }
    }
}