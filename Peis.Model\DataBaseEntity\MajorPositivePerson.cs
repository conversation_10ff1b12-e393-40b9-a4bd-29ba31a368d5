﻿using Peis.Model.Other.PeEnum;
using Peis.Model.TableFilter;
using SqlSugar;
using System;

namespace Peis.Model.DataBaseEntity
{
    ///<summary>
    ///重大阳性人员表
    ///</summary>
    [SugarTable("MajorPositivePerson")]
    public class MajorPositivePerson: IHospCodeFilter
    {
        /// <summary>
        /// 体检号
        /// </summary>        
        [SugarColumn(IsNullable = false, IsPrimaryKey = true, Length = 12)]
        public string RegNo { get; set; }

        /// <summary>
        /// 姓名
        /// </summary>        
        [SugarColumn(IsNullable = false, Length = 50)]
        public string Name { get; set; }

        /// <summary>
        /// 性别（0通用1男2女）
        /// </summary>        
        [SugarColumn(IsNullable = false)]
        public Sex Sex { get; set; }

        /// <summary>
        /// 年龄
        /// </summary>        
        [SugarColumn(IsNullable = false)]
        public int Age { get; set; }

        /// <summary>
        /// 手机号码
        /// </summary>        
        [SugarColumn(IsNullable = true, Length = 32)]
        public string Tel { get; set; }

        /// <summary>
        /// 公司名称
        /// </summary>        
        [SugarColumn(IsNullable = true, Length = 100)]
        public string CompanyName { get; set; }

        /// <summary>
        /// 团检标识
        /// </summary>        
        [SugarColumn(IsNullable = false)]
        public bool IsCompanyCheck { get; set; }

        /// <summary>
        /// 是否处理标识
        /// </summary>        
        [SugarColumn(IsNullable = false)]
        public bool IsProcessed { get; set; }

        /// <summary>
        /// 创建人
        /// </summary>        
        [SugarColumn(IsNullable = false, Length = 10)]
        public string Creator { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>        
        [SugarColumn(IsNullable = false)]
        public DateTime CreateTime { get; set; }

        /// <summary>
        /// 院区编码
        /// </summary>        
        [SugarColumn(IsNullable = false, Length = 1)]
        public string HospCode { get; set; }
    }
}