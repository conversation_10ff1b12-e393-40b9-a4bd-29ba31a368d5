﻿using Peis.External.Platform.Service.Service.IService.BaiHui;
using Peis.External.Platform.Service.Service.IService.Permission;
using Peis.Model.DTO.External.BaiHui;
using Peis.Model.DTO.External.Permission;
using Peis.Model.Other;
using Peis.Service.IService;

namespace Peis.External.Platform.Service.Service.ServiceImpl.Permission;

/// <summary>
/// 扩展三方认证、授权等
/// </summary>
public class ExternalPermission : IExternalSystemPermission
{
    readonly IBHBasicService _bHBasicService;
    readonly IPermissionService _permissionService;
    readonly IMapper _mapper;

    public ExternalPermission(IBHBasicService bHBasicService, IPermissionService permissionService, IMapper mapper)
    {
        _bHBasicService = bHBasicService;
        _permissionService = permissionService;
        _mapper = mapper;
    }

    /// <summary>
    /// 校验统一门户Token 并获取登录用户信息。
    /// </summary>
    /// <param name="token">统一门户token</param>
    /// <param name="msg">消息</param>
    /// <returns>UserInfoExternalDto</returns>
    public UserInfoExternalDto VerifyToken(string token, out string msg)
    {
        if (token.IsNullOrEmpty(nameof(token), out msg))
            return null;

        var msgContent = new { token }.ToJson();
        var bhRequestMsg = new BHRequestMsg(ResxExternal.BHApiQueryTokenInfo, msgContent, true, false);
        var returnInfo = _bHBasicService.PostMsgTransfer<string>(bhRequestMsg);
        var resInfo = returnInfo.SyncResult.ToObject<BHApiResultData<UserInfoExternalDto>>();
        if (!resInfo.IsSuccess)
        {
            msg = $"验证三方token信息失败[{resInfo.Msg}]，请稍后重试！";
            return null;
        }

        msg = ResxCommon.Success;
        return resInfo.Data;
    }

    /// <summary>
    /// 根据统一门户Token登录
    /// </summary>
    /// <param name="token">统一门户token</param>
    /// <param name="msg">消息</param>
    /// <returns>object</returns>
    public object Login(string token, out string msg)
    {     
        var userInfoExternal = VerifyToken(token, out msg);
        if (userInfoExternal.IsNullOrEmpty()) return null;

        var user = new UserInfo
        {
            OperatorCode = userInfoExternal.UserCode,
            HospCode = "A",
            Password = "123"
        };
        var resInfo = _permissionService.Login(user, ref msg);
        if (resInfo.IsNullOrEmpty()) return null;

        var clinicUserInfo = _mapper.Map<UserInfoResponseDto>(userInfoExternal);
        var dic = ObjectToDictionary(resInfo);
        if(!dic.ContainsKey(nameof(clinicUserInfo)))
            dic.Add(nameof(clinicUserInfo), clinicUserInfo);

        msg = ResxCommon.Success;
        return dic;
    }

    #region private
    Dictionary<string, object> ObjectToDictionary(object obj)
    {
        var dict = new Dictionary<string, object>();

        foreach (var prop in obj.GetType().GetProperties())
        {
            dict[prop.Name] = prop.GetValue(obj);
        }

        return dict;
    }

    #endregion

}
