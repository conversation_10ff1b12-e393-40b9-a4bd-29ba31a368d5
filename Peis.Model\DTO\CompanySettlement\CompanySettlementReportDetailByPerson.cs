﻿using Peis.Model.Other.PeEnum;

namespace Peis.Model.DTO.CompanySettlement
{
    /// <summary>
    /// 结算费用明细报表(按人员)
    /// </summary>
    public class CompanySettlementReportDetailByPerson
    {
        /// <summary>
        /// 结算批次号
        /// </summary>
        public string BillSeqNo { set; get; }
        /// <summary>
        /// 单位代码
        /// </summary>
        public string CompanyCode { set; get; }
        /// <summary>
        /// 单位名称
        /// </summary>
        public string CompanyName { set; get; }
        /// <summary>
        /// 体检号
        /// </summary>
        public string RegNo { set; get; }
        /// <summary>
        /// 姓名
        /// </summary>
        public string Name { set; get; }
        /// <summary>
        /// 身份证
        /// </summary>
        public string CardNo { set; get; }
        /// <summary>
        /// 年龄
        /// </summary>
        public int Age { set; get; }
        /// <summary>
        /// 性别
        /// </summary>
        public Sex Sex { set; get; }
        /// <summary>
        /// 电话
        /// </summary>
        public string Tel { set; get; }
        /// <summary>
        /// 套餐名
        /// </summary>
        public string ClusterName { set; get; }
        /// <summary>
        /// 金额
        /// </summary>
        public decimal Price { set; get; }
    }

    public class CompanySettlementReportByPerson
    {
        /// <summary>
        /// 数量合计
        /// </summary>
        public int Count { set; get; }
        /// <summary>
        /// 金额合计
        /// </summary>
        public decimal Price { set; get; }
        /// <summary>
        /// 明细列表
        /// </summary>
        public List<CompanySettlementReportDetailByPerson> list { set; get; }
    }
}
