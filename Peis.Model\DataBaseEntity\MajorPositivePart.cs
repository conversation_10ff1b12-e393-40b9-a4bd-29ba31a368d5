﻿namespace Peis.Model.DataBaseEntity;

/// <summary>
/// 重大阳性部位表
/// </summary>
[SugarTable(TableName = nameof(MajorPositivePart), TableDescription = "重大阳性部位表")]
[SugarIndex($"idx_{nameof(MajorPositivePart)}", nameof(PartCode), OrderByType.Asc, nameof(PartName), OrderByType.Asc)]
public class MajorPositivePart
{
    /// <summary>
    /// 部位代码
    /// </summary>        
    [SugarColumn(IsNullable = false, IsPrimaryKey = true, Length = 6)]
    public string PartCode { get; set; }

    /// <summary>
    /// 部位名称
    /// </summary>
    [SugarColumn(IsNullable = false, Length = 255)]
    [Required(ErrorMessage = "部位名称不能为空")]
    public string PartName { get; set; }    
}


