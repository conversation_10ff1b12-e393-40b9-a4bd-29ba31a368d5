﻿using Microsoft.AspNetCore.Mvc;
using Peis.Model.DTO.ReportTemplate;
using Peis.Service.IService;

namespace Peis.API.Controllers.DataMaintenance
{
    /// <summary>
    /// 报表模板维护
    /// </summary>
    [Route("api/[controller]")]
    [ApiController]
    public class ReportTemplateController : BaseApiController
    {
        private readonly IReportTemplateService _reportTemplateService;

        public ReportTemplateController(IReportTemplateService reportTemplateService)
        {
            _reportTemplateService = reportTemplateService;
        }

        /// <summary>
        /// 获取报表模板列表
        /// </summary>
        /// <returns></returns>
        [HttpPost("ReadReportList")]
        public IActionResult ReadReportList()
        {
            result.ReturnData = _reportTemplateService.ReadReportList();
            result.Success = result.ReturnData != null;

            return Ok(result);
        }

        /// <summary>
        /// 新建空报表模板
        /// </summary>
        /// <param name="typeCode"></param>
        /// <param name="reportName"></param>
        /// <returns></returns>
        [HttpPost("NewEmptyReport")]
        public IActionResult NewReport([FromQuery] string typeCode, [FromQuery] string reportName)
        {
            result.Success = _reportTemplateService.NewEmptyReport(typeCode, reportName, out ReportTemplate report, out string msg);
            result.ReturnMsg = msg;
            result.ReturnData = report;

            return Ok(result);
        }

        /// <summary>
        /// 删除报表模板
        /// </summary>
        /// <param name="reportCode"></param>
        /// <returns></returns>
        [HttpPost("DeleteReport")]
        public IActionResult DeleteReport([FromQuery] string reportCode)
        {
            result.Success = _reportTemplateService.DeleteReport(reportCode, out string msg);
            result.ReturnMsg = msg;

            return Ok(result);
        }
    }
}
