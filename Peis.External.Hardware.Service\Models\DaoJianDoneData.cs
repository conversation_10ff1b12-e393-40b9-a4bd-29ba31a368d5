﻿namespace Peis.External.Hardware.Service.Models;

/// <summary>
/// 完成导检请求报文内容
/// </summary>
public record DaoJianDoneData
{
    /// <summary>
    /// MacCode
    /// </summary>
    [Regex(ErrorMessage = "[MacCode]-格式错误！")]
    public string MacCode { get; set; }

    /// <summary>
    /// UserCode
    /// </summary>
    [Regex(ErrorMessage = "[UserCode]-操作人编号格式错误！")]
    public string UserCode { get; set; }

    /// <summary>
    /// 体检号
    /// </summary>
    [Regex(VerifyRegNo = true, ErrorMessage = "[RegNo]-体检号格式错误！")]
    public string RegNo { get; set; }
}
