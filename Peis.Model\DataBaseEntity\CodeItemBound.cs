﻿using SqlSugar;

namespace Peis.Model.DataBaseEntity
{
    ///<summary>
    ///参考范围
    ///</summary>
    [SugarTable("CodeItemBound")]
    public class CodeItemBound
    {
        /// <summary>
        /// 项目代码
        /// </summary>        
        [SugarColumn(IsNullable = false, IsPrimaryKey = true, Length = 6)]
        public string ItemCode { get; set; }

        /// <summary>
        /// 参考范围类型
        /// </summary>        
        [SugarColumn(IsNullable = false, IsPrimaryKey = true, Length = 6)]
        public string BoundCodeType { get; set; }

        /// <summary>
        /// 下限
        /// </summary>        
        [SugarColumn(IsNullable = true, Length = 10)]
        public string LowerLimit { get; set; }

        /// <summary>
        /// 上限
        /// </summary>        
        [SugarColumn(IsNullable = true, Length = 10)]
        public string UpperLimit { get; set; }

        /// <summary>
        /// 危急下限
        /// </summary>        
        [SugarColumn(IsNullable = true, Length = 10)]
        public string DangerLowerLimit { get; set; }

        /// <summary>
        /// 危急上限
        /// </summary>        
        [SugarColumn(IsNullable = true, Length = 10)]
        public string DangerUpperLimit { get; set; }

        /// <summary>
        /// 重大阳性下限
        /// </summary>        
        [SugarColumn(IsNullable = true, Length = 10)]
        public string PositiveLowerLimit { get; set; }

        /// <summary>
        /// 重大阳性上限
        /// </summary>        
        [SugarColumn(IsNullable = true, Length = 10)]
        public string PositiveUpperLimit { get; set; }
    }
}