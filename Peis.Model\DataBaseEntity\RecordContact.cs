﻿using SqlSugar;
using System;

namespace Peis.Model.DataBaseEntity
{
    ///<summary>
    ///记录联系表
    ///</summary>
    [SugarTable("RecordContact")]
    public class RecordContact
    {
        /// <summary>
        /// Id
        /// </summary>        
        [SugarColumn(IsNullable = false, IsPrimaryKey = true, IsIdentity = true)]
        public int Id { get; set; }

        /// <summary>
        /// 体检号
        /// </summary>        
        [SugarColumn(IsNullable = false, Length = 12)]
        public string RegNo { get; set; }

        /// <summary>
        /// 联系人
        /// </summary>        
        [SugarColumn(IsNullable = false, Length = 10)]
        public string ContactPerson { get; set; }

        /// <summary>
        /// 联系内容
        /// </summary>        
        [SugarColumn(IsNullable = false, Length = 200)]
        public string Content { get; set; }

        /// <summary>
        /// 联系时间
        /// </summary>        
        [SugarColumn(IsNullable = false)]
        public DateTime CreateTime { get; set; }
    }
}