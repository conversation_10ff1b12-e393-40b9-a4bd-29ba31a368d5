﻿using Aspose.Words.MailMerging;

namespace Peis.Model.DTO.CompanyStatistics;

/// <summary>
/// Aspose.Words-自定义邮件合并数据源-各年龄段有异常明细统计内容
/// </summary>
public class MailMergeDataSourceOfSummaryTopDiseOfAge : IMailMergeDataSource
{
    private readonly List<CompanySumDiseaseAge> _customers;
    private int _currentIndex;

    public MailMergeDataSourceOfSummaryTopDiseOfAge(List<CompanySumDiseaseAge> customers)
    {
        _customers = customers;
        _currentIndex = -1;
    }

    public string TableName => "SummaryTopDiseOfAgeList";

    public bool GetValue(string fieldName, out object fieldValue)
    {
        var customer = _customers[_currentIndex];
        var prop = customer.GetType().GetProperty(fieldName);

        if (prop != null)
        {
            fieldValue = prop.GetValue(customer);
            return true;
        }

        fieldValue = default;
        return false;
    }

    public bool MoveNext()
    {
        _currentIndex++;
        return _currentIndex < (_customers?.Count ?? 0);
    }

    public IMailMergeDataSource GetChildDataSource(string tableName)
    {
        if ("SummaryTopDiseDetailList".Equals(tableName, StringComparison.OrdinalIgnoreCase))
        {
            return new MailMergeDataSourceOfSummaryTopDiseDetail(_customers[_currentIndex].DiseaseDetailList);
        }

        return default;
    }
}