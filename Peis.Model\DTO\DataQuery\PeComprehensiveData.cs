﻿using Peis.Model.Other.PeEnum;
using System.Collections.Generic;
using System.Text.Json.Serialization;

namespace Peis.Model.DTO.DataQuery
{
    /// <summary>
    /// 体检综合查询
    /// </summary>
    public class PeComprehensiveData
    {
        /// <summary>
        /// 综合数据
        /// </summary>
        public List<ComprehensiveData> ComprehensiveData { get; set; }

        /// <summary>
        /// 记录数
        /// </summary>
        public RecordStatus RecordStatus { get; set; }
    }

    /// <summary>
    /// 综合数据
    /// </summary>
    public class ComprehensiveData
    {
        /// <summary>
        /// 预约类型
        /// </summary>
        public BookType BookType { get; set; }

        /// <summary>
        /// 体检状态
        /// </summary>
        public PeStatus PeStatus { get; set; }

        /// <summary>
        /// 姓名
        /// </summary>
        public string Name { get; set; }

        /// <summary>
        /// 档案卡号
        /// </summary>
        public string PatCode { get; set; }

        /// <summary>
        /// 体检号
        /// </summary>
        public string RegNo { get; set; }

        /// <summary>
        /// 体检次数
        /// </summary>
        public int RegisterTimes { get; set; }

        ///// <summary>
        ///// 传输标志
        ///// </summary>
        //public bool xxx { get; set; }

        /// <summary>
        /// 激活标志
        /// </summary>
        public bool IsActive { get; set; }

        ///// <summary>
        ///// 短信发送标志
        ///// </summary>
        //public string IsSend { get; set; }

        /// <summary>
        /// 团体标识
        /// </summary>
        public bool IsCompanyCheck { get; set; }

        /// <summary>
        /// VIP标识
        /// </summary>
        public bool? IsVIP { get; set; }

        /// <summary>
        /// 体检分类
        /// </summary>
        public PeCls PeCls { get; set; }

        /// <summary>
        /// 性别
        /// </summary>
        public Sex? Sex { get; set; }

        /// <summary>
        /// 年龄
        /// </summary>
        public int? Age { get; set; }

        /// <summary>
        /// 婚姻状态
        /// </summary>
        public MarryStatus? MarryStatus { get; set; }

        /// <summary>
        /// 证件号
        /// </summary>
        public string CardNo { get; set; }

        /// <summary>
        /// 电话号码
        /// </summary>
        public string Tel { get; set; }

        /// <summary>
        /// 工作单位
        /// </summary>
        public string CompanyName { get; set; }

        /// <summary>
        /// 部门名称
        /// </summary>
        public string DeptName { get; set; }

        /// <summary>
        /// 指引单类型
        /// </summary>
        public string GuidanceType { get; set; }

        /// <summary>
        /// 主检医生
        /// </summary>
        public string CheckDoctorName { get; set; }

        /// <summary>
        /// 审核医生
        /// </summary>
        public string AuditDoctorName { get; set; }

        /// <summary>
        /// 报告打印标识
        /// </summary>
        public bool ReportPrinted { get; set; }

        /// <summary>
        /// 报告打印时间
        /// </summary>
        public DateTime? ReportPrintedTime { get; set; }

        /// <summary>
        /// 套餐名
        /// </summary>
        public string ClusterName { get; set; }

        /// <summary>
        /// 金额
        /// </summary>
        public decimal Price { get; set; }

        /// <summary>
        /// 缴费状态
        /// </summary>
        public bool IsPaid { get; set; }

        /// <summary>
        /// 激活时间
        /// </summary>
        public DateTime? ActiveTime { get; set; }
        /// <summary>
        /// 指引单打印标识
        /// </summary>        
        public bool? GuidancePrinted { get; set; }

        /// <summary>
        /// 指引单打印时间
        /// </summary>        
        public DateTime? GuidancePrintTime { get; set; }
        /// <summary>
        /// 自费类型
        /// </summary>
        public string CombChargeMode { get; set; }
        /// <summary>
        /// 复查标识
        /// </summary>        
        public bool? IsRecheck { get; set; }

        /// <summary>
        /// 复查号
        /// </summary>        
        public string RecheckNo { get; set; }
    }
}