﻿using MediatR;
using Peis.Model.DTO.CompanySettlement;
using Peis.Model.DTO.External.His;
using Peis.Model.DTO.MajorPositive;
using Peis.Model.Other.PeEnum;
using Peis.Repository.IRepository;
using Peis.Service.IService;
using Peis.Service.IService.ExternalSystem;
using Peis.Service.IService.Helper;
using Peis.Service.Service.MediatR;
using Peis.Utility.PeUser;

namespace Peis.External.Platform.Service.Service.ServiceImpl.His;

public class CompanySettlementService : IExternalSystemCompanySettlementService
{
    private readonly ISplitTable _splitTable;
    private readonly INoGeneration _noGeneration;
    private readonly IHttpContextUser _httpContextUser;
    private readonly ISystemParameterService _systemParameterService;
    private readonly IDataTranRepository _dataTranRepository;
    private readonly IRegisterRepository _registerRepository;
    private readonly ICompanyRepository _companyRepository;
    private readonly IDataRepository<SysOperator> _operatorRepository;
    private readonly IDataRepository<PeRegisterCluster> _registerClusterRepository;
    private readonly IDataRepository<CodeCompany> _codeCompanyRepository;
    private readonly IDataRepository<CodeCompanyTimes> _companyTimesRepository;
    private readonly IDataRepository<CodeCompanyCluster> _codeCompanyClusterRepository;
    private readonly IDataRepository<CompanySettlement> _companySettlementRepository;
    private readonly IDataRepository<CompanySettlementEntry> _companySettlementEntryRepository;
    private readonly IDataRepository<MapCompanySettlementPerson> _mapCompanySettlementPersonRepository;
    private readonly IDataRepository<MapCodeItemCombHisChargeItem> _mapCodeItemCombHisChargeItemRepository;
    private readonly IDataRepository<MapCompanyCombChargeItem> _mapCompanyCombChargeItemRepository;
    private readonly IDataRepository<MapCompanyClusterCombTestTube> _mapCompanyClusterCombTestTubeRepository;
    private readonly IDataRepository<MapCompanyClusterComb> _mapCompanyClusterCombRepository;
    private readonly IDataRepository<MapCompanySettlementComb> _mapCompanySettlementPeComb;
    private readonly IDataRepository<MapCompanyPersonCombChargeItem> _mapCompanyPersonCombChargeItemRepository;
    private readonly IDataRepository<PeRegister> _peRegisterRepository;
    private readonly ITempTableHelper _tempTableHelper;
    private readonly IMediator _mediator;
    private readonly IServiceProvider _serviceProvider;

    public CompanySettlementService(ISplitTable splitTable,
        INoGeneration noGeneration,
        IHttpContextUser httpContextUser,
        ISystemParameterService systemParameterService,
        IDataTranRepository dataTranRepository,
        IRegisterRepository registerRepository,
        ICompanyRepository companyRepository,
        IDataRepository<SysOperator> operatorRepository,
        IDataRepository<PeRegisterCluster> registerClusterRepository,
        IDataRepository<CodeCompany> codeCompanyRepository,
        IDataRepository<CodeCompanyTimes> companyTimesRepository,
        IDataRepository<CodeCompanyCluster> codeCompanyClusterRepository,
        IDataRepository<CompanySettlement> companySettlementRepository,
        IDataRepository<CompanySettlementEntry> companySettlementEntryRepository,
        IDataRepository<MapCompanySettlementPerson> mapCompanySettlementPersonRepository,
        IDataRepository<MapCodeItemCombHisChargeItem> mapCodeItemCombHisChargeItemRepository,
        IDataRepository<MapCompanyCombChargeItem> mapCompanyCombChargeItemRepository,
        IDataRepository<MapCompanyClusterCombTestTube> mapCompanyClusterCombTestTubeRepository,
        IDataRepository<MapCompanyClusterComb> mapCompanyClusterCombRepository,
        IDataRepository<MapCompanySettlementComb> mapCompanySettlementPeComb,
        IDataRepository<MapCompanyPersonCombChargeItem> mapCompanyPersonCombChargeItemRepository,
        IDataRepository<PeRegister> peRegisterRepository,
        IMediator mediator,
        ITempTableHelper tempTableHelper,
        IServiceProvider serviceProvider)
    {
        _splitTable = splitTable;
        _noGeneration = noGeneration;
        _httpContextUser = httpContextUser;
        _systemParameterService = systemParameterService;
        _dataTranRepository = dataTranRepository;
        _registerRepository = registerRepository;
        _companyRepository = companyRepository;
        _operatorRepository = operatorRepository;
        _registerClusterRepository = registerClusterRepository;
        _codeCompanyRepository = codeCompanyRepository;
        _companyTimesRepository = companyTimesRepository;
        _codeCompanyClusterRepository = codeCompanyClusterRepository;
        _companySettlementRepository = companySettlementRepository;
        _companySettlementEntryRepository = companySettlementEntryRepository;
        _mapCompanySettlementPersonRepository = mapCompanySettlementPersonRepository;
        _mapCodeItemCombHisChargeItemRepository = mapCodeItemCombHisChargeItemRepository;
        _mapCompanyCombChargeItemRepository = mapCompanyCombChargeItemRepository;
        _mapCompanyClusterCombTestTubeRepository = mapCompanyClusterCombTestTubeRepository;
        _mapCompanyClusterCombRepository = mapCompanyClusterCombRepository;
        _mapCompanySettlementPeComb = mapCompanySettlementPeComb;
        _mapCompanyPersonCombChargeItemRepository = mapCompanyPersonCombChargeItemRepository;
        _peRegisterRepository = peRegisterRepository;
        _mediator = mediator;
        _tempTableHelper = tempTableHelper;
        _serviceProvider = serviceProvider;
    }

    #region 结算
    /// <summary>
    /// 获取待结算人员信息
    /// </summary>
    public List<CompanyPaymentListPerson> QueryPersonListForSettlement(QuerySettlementList query)
    {
        query.StartTime = query.StartTime.Date;
        query.EndTime = query.EndTime.Date.Add(new TimeSpan(23, 59, 59));
        List<CompanyPaymentListPerson> personList;
        if (query.Type == StatisticType.按登记时间)
        {
            personList = _registerRepository.ReadRegister()
              .InnerJoin(_splitTable.GetTableOrDefault<PeRegisterComb>(query.StartTime, query.EndTime), (x, y) => x.RegNo == y.RegNo)
              .LeftJoin<CodeCompanyDepartment>((x, y, w) => x.CompanyDeptCode == w.DeptCode)
              .Where(x => x.IsCompanyCheck)
              .Where(x => x.CompanyCode == query.CompanyCode)
              .Where(x => x.CompanyTimes == query.CompanyTimes)
              .Where(x => x.PayStatus == PaymentStatus.Unpaid||x.PayStatus==PaymentStatus.PartiallyPaid)
              .Where(x => SqlFunc.Between(x.RegisterTime, query.StartTime, query.EndTime))
              .WhereIF(query.ClusCodes.Length > 0, x => SqlFunc.Subqueryable<PeRegisterCluster>().Where(y => SqlFunc.ContainsArray(query.ClusCodes, y.ClusCode) && y.RegNo == x.RegNo).Any())
              .WhereIF(query.CompanyDeptCodes.Length > 0, x => SqlFunc.ContainsArray(query.CompanyDeptCodes, x.CompanyDeptCode))
              .Where((x, y) => y.IsPayBySelf == false)
              .Select((x, y, w) => new CompanyPaymentListPerson
              {
                  RegNo           = x.RegNo,
                  PatCode         = x.PatCode,
                  RegisterTimes   = x.RegisterTimes,
                  Name            = x.Name,
                  Sex             = x.Sex,
                  Age             = x.Age,
                  CompanyCode     = query.CompanyCode,
                  CardNo          = x.CardNo,
                  CompanyDeptName = w.DeptName,
                  RegisterTime    = x.RegisterTime.ToString("yyyy-MM-dd"),
                  ActiveTime      = x.ActiveTime == null ? string.Empty : x.ActiveTime.Value.ToString("yyyy-MM-dd"),
                  PeStatus        = x.PeStatus
              }).Distinct().ToList();
        }
        else
        {
            var time = _registerRepository.ReadRegDateRangeByActiveTime(query.StartTime, query.EndTime);
            var conclusion = _splitTable.GetTableOrDefault<PeReportConclusion>(time.MinRegTime, time.MaxRegTime);
            personList = _registerRepository.ReadRegister()
                .InnerJoin(_splitTable.GetTableOrDefault<PeRegisterComb>(time.MinRegTime, time.MaxRegTime), (reg, comb) => reg.RegNo == comb.RegNo)
                .InnerJoin(conclusion, (reg, comb, con) => reg.RegNo == con.RegNo)
                .LeftJoin<CodeCompanyDepartment>((reg, comb, con,dept) => reg.CompanyDeptCode == dept.DeptCode)
                .Where(reg => reg.CompanyCode == query.CompanyCode)
                .Where(reg => reg.IsCompanyCheck)
                .Where(reg => reg.PayStatus == PaymentStatus.Unpaid || reg.PayStatus == PaymentStatus.PartiallyPaid)
                .Where((reg, comb,con) => SqlFunc.Between(con.AuditTime, query.StartTime, query.EndTime))
                .Where((reg, comb) => comb.IsPayBySelf == false)
                .WhereIF(query.ClusCodes.Length > 0, reg => SqlFunc.Subqueryable<PeRegisterCluster>().Where(y => SqlFunc.ContainsArray(query.ClusCodes, y.ClusCode) && y.RegNo == reg.RegNo).Any())
                .WhereIF(query.CompanyDeptCodes.Length > 0, reg => SqlFunc.ContainsArray(query.CompanyDeptCodes, reg.CompanyDeptCode))
                .Select((reg, comb, con, dept) => new CompanyPaymentListPerson
                {
                    RegNo           = reg.RegNo,
                    PatCode         = reg.PatCode,
                    RegisterTimes   = reg.RegisterTimes,
                    Name            = reg.Name,
                    Sex             = reg.Sex,
                    Age             = reg.Age,
                    CompanyCode     = query.CompanyCode,
                    CardNo          = reg.CardNo,
                    CompanyDeptName = dept.DeptName,
                    RegisterTime    = reg.RegisterTime.ToString("yyyy-MM-dd"),
                    ActiveTime      = reg.ActiveTime == null ? string.Empty : reg.ActiveTime.Value.ToString("yyyy-MM-dd"),
                    PeStatus        = reg.PeStatus
                }).Distinct().ToList();
        }
        return personList;
    }

    /// <summary>
    /// 生成结算数据
    /// </summary>
    /// <param name="query"></param>
    /// <returns></returns>
    /// <exception cref="BusinessException"></exception>
    public bool CreateCompanySettlement(QueryCreateCompanySettlement query)
    {
        var companyTimes = _companyTimesRepository.First(x => x.CompanyCode == query.CompanyCode && x.CompanyTimes == query.CompanyTimes);
        if (companyTimes == null)
        {
            throw new BusinessException("当前体检次数存不存在");
        }
        int maxTimes;
        if (_companySettlementRepository.Any(x => x.CompanyCode == query.CompanyCode && x.CompanyTimes == query.CompanyTimes))
        {
            maxTimes = _companySettlementRepository.FindAll(x => x.CompanyCode == query.CompanyCode && x.CompanyTimes == query.CompanyTimes).Max(x => x.CompanyTimes) + 1;
        }
        else
        {
            maxTimes = 1;
        }
        var seqNo = _noGeneration.NextCompanySettlementNo();//获取流水号
        string note = string.Empty;
        //生成结算主表记录
        var companyPayment = new CompanySettlement
        {
            Id              = _noGeneration.NextSnowflakeId(),
            BillSeqNo       = seqNo,
            CompanyCode     = query.CompanyCode,
            InvoiceName     = companyTimes.InvoiceHeader ?? string.Empty,
            TaxID           = companyTimes.TaxID ?? string.Empty,
            BillTimes       = maxTimes,
            BillPersonCount = query.regNos.Length,
            CreateTime      = DateTime.Now,
            FeeType         = FeeType.正常,
            PayStatus       = PayStatus.未收费,
            SendOperator    = _httpContextUser.UserId,
            CaluateType     = query.Type,
            CompanyTimes    = query.CompanyTimes,
            ChargeDateScope = query.ChargeDateScope
        };
        //生成结算组合表记录
        List<MapCompanySettlementComb> billCombList;
        List<MapCompanySettlementPerson> mapSettlementPerson;
        List<PeRegister> upadteRegList = query.regNos.Select(x=>new PeRegister { RegNo = x,PayStatus = PaymentStatus.Pending}).ToList();
        _registerRepository.ReadRegisterTime(new string[] {query.regNos.Min(),query.regNos.Max()}, out var startTime, out var endTime);
        var combs = _splitTable.GetTableOrDefault<PeRegisterComb>(startTime, endTime);
        if (query.Type == StatisticType.按登记时间)
        {
            var ddquery = _registerRepository.ReadRegister()
                .InnerJoin(_tempTableHelper.TempTable(query.regNos), (reg, temp) => reg.RegNo == temp.ColumnName)
                .InnerJoin(combs, (reg, temp, comb) => reg.RegNo == comb.RegNo)
                .Where((reg, temp, comb) => comb.IsPayBySelf == false);
            billCombList = ddquery.Clone()
                .GroupBy((reg, temp, comb) => new { comb.CombCode, comb.Discount,comb.Price })
                .Select((reg, temp, comb) => new MapCompanySettlementComb
                {
                    BillSeqNo     = seqNo,
                    CombCode      = comb.CombCode,
                    CombName      = SqlFunc.AggregateMax(comb.CombName),
                    ClsCode       = SqlFunc.AggregateMax(comb.ClsCode),
                    Discount      = comb.Discount,
                    Count         = SqlFunc.AggregateCount(comb.CombCode),
                    Price         = SqlFunc.AggregateMax(comb.Price),
                    ExamDeptCode  = SqlFunc.AggregateMax(comb.ExamDeptCode),
                    OriginalPrice = SqlFunc.AggregateMax(comb.OriginalPrice),
                }).ToList();
            mapSettlementPerson = ddquery.Select((reg, temp, comb) => new MapCompanySettlementPerson
            {
                BillSeqNo = seqNo,
                RegNo = reg.RegNo,
                RegCombId = comb.Id
            }).ToList();
        }
        else
        {
            var record = _splitTable.GetTableOrDefault<PeRecordComb>(startTime, endTime);
            var conclusion = _splitTable.GetTableOrDefault<PeReportConclusion>(startTime, endTime);
            var commonCombs = GetActualCombPriceWithBindPrice(startTime, endTime, query.regNos);
            billCombList = commonCombs
                .GroupBy(comb => new { comb.CombCode, comb.Discount, comb.Price })
                .Select(comb => new MapCompanySettlementComb
                {
                    BillSeqNo     = seqNo,
                    CombCode      = comb.Key.CombCode,
                    CombName      = comb.Max(x => x.CombName),
                    ClsCode       = comb.Max(x => x.ClsCode),
                    Discount      = comb.Key.Discount,
                    Count         = comb.Count(),
                    Price         = comb.Max(x => x.Price),
                    ExamDeptCode  = comb.Max(x => x.ExamDeptCode),
                    OriginalPrice = comb.Max(x => x.OriginalPrice),
                }).ToList();

            var abandonCombs = _registerRepository.ReadRegister()
                 .InnerJoin(combs, (reg, comb) => reg.RegNo == comb.RegNo)
                 .LeftJoin(record, (reg, comb, record) => comb.Id == record.RegCombId)
                 .InnerJoin(conclusion, (reg, comb, record, con) => reg.RegNo == con.RegNo)
                 .Where(reg => SqlFunc.ContainsArray(query.regNos, reg.RegNo))
                 .Where((reg, comb, record, con) => SqlFunc.Between(con.AuditTime, startTime, endTime))
                 .Where((reg, comb) => comb.IsPayBySelf == false)
                 .Where((reg, comb, record, con) => record.DoctorName == "弃检")
                 .Select((reg, comb, record, con) => new { comb.CombCode, comb.Price }).ToList();
            if (abandonCombs.Count > 0)
            {
                note = "拒检项目数：" + abandonCombs.Count + "，共：" + Math.Round(abandonCombs.Sum(x => x.Price), 2) + "（元）";
            }
            mapSettlementPerson = commonCombs.Select(x => new MapCompanySettlementPerson
            {
                BillSeqNo = seqNo,
                RegNo = x.RegNo,
                RegCombId = x.Id
            }).ToList();
        }
        if (billCombList.Count == 0)
        {
            throw new BusinessException("当前选择人员没有可结算项目");
        }
        companyPayment.OriginalPrice = billCombList.Sum(x => x.OriginalPrice * x.Count);
        companyPayment.ActuallyPrice = billCombList.Sum(x => x.Price * x.Count);
        companyPayment.Note = note;
        //生成结算体检号表记录
        _dataTranRepository.ExecTran(() =>
        {
            _companySettlementRepository.Insert(companyPayment);
            _mapCompanySettlementPeComb.Insert(billCombList);
            _mapCompanySettlementPersonRepository.BulkCopy(mapSettlementPerson);
            _peRegisterRepository.BulkUpdate(upadteRegList,new string[] {"RegNo"},new string[] { "PayStatus" });
        });
        return true;
    }
    /// <summary>
    /// 删除结算记录
    /// </summary>
    /// <param name="seqNo"></param>
    /// <returns></returns>
    /// <exception cref="BusinessException"></exception>
    public bool DeleteCompanySettlement(string seqNo)
    {
        var payment = _companySettlementRepository.First(x => x.BillSeqNo == seqNo);
        if (payment == null)
        {
            throw new BusinessException("当前序列号没有结算数据");
        }
        if (payment.PayStatus != PayStatus.未收费)
        {
            throw new BusinessException("当前序列号已经有结算记录，无法删除");
        }
        var regNoArray = _mapCompanySettlementPersonRepository.FindAll(x => x.BillSeqNo == seqNo).Select(x=>x.RegNo).Distinct().ToArray();
        
        _dataTranRepository.ExecTran(() =>
        {
            _companySettlementRepository.Delete(payment);
            _companySettlementEntryRepository.Delete(x => x.PE_SEQ_NO == seqNo);
            _mapCompanySettlementPersonRepository.Delete(x => x.BillSeqNo == seqNo);
        });
        _mediator.Publish(new SyncPeRegisterOrderHandle.Data(regNoArray));

        return true;
    }
    /// <summary>
    ///  审核结算信息 产生HIS结算数据
    /// </summary>
    /// <param name="seqNo"></param>
    /// <returns></returns>
    /// <exception cref="BusinessException"></exception>
    public async Task<bool> AuditCompanySettlement(string seqNo)
    {
        var settlement = _companySettlementRepository.First(x => x.BillSeqNo == seqNo) ?? throw new BusinessException("此结算序列号不存在！"); ;
        if (!settlement.AuditOperator.IsNullOrEmpty())
        {
            throw new BusinessException("此结算记录已经审核过！");
        }
        if (settlement.InvoiceName.IsNullOrEmpty())
        {
            throw new BusinessException("发票抬头为空！");
        }
        //SaveExtra(settlement.CompanyCode);//更新一次加项的字典，做个保险
        settlement.AuditOperator = _httpContextUser.UserId;
        settlement.AuditTime = DateTime.Now;
        var chargeRegNos = _mapCompanySettlementPersonRepository.FindAll(x => x.BillSeqNo == seqNo).Select(x => x.RegNo).Distinct().ToArray();
        _registerRepository.ReadRegisterTime(new string[] { chargeRegNos.Min(), chargeRegNos.Max() }, out var startTime, out var endTime);
        var billList_task =  Task.Run(() =>
        {
            // 采用ioc获取新实例，解决线程安全
            using (var serviceScope = _serviceProvider.CreateScope())
            {
                var registerRepository = serviceScope.ServiceProvider.GetService<IRegisterRepository>();
                var billList = registerRepository.ReadRegisterCombs(startTime, endTime)//.MergeTable()
                .InnerJoin<MapCompanySettlementPerson>((comb, map1) => comb.RegNo == map1.RegNo && comb.Id == map1.RegCombId)
                .InnerJoin<MapCompanyPersonCombChargeItem>((comb, map1, map) => comb.RegNo == map.RegNo && comb.CombCode == map.CombCode)
                .InnerJoin<CodeDepartment>((comb, map1, map, dept) => comb.ExamDeptCode == dept.DeptCode)
                .Where((comb, map1, map, dept)=>map1.BillSeqNo == seqNo)
                .Select((comb, map1, map, dept) => new CompanySettlementEntry
                {
                    PE_SEQ_NO             = seqNo,
                    PE_NO                 = settlement.CompanyCode,
                    CARD_ID               = string.Empty,
                    PE_ITEM_CODE          = comb.CombCode,
                    PE_ITEM_NAME          = comb.CombName,
                    HIS_ITEM_CODE         = map.HisChargeItemCode,
                    HIS_ITEM_NAME         = map.HisChargeItemCNName,
                    PE_TIMES              = settlement.BillPersonCount,
                    UNIT                  = map.HisChargeItemPrice,
                    THEORY_TOTAL_AMOUNT   = SqlFunc.Round(SqlFunc.IF(comb.ClsCode == "34").Return(1).End(map.HisChargeItemCount)
                                       * SqlFunc.IsNull(map.HisChargeItemPrice, 0)
                                       * SqlFunc.IsNull(map.HisChargeItemDiscount, 0), 2),
                    ACTUAL_TOTAL_AMOUNT   = SqlFunc.Round(SqlFunc.IF(comb.ClsCode == "34").Return(1).End(map.HisChargeItemCount)
                                       * SqlFunc.IsNull(map.HisChargeItemPrice, 0)
                                       * SqlFunc.IsNull(map.HisChargeItemDiscount, 0), 2),
                    ACTIVE_FLAG           = "Y",
                    ACCOUNT_FLAG          = "N",
                    PAYMENT_INACTIVE_FLAG = "N",
                    PERFORMING_DEPT       = dept.HisCode,
                    PE_DOCTOR_ID          = _httpContextUser.UserId == "0000" ? "s0553" : _httpContextUser.UserId,
                    QUANTITY              = (int)SqlFunc.IF(comb.ClsCode == "34").Return(1).End(map.HisChargeItemCount),
                    REMARK                = string.Empty,
                    scc_code              = string.Empty,
                    RegNo                 = comb.RegNo,
                    RegCombId             = comb.Id
                }).ToList();
                return billList;
            }
        });

        var priceDistribution_task = Task.Run(() =>
        {
            // 采用ioc获取新实例，解决线程安全
            using (var serviceScope = _serviceProvider.CreateScope())
            {
                var registerRepository = serviceScope.ServiceProvider.GetService<IRegisterRepository>();
                //分摊折扣金额
                var combDiscount = _registerRepository.ReadRegisterCombs(startTime, endTime)//.MergeTable()
                    .InnerJoin<MapCompanySettlementPerson>((comb, map1) => comb.RegNo == map1.RegNo && comb.Id == map1.RegCombId)
                    .InnerJoin<MapCompanyPersonCombChargeItem>((comb, map1, map) => comb.RegNo == map.RegNo && comb.CombCode == map.CombCode)
                    .Where((comb, map1, map)=>map1.BillSeqNo == seqNo)
                    .Select((comb, map1, map) => new
                    {
                        comb.CombCode,
                        comb.Discount,
                        comb.RegNo,
                        comb.Price,
                        map.HisChargeItemCode,
                        HisChargeItemPrice = map.HisChargeItemPrice * map.HisChargeItemDiscount,
                        map.HisChargeItemCount,
                        comb.Id
                    }).ToList();
                var priceDistribution = new List<PriceDistribution>();
                foreach (var i in combDiscount.GroupBy(x => new { x.CombCode, x.RegNo,x.Id}))
                {
                    var price = i.Max(x => Math.Round(x.Price, 2));
                    var list = i.ToList();
                    decimal tempPrice = 0;
                    for (int j = 0; j < list.Count; j++)
                    {
                        if (j == list.Count - 1)
                            break;
                        var item = list[j];
                        tempPrice += Math.Round(item.HisChargeItemPrice * item.Discount * item.HisChargeItemCount, 2);
                        priceDistribution.Add(new PriceDistribution { CombCode = item.CombCode, HisCode = item.HisChargeItemCode, Price = Math.Round(item.HisChargeItemPrice * item.Discount * item.HisChargeItemCount, 2), RegNo = item.RegNo, RecCombId = item.Id });
                    }
                    var lastPrice = price - tempPrice;
                    var last = list.Last();
                    priceDistribution.Add(new PriceDistribution { CombCode = last.CombCode, HisCode = last.HisChargeItemCode, Price = lastPrice, RegNo = i.Key.RegNo, RecCombId = last.Id });
                }
                return priceDistribution;
            }
        });
        Task.WaitAll(billList_task, priceDistribution_task);
        var billList = billList_task.Result;
        var priceDistribution = priceDistribution_task.Result.GroupBy(x => x.RegNo).ToDictionary(x=>x.Key,x=>x.ToList());
        if (billList.Count == 0)
        {
            throw new BusinessException("当前选择人员没有可结算项目");
        }
        Parallel.ForEach(billList.GroupBy(x=>x.RegNo), x =>
        {
            var distribution = priceDistribution[x.Key];
            foreach (var y in x)
            {
                y.ACTUAL_TOTAL_AMOUNT = distribution.Where(a => a.CombCode == y.PE_ITEM_CODE && a.HisCode == y.HIS_ITEM_CODE && y.RegNo == a.RegNo&&y.RegCombId==a.RecCombId).Sum(a => a.Price);
            }
        });
        billList = billList.GroupBy(x => new { x.PE_ITEM_CODE, x.HIS_ITEM_CODE })
            .Select(x => new CompanySettlementEntry
            {
                PE_SEQ_NO             = seqNo,
                PE_NO                 = settlement.CompanyCode,
                CARD_ID               = string.Empty,
                PE_ITEM_CODE          = x.Key.PE_ITEM_CODE,
                PE_ITEM_NAME          = x.Max(a => a.PE_ITEM_NAME),
                HIS_ITEM_CODE         = x.Key.HIS_ITEM_CODE,
                HIS_ITEM_NAME         = x.Max(a => a.HIS_ITEM_NAME),
                PE_TIMES              = settlement.BillPersonCount,
                UNIT                  = x.Max(a => a.UNIT),
                THEORY_TOTAL_AMOUNT   = x.Sum(a => a.THEORY_TOTAL_AMOUNT),
                ACTUAL_TOTAL_AMOUNT   = x.Sum(a => a.ACTUAL_TOTAL_AMOUNT),
                ACTIVE_FLAG           = "Y",
                ACCOUNT_FLAG          = "N",
                PAYMENT_INACTIVE_FLAG = "N",
                PERFORMING_DEPT       = x.Max(a => a.PERFORMING_DEPT),
                PE_DOCTOR_ID          = _httpContextUser.UserId == "0000" ? "s0553" : _httpContextUser.UserId,
                QUANTITY              = x.Sum(a => a.QUANTITY),
                REMARK                = string.Empty,
                scc_code              = string.Empty,
            }).ToList();

        _dataTranRepository.ExecTran(() =>
        {
            _companySettlementRepository.Update(settlement);
            _companySettlementEntryRepository.Insert(billList);
        });
        return true;
    }

    /// <summary>
    /// 取消审核
    /// </summary>
    /// <param name="seqNo"></param>
    /// <returns></returns>
    /// <exception cref="NotImplementedException"></exception>
    public bool CancelAuditCompanySettlement(string seqNo)
    {
        var settlement = _companySettlementRepository.First(x => x.BillSeqNo == seqNo);
        if (settlement == null)
        {
            throw new BusinessException("此结算序列号不存在！");
        }
        if (settlement.AuditOperator.IsNullOrEmpty())
        {
            throw new BusinessException("此结算记录未审核！");
        }
        if (settlement.AuditOperator != _httpContextUser.UserId)
        {
            throw new BusinessException("此结算记录非本人审核，无法取消！");
        }
        settlement.AuditOperator = null;
        settlement.AuditTime = null;
        _dataTranRepository.ExecTran(() =>
        {
            _companySettlementRepository.Update(settlement);
            _companySettlementEntryRepository.Delete(x => x.PE_SEQ_NO == seqNo);
        });
        return true;
    }

    /// <summary>
    /// 更新结算记录
    /// </summary>
    /// <param name="param"></param>
    /// <returns></returns>
    public bool UpdateCompanySettlement(UpdateCompanySettlementParam param)
    {
        var settlement = _companySettlementRepository.First(x => x.BillSeqNo == param.SeqNo);
        bool isModified = false;
        if (settlement == null)
        {
            throw new BusinessException("未找到对应结算记录！");
        }
        if (!param.ElectronicInvoiceNo.IsNullOrEmpty())
        {
            settlement.ElectronicInvoiceNo = param.ElectronicInvoiceNo;
            isModified = true;
        }
        else
        {
            if (!settlement.AuditOperator.IsNullOrEmpty())
            {
                throw new BusinessException("已审核，无法修改！");
            }
            if (!param.InvoiceName.IsNullOrEmpty())
            {
                settlement.InvoiceName = param.InvoiceName;
                isModified = true;
            }
            if (!param.TaxID.IsNullOrEmpty())
            {
                settlement.TaxID = param.TaxID;
                isModified = true;
            }
        }
        if (isModified)
            _companySettlementRepository.Update(settlement);
        return true;
    }
    /// <summary>
    /// 退费申请
    /// </summary>
    /// <param name="seqNo"></param>
    /// <returns></returns>
    /// <exception cref="BusinessException"></exception>
    public bool RefundCompanySettlement(string seqNo)
    {
        var settlement = _companySettlementRepository.First(x => x.BillSeqNo == seqNo);
        if (settlement == null)
        {
            throw new BusinessException("此结算序列号不存在！");
        }
        if (settlement.PayStatus != PayStatus.收费)
        {
            throw new BusinessException("此结算记录未进行收费！");
        }
        if (settlement.FeeType == FeeType.待退费)
        {
            throw new BusinessException("此结算记录已发送退费申请！");
        }
        settlement.FeeType = FeeType.待退费;
        _companySettlementRepository.Update(settlement);
        return true;
    }

    /// <summary>
    /// 撤销退费申请
    /// </summary>
    /// <param name="seqNo"></param>
    /// <returns></returns>
    /// <exception cref="BusinessException"></exception>
    public bool CancelRefundCompanySettlement(string seqNo)
    {
        var settlement = _companySettlementRepository.First(x => x.BillSeqNo == seqNo);
        if (settlement == null)
        {
            throw new BusinessException("此结算序列号不存在！");
        }
        if (settlement.PayStatus != PayStatus.收费)
        {
            throw new BusinessException("此结算记录未进行收费！");
        }
        if (settlement.FeeType != FeeType.待退费)
        {
            throw new BusinessException("此结算记录未发送退费申请！");
        }
        settlement.FeeType = FeeType.正常;
        _companySettlementRepository.Update(settlement);
        return true;
    }

    /// <summary>
    /// 查询结算记录
    /// </summary>
    /// <param name="companyCode"></param>
    /// <param name="companyTimes"></param>
    /// <returns></returns>
    public List<CompanySettlement> GetCompanySettlement(string companyCode, int companyTimes)
    {
        var operDict = _operatorRepository.FindAll().ToDictionary(x => x.OperatorCode, x => x.Name);
        var companySettlements = _companySettlementRepository.FindAll(x => x.CompanyCode == companyCode && x.CompanyTimes == companyTimes).ToList();
        companySettlements.ForEach(x =>
        {
            x.SendOperator = GetOperatorName(x.SendOperator);
            x.AuditOperator = GetOperatorName(x.AuditOperator);
        });
        return companySettlements;

        string GetOperatorName(string operatorCode)
        {
            if (operatorCode.IsNullOrEmpty())
                return default;

            if (!operDict.ContainsKey(operatorCode))
                return operatorCode;

            return $"{operDict[operatorCode]}({operatorCode})";
        }
    }
    #endregion

    #region 查询统计

    /// <summary>
    /// 结算费用明细报表 
    /// </summary>
    /// <param name="seqNo"></param>
    /// <returns></returns>
    /// <exception cref="BusinessException"></exception>
    public CompanySettlementReport QueryCompanySettlementDetail(string seqNo)
    {
        var settlement = _companySettlementRepository.First(x => x.BillSeqNo == seqNo);
        if (settlement == null)
        {
            throw new BusinessException("结算批号不存在!");
        }
        var combs = _mapCompanySettlementPeComb.FindAll(x => x.BillSeqNo == seqNo).ToList();
        if (combs == null)
        {
            throw new BusinessException("没有找到对应收费数据！");
        }
        var companyName = _codeCompanyRepository.First(x => x.CompanyCode == settlement.CompanyCode).CompanyName;
        var companySettlementReport = new CompanySettlementReport
        {
            Count = settlement.BillPersonCount,
            Price = settlement.ActuallyPrice
        };
        List<CompanySettlementReportDetail> details = new();
        foreach (var comb in combs)
        {
            details.Add(new CompanySettlementReportDetail
            {
                BillSeqNo = comb.BillSeqNo,
                CompanyCode = settlement.CompanyCode,
                CompanyName = companyName,
                CombCode = comb.CombCode,
                CombName = comb.CombName,
                Price = comb.Price,
                Discount = comb.Discount,
                Count = comb.Count,
                ChargeTime = settlement.ChargeTime,
                InvoinceNo = settlement.InvoiceNo,
                SumPrice = comb.Price
            });
        }
        companySettlementReport.list = details;
        return companySettlementReport;
    }

    /// <summary>
    /// 结算费用明细报表(按人员)
    /// </summary>
    /// <param name="seqNo"></param>
    /// <returns></returns>
    /// <exception cref="BusinessException"></exception>
    public CompanySettlementReportByPerson QueryCompanySettlementDetailByPerson(string seqNo)
    {
        var settlement = _companySettlementRepository.First(x => x.BillSeqNo == seqNo);
        if (settlement == null)
        {
            throw new BusinessException("结算批号不存在!");
        }
        var persons = _mapCompanySettlementPersonRepository.FindAll(x => x.BillSeqNo == seqNo).ToList();
        if (persons == null)
        {
            throw new BusinessException("没有找到对应收费数据！");
        }
        var companyName = _codeCompanyRepository.First(x => x.CompanyCode == settlement.CompanyCode).CompanyName;
        var companySettlementReport = new CompanySettlementReportByPerson
        {
            Count = settlement.BillPersonCount,
            Price = settlement.ActuallyPrice
        };
        var regNos = persons.Select(x => x.RegNo).Distinct().ToArray();
        _registerRepository.ReadRegisterTime(regNos, out var startTime, out var endTime);
        List<CompanySettlementReportDetailByPerson> details;
        if (settlement.CaluateType == StatisticType.按登记时间)
        {
            details = _registerRepository.QueryPeRegCombs(startTime, endTime)
            .Where((a, b) => SqlFunc.ContainsArray(regNos, a.RegNo))
            .Where((a, b) => b.IsPayBySelf == false)
            .GroupBy(a => a.RegNo)
            .Select((a, b) => new CompanySettlementReportDetailByPerson
            {
                BillSeqNo = seqNo,
                CompanyCode = settlement.CompanyCode,
                CompanyName = companyName,
                RegNo = a.RegNo,
                Name = SqlFunc.AggregateMax(a.Name),
                Age = SqlFunc.AggregateMax(a.Age),
                CardNo = SqlFunc.AggregateMax(a.CardNo),
                ClusterName = "",
                Price = SqlFunc.AggregateSum(b.Price),
                Sex = SqlFunc.AggregateMax(a.Sex),
                Tel = SqlFunc.AggregateMax(a.Tel),
            }).ToList();
        }
        else
        {
            details = _registerRepository.QueryPeRegCombs(startTime, endTime)
            .Where((a, b) => SqlFunc.ContainsArray(regNos, a.RegNo))
            .Where((a, b) => b.IsPayBySelf == false)
            .GroupBy(a => a.RegNo)
            .Select((a, b) => new CompanySettlementReportDetailByPerson
            {
                BillSeqNo = seqNo,
                CompanyCode = settlement.CompanyCode,
                CompanyName = companyName,
                RegNo = a.RegNo,
                Name = SqlFunc.AggregateMax(a.Name),
                Age = SqlFunc.AggregateMax(a.Age),
                CardNo = SqlFunc.AggregateMax(a.CardNo),
                ClusterName = "",
                Price = 0,
                Sex = SqlFunc.AggregateMax(a.Sex),
                Tel = SqlFunc.AggregateMax(a.Tel),
            }).ToList();
            var commonCombs = GetActualCombPriceWithBindPrice(startTime, endTime, details.Select(x => x.RegNo).ToArray());
            details.BatchUpdate(x => x.Price = commonCombs.Where(c => c.RegNo == x.RegNo).Sum(c => c.Price));
        }

        var regClusters = _registerClusterRepository.FindAll(x => SqlFunc.ContainsArray(regNos, x.RegNo)).ToDictionary(x => x.RegNo, x => x.ClusName);
        details.BatchUpdate(x => x.ClusterName = regClusters.ContainsKey(x.RegNo) ? regClusters[x.RegNo] : string.Empty);
        companySettlementReport.list = details;
        return companySettlementReport;
    }

    /// <summary>
    /// 按结算时间获取单位结算记录
    /// </summary>
    /// <param name="beginTime"></param>
    /// <param name="endTime"></param>
    /// <returns></returns>
    public List<CompanySettlement> QueryCompanySettlementByChargeTime(DateTime beginTime, DateTime endTime)
    {
        beginTime = beginTime.Date;
        endTime = endTime.Date.Add(new TimeSpan(23, 59, 59));
        var operDict = _operatorRepository.FindAll().ToDictionary(x => x.OperatorCode, x => x.Name);
        var companySettlements = _companySettlementRepository.FindAll(x => SqlFunc.Between(x.CreateTime, beginTime, endTime)).ToList();
        companySettlements.BatchUpdate(x => x.SendOperator = $"{operDict[x.SendOperator]}({x.SendOperator})");
        companySettlements.BatchUpdate(x => { if (!x.AuditOperator.IsNullOrEmpty()) x.AuditOperator = $"{operDict[x.AuditOperator]}({x.AuditOperator})"; });
        return companySettlements;
    }

    /// <summary>
    /// 费用结算打印单数据源
    /// </summary>
    /// <param name="seqNo"></param>
    /// <returns></returns>
    public SettlementFormDatasource GetSettlementFormDatasource(string seqNo)
    {
        if (!_companySettlementRepository.Any(x => x.BillSeqNo == seqNo))
        {
            return null;
        }
        var settlement = _companySettlementRepository.First(x => x.BillSeqNo == seqNo);
        var companyName = _codeCompanyRepository.First(x => x.CompanyCode == settlement.CompanyCode).CompanyName;
        var operatorDict = _operatorRepository.FindAll().ToDictionary(x => x.OperatorCode, x => x.Name);
        return new SettlementFormDatasource
        {
            CompanyName = companyName,
            InvoiceName = settlement.InvoiceName,
            PrinttTime = DateTime.Now,
            SeqNo = seqNo,
            DateScope = settlement.ChargeDateScope,
            Type = settlement.CaluateType.ToString(),
            Count = settlement.BillPersonCount,
            Price = settlement.ActuallyPrice,
            ChargeTime = settlement.ChargeTime ?? DateTime.Now,
            AuditTime = (DateTime)settlement.AuditTime,
            CreateOperator = operatorDict[settlement.SendOperator],
            AuditOperator = operatorDict[settlement.AuditOperator],
        };
    }

    /// <summary>
    /// 获取弃检人员及其弃检项目金额
    /// </summary>
    /// <param name="seqNo"></param>
    /// <returns></returns>
    public SettlementPersonAbandomCombs[] ReadSettlementPersonAbandomCombs(string seqNo)
    {
        var settlement = _companySettlementRepository.First(x => x.BillSeqNo == seqNo);
        if (settlement.CaluateType == StatisticType.按登记时间)
            return null;
        var regNos = _mapCompanySettlementPersonRepository.FindAll(x => x.BillSeqNo == seqNo).Select(x => x.RegNo).Distinct().ToArray();
        _registerRepository.ReadRegisterTime(regNos, out var startTime, out var endTime);
        var combs = _splitTable.GetTableOrDefault<PeRegisterComb>(startTime, endTime);
        var record = _splitTable.GetTableOrDefault<PeRecordComb>(startTime, endTime);
        var abandonCombs = _registerRepository.ReadRegister()
             .InnerJoin(combs, (reg, comb) => reg.RegNo == comb.RegNo)
             .LeftJoin(record, (reg, comb, record) => comb.Id == record.RegCombId)
             .Where(reg => SqlFunc.ContainsArray(regNos, reg.RegNo))
             .Where((reg, comb, record) => record.DoctorName == "弃检")
             .Where((reg, comb, record) => comb.IsPayBySelf == false && comb.ReportShow)
             .Select((reg, comb, record) => new
             {
                 reg.RegNo,
                 reg.Name,
                 reg.Age,
                 reg.Sex,
                 comb.CombCode,
                 comb.CombName,
                 comb.Price
             }).ToList();
        var returnList = abandonCombs.Select(x => new SettlementPersonAbandomCombs
        {
            RegNo = x.RegNo,
            Name = x.Name,
            Age = x.Age,
            Sex = x.Sex,
        }).DistinctBy(x => x.RegNo).ToArray();
        foreach (var item in abandonCombs.GroupBy(x => x.RegNo))
        {
            var r = returnList.First(x => x.RegNo == item.Key);
            r.AbandonCombs = string.Join(",", item.Select(x => x.CombName));
            r.AbandonPrice = item.Sum(x => x.Price);
        }
        return returnList;
    }
    #endregion

    #region 字典
    /// <summary>
    /// 保存对应字典
    /// </summary>
    /// <param name="companyClusterCode"></param>
    /// <returns></returns>
    /// <exception cref="BusinessException"></exception>
    public bool SaveCompanyCombChargeItemMap(string companyClusterCode)
    {
        var companyCluster = _codeCompanyClusterRepository.FindInSingleKey(companyClusterCode);
        var combs = _mapCompanyClusterCombRepository.FindAll(x => x.ClusterCode == companyClusterCode).Select(x => x.CombCode).ToArray();
        var tubeCombs = _mapCompanyClusterCombTestTubeRepository.FindAll(x => x.ClusterCode == companyClusterCode).Select(x => x.CombCode).ToArray();
        var combMap = _mapCodeItemCombHisChargeItemRepository.FindAll(x => SqlFunc.ContainsArray(combs, x.CombCode)).ToList();
        var tubeMap = _mapCodeItemCombHisChargeItemRepository.FindAll(x => SqlFunc.ContainsArray(tubeCombs, x.CombCode)).ToList();
        if (combMap.Count == 0 && tubeMap.Count == 0)
        {
            return false;
        }
        var companyMap = new List<MapCompanyCombChargeItem>();
        foreach (var item in combMap)
        {
            if (!_mapCompanyCombChargeItemRepository.Any(x => x.CombCode == item.CombCode && x.CompanyCode == companyCluster.CompanyCode && x.CompanyTimes == companyCluster.CompanyTimes))
            {
                companyMap.Add(new MapCompanyCombChargeItem(companyCluster.CompanyCode, companyCluster.CompanyTimes, item));
            }
        }
        foreach (var item in tubeMap)
        {
            if (!_mapCompanyCombChargeItemRepository.Any(x => x.CombCode == item.CombCode && x.CompanyCode == companyCluster.CompanyCode && x.CompanyTimes == companyCluster.CompanyTimes))
            {
                companyMap.Add(new MapCompanyCombChargeItem(companyCluster.CompanyCode, companyCluster.CompanyTimes, item));
            }
        }
        _mapCompanyCombChargeItemRepository.Insert(companyMap);
        return true;
    }

    /// <summary>
    /// 保存套餐外未有保存字典的项目
    /// </summary>
    /// <param name="register"></param>
    public void SavePersonExtraChargeCombs(PeRegister register)
    {
        //查
        if (!register.IsCompanyCheck)
            return;
        var combs = _companyRepository.GetRegisterExcraComb(register.RegNo, register.CompanyCode, register.CompanyTimes.Value);
        //删
        _mapCompanyPersonCombChargeItemRepository.Delete(x => x.RegNo == register.RegNo && x.IsInCluster == false);
        if (combs.Length == 0) return;
        //增
        var newExtraCombs = _registerRepository.ReadRegisterCombs(register.RegNo)
            .InnerJoin<MapCompanyCombChargeItem>((a, b) => a.CombCode == b.CombCode)
            .InnerJoin<CodeDepartment>((a, b, c) => a.ExamDeptCode == c.DeptCode)
            .Where((a, b, c) => a.RegNo == register.RegNo && SqlFunc.ContainsArray(combs, a.CombCode))
            .Where((a, b, c) => b.CompanyCode == register.CompanyCode && b.CompanyTimes == register.CompanyTimes.Value)
        .Select((a, b, c) => new MapCompanyPersonCombChargeItem
        {
            RegNo = register.RegNo,
            CombCode = b.CombCode,
            CombName = b.CombName,
            HisChargeItemCode = b.HisChargeItemCode,
            ItemType = b.ItemType,
            HisChargeItemPrice = b.HisChargeItemPrice,
            HisChargeItemDiscount = b.HisChargeItemDiscount,
            HisChargeItemCount = b.HisChargeItemCount,
            HisChargeItemCNName = b.HisChargeItemCNName,
            HisChargeItemId = b.HisChargeItemId,
            IsInCluster = false,
        }).Distinct().ToList();
        var extraCombsOutOfCluster = combs.Where(x => !newExtraCombs.Any(y => y.CombCode == x)).ToArray();
        var newExtraCombsOutOfCluster = _registerRepository.ReadRegisterCombs(register.RegNo)
            .InnerJoin<MapCodeItemCombHisChargeItem>((a, b) => a.CombCode == b.CombCode)
            .InnerJoin<CodeDepartment>((a, b, c) => a.ExamDeptCode == c.DeptCode)
            .Where((a, b, c) => a.RegNo == register.RegNo && SqlFunc.ContainsArray(extraCombsOutOfCluster, a.CombCode))
        .Select((a, b, c) => new MapCompanyPersonCombChargeItem
        {
            RegNo = register.RegNo,
            CombCode = b.CombCode,
            CombName = b.CombName,
            HisChargeItemCode = b.HisChargeItemCode,
            ItemType = b.ItemType,
            HisChargeItemPrice = b.HisChargeItemPrice,
            HisChargeItemDiscount = b.HisChargeItemDiscount,
            HisChargeItemCount = b.HisChargeItemCount,
            HisChargeItemCNName = b.HisChargeItemCNName,
            HisChargeItemId = b.HisChargeItemId,
            IsInCluster = false,
        }).Distinct().ToList();
        newExtraCombs.AddRange(newExtraCombsOutOfCluster);
        if (newExtraCombs.Count > 0)
            _mapCompanyPersonCombChargeItemRepository.Insert(newExtraCombs);
    }

    /// <summary>
    /// 保存套餐对应人员字典（登记用）
    /// </summary>
    /// <param name="register"></param>
    /// <param name="clusterCode"></param>
    public void SavePersonClusterChargeItemEntry(PeRegister register, string clusterCode)
    {
        if (!register.IsCompanyCheck)
            return;
        if (clusterCode.IsNullOrEmpty())
        {
            _mapCompanyPersonCombChargeItemRepository.Delete(x => x.RegNo == register.RegNo && x.IsInCluster == true);
            return;
        }
        var combs = GetClusterChargeItemEntry(clusterCode);
        if (combs.Count > 0)
        {
            _mapCompanyPersonCombChargeItemRepository.Delete(x => x.RegNo == register.RegNo && x.IsInCluster == true);
            combs.BatchUpdate(x => x.RegNo = register.RegNo);
            _mapCompanyPersonCombChargeItemRepository.Insert(combs);
        }
    }

    /// <summary>
    /// 获取套餐对应收费字典
    /// </summary>
    /// <param name="clusterCode"></param>
    /// <returns></returns>
    public List<MapCompanyPersonCombChargeItem> GetClusterChargeItemEntry(string clusterCode)
    {
        var comb = _companyRepository.ReadCompanyClusterChargeItem(clusterCode)
       .Select((a, b, c) => new MapCompanyPersonCombChargeItem
       {
           CombCode = c.CombCode,
           CombName = c.CombName,
           HisChargeItemCode = c.HisChargeItemCode,
           ItemType = c.ItemType,
           HisChargeItemPrice = c.HisChargeItemPrice,
           HisChargeItemDiscount = c.HisChargeItemDiscount,
           HisChargeItemCount = c.HisChargeItemCount,
           HisChargeItemCNName = c.HisChargeItemCNName,
           HisChargeItemId = c.HisChargeItemId,
           IsInCluster = true,
       }).Distinct().ToList();
        var testtube = _companyRepository.ReadCompanyClusterChargeItemTestTube(clusterCode)
                .Select((a, b, c) => new MapCompanyPersonCombChargeItem
                {
                    CombCode = c.CombCode,
                    CombName = c.CombName,
                    HisChargeItemCode = c.HisChargeItemCode,
                    ItemType = c.ItemType,
                    HisChargeItemPrice = c.HisChargeItemPrice,
                    HisChargeItemDiscount = c.HisChargeItemDiscount,
                    HisChargeItemCount = c.HisChargeItemCount,
                    HisChargeItemCNName = c.HisChargeItemCNName,
                    HisChargeItemId = c.HisChargeItemId,
                    IsInCluster = true,
                }).Distinct().ToList();
        return comb.Concat(testtube).ToList();
    }

    /// <summary>
    /// 更新单位对应收费字典
    /// </summary>
    /// <param name="companyCode"></param>
    /// <param name="companyTimes"></param>
    public void UpdateCompanyChargeItem(string companyCode, int companyTimes)
    {
        var clusters = _companyRepository.ReadCompanyCluster(companyCode, companyTimes).Select(x => x.ClusterCode).ToArray();
        _mapCompanyCombChargeItemRepository.Delete(x => x.CompanyCode == companyCode && x.CompanyTimes == companyTimes);
        foreach (var cluster in clusters)
        {
            SaveCompanyCombChargeItemMap(cluster);
        }
    }
    #endregion

    #region 私有
    /// <summary>
    /// 获取组合金额及加收
    /// </summary>
    /// <returns></returns>
    private List<CombPriceWithBindPrice> GetActualCombPriceWithBindPrice(DateTime startTime, DateTime endTime, string[] regNos)
    {
        var combs = _splitTable.GetTableOrDefault<PeRegisterComb>(startTime, endTime);
        var record = _splitTable.GetTableOrDefault<PeRecordComb>(startTime, endTime);
        var conclusion = _splitTable.GetTableOrDefault<PeReportConclusion>(startTime, endTime);
        var queryable = _registerRepository.ReadRegister()
            .InnerJoin(_tempTableHelper.TempTable(regNos), (reg, temp) => reg.RegNo == temp.ColumnName)
            .InnerJoin(combs, (reg, temp, comb) => reg.RegNo == comb.RegNo);
        var commonCombs = queryable
            .Clone()
            .LeftJoin(record, (reg, temp, comb, record) => comb.Id == record.RegCombId)
            .InnerJoin(conclusion, (reg, temp, comb, record, con) => reg.RegNo == con.RegNo)
            .Where((reg, temp, comb) => comb.IsPayBySelf == false)
            .Where((reg, temp, comb, record) => record.DoctorName != null && record.DoctorName != "弃检" && comb.ReportShow)
            .Select((reg, temp, comb) => new CombPriceWithBindPrice
            {
                Id = comb.Id,
                RegNo = reg.RegNo,
                CombCode = comb.CombCode,
                CombName = comb.CombName,
                ClsCode = comb.ClsCode,
                Discount = comb.Discount,
                Price = comb.Price,
                ExamDeptCode = comb.ExamDeptCode,
                OriginalPrice = comb.OriginalPrice,
            }).ToList();
        var otherCombs = queryable.Clone()
            .Where((reg, temp, comb) => !comb.ReportShow)
            .Where((reg, temp, comb) => comb.IsPayBySelf == false)
            .Select((reg, temp, comb) => new CombPriceWithBindPrice
            {
                Id = comb.Id,
                RegNo = reg.RegNo,
                CombCode = comb.CombCode,
                CombName = comb.CombName,
                ClsCode = comb.ClsCode,
                Discount = comb.Discount,
                Price = comb.Price,
                ExamDeptCode = comb.ExamDeptCode,
                OriginalPrice = comb.OriginalPrice,
                Befrom = comb.BeFrom
            }).ToList();
        foreach (var comb in otherCombs.GroupBy(x => new { x.RegNo, x.CombCode }))
        {
            if (comb.Key.CombCode == "5056" || comb.Key.CombCode == "5243")//一般健康 健康档案 
            {
                commonCombs.Add(comb.First());
                continue;
            }
            if (comb.Key.CombCode == "5057")//图文
            {
                if (commonCombs.Any(x => x.RegNo == comb.Key.RegNo && x.ClsCode == "15"))
                    commonCombs.Add(comb.First());
                continue;
            }
            if (comb.Key.CombCode == _systemParameterService.VenousBloodComb)
            {
                var blood = comb.First();
                if (commonCombs.Any(x => x.RegNo == comb.Key.RegNo) && blood.Befrom.Any(b => commonCombs.Any(s => s.Id.ToString() == b)))
                    commonCombs.Add(blood);
                continue;
            }
            foreach (var item in comb)
            {
                if (item.Befrom.Count() > 0 && item.Befrom.Any(x => commonCombs.Any(s => s.Id.ToString() == x)))
                {
                    commonCombs.Add(item);
                }
            }
        }
        return commonCombs;
    }

    /// <summary>
    /// 更新加项对应字典
    /// </summary>
    /// <param name="companyCode"></param>
    private void SaveExtra(string companyCode)
    {
        var registers = _registerRepository.ReadRegister().Where(x => x.CompanyCode == companyCode).ToArray();
        foreach (var reg in registers)
            SavePersonExtraChargeCombs(reg);
    }
    #endregion
}
