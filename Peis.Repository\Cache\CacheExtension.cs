﻿using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.DependencyInjection;
using Peis.Utility.Cache;
using SqlSugar;

namespace Peis.Repository.Cache;

public static class CacheExtension
{
    public static IServiceCollection AddSimpleCache(this IServiceCollection services)
    {
        ServiceProvider provider = services.BuildServiceProvider();
        IMemoryCache memoryCache = provider.GetRequiredService<IMemoryCache>();        
        MemoryCacheAdapter cacheAdapter = new(memoryCache);

        ISqlSugarClient db = provider.GetRequiredService<ISqlSugarClient>();
        ProvidersInjector injector = new(db);

        SimpleCache simpleCache = new(cacheAdapter);
        injector.Inject(simpleCache);

        services.AddSingleton<ISimpleCache>(simpleCache);

        return services;
    }
}
