﻿using Microsoft.AspNetCore.Mvc;
using Peis.Model.DTO.IndexPageStatistics;
using Peis.Service.IService;

namespace Peis.API.Controllers.Statistics
{
    /// <summary>
    /// 首页统计
    /// </summary>
    [Route("api/[controller]")]
    [ApiController]
    public class IndexPageStatisticsController : BaseApiController
    {
        private readonly IIndexPageStatisticsService _indexPageStatisticsService;

        public IndexPageStatisticsController(IIndexPageStatisticsService indexPageStatisticsService)
        {
            _indexPageStatisticsService = indexPageStatisticsService;
        }

        /// <summary>
        /// 获取首页统计
        /// </summary>
        /// <returns></returns>
        [HttpPost("GetIndexPageStatistics")]
        [ProducesResponseType(typeof(IndexPageStatistics), 200)]
        public IActionResult GetIndexPageStatistics()
        {
            IndexPageStatistics indexPageStatistics = new();
            result.Success = _indexPageStatisticsService.GetIndexPageStatistics(ref indexPageStatistics);
            result.ReturnMsg = string.Empty;
            result.ReturnData = indexPageStatistics;

            return Ok(result);
        }
    }
}