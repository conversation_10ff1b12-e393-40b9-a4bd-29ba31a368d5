﻿using SqlSugar;
using System;

namespace Peis.Model.DataBaseEntity
{
    /// <summary>
    /// 组合记录表
    /// </summary>
    [SplitTable(SplitType.Year)]
    [SugarTable("PeRecordComb_{yyyy}", "组合记录表")]
    [SugarIndex("index_RegCombId_", nameof(RegCombId), OrderByType.Asc)]
    [SugarIndex("index_RegNo_", nameof(RegNo), OrderByType.Asc)]
    public class PeRecordComb
    {
        /// <summary>
        /// 组合记录Id
        /// </summary>        
        [SugarColumn(IsNullable = false, IsPrimaryKey = true)]
        public long Id { get; set; }

        /// <summary>
        /// 体检号
        /// </summary>        
        [SugarColumn(IsNullable = false, Length = 12)]
        public string RegNo { get; set; }

        /// <summary>
        /// 登记组合Id
        /// </summary>        
        [SugarColumn(IsNullable = false)]
        public long RegCombId { get; set; }

        /// <summary>
        /// 组合代码
        /// </summary>        
        [SugarColumn(IsNullable = false, Length = 8)]
        public string CombCode { get; set; }

        /// <summary>
        /// 小结
        /// </summary>        
        [SugarColumn(IsNullable = false, ColumnDataType = StaticConfig.CodeFirst_BigString)]
        public string CombResult { get; set; }

        /// <summary>
        /// 项目分类
        /// </summary>        
        [SugarColumn(IsNullable = false, Length = 6)]
        public string ClsCode { get; set; }

        /// <summary>
        /// 检查科室代码
        /// </summary>        
        [SugarColumn(IsNullable = false, Length = 10)]
        public string ExamDeptCode { get; set; }

        /// <summary>
        /// 操作员名称
        /// </summary>        
        [SugarColumn(IsNullable = true, Length = 50)]
        public string OperName { get; set; }

        /// <summary>
        /// 医生名称
        /// </summary>        
        [SugarColumn(IsNullable = true, Length = 50)]
        public string DoctorName { get; set; }

        /// <summary>
        /// 组合异常提醒标识
        /// </summary>        
        [SugarColumn(IsNullable = false)]
        public bool IsError { get; set; }

        /// <summary>
        /// 操作时间
        /// </summary>        
        [SugarColumn(IsNullable = false)]
        public DateTime OperTime { get; set; }

        /// <summary>
        /// 检查时间
        /// </summary>        
        [SugarColumn(IsNullable = false)]
        public DateTime ExamTime { get; set; }

        /// <summary>
        /// 体检登记时间（分表依据）
        /// </summary>        
        [SplitField]
        [SugarColumn(IsNullable = false)]
        public DateTime RegisterTime { get; set; }
    }
}