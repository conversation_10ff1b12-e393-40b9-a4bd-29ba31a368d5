﻿namespace Peis.Model.DataBaseEntity;

///<summary>
/// 重大阳性审核条件表
///</summary>
[SugarTable(TableName = nameof(MajorPositiveCriteria), TableDescription = "重大阳性审核条件表")]
[SugarIndex($"idx_{nameof(MajorPositiveCriteria)}", nameof(PositiveCode), OrderByType.Asc)]
public class MajorPositiveCriteria
{
    /// <summary>
    /// 重大阳性码
    /// </summary>        
    [SugarColumn(IsNullable = false, IsPrimaryKey = true, Length = 8)]
    [Required(ErrorMessage = "重大阳性码不能为空")]
    public string PositiveCode { get; set; }

    /// <summary>
    /// 关联逻辑（1:And 2:Or）
    /// </summary>        
    [SugarColumn(IsNullable = false)]
    public int Logic { get; set; }
}