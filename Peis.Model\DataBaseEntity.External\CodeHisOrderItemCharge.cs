﻿using Peis.Model.Other.PeEnum;

namespace Peis.Model.DataBaseEntity.External;

///<summary>
/// 基础数据：His医嘱项目明细(来源系统对接数据提取)
///</summary>
[SugarTable(nameof(CodeHisOrderItemCharge), TableDescription = "His医嘱项目明细(来源系统对接数据提取)")]
[SugarIndex("index_CodeHisOrderItemCharge_MainPId_CNName", nameof(MainPId), OrderByType.Asc, nameof(ChargeItemCNName), OrderByType.Asc)]
public class CodeHisOrderItemCharge
{
    [SugarColumn(IsPrimaryKey = true, Length = 64)]
    public string PId { get; set; }

    /// <summary>
    /// 医嘱PID
    /// </summary>
    [SugarColumn(Length = 64)]
    public string MainPId { get; set; }

    [SugarColumn(Length = 64)]
    public string ChargeItemId { get; set; }

    [SugarColumn(ColumnDataType = "nvarchar(128)")]
    public string ChargeItemCNName { get; set; }

    [SugarColumn(Length = 12, DecimalDigits = 4)]
    public decimal BasePrice { get; set; }

    [SugarColumn(Length = 12, DecimalDigits = 4)]
    public decimal PriceRatio { get; set; }

    public int Quantity { get; set; }

    [SugarColumn(Length = 1)]
    public string IsMust { get; set; }

    public int OrderNum { get; set; }

    [SugarColumn(Length = 1)]
    public string IsAvailable { get; set; }

    public DateTime CreateTime { get; set; }

    [SugarColumn(IsNullable = true)]
    public DateTime? UpdatedTime { get; set; }

    #region Ext

    [SugarColumn(IsIgnore = true)]
    public string CombCode { get; set; }

    [SugarColumn(IsIgnore = true)]
    public string CombName { get; set; }

    /// <summary>
    /// 医嘱项目Code
    /// </summary>
    [SugarColumn(IsIgnore = true)]
    public string MainOrderCode { get; set; }

    /// <summary>
    /// 医嘱项目中文名称
    /// </summary>
    [SugarColumn(IsIgnore = true)]
    public string MainOrderCNName { get; set; }

    /// <summary>
    /// 医嘱项目是否启用
    /// </summary>
    [SugarColumn(IsIgnore = true)]
    public string MainOrderIsAvailable { get; set; }

    /// <summary>
    /// 医嘱项目是删除
    /// </summary>
    [SugarColumn(IsIgnore = true)]
    public string MainOrderIsDeleted { get; set; }

    /// <summary>
    /// 关系code
    /// </summary>
    [SugarColumn(IsIgnore = true)]
    public string MapOrderCode { get; set; }

    /// <summary>
    /// 关系表存储时，医嘱项目是否启用
    /// </summary>
    [SugarColumn(IsIgnore = true)]
    public string MapOrderIsAvailable { get; set; }

    /// <summary>
    /// 根据医嘱是否可用
    /// </summary>
    [SugarColumn(IsIgnore = true)]
    public bool MainOrderIsAvailableBool => MainOrderIsDeleted.Equals(EnumYesOrNoStr.N.ToString(), StringComparison.OrdinalIgnoreCase) &&
        MainOrderIsAvailable.Equals(EnumYesOrNoStr.Y.ToString(), StringComparison.OrdinalIgnoreCase);

    /// <summary>
    /// 判断当前医嘱项目是否可用
    /// </summary>
    [SugarColumn(IsIgnore = true)]
    public bool IsAvailableBool => IsAvailable.Equals(EnumYesOrNoStr.Y.ToString(), StringComparison.OrdinalIgnoreCase);

    /// <summary>
    /// 根据父表&子表的关系，判断当前医嘱是否可用
    /// </summary>
    [SugarColumn(IsIgnore = true)]
    public bool IsAvailableAll => MainOrderIsAvailableBool && IsAvailableBool;

    /// <summary>
    /// 是否体检专用（用于公告提示过滤）
    /// </summary>        
    [SugarColumn(IsIgnore = true)]
    public bool IsDedicatePies { get; set; }
    #endregion
}
