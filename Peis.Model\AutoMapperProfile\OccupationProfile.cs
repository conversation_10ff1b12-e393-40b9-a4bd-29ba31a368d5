﻿using Peis.Model.DataBaseEntity.Occupation;
using Peis.Model.DTO.Occupation.Record;
using Peis.Model.DTO.Occupation.Upload;
using Peis.Model.DTO.Register;

namespace Peis.Model.AutoMapperProfile
{
    public class OccupationProfile: Profile
    {
        public OccupationProfile()
        {
            CreateMap<PeRecordAuditoryResult, AuditoryResultDto>();
            CreateMap<AuditoryResultDto, PeRecordAuditoryResult>();
            CreateMap<PeRecordGeneralConsultation, GeneralConsultation>();
            CreateMap<GeneralConsultation, PeRecordGeneralConsultation>();
            CreateMap<PeRecordGeneralConsultation, GeneralConsultationMenarcheHistory>();
            CreateMap<PeRecordGeneralConsultation, GeneralConsultationProcreateHistory>();
            CreateMap<PeRecordGeneralConsultation, GeneralConsultationMarrageHistory>();
            CreateMap<PeRecordGeneralConsultation, GeneralConsultationSmokingHistory>();
            CreateMap<PeRecordGeneralConsultation, GeneralConsultationDrinkingHistory>();
            CreateMap<PeRecordOccupationalHistory, OccupationalHistory>();
            CreateMap<OccupationalHistory, PeRecordOccupationalHistory>();
            CreateMap<PeRecordAnamneses, Anamneses>();
            CreateMap<Anamneses, PeRecordAnamneses>();
            CreateMap<PeRecordOccupationalHistory, OccupationHistoryDto>();
            CreateMap<OccupationHistoryDto, PeRecordOccupationalHistory>();
            CreateMap<PeRecordAnamneses, AnamnesesDto>();
            CreateMap<AnamnesesDto, PeRecordAnamneses>();
            CreateMap<CodeCompanyCluster, RegisterClusterDto>();
            CreateMap<CodeCluster, RegisterClusterDto>()
               .ForMember(dest => dest.ClusterCode, opt => opt.MapFrom(src => src.ClusCode))
               .ForMember(dest => dest.ClusterName, opt => opt.MapFrom(src => src.ClusName));
        }
    }
}
