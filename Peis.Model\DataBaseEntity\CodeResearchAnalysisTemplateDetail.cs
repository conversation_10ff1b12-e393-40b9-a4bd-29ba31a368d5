﻿using SqlSugar;

namespace Peis.Model.DataBaseEntity
{
    ///<summary>
    ///科研分析模板详情
    ///</summary>
    [SugarTable("CodeResearchAnalysisTemplateDetail")]
    public class CodeResearchAnalysisTemplateDetail
    {
        /// <summary>
        /// 科研分析模板代码
        /// </summary>        
        [SugarColumn(IsNullable = false, IsPrimaryKey = true, Length = 5)]
        public string TemplateCode { get; set; }

        /// <summary>
        /// 代码(组合代码/项目代码)
        /// </summary>        
        [SugarColumn(IsNullable = false, IsPrimaryKey = true, Length = 6)]
        public string Code { get; set; }

        /// <summary>
        /// 名称(项目名称/组合名称)
        /// </summary>        
        [SugarColumn(IsNullable = false, Length = 200)]
        public string Name { get; set; }

        /// <summary>
        /// 操作符
        /// </summary>        
        [SugarColumn(IsNullable = false, IsPrimaryKey = true, Length = 6)]
        public string Operator { get; set; }

        /// <summary>
        /// 值(比较值)
        /// </summary>        
        [SugarColumn(IsNullable = false, Length = 6)]
        public string CodeValue { get; set; }
    }
}