﻿using Peis.Model.DTO.External.ShortMessage;
using Peis.Model.Other.PeEnum;
using Peis.Model.TableFilter;

namespace Peis.Model.DataBaseEntity.External;

/// <summary>
/// 短信发送/待发记录表
/// </summary>
[SplitTable(SplitType.Year)]
[SugarTable("PeSendShortMsgRecord_{yyyy}", "短信发送/待发记录表")]
[SugarIndex("index_PeSendShortMsgRecord_RegT_RegNo_PatN", nameof(RegisterTime), OrderByType.Asc, nameof(RegNo), OrderByType.Asc, nameof(PatientName), OrderByType.Asc)]
public class PeSendShortMsgRecord: IHospCodeFilter
{
    public void Init([NotNull] long id, [NotNull] PeRegister reg)
    {
        Id = id;
        RegNo = reg.RegNo;
        PatientName = reg.Name;
        Sex = reg.Sex;
        PhoneNumber = reg.Tel;
        RegisterTime = reg.RegisterTime;
        HospCode = reg.HospCode;
    }

    public void SetCreateValue([NotNull] string code, [NotNull] string name)
    {
        CreatorCode = code;
        CreatorName = name;
        CreatedTime = DateTime.Now;
        Status = EnumPeSendShortMsgRecordStatus.New;
    }

    public void SetUpdateValue([NotNull] string code, [NotNull] string name)
    {
        UpdatedCode = code;
        UpdatedName = name;
        UpdatedTime = DateTime.Now;
    }

    public void SetApprovedValue([NotNull] string code, [NotNull] string name)
    {
        ApprovedCode = code;
        ApprovedName = name;
        ApprovedTime = DateTime.Now;
        Status = EnumPeSendShortMsgRecordStatus.Approved;
    }
    public void SetSentTime()
    {
        SentTime = DateTime.Now;
        ++SentTimes;
        Status = EnumPeSendShortMsgRecordStatus.Sent;
    }

    public void SetStatus(EnumPeSendShortMsgRecordStatus status)
    {
        Status = status;
    }

    /// <summary>
    /// 主键Id
    /// </summary>        
    [SugarColumn(IsNullable = false, IsPrimaryKey = true)]
    public long Id { get; set; }

    /// <summary>
    /// 体检号
    /// </summary>        
    [SugarColumn(IsNullable = false, Length = 12)]
    public string RegNo { get; set; }

    /// <summary>
    /// 患者姓名
    /// </summary>        
    [SugarColumn(IsNullable = false, Length = 50)]
    public string PatientName { get; set; }

    /// <summary>
    /// 性别（0通用1男2女）
    /// </summary>        
    [SugarColumn(IsNullable = false)]
    public Sex Sex { get; set; }

    /// <summary>
    /// 手机号
    /// </summary>        
    [SugarColumn(IsNullable = false, Length = 20)]
    public string PhoneNumber { get; set; }

    /// <summary>
    /// 体检登记时间（分表依据）
    /// </summary>        
    [SplitField]
    [SugarColumn(IsNullable = false)]
    public DateTime RegisterTime { get; set; }

    /// <summary>
    /// 院区编码
    /// </summary>        
    [SugarColumn(IsNullable = false, Length = 1)]
    public string HospCode { get; set; }

    /// <summary>
    /// 模板Code
    /// </summary>        
    [SugarColumn(IsNullable = false, Length = 64)]
    public string ShortMsgCode { get; set; }

    /// <summary>
    /// 模板类型
    /// </summary>        
    [SugarColumn(IsNullable = false)]
    public EnumShortMsgType ShortMsgType { get; set; }

    /// <summary>
    /// 模板内容
    /// </summary>        
    [SugarColumn(IsNullable = false, ColumnDataType = "nvarchar(1500)")]
    public string ShortMsgContent { get; set; }

    /// <summary>
    /// 发送时间
    /// </summary>        
    [SugarColumn(IsNullable = true)]
    public DateTime? SentTime { get; set; }

    /// <summary>
    /// 发送次数
    /// </summary>        
    [SugarColumn(IsNullable = false)]
    public int SentTimes { get; set; }

    /// <summary>
    /// 状态
    /// </summary>        
    [SugarColumn(IsNullable = false)]
    public EnumPeSendShortMsgRecordStatus Status { get; set; }

    /// <summary>
    /// 创建人Code
    /// </summary>        
    [SugarColumn(IsNullable = false, Length = 10, IsOnlyIgnoreUpdate = true)]
    public string CreatorCode { get; set; }

    /// <summary>
    /// 创建人名称
    /// </summary>        
    [SugarColumn(IsNullable = false, Length = 50, IsOnlyIgnoreUpdate = true)]
    public string CreatorName { get; set; }

    /// <summary>
    /// 创建时间
    /// </summary>        
    [SugarColumn(IsNullable = false, IsOnlyIgnoreUpdate = true)]
    public DateTime CreatedTime { get; set; }

    /// <summary>
    /// 修改人Code
    /// </summary>        
    [SugarColumn(IsNullable = true, Length = 10)]
    public string UpdatedCode { get; set; }

    /// <summary>
    /// 修改人名称
    /// </summary>        
    [SugarColumn(IsNullable = true, Length = 50)]
    public string UpdatedName { get; set; }

    /// <summary>
    /// 修改时间
    /// </summary>        
    [SugarColumn(IsNullable = true)]
    public DateTime? UpdatedTime { get; set; }

    /// <summary>
    /// 审核人Code
    /// </summary>        
    [SugarColumn(IsNullable = true, Length = 10)]
    public string ApprovedCode { get; set; }

    /// <summary>
    /// 审核人名称
    /// </summary>        
    [SugarColumn(IsNullable = true, Length = 50)]
    public string ApprovedName { get; set; }

    /// <summary>
    /// 审核时间
    /// </summary>        
    [SugarColumn(IsNullable = true)]
    public DateTime? ApprovedTime { get; set; }
}
