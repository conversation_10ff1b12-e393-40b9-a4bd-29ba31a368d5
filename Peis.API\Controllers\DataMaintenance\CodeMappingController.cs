﻿using Microsoft.AspNetCore.Mvc;
using Peis.Model.DataBaseEntity;
using Peis.Model.Other.PeEnum;
using Peis.Repository.Repository.TransactionAttribute;
using Peis.Service.IService;
using Peis.Utility.PeUser;
using System;
using System.Collections.Generic;

namespace Peis.API.Controllers.DataMaintenance
{
    /// <summary>
    /// 代码对应
    /// </summary>
    [Route("api/[controller]")]
    [ApiController]
    public class CodeMappingController : BaseApiController
    {
        private readonly ICodeMappingService _codeMappingService;
        private readonly IHttpContextUser _httpContextUser;

        public CodeMappingController(
            ICodeMappingService codeMappingService,
            IHttpContextUser httpContextUser)
        {
            _codeMappingService = codeMappingService;
            _httpContextUser = httpContextUser;
        }

        #region 体检组合-项目对应信息 MapItemComb
        /// <summary>
        /// 获取体检组合-项目对应信息
        /// </summary>
        /// <param name="combCode">项目分类</param>
        /// <param name="pageNumber"></param>
        /// <param name="pageSize"></param>
        /// <returns></returns>
        [HttpPost("PageQuery_ItemComb")]
        public IActionResult PageQuery_ItemComb([FromQuery] string combCode, [FromQuery] int pageNumber, [FromQuery] int pageSize)
        {
            try
            {
                int totalNumber = 0;
                int totalPage = 0;

                result.ReturnData = _codeMappingService.ReadItemComb(combCode, pageNumber, pageSize, ref totalNumber, ref totalPage);
                result.TotalNumber = totalNumber;
                result.TotalPage = totalPage;
            }
            catch (Exception e)
            {
                result.Success = false;
                result.ReturnMsg = $"查询数据时发生异常:{e.Message}";
            }

            return Ok(result);
        }

        /// <summary>
        /// /CD_ItemComb/Create 新增体检组合-项目对应信息
        /// /CD_ItemComb/Delete 删除体检组合-项目对应信息
        /// </summary>
        /// <param name="type"></param>
        /// <param name="mapItemCombs"></param>
        /// <returns></returns>
        [HttpPost("CD_ItemComb/{type}")]
        [UnitOfWork]
        public IActionResult CD_ItemComb([FromRoute] string type, [FromBody] List<MapItemComb> mapItemCombs)
        {
            string msg = string.Empty;
            switch (type.ToLower())
            {
                case "create":
                    result.Success = _codeMappingService.CreateItemComb(mapItemCombs);
                    break;
                case "delete":
                    result.Success = _codeMappingService.DeleteItemComb(mapItemCombs);
                    break;
                default:
                    return new BadRequestResult();
            }

            result.ReturnMsg = msg;
            return Ok(result);
        }
        #endregion

        #region 体检套餐-组合对应信息 MapClusterComb
        /// <summary>
        /// 获取体检套餐-组合对应信息
        /// </summary>
        /// <param name="clusCode"></param>
        /// <returns></returns>
        [HttpPost("Query_ClusterComb")]
        public IActionResult Query_ClusterComb([FromQuery] string clusCode)
        {
            try
            {
                result.ReturnData = _codeMappingService.QueryClusterComb(clusCode);
            }
            catch (Exception e)
            {
                result.Success = false;
                result.ReturnMsg = $"查询数据时发生异常:{e.Message}";
            }

            return Ok(result);
        }

        /// <summary>
        /// /CD_ClusterComb/Create 新增体检套餐-组合对应信息
        /// /CD_ClusterComb/Delete 删除体检套餐-组合对应信息
        /// </summary>
        /// <param name="type"></param>
        /// <param name="mapClusterCombs"></param>
        /// <returns></returns>
        [HttpPost("CD_ClusterComb/{type}")]
        [UnitOfWork]
        public IActionResult CD_ClusterComb([FromRoute] string type, [FromBody] List<MapClusterComb> mapClusterCombs)
        {
            string msg = string.Empty;
            switch (type.ToLower())
            {
                case "create":
                    result.Success = _codeMappingService.CreateClusterComb(mapClusterCombs);
                    break;
                case "delete":
                    result.Success = _codeMappingService.DeleteClusterComb(mapClusterCombs);
                    break;
                default:
                    return new BadRequestResult();
            }

            result.ReturnMsg = msg;
            return Ok(result);
        }

        /// <summary>
        /// 同步套餐价格
        /// </summary>
        /// <returns></returns>
        [HttpPost("SynchronClusterPrice")]
        public IActionResult SynchronClusterPrice()
        {
            result.Success = _codeMappingService.SynchronClusterPrice();
            return Ok(result);
        }

        /// <summary>
        /// 更新体检套餐-组合对应信息
        /// </summary>
        /// <returns></returns>
        [HttpPost("UpdateClusterComb")]
        public IActionResult UpdateClusterComb(List<MapClusterComb> mapClusterCombs)
        {
            result.Success = _codeMappingService.UpdateClusterComb(mapClusterCombs);
            return Ok(result);
        }
        #endregion

        #region 项目结果公式 CodeItemResultExpression
        /// <summary>
        /// 获取项目结果公式信息
        /// </summary>
        /// <param name="itemCode">项目分类</param>
        /// <param name="pageNumber"></param>
        /// <param name="pageSize"></param>
        /// <returns></returns>
        [HttpPost("PageQuery_ItemResultExpression")]
        public IActionResult PageQuery_ItemResultExpression([FromQuery] string itemCode, [FromQuery] int pageNumber, [FromQuery] int pageSize)
        {
            try
            {
                int totalNumber = 0;
                int totalPage = 0;

                result.ReturnData = _codeMappingService.ReadItemResultExpression(itemCode, pageNumber, pageSize, ref totalNumber, ref totalPage);
                result.TotalNumber = totalNumber;
                result.TotalPage = totalPage;
            }
            catch (Exception e)
            {
                result.Success = false;
                result.ReturnMsg = $"查询数据时发生异常:{e.Message}";
            }

            return Ok(result);
        }

        /// <summary>
        /// /CD_ItemResultExpression/Create 新增项目结果公式信息
        /// /CD_ItemResultExpression/Delete 删除项目结果公式信息
        /// </summary>
        /// <param name="type"></param>
        /// <param name="codeItemResultExpressions"></param>
        /// <returns></returns>
        [HttpPost("CD_ItemResultExpression/{type}")]
        public IActionResult CD_ItemResultExpression([FromRoute] string type, [FromBody] List<CodeItemResultExpression> codeItemResultExpressions)
        {
            string msg = string.Empty;

            switch (type.ToLower())
            {
                case "create":
                    result.ReturnData = _codeMappingService.CreateItemResultExpression(codeItemResultExpressions[0], ref msg);
                    result.Success = result.ReturnData != null;
                    break;
                case "delete":
                    result.Success = _codeMappingService.DeleteItemResultExpression(codeItemResultExpressions, ref msg);
                    break;
                default:
                    return new BadRequestResult();
            }

            result.ReturnMsg = msg;
            return Ok(result);
        }

        /// <summary>
        /// 验证字符串公式
        /// </summary>
        /// <param name="expression"></param>
        /// <returns></returns>
        [HttpPost("ItemResultExpressionVerify")]
        public IActionResult ItemResultExpressionVerify([FromBody] string expression)
        {
            string msg = string.Empty;
            result.Success = _codeMappingService.ItemResultExpressionVerify(expression, ref msg, out _);
            result.ReturnMsg = msg;
            return Ok(result);
        }
        #endregion

        #region 档案项目对应信息 MapArchiveItem
        /// <summary>
        /// 获取档案项目对应信息
        /// </summary>
        /// <param name="pageNumber"></param>
        /// <param name="pageSize"></param>
        /// <returns></returns>
        [HttpPost("PageQuery_MapArchiveItem")]
        public IActionResult PageQuery_MapArchiveItem([FromQuery] int pageNumber, [FromQuery] int pageSize)
        {
            try
            {
                int totalNumber = 0;
                int totalPage = 0;

                result.ReturnData = _codeMappingService.ReadArchiveItem(pageNumber, pageSize, ref totalNumber, ref totalPage);
                result.TotalNumber = totalNumber;
                result.TotalPage = totalPage;
            }
            catch (Exception e)
            {
                result.Success = false;
                result.ReturnMsg = $"查询数据时发生异常:{e.Message}";
            }

            return Ok(result);
        }

        /// <summary>
        /// /CD_MapArchiveItem/Create 新增档案项目对应信息
        /// /CD_MapArchiveItem/Delete 删除档案项目对应信息
        /// </summary>
        /// <param name="type"></param>
        /// <param name="mapArchiveItems"></param>
        /// <returns></returns>
        [HttpPost("CD_MapArchiveItem/{type}")]
        public IActionResult CD_MapArchiveItem([FromRoute] string type, [FromBody] List<MapArchiveItem> mapArchiveItems)
        {
            string msg = string.Empty;

            switch (type.ToLower())
            {
                case "create":
                    result.Success = _codeMappingService.CreateArchiveItem(mapArchiveItems, ref msg);
                    break;
                case "delete":
                    result.Success = _codeMappingService.DeleteArchiveItem(mapArchiveItems, ref msg);
                    break;
                default:
                    return new BadRequestResult();
            }

            result.ReturnMsg = msg;
            return Ok(result);
        }
        #endregion

        #region 体检自动添加组合对应信息
        /// <summary>
        /// 获取体检项目组合对应信息
        /// </summary>
        /// <param name="combCode">组合编码</param>
        /// <param name="pageNumber"></param>
        /// <param name="pageSize"></param>
        /// <returns></returns>
        [HttpPost("PageQuery_CodeCombComb")]
        public IActionResult PageQuery_CodeCombComb([FromQuery] string combCode, [FromQuery] int pageNumber, [FromQuery] int pageSize)
        {
            try
            {
                int totalNumber = 0;
                int totalPage = 0;

                result.ReturnData = _codeMappingService.ReadCombWithComb(combCode, pageNumber, pageSize, ref totalNumber, ref totalPage);
                result.TotalNumber = totalNumber;
                result.TotalPage = totalPage;
            }
            catch (Exception e)
            {
                result.Success = false;
                result.ReturnMsg = $"查询数据时发生异常:{e.Message}";
            }

            return Ok(result);
        }

        /// <summary>
        /// /CUD_CodeCombComb/Create 新增项目组合对应信息
        /// /CUD_CodeCombComb/Delete 删除项目组合对应信息
        /// /CUD_CodeCombComb/Update 修改项目组合对应的数量
        /// </summary>
        /// <param name="type"></param>
        /// <param name="codeCombCombs"></param>
        /// <returns></returns>
        [HttpPost("CUD_CodeCombComb/{type}")]
        public IActionResult CUD_CodeCombComb([FromRoute] string type, [FromBody] List<CodeCombComb> codeCombCombs)
        {
            string msg = string.Empty;
            switch (type.ToLower())
            {
                case "create":
                    result.Success = _codeMappingService.CreateCombWithComb(codeCombCombs, ref msg);
                    break;
                case "delete":
                    result.Success = _codeMappingService.DeleteCombWithComb(codeCombCombs, ref msg);
                    break;
                case "update":
                    result.Success = _codeMappingService.UpdateCombWithComb(codeCombCombs, ref msg);
                    break;
                default:
                    return new BadRequestResult();
            }

            result.ReturnMsg = msg;
            return Ok(result);
        }
        #endregion

        #region 医生跨科关系设置
        /// <summary>
        /// 获取有跨科的医生列表
        /// </summary>
        /// <returns></returns>
        [HttpPost("Query_MultiDeptDoctors")]
        public IActionResult Query_MultiDeptDoctors()
        {
            try
            {
                result.ReturnData = _codeMappingService.ReadMultiDeptDoctors();
            }
            catch (Exception e)
            {
                result.Success = false;
                result.ReturnMsg = $"查询数据时发生异常:{e.Message}";
            }

            return Ok(result);
        }

        /// <summary>
        /// 获取医生跨科关系信息
        /// </summary>
        /// <param name="operatorCode">医生代码</param>
        /// <param name="pageNumber"></param>
        /// <param name="pageSize"></param>
        /// <returns></returns>
        [HttpPost("PageQuery_MapDoctorDept")]
        public IActionResult PageQuery_MapDoctorDept([FromQuery] string operatorCode, [FromQuery] int pageNumber, [FromQuery] int pageSize)
        {
            try
            {
                int totalNumber = 0;
                int totalPage = 0;

                result.ReturnData = _codeMappingService.ReadMapDoctorDept(operatorCode, pageNumber, pageSize, ref totalNumber, ref totalPage);
                result.TotalNumber = totalNumber;
                result.TotalPage = totalPage;
            }
            catch (Exception e)
            {
                result.Success = false;
                result.ReturnMsg = $"查询数据时发生异常:{e.Message}";
            }

            return Ok(result);
        }

        /// <summary>
        /// /CD_MapDoctorDept/Create 新增医生跨科关系信息
        /// /CD_MapDoctorDept/Delete 删除医生跨科关系信息
        /// </summary>
        /// <param name="type"></param>
        /// <param name="mapDoctorDepts"></param>
        /// <returns></returns>
        [HttpPost("CD_MapDoctorDept/{type}")]
        [UnitOfWork]
        public IActionResult CD_MapDoctorDept([FromRoute] string type, [FromBody] List<MapDoctorDept> mapDoctorDepts)
        {
            string msg = string.Empty;
            switch (type.ToLower())
            {
                case "create":
                    result.Success = _codeMappingService.CreateMapDoctorDept(mapDoctorDepts);
                    break;
                case "delete":
                    result.Success = _codeMappingService.DeleteMapDoctorDept(mapDoctorDepts);
                    break;
                default:
                    return new BadRequestResult();
            }

            result.ReturnMsg = msg;
            return Ok(result);
        }
        #endregion

        #region 体检组合---his收费项目对应 未实现

        #endregion

        #region 套餐与组合互斥信息
        /// <summary>
        /// 获取套餐与组合互斥信息
        /// </summary>
        /// <param name="clusCode">套餐编码</param>
        /// <param name="pageNumber"></param>
        /// <param name="pageSize"></param>
        /// <returns></returns>
        [HttpPost("PageQuery_ClusMutexComb")]
        public IActionResult PageQuery_ClusMutexComb([FromQuery] string clusCode, [FromQuery] int pageNumber, [FromQuery] int pageSize)
        {
            try
            {
                int totalNumber = 0;
                int totalPage = 0;

                result.ReturnData = _codeMappingService.ReadClusMutexComb(clusCode, pageNumber, pageSize, ref totalNumber, ref totalPage);
                result.TotalNumber = totalNumber;
                result.TotalPage = totalPage;
            }
            catch (Exception e)
            {
                result.Success = false;
                result.ReturnMsg = $"查询数据时发生异常:{e.Message}";
            }

            return Ok(result);
        }

        /// <summary>
        /// /CD_ClusMutexComb/Create 新增套餐与组合互斥信息
        /// /CD_ClusMutexComb/Delete 删除套餐与组合互斥信息
        /// </summary>
        /// <param name="type"></param>
        /// <param name="clusMutexCombs"></param>
        /// <returns></returns>
        [HttpPost("CD_ClusMutexComb/{type}")]
        [UnitOfWork]
        public IActionResult CD_ClusMutexComb([FromRoute] string type, [FromBody] List<CodeClusMutexComb> clusMutexCombs)
        {
            string msg = string.Empty;
            switch (type.ToLower())
            {
                case "create":
                    result.Success = _codeMappingService.CreateClusMutexComb(clusMutexCombs);
                    break;
                case "delete":
                    result.Success = _codeMappingService.DeleteClusMutexComb(clusMutexCombs);
                    break;
                default:
                    return new BadRequestResult();
            }

            result.ReturnMsg = msg;
            return Ok(result);
        }
        #endregion

        #region 检验组合对应 MapCombGroup
        /// <summary>
        /// 获取检验组合对应
        /// </summary>
        /// <param name="combCode"></param>
        /// <param name="pageNumber"></param>
        /// <param name="pageSize"></param>
        /// <returns></returns>
        [HttpPost("PageQuery_MapCombGroup")]
        public IActionResult PageQuery_MapCombGroup([FromQuery] string combCode, [FromQuery] int pageNumber, [FromQuery] int pageSize)
        {
            try
            {
                int totalNumber = 0;
                int totalPage = 0;

                result.ReturnData = _codeMappingService.ReadMapCombGroup(combCode, pageNumber, pageSize, ref totalNumber, ref totalPage);
                result.TotalNumber = totalNumber;
                result.TotalPage = totalPage;
            }
            catch (Exception e)
            {
                result.Success = false;
                result.ReturnMsg = $"查询数据时发生异常:{e.Message}";
            }

            return Ok(result);
        }

        /// <summary>
        /// CD_MapCombGroup/Create 新增检验组合对应
        /// CD_MapCombGroup/Delete 删除检验组合对应
        /// </summary>
        /// <param name="type"></param>
        /// <param name="mapCombGroups"></param>
        /// <returns></returns>
        [HttpPost("CD_MapCombGroup/{type}")]
        public IActionResult CD_MapCombGroup([FromRoute] string type, [FromBody] List<MapCombGroup> mapCombGroups)
        {
            string msg = string.Empty;
            switch (type.ToLower())
            {
                case "create":
                    result.Success = _codeMappingService.CreateMapCombGroup(mapCombGroups, ref msg);
                    break;
                case "delete":
                    result.Success = _codeMappingService.DeleteMapCombGroup(mapCombGroups, ref msg);
                    break;
                default:
                    return new BadRequestResult();
            }

            result.ReturnMsg = msg;
            return Ok(result);
        }
        #endregion

        #region 检验项目对应 MapItemLisItem

        /// <summary>
        /// 获取检验项目
        /// </summary>
        /// <returns></returns>
        [HttpPost("PageQuery_LisItem")]
        public IActionResult PageQuery_LisItem([FromQuery] int pageNumber, [FromQuery] int pageSize)
        {
            try
            {
                int totalNumber = 0;
                int totalPage = 0;

                result.ReturnData = _codeMappingService.ReadLisItem(pageNumber, pageSize, ref totalNumber, ref totalPage);
                result.TotalNumber = totalNumber;
                result.TotalPage = totalPage;
            }
            catch (Exception e)
            {
                result.Success = false;
                result.ReturnMsg = $"查询数据时发生异常:{e.Message}";
            }

            return Ok(result);
        }

        /// <summary>
        /// 获取检验项目对应
        /// </summary>
        /// <param name="itemCode"></param>
        /// <param name="pageNumber"></param>
        /// <param name="pageSize"></param>
        /// <returns></returns>
        [HttpPost("PageQuery_MapItemLisItem")]
        public IActionResult PageQuery_MapItemLisItem([FromQuery] string itemCode, [FromQuery] int pageNumber, [FromQuery] int pageSize)
        {
            try
            {
                int totalNumber = 0;
                int totalPage = 0;

                result.ReturnData = _codeMappingService.ReadMapItemLisItem(itemCode, pageNumber, pageSize, ref totalNumber, ref totalPage);
                result.TotalNumber = totalNumber;
                result.TotalPage = totalPage;
            }
            catch (Exception e)
            {
                result.Success = false;
                result.ReturnMsg = $"查询数据时发生异常:{e.Message}";
            }

            return Ok(result);
        }

        /// <summary>
        /// CD_MapItemLisItem/Create 新增检验项目对应
        /// CD_MapItemLisItem/Delete 删除检验项目对应
        /// </summary>
        /// <param name="type"></param>
        /// <param name="mapItemLisItems"></param>
        /// <returns></returns>
        [HttpPost("CD_MapItemLisItem/{type}")]
        public IActionResult CD_MapItemLisItem([FromRoute] string type, [FromBody] List<MapItemLisItem> mapItemLisItems)
        {
            string msg = string.Empty;

            switch (type.ToLower())
            {
                case "create":
                    result.Success = _codeMappingService.CreateMapItemLisItem(mapItemLisItems, ref msg);
                    break;
                case "delete":
                    result.Success = _codeMappingService.DeleteMapItemLisItem(mapItemLisItems, ref msg);
                    break;
                default:
                    return new BadRequestResult();
            }

            result.ReturnMsg = msg;
            return Ok(result);
        }

        #endregion

        #region 影像对应 MapItemRisPart
        /// <summary>
        /// 获取影像对应
        /// </summary>
        /// <param name="itemCode"></param>
        /// <param name="pageNumber"></param>
        /// <param name="pageSize"></param>
        /// <returns></returns>
        [HttpPost("PageQuery_MapItemRisPart")]
        public IActionResult PageQuery_MapItemRisPart([FromQuery] string itemCode, [FromQuery] int pageNumber, [FromQuery] int pageSize)
        {
            try
            {
                int totalNumber = 0;
                int totalPage = 0;

                result.ReturnData = _codeMappingService.ReadMapItemRisPart(itemCode, pageNumber, pageSize, ref totalNumber, ref totalPage);
                result.TotalNumber = totalNumber;
                result.TotalPage = totalPage;
            }
            catch (Exception e)
            {
                result.Success = false;
                result.ReturnMsg = $"查询数据时发生异常:{e.Message}";
            }

            return Ok(result);
        }

        /// <summary>
        /// CD_MapItemRisPart/Create 新增影像对应
        /// CD_MapItemRisPart/Delete 删除影像对应
        /// </summary>
        /// <param name="type"></param>
        /// <param name="mapItemRisParts"></param>
        /// <returns></returns>
        [HttpPost("CD_MapItemRisPart/{type}")]
        public IActionResult CD_MapItemRisPart([FromRoute] string type, [FromBody] List<MapItemRisPart> mapItemRisParts)
        {
            string msg = string.Empty;

            switch (type.ToLower())
            {
                case "create":
                    result.Success = _codeMappingService.CreateMapItemRisPart(mapItemRisParts, ref msg);
                    break;
                case "delete":
                    result.Success = _codeMappingService.DeleteMapItemRisPart(mapItemRisParts, ref msg);
                    break;
                default:
                    return new BadRequestResult();
            }

            result.ReturnMsg = msg;
            return Ok(result);
        }
        #endregion

        #region 体检分类-主检审核医生对应信息 MapPeClsDoctor
        /// <summary>
        /// 获取体检分类-主检审核医生对应信息
        /// </summary>
        /// <param name="checkAudit">主检/审核</param>
        /// <param name="peCls">体检分类</param>
        /// <param name="pageNumber"></param>
        /// <param name="pageSize"></param>
        /// <returns></returns>
        [HttpPost("PageQuery_PeClsDoctor")]
        public IActionResult PageQuery_PeClsDoctor([FromQuery] CheckAudit checkAudit, [FromQuery] PeCls peCls, [FromQuery] int pageNumber, [FromQuery] int pageSize)
        {
            try
            {
                int totalNumber = 0;
                int totalPage = 0;

                result.ReturnData = _codeMappingService.ReadPeClsDoctor(checkAudit, peCls, pageNumber, pageSize, ref totalNumber, ref totalPage);
                result.TotalNumber = totalNumber;
                result.TotalPage = totalPage;
            }
            catch (Exception e)
            {
                result.Success = false;
                result.ReturnMsg = $"查询数据时发生异常:{e.Message}";
            }

            return Ok(result);
        }

        /// <summary>
        /// /CD_PeClsDoctor/Create 新增体检分类-主检审核医生对应信息
        /// /CD_PeClsDoctor/Delete 删除体检分类-主检审核医生对应信息
        /// </summary>
        /// <param name="type"></param>
        /// <param name="mapPeClsDoctors"></param>
        /// <returns></returns>
        [HttpPost("CD_PeClsDoctor/{type}")]
        public IActionResult CD_PeClsDoctor([FromRoute] string type, [FromBody] List<MapPeClsDoctor> mapPeClsDoctors)
        {
            string msg = string.Empty;
            switch (type.ToLower())
            {
                case "create":                   
                    result.Success = _codeMappingService.CreatePeClsDoctor(mapPeClsDoctors, ref msg);
                    break;
                case "delete":
                    result.Success = _codeMappingService.DeletePeClsDoctor(mapPeClsDoctors, ref msg);
                    break;
                default:
                    return new BadRequestResult();
            }

            result.ReturnMsg = msg;
            return Ok(result);
        }
        #endregion

        #region 费用分类分组与费用分类对应信息（收费类代码） MapFeeClsGroupFeeCls
        /// <summary>
        /// 获取费用分类分组与费用分类对应信息（收费类代码）
        /// </summary>
        /// <param name="feeClsGroupCode"></param>
        /// <param name="pageNumber"></param>
        /// <param name="pageSize"></param>
        /// <returns></returns>
        [HttpPost("PageQuery_MapFeeClsGroupFeeCls")]
        public IActionResult PageQuery_MapFeeClsGroupFeeCls([FromQuery] string feeClsGroupCode, [FromQuery] int pageNumber, [FromQuery] int pageSize)
        {
            try
            {
                int totalNumber = 0;
                int totalPage = 0;

                result.ReturnData = _codeMappingService.ReadMapFeeClsGroupFeeCls(feeClsGroupCode, pageNumber, pageSize, ref totalNumber, ref totalPage);
                result.TotalNumber = totalNumber;
                result.TotalPage = totalPage;
            }
            catch (Exception e)
            {
                result.Success = false;
                result.ReturnMsg = $"查询数据时发生异常:{e.Message}";
            }

            return Ok(result);
        }

        /// <summary>
        /// CD_MapFeeClsGroupFeeCls/Create 新增费用分类分组与费用分类对应信息（收费类代码）
        /// CD_MapFeeClsGroupFeeCls/Delete 删除费用分类分组与费用分类对应信息（收费类代码）
        /// </summary>
        /// <param name="type"></param>
        /// <param name="mapFeeClsGroupFeeCls"></param>
        /// <returns></returns>
        [HttpPost("CD_MapFeeClsGroupFeeCls/{type}")]
        public IActionResult CD_MapFeeClsGroupFeeCls([FromRoute] string type, [FromBody] List<MapFeeClsGroupFeeCls> mapFeeClsGroupFeeCls)
        {
            string msg = string.Empty;
            switch (type.ToLower())
            {
                case "create":
                    result.Success = _codeMappingService.CreateMapFeeClsGroupFeeCls(mapFeeClsGroupFeeCls, ref msg);
                    break;
                case "delete":
                    result.Success = _codeMappingService.DeleteMapFeeClsGroupFeeCls(mapFeeClsGroupFeeCls, ref msg);
                    break;
                default:
                    return new BadRequestResult();
            }

            result.ReturnMsg = msg;
            return Ok(result);
        }
        #endregion

        #region 结账模式-结账类型对应信息（收费类代码） MapPayModePayment
        /// <summary>
        /// 获取结账模式-结账类型对应信息（收费类代码）
        /// </summary>
        /// <param name="payModeCode"></param>
        /// <param name="pageNumber"></param>
        /// <param name="pageSize"></param>
        /// <returns></returns>
        [HttpPost("PageQuery_MapPayModePayment")]
        public IActionResult PageQuery_MapPayModePayment([FromQuery] string payModeCode, [FromQuery] int pageNumber, [FromQuery] int pageSize)
        {
            try
            {
                int totalNumber = 0;
                int totalPage = 0;

                result.ReturnData = _codeMappingService.ReadMapPayModePayment(payModeCode, pageNumber, pageSize, ref totalNumber, ref totalPage);
                result.TotalNumber = totalNumber;
                result.TotalPage = totalPage;
            }
            catch (Exception e)
            {
                result.Success = false;
                result.ReturnMsg = $"查询数据时发生异常:{e.Message}";
            }

            return Ok(result);
        }

        /// <summary>
        /// CD_MapPayModePayment/Create 新增结账模式-结账类型对应信息（收费类代码）
        /// CD_MapPayModePayment/Delete 删除结账模式-结账类型对应信息（收费类代码）
        /// </summary>
        /// <param name="type"></param>
        /// <param name="mapPayModePayments"></param>
        /// <returns></returns>
        [HttpPost("CD_MapPayModePayment/{type}")]
        public IActionResult CD_MapPayModePayment([FromRoute] string type, [FromBody] List<MapPayModePayment> mapPayModePayments)
        {
            string msg = string.Empty;

            switch (type.ToLower())
            {
                case "create":
                    result.Success = _codeMappingService.CreateMapPayModePayment(mapPayModePayments, ref msg);
                    break;
                case "delete":
                    result.Success = _codeMappingService.DeleteMapPayModePayment(mapPayModePayments, ref msg);
                    break;
                default:
                    return new BadRequestResult();
            }

            result.ReturnMsg = msg;
            return Ok(result);
        }
        #endregion

    }
}
