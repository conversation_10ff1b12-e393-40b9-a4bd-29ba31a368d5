﻿using Peis.Model.Other.PeEnum;
using SqlSugar;

namespace Peis.Model.DataBaseEntity.External
{
    /// <summary>
    /// 门诊申请组合关联表
    /// </summary>
    [SplitTable(SplitType.Year)]
    [SugarTable("ExtHisApplyComb_{yyyy}", "门诊申请组合关联表")]
    [SugarIndex("index_RegNo_", nameof(RegNo), OrderByType.Asc)]
    public class ExtHisApplyComb
    {
        /// <summary>
        /// 登记组合Id
        /// </summary>
        [SugarColumn(IsNullable = false, IsPrimaryKey = true, IsIdentity = false)]
        public long RegCombId { get; set; }

        /// <summary>
        /// 体检号
        /// </summary>
        [SugarColumn(IsNullable = false, Length = 12)]
        public string RegNo { get; set; }

        /// <summary>
        /// 姓名
        /// </summary>
        [SugarColumn(IsNullable = false, Length = 50)]
        public string Name { get; set; }

        /// <summary>
        /// 申请类型（Lis、Exam）
        /// </summary>
        [SugarColumn(IsNullable = false, Length = 40)]
        public string ApplyType { get; set; }

        /// <summary>
        /// 检查申请单状态(1 接收登记 2 取消接收登记 4 确认执行 5 取消执行 7 报告发布 8 取消发布 )
        /// 检验申请单状态(1 条码打印 2 条码采集     3 条码接收 4 条码退回 5 条码作废 6 报告审核 7 取消审核  )
        /// </summary>
        [SugarColumn(IsNullable = false, Length = 1)]
        public string ApplyStatus { get; set; }

        /// <summary>
        /// 支付状态（0未收费 1收费 2冲销 3已退费）
        /// </summary>        
        [SugarColumn(IsNullable = false)]
        public PayStatus PayStatus { get; set; }

        /// <summary>
        /// 组合代码
        /// </summary>
        [SugarColumn(IsNullable = false, Length = 12)]
        public string CombCode { get; set; }

        /// <summary>
        /// 组合名称
        /// </summary>
        [SugarColumn(IsNullable = false, Length = 400)]
        public string CombName { get; set; }

        /// <summary>
        /// 项目分类
        /// </summary>        
        [SugarColumn(IsNullable = false, Length = 6)]
        public string ClsCode { get; set; }

        /// <summary>
        /// 单价
        /// </summary>        
        [SugarColumn(IsNullable = false, Length = 12, DecimalDigits = 2)]
        public decimal Price { get; set; }

        /// <summary>
        /// His-医嘱项目CODE
        /// </summary>        
        [SugarColumn(IsNullable = true, Length = 64)]
        public string HisOrderCode { get; set; }

        /// <summary>
        /// His-医嘱项目中文名称
        /// </summary>
        [SugarColumn(IsNullable = true, Length = 256)]
        public string HisOrderCNName { get; set; }

        /// <summary>
        /// 开单人代码
        /// </summary>        
        [SugarColumn(IsNullable = true, Length = 10)]
        public string ApplicantCode { get; set; }

        /// <summary>
        /// 开单人名字
        /// </summary>        
        [SugarColumn(IsNullable = true, Length = 50)]
        public string ApplicantName { get; set; }

        /// <summary>
        /// 体检登记时间（分表依据）
        /// </summary>
        [SplitField]
        [SugarColumn(IsNullable = false)]
        public DateTime RegisterTime { get; set; }
    }
}
