﻿using AutoMapper;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Peis.Model.DataBaseEntity;
using Peis.Model.DTO;
using Peis.Model.DTO.CompanyData;
using Peis.Model.Other.Input;
using Peis.Repository.Repository.TransactionAttribute;
using Peis.Service.IService;
using Peis.Service.IService.Helper;
using Peis.Utility.PeUser;
using System.Collections.Generic;

namespace Peis.API.Controllers.DataMaintenance
{
    /// <summary>
    /// 单位维护
    /// </summary>
    [Route("api/[controller]")]
    [ApiController]
    public class CompanyController : BaseApiController
    {
        private readonly ICompanyService _companyService;
        private readonly IHttpContextUser _httpContextUser;
        private readonly INoGeneration _noGeneration;
        private readonly IMapper _mapper;

        public CompanyController(
            ICompanyService companyService,
            IHttpContextUser httpContextUser,
            INoGeneration noGeneration,
            IMapper mapper)
        {
            _companyService = companyService;
            _httpContextUser = httpContextUser;
            _noGeneration = noGeneration;
            _mapper = mapper;
        }

        #region 单位分类代码
        /// <summary>
        /// 获取单位分类及其单位的层级信息
        /// </summary>
        ///  <response code="200">执行成功</response>
        /// <returns></returns>
        [HttpPost("ReadCompanyLevel")]
        [ProducesResponseType(typeof(List<CompanyLevel>), 200)]
        public IActionResult ReadCompanyLevel()
        {
            result.ReturnData = _companyService.ReadCompanyLevel();

            return Ok(result);
        }

        /// <summary>
        /// CUD_CodeCompanyCls/Create 新增单位分类代码
        /// CUD_CodeCompanyCls/Update 更新单位分类代码
        /// CUD_CodeCompanyCls/Delete 删除单位分类代码
        /// </summary>
        /// <param name="type"></param>
        /// <param name="codeCompanyCls"></param>
        /// <response code="200">执行成功</response>
        /// <returns></returns>
        [HttpPost("CUD_CodeCompanyCls/{type}")]
        [UnitOfWork]
        public IActionResult CUD_CodeCompanyCls([FromRoute] string type, [FromBody] CodeCompanyCls codeCompanyCls)
        {
            string msg = string.Empty;
            codeCompanyCls.HospCode = _httpContextUser.HospCode;
            switch (type.ToLower())
            {
                case "create":
                    codeCompanyCls.CompanyClsCode = _noGeneration.NextCompanyClsNo(_httpContextUser.HospCode)[0];
                    result.Success = _companyService.CreateCodeCompanyCls(codeCompanyCls, ref msg);
                    break;
                case "update":
                    result.Success = _companyService.UpdateCodeCompanyCls(codeCompanyCls, ref msg);
                    break;
                case "delete":
                    result.Success = _companyService.DeleteCodeCompanyCls(codeCompanyCls, ref msg);
                    break;
                default:
                    return new BadRequestResult();
            }

            result.ReturnMsg = msg;

            return Ok(result);
        }

        /// <summary>
        /// /R_CodeCompanyCls 获取单位分类代码
        /// </summary>
        /// <response code="200">执行成功</response>
        /// <returns></returns>
        [HttpPost("R_CodeCompanyCls")]
        [ProducesResponseType(typeof(List<CodeCompanyCls>), 200)]
        public IActionResult R_CodeCompanyCls()
        {
            result.ReturnData = _companyService.ReadCodeCompanyCls();

            return Ok(result);
        }
        #endregion

        #region 单位信息
        /// <summary>
        /// /CUD_CodeCompany/Create 新增单位信息
        /// /CUD_CodeCompany/Update 更新单位信息
        /// /CUD_CodeCompany/Delete 删除单位信息
        /// </summary>
        /// <param name="type"></param>
        /// <param name="codeCompany"></param>
        /// <response code="200">执行成功</response>
        /// <returns></returns>
        [HttpPost("CUD_CodeCompany/{type}")]
        [UnitOfWork]
        public IActionResult CUD_CodeCompany([FromRoute] string type, [FromBody] CodeCompany codeCompany)
        {
            string msg = string.Empty;
            codeCompany.HospCode = _httpContextUser.HospCode;
            switch (type.ToLower())
            {
                case "create":
                    codeCompany.CompanyCode = _noGeneration.NextCompanyNo(_httpContextUser.HospCode)[0];
                    result.Success = _companyService.CreateCompany(codeCompany);
                    break;
                case "update":
                    result.Success = _companyService.UpdateCompany(codeCompany);
                    break;
                case "delete":
                    result.Success = _companyService.DeleteCompany(codeCompany);
                    break;
                default:
                    return new BadRequestResult();
            }

            result.ReturnMsg = msg;

            return Ok(result);
        }

        /// <summary>
        /// R_CodeCompany  获取单位信息
        /// </summary>
        /// <param name="companyQuery"></param>
        /// <response code="200">执行成功</response>
        /// <returns></returns>
        [HttpPost("R_CodeCompany")]
        [ProducesResponseType(typeof(List<CodeCompanyDto>), 200)]
        public IActionResult R_CodeCompany([FromBody] CompanyQuery companyQuery)
        {
            var data = _companyService.ReadCompany(companyQuery);
            result.ReturnData = _mapper.Map<List<CodeCompanyDto>>(data);
            result.TotalPage = 1;
            result.TotalNumber = data.Length;

            return Ok(result);
        }

        /// <summary>
        /// 获取单位信息及单位次数
        /// </summary>
        /// <param name="query">查询条件</param>
        /// <returns></returns>
        [HttpPost("GetCompaniesWithTimes")]
        [ProducesResponseType(typeof(List<CodeCompanyDto>), 200)]
        public IActionResult GetCompaniesWithTimes([FromBody] CompanyQuery query)
        {
            int totalNumber = 0;
            int totalPage = 0;
            var data = _companyService.GetCompaniesWithTimes(query, ref totalNumber, ref totalPage);

            result.ReturnData = _mapper.Map<List<CodeCompanyDto>>(data);
            result.TotalNumber = totalNumber;
            result.TotalPage = totalPage;

            return Ok(result);
        }
        #endregion

        #region 单位部门信息
        /// <summary>
        /// CUD_CodeCompanyDepartment/Create 新增单位部门信息
        /// CUD_CodeCompanyDepartment/Update 更新单位部门信息
        /// CUD_CodeCompanyDepartment/Delete 删除单位部门信息
        /// </summary>
        /// <param name="type"></param>
        /// <param name="codeCompanyDepartment"></param>
        /// <response code="200">执行成功</response>
        /// <returns></returns>
        [HttpPost("CUD_CodeCompanyDepartment/{type}")]
        public IActionResult CUD_CodeCompanyDepartment([FromRoute] string type, [FromBody] CodeCompanyDepartment codeCompanyDepartment)
        {
            string msg = string.Empty;
            switch (type.ToLower())
            {
                case "create":
                    codeCompanyDepartment.DeptCode = _noGeneration.NextCompanyDeptNo()[0];
                    result.Success = _companyService.CreateCodeCompanyDepartment(codeCompanyDepartment, ref msg);
                    break;
                case "update":
                    result.Success = _companyService.UpdateCodeCompanyDepartment(codeCompanyDepartment, ref msg);
                    break;
                case "delete":
                    result.Success = _companyService.DeleteCodeCompanyDepartment(codeCompanyDepartment, ref msg);
                    break;
                default:
                    return new BadRequestResult();
            }

            result.ReturnMsg = msg;

            return Ok(result);
        }

        /// <summary>
        /// /R_CodeCompanyDepartment 获取单位部门信息
        /// </summary>
        /// <param name="codeCompanyDepartment"></param>
        /// <response code="200">执行成功</response>
        /// <returns></returns>
        [HttpPost("R_CodeCompanyDepartment")]
        [ProducesResponseType(typeof(CodeCompanyDepartment[]), 200)]
        public IActionResult R_CodeCompanyDepartment([FromBody] CodeCompanyDepartment codeCompanyDepartment)
        {
            result.ReturnData = _companyService.ReadCodeCompanyDepartment(codeCompanyDepartment.CompanyCode);

            return Ok(result);
        }
        #endregion

        #region 单位次数
        /// <summary>
        /// /CUD_CodeCompanyTimes/Create 新增单位体检次数信息
        /// /CUD_CodeCompanyTimes/Update 更新单位体检次数信息
        /// /CUD_CodeCompanyTimes/Delete 删除单位体检次数信息
        /// </summary>
        /// <param name="type"></param>
        /// <param name="companyTimes"></param>
        /// <response code="200">执行成功</response>
        /// <returns></returns>
        [HttpPost("CUD_CodeCompanyTimes/{type}")]
        public IActionResult CUD_CodeCompanyTimes([FromRoute] string type, [FromBody] CodeCompanyTimes companyTimes)
        {
            string msg = string.Empty;
            switch (type.ToLower())
            {
                case "create":
                    result.Success = _companyService.CreateCompanyTimes(companyTimes, ref msg);
                    break;
                case "update":
                    result.Success = _companyService.UpdateCompanyTimes(companyTimes, ref msg);
                    break;
                case "delete":
                    result.Success = _companyService.DeleteCompanyTimes(companyTimes.CompanyCode, companyTimes.CompanyTimes, ref msg);
                    break;
                default:
                    return new BadRequestResult();
            }

            result.ReturnMsg = msg;
            return Ok(result);
        }

        /// <summary>
        /// /RD_CodeCompanyTimes  获取单位体检次数信息
        /// </summary>
        /// <param name="CompanyCode">单位编码</param>
        /// <response code="200">执行成功</response>
        /// <returns></returns>
        [HttpPost("R_CodeCompanyTimes")]
        [ProducesResponseType(typeof(List<CodeCompanyTimes>), 200)]
        public IActionResult R_CodeCompanyTimes([FromQuery] string CompanyCode)
        {
            result.ReturnData = _companyService.ReadCompanyTimes(CompanyCode);

            return Ok(result);
        }

        /// <summary>
        /// /GetSimpleCompanyTimes  获取体检次数简要信息
        /// </summary>
        /// <param name="CompanyCode">单位编码</param>
        /// <returns></returns>
        [HttpPost("GetSimpleCompanyTimes")]
        public IActionResult GetSimpleCompanyTimes([FromQuery] string CompanyCode)
        {
            result.ReturnData = _companyService.SimpleCompanyTimes(CompanyCode);
            return Ok(result);
        }
        #endregion

        #region 单位套餐、单位套餐的组合
        /// <summary>
        /// 获取单位套餐概要信息列表
        /// </summary>
        /// <param name="companyCode">单位码</param>
        /// <param name="companyTimes">单位次数</param>
        /// <returns></returns>
        [HttpPost("ReadCompanyClusSimpleInfos")]
        public IActionResult ReadCompanyClusSimpleInfos([FromQuery] string companyCode, [FromQuery] int companyTimes)
        {
            result.ReturnData = _companyService.ReadCompanyClusSimpleInfos(companyCode, companyTimes);

            return Ok(result);
        }

        /// <summary>
        /// 获取单位套餐及其明细内容
        /// </summary>
        /// <param name="companyCode">单位码</param>
        /// <param name="companyTimes">单位次数</param>
        /// <returns></returns>
        [HttpPost("GetCompanyClusWithDetails")]
        [ProducesResponseType(typeof(List<CodeCompanyCluster>), StatusCodes.Status200OK)]
        public IActionResult GetCompanyClusWithDetails([FromQuery] string companyCode, [FromQuery] int companyTimes)
        {
            result.ReturnData = _companyService.GetCompanyClusWithDetails(companyCode, companyTimes);

            return Ok(result);
        }

        /// <summary>
        /// 复制单位套餐的组合列表
        /// </summary>
        /// <param name="IsCompany">True 团体 False 个人</param>
        /// <param name="clusterCode">套餐码</param>
        /// <returns></returns>
        [HttpPost("CopyCompanyClusterCombs")]
        public IActionResult CopyCompanyClusterCombs([FromQuery] bool IsCompany, [FromQuery] string clusterCode)
        {
            result.ReturnData = _companyService.CopyCompanyClusterCombs(IsCompany, clusterCode);

            return Ok(result);
        }

        /// <summary>
        /// 获取单位套餐信息及组合列表
        /// </summary>
        /// <param name="clusterCode">套餐码</param>
        /// <returns></returns>
        [HttpPost("ReadCompanyClusAndCombs")]
        [ProducesResponseType(typeof(CompanyClusterDetail), 200)]
        public IActionResult ReadCompanyClusAndCombs([FromQuery] string clusterCode)
        {
            result.ReturnData = _companyService.ReadCompanyClusAndCombs(clusterCode);

            return Ok(result);
        }

        /// <summary>
        /// 删除单位套餐及组合信息
        /// </summary>
        /// <param name="clusterCode">单位套餐码</param>
        /// <returns></returns>
        [HttpPost("DeleteCompanyCluster")]
        [UnitOfWork]
        public IActionResult DeleteCompanyClusterAndComb([FromQuery] string clusterCode)
        {
            string msg = string.Empty;
            result.Success = _companyService.DeleteCompanyClusterAndComb(clusterCode);
            result.ReturnMsg = msg;
            return Ok(result);
        }

        /// <summary>
        /// CompanyClusterAndComb/Create 新建单位套餐及单位套餐组合信息
        /// CompanyClusterAndComb/Update 更新单位套餐及单位套餐组合信息
        /// </summary>
        /// <param name="type"></param>
        /// <param name="companyClusterDetail">单位套餐及单位套餐组合信息</param>
        /// <returns></returns>
        [HttpPost("CompanyClusterAndComb/{type}")]
        [UnitOfWork]
        public IActionResult CompanyClusterAndComb([FromRoute] string type, [FromBody] CompanyClusterDetail companyClusterDetail)
        {
            string msg = string.Empty;

            switch (type.ToLower())
            {
                case "create":
                    result.Success = _companyService.InsertCompanyClusterAndComb(companyClusterDetail);
                    break;
                case "update":
                    result.Success = _companyService.UpdateCompanyClusterAndComb(companyClusterDetail);
                    break;
                default:
                    return new BadRequestResult();
            }

            result.ReturnMsg = msg;
            return Ok(result);
        }

        /// <summary>
        /// 根据体检次数获取单位套餐
        /// </summary>
        /// <param name="companyCode"></param>
        /// <param name="companyTimes"></param>
        /// <response code="200">执行成功</response>
        /// <returns></returns>
        [HttpPost("GetCompanyCluster")]
        [ProducesResponseType(typeof(CompanyCluster[]), 200)]
        public IActionResult GetCompanyCluster([FromQuery] string companyCode, [FromQuery] int companyTimes)
        {
            result.ReturnData = _companyService.GetCompanyCluster(companyCode, companyTimes);
            return Ok(result);
        }

        /// <summary>
        /// 更新团体套餐组合(此套餐下的人登记组合需要修改)
        /// </summary>
        /// <param name="clusterComb"></param>
        /// <response code="200">执行成功</response>
        /// <returns></returns>
        [HttpPost("UpdateCompanyCluster")]
        public IActionResult UpdateCompanyCluster([FromBody] UpdateClusterComb clusterComb)
        {
            string msg = string.Empty;
            var flag = _companyService.UpdateCompanyCluster(clusterComb, ref msg);
            if (!flag)
            {
                result.Success = false;
                result.ReturnMsg = msg;
                return Ok(result);
            }
            return Ok(result);
        }
        #endregion

        #region 团体导入
        /// <summary>
        /// 团体名单导入
        /// </summary>
        /// <param name="regList"></param>
        /// <response code="200">执行成功</response>
        /// <returns></returns>
        [HttpPost("CompanyImport")]
        [ProducesResponseType(typeof(CompanyImportResult), 200)]
        public IActionResult CompanyImport([FromBody] List<CompanyImport> regList)
        {
            var importResult = new CompanyImportResult();
            var msg = string.Empty;
            if (!_companyService.CompanyImport(regList, ref importResult, ref msg))
            {
                result.Success = false;
                result.ReturnMsg = msg;
            }
            result.ReturnData = importResult;
            return Ok(result);
        }

        /// <summary>
        /// 根据单位获取导入的数据
        /// </summary>
        /// <param name="companyInfo"></param>
        /// <response code="200">执行成功</response>
        /// <returns></returns>
        [HttpPost("GetCompanyRegList")]
        [ProducesResponseType(typeof(List<CompanyRegInfo>), 200)]
        public IActionResult GetCompanyRegList([FromBody] CompanyInfo companyInfo)
        {
            int totalNumber = 0;
            int totalPage = 0;

            result.ReturnData = _companyService.GetCompanyRegList(companyInfo, ref totalNumber, ref totalPage);
            result.TotalNumber = totalNumber;
            result.TotalPage = totalPage;
            return Ok(result);
        }

        /// <summary>
        /// 删除导入的数据
        /// </summary>
        /// <param name="regNoArray">["regno","regno"]</param>
        /// <response code="200">执行成功</response>
        /// <returns></returns>
        [HttpPost("DelCompanyRegList")]
        public IActionResult DelCompanyRegList([FromBody] string[] regNoArray)
        {
            var msg = string.Empty;
            var flag = _companyService.DelCompanyRegList(regNoArray, ref msg);
            if (!flag)
            {
                result.Success = false;
                result.ReturnMsg = msg;
                return Ok(result);
            }
            return Ok(result);
        }

        /// <summary>
        /// 分配套餐
        /// </summary>
        /// <param name="clusterArrays"></param>
        /// <response code="200">执行成功</response>
        /// <returns></returns>
        [HttpPost("SetCompanyCluster")]
        public IActionResult SetCompanyCluster([FromBody] ClusterArray clusterArrays)
        {
            string msg = string.Empty;
            var flag = _companyService.SetCompanyCluster(clusterArrays, ref msg);
            if (!flag)
            {
                result.Success = false;
                result.ReturnMsg = msg;
            }
            return Ok(result);
        }

        /// <summary>
        /// 取消分配套餐
        /// </summary>
        /// <param name="clusterArrays">string[] RegNos:体检号数组</param>
        /// <response code="200">执行成功</response>
        /// <returns></returns>
        [HttpPost("CancelSetCompanyCluster")]
        public IActionResult CancelSetCompanyCluster([FromBody] ClusterArray clusterArrays)
        {
            string msg = string.Empty;
            var flag = _companyService.CancelSetCompanyCluster(clusterArrays, ref msg);
            if (!flag)
            {
                result.Success = false;
                result.ReturnMsg = msg;
            }
            return Ok(result);
        }
        #endregion

        /// <summary>
        /// 计算套餐试管、采血费用
        /// </summary>
        /// <param name="clusterCombs"></param>
        /// <returns></returns>
        [HttpPost("CalculateTustTubeCombs")]
        [ProducesResponseType(typeof(List<CompanyComb>), 200)]
        public IActionResult CalculateTustTubeCombs([FromBody] CompanyComb[] clusterCombs)
        {
            result.Success = true;
            result.ReturnData = _companyService.CalculateTustTubeCombs(clusterCombs);
            return Ok(result);
        }

        /// <summary>
        /// 保存个人收费项目
        /// </summary>
        /// <param name="companyCode"></param>
        /// <returns></returns>
        [HttpPost("SavePersonChargeItem")]
        [AllowAnonymous]
        public IActionResult SavePersonChargeItem([FromQuery] string companyCode)
        {
            _companyService.SavePersonChargeItem(companyCode);
            result.Success = true;
            return Ok(result);
        }

        [HttpPost("SaveExtra")]
        [AllowAnonymous]
        public IActionResult SaveExtra([FromQuery] string companyCode)
        {
            _companyService.SaveExtra(companyCode);
            return Ok(result);
        }

        /// <summary>
        /// 获取单位信息（仅代码和名称）
        /// </summary>
        /// <param name="companyQuery"></param>
        /// <returns></returns>
        [HttpPost("R_CodeCompanySimple")]
        public IActionResult R_CodeCompanySimple([FromBody] CompanyQuery companyQuery)
        {
            result.ReturnData = _companyService.ReadSimpleCompanies(companyQuery);
            result.Success = true;
            return Ok(result);
        }
    }
}
