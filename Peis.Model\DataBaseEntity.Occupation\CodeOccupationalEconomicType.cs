﻿namespace Peis.Model.DataBaseEntity.Occupation
{
    /// <summary>
    /// 经济类型
    /// </summary>
    [SugarTable]
    public class CodeOccupationalEconomicType
    {
        /// <summary>
        /// 经济类型代码
        /// </summary>
        [SugarColumn(Length =10,IsPrimaryKey = true)]
        public string EconomicCode {  get; set; }
        /// <summary>
        /// 经济类型名称
        /// </summary>
        [SugarColumn(Length =50,IsNullable =false)]
        public string EconomicName { get; set; }
    }
}
