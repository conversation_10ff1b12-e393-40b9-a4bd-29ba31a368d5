﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Peis.External.Platform.Service.Service.IService.Permission;

namespace Peis.External.Platform.Service.Controllers
{
    /// <summary>
    /// 深汕授权
    /// </summary>
    [Route("api/<PERSON>han/[controller]")]
    [ApiController]
    public class PermissionController : BaseApiController
    {
        private readonly IExternalSystemPermission _externalPermission;

        public PermissionController(IExternalSystemPermission externalPermission)
        {
            _externalPermission = externalPermission;
        }

        /// <summary>
        /// 前端登录（根据统一门户Token）
        /// </summary>
        /// <param name="token">统一门户Token</param>
        /// <returns>object</returns>
        [HttpPost("Login")]
        [AllowAnonymous]
        public IActionResult Login(string token)
        {
            var errMsg = "";
            var loginInfo = _externalPermission.Login(token, out errMsg);
            if (loginInfo.IsNullOrEmpty())
            {
                result.Success = false;
                result.ReturnMsg = errMsg;
                return Ok(result);
            }

            result.ReturnData = loginInfo;
            return Ok(result);
        }

        /// <summary>
        /// 验证统一门户Token
        /// </summary>
        /// <param name="token">统一门户Token</param>
        /// <returns>object</returns>
        [HttpPost("VerifyPortalToken")]
        [AllowAnonymous]
        public IActionResult VerifyPortalToken(string token)
        {
            var errMsg = "";
            var userInfo = _externalPermission.VerifyToken(token, out errMsg);
            if (userInfo.IsNullOrEmpty())
            {
                result.Success = false;
                result.ReturnMsg = errMsg;
                return Ok(result);
            }

            result.ReturnData = userInfo;
            return Ok(result);
        }
    }
}
