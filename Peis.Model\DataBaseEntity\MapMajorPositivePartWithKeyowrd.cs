﻿namespace Peis.Model.DataBaseEntity;

///<summary>
///重大阳性对应部位与关键字表
///</summary>
[SugarTable(TableName = nameof(MapMajorPositivePartWithKeyowrd), TableDescription = "重大阳性对应部位与关键字表")]
[SugarIndex($"idx_{nameof(MapMajorPositivePartWithKeyowrd)}", nameof(PositiveCode), OrderByType.Asc, nameof(PartCode), OrderByType.Asc, nameof(KeywordCode), OrderByType.Asc)]
public class MapMajorPositivePartWithKeyowrd
{
    /// <summary>
    /// 主键Id
    /// </summary>        
    [SugarColumn(IsNullable = false, IsPrimaryKey = true)]
    public long Id { get; set; }

    /// <summary>
    /// 重大阳性代码
    /// </summary>        
    [SugarColumn(IsNullable = false, Length = 8)]
    public string PositiveCode { get; set; }

    #region 部位信息
    /// <summary>
    /// 重大阳性部位代码
    /// </summary>        
    [SugarColumn(IsNullable = false, Length = 8)]
    public string PartCode { get; set; }

    /// <summary>
    /// 部位名称
    /// </summary>
    [SugarColumn(IsNullable = false, Length = 255)]
    public string PartName { get; set; }

    /// <summary>
    /// 尺寸大小
    /// </summary>
    [SugarColumn(IsNullable = false, DecimalDigits = 2)]
    public decimal? Size { get; set; }

    /// <summary>
    /// 回声情况<br/>
    /// <see cref="DTO.MajorPositive.EchoType"/>
    /// </summary>
    [SugarColumn(IsNullable = true, Length = 255)]
    public string EchoType { get; set; }

    /// <summary>
    /// 年龄-下限
    /// </summary>
    [SugarColumn(IsNullable = true)]
    public int? AgeLowest { get; set; }

    /// <summary>
    /// 年龄-上限
    /// </summary>
    [SugarColumn(IsNullable = true)]
    public int? AgeHighest { get; set; }

    /// <summary>
    /// 部位显示顺序
    /// </summary>        
    public int SortIndex { get; set; }

    #endregion

    #region 关键字信息
    /// <summary>
    /// 重大阳性关键字代码
    /// </summary>        
    [SugarColumn(IsNullable = true, Length = 6)]
    public string KeywordCode { get; set; }

    /// <summary>
    /// 关键字名称
    /// </summary>
    [SugarColumn(IsNullable = true, Length = 255)]
    public string KeywordName { get; set; }

    /// <summary>
    /// 关键字显示顺序
    /// </summary>        
    public int SortIndexOfKeyword { get; set; }
    #endregion
}