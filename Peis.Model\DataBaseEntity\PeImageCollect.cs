﻿using SqlSugar;
using System;

namespace Peis.Model.DataBaseEntity
{
    ///<summary>
    ///图像采集表
    ///</summary>
    [SugarTable("PeImageCollect")]
    public class PeImageCollect
    {
        /// <summary>
        /// 采集id
        /// </summary>        
        [SugarColumn(IsNullable = false, IsPrimaryKey = true, IsIdentity = true)]
        public long Id { get; set; }

        /// <summary>
        /// 体检号
        /// </summary>        
        [SugarColumn(IsNullable = false, Length = 12)]
        public string RegNo { get; set; }

        /// <summary>
        /// 科室代码
        /// </summary>        
        [SugarColumn(IsNullable = false, Length = 6)]
        public string DeptCode { get; set; }

        /// <summary>
        /// 登记组合id
        /// </summary>        
        [SugarColumn(IsNullable = true)]
        public long? RegCombId { get; set; }

        /// <summary>
        /// 组合码
        /// </summary>        
        [SugarColumn(IsNullable = true, Length = 8)]
        public string CombCode { get; set; }

        /// <summary>
        /// 图像记录id
        /// </summary>        
        [SugarColumn(IsNullable = true)]
        public long? RecImageId { get; set; }

        /// <summary>
        /// 图像路径
        /// </summary>        
        [SugarColumn(IsNullable = false, Length = 250)]
        public string ImagePath { get; set; }

        /// <summary>
        /// 采集人代码
        /// </summary>        
        [SugarColumn(IsNullable = false, Length = 10)]
        public string CollectOperator { get; set; }

        /// <summary>
        /// 采集时间
        /// </summary>        
        [SugarColumn(IsNullable = false)]
        public DateTime CollectTime { get; set; }

        /// <summary>
        /// 选中组合操作员代码
        /// </summary>        
        [SugarColumn(IsNullable = true, Length = 10)]
        public string CheckOperator { get; set; }

        /// <summary>
        /// 选中组合操作时间
        /// </summary>        
        [SugarColumn(IsNullable = true)]
        public DateTime? CheckTime { get; set; }
    }
}