﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Peis.Model.DTO.Occupation.Upload;
using Peis.Model.Other.PeEnum.Occupation;
using Peis.Service.IService.Occupation.Upload;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Peis.API.Controllers.Occupation;

[Route("api/[controller]")]
[ApiController]
public class UploadController : BaseApiController
{
    private readonly IUploadService _uploadService;

    public UploadController(IUploadService uploadService)
    {
        _uploadService = uploadService;
    }

    /// <summary>
    /// 上传人员信息
    /// </summary>
    /// <param name="regNo"></param>
    /// <param name="uploadType"></param>
    /// <returns></returns>
    [HttpPost("UploadPersonReport")]
    public async Task<IActionResult> UploadPersonReport(string regNo, UploadType uploadType)
    {
        var err = await _uploadService.UploadPersonReport(regNo, uploadType);
        if (err == null)
        {
            result.Success = true;
        }
        else
        {
            result.Success = false;
            result.ReturnData = err;
        }
        return Ok(result);
    }

    /// <summary>
    /// 获取职业病上传人员列表
    /// </summary>
    /// <param name="query"></param>
    /// <returns></returns>
    [HttpPost("ReadOccupationUploadPersons")]
    [ProducesResponseType(typeof(List<OccupationUploadPerson>), 200)]
    public IActionResult ReadOccupationUploadPersons([FromBody] ReadOccupationUploadPersonsQuery query)
    {
        int totalNumber = 0;
        int totalPage = 0;
        result.ReturnData = _uploadService.ReadOccupationUploadPersons(query, ref totalNumber, ref totalPage);
        result.Success = true;
        result.TotalNumber = totalNumber;
        result.TotalPage = totalPage;
        return Ok(result);
    }

    /// <summary>
    /// 上传单位信息
    /// </summary>
    /// <param name="companyCode"></param>
    /// <returns></returns>
    [HttpPost("UploadCompany")]
    public async Task<IActionResult> UploadCompany(string companyCode)
    {
        var success = await _uploadService.UploadCompany(companyCode);
        result.Success = success;
        return Ok(result);
    }
}

