﻿using Peis.Model.TableFilter;
using SqlSugar;
using System;

namespace Peis.Model.DataBaseEntity
{
    ///<summary>
    ///业务日志
    ///</summary>
    [SugarTable("LogBusiness")]
    public class LogBusiness: IHospCodeFilter
    {
        /// <summary>
        /// 自增ID
        /// </summary>        
        [SugarColumn(IsNullable = false, IsPrimaryKey = true, IsIdentity = true)]
        public int LogID { get; set; }

        /// <summary>
        /// 日志时间
        /// </summary>        
        [SugarColumn(IsNullable = false)]
        public DateTime LogTime { get; set; }

        /// <summary>
        /// 体检号
        /// </summary>        
        [SugarColumn(IsNullable = false, Length = 12)]
        public string RegNo { get; set; }

        /// <summary>
        /// 操作内容
        /// </summary>        
        [SugarColumn(IsNullable = false, ColumnDataType = StaticConfig.CodeFirst_BigString)]
        public string Message { get; set; }

        /// <summary>
        /// 操作员代码
        /// </summary>        
        [SugarColumn(IsNullable = false)]
        public string OperatorCode { get; set; }

        /// <summary>
        /// 操作员名称
        /// </summary>        
        [SugarColumn(IsNullable = false, Length = 50)]
        public string OperatorName { get; set; }

        /// <summary>
        /// 登录IP
        /// </summary>        
        [SugarColumn(IsNullable = false, Length = 20)]
        public string IP { get; set; }

        /// <summary>
        /// 院区编码
        /// </summary>        
        [SugarColumn(IsNullable = false, Length = 1)]
        public string HospCode { get; set; }
    }
}