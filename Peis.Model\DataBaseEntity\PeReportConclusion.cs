namespace Peis.Model.DataBaseEntity
{
    ///<summary>
    ///体检结论表
    ///</summary>
    [SplitTable(SplitType.Year)]
    [SugarTable("PeReportConclusion_{yyyy}", "体检结论表")]
    public class PeReportConclusion
    {
        /// <summary>
        /// 体检号
        /// </summary>        
        [SugarColumn(IsNullable = false, IsPrimaryKey = true, Length = 12)]
        public string RegNo { get; set; }

        /// <summary>
        /// 是否职业病
        /// </summary>
        [SugarColumn(IsNullable = false,IsPrimaryKey = true)]
        public bool IsOccupaiton { get; set; }

        /// <summary>
        /// 是否空小结标签
        /// </summary>        
        [SugarColumn(IsNullable = false)]
        public bool EmptySumTag { get; set; }

        /// <summary>
        /// 综述
        /// </summary>        
        [SugarColumn(IsNullable = false, ColumnDataType = StaticConfig.CodeFirst_BigString)]
        public string Summary { get; set; }

        /// <summary>
        /// 建议
        /// </summary>        
        [SugarColumn(IsNullable = false, ColumnDataType = StaticConfig.CodeFirst_BigString)]
        public string Suggestion { get; set; }

        /// <summary>
        /// 结论
        /// </summary>        
        [SugarColumn(IsNullable = false, ColumnDataType = StaticConfig.CodeFirst_BigString)]
        public string Conclusion { get; set; }

        /// <summary>
        /// 主检医生代码
        /// </summary>        
        [SugarColumn(IsNullable = true, Length = 10)]
        public string CheckDoctorCode { get; set; }

        /// <summary>
        /// 主检医生名称
        /// </summary>        
        [SugarColumn(IsNullable = true, Length = 50)]
        public string CheckDoctorName { get; set; }

        /// <summary>
        /// 主检时间
        /// </summary>        
        [SugarColumn(IsNullable = true)]
        public DateTime? CheckTime { get; set; }

        /// <summary>
        /// 审核医生代码
        /// </summary>        
        [SugarColumn(IsNullable = true, Length = 10)]
        public string AuditDoctorCode { get; set; }

        /// <summary>
        /// 审核医生名称
        /// </summary>        
        [SugarColumn(IsNullable = true, Length = 50)]
        public string AuditDoctorName { get; set; }

        /// <summary>
        /// 审核时间
        /// </summary>        
        [SugarColumn(IsNullable = true)]
        public DateTime? AuditTime { get; set; }

        /// <summary>
        /// 职业病处理意见
        /// </summary>
        [SugarColumn(IsNullable = true, ColumnDataType = StaticConfig.CodeFirst_BigString)]
        public string OccupationalAdvice { get; set; }

        /// <summary>
        /// 体检登记时间（分表依据）
        /// </summary>        
        [SplitField]
        [SugarColumn(IsNullable = false)]
        public DateTime RegisterTime { get; set; }

    }
}