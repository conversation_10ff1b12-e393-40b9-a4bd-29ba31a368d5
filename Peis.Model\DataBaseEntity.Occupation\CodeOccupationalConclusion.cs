﻿namespace Peis.Model.DataBaseEntity.Occupation
{
    [SugarTable]
    public class CodeOccupationalConclusion
    {
        /// <summary>
        /// 结论代码
        /// </summary>
        [SugarColumn(Length =10,IsPrimaryKey = true)]
        public string ConclusionCode { get; set; }

        /// <summary>
        /// 结论内容
        /// </summary>
        [SugarColumn(Length =255,IsNullable =false)]
        public string ConclusionValue { get; set; }

        /// <summary>
        /// 等级
        /// </summary>
        [SugarColumn(IsNullable = false)]
        public int Level { get; set; }
    }
}
