﻿using Peis.Utility.Helper;
using Peis.Utility.SqlSugarExtension;
using SqlSugar;
using System.Reflection;

namespace Peis.ConsoleTest.Test_Yx.SqlSugar
{
    public class InitTables
    {
        public static void Invoke()
        {
            var _db = new SqlSugarClient(new ConnectionConfig()
            {
                ConnectionString      = Appsettings.GetSectionValue("ConnectionString:Entities"),
                DbType                = DbType.SqlServer,
                IsAutoCloseConnection = true,//开启自动释放模式
                InitKeyType           = InitKeyType.Attribute,//从特性读取主键和自增列信息
            });

            _db.CurrentConnectionConfig.ConfigureExternalServices = new ConfigureExternalServices()
            {
                SplitTableService = new yyyySplitTableService()
            };

            _db.DbMaintenance.CreateDatabase();
            foreach (var type in Assembly.Load("Peis.Model").GetTypes())
            {
                if (type.Namespace == "Peis.Model.DataBaseEntity")
                    _db.CodeFirst.InitTables(type);
            }
        }
    }
}
