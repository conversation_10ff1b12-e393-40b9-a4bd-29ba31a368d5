﻿using SqlSugar;

namespace Peis.Model.DataBaseEntity
{
    ///<summary>
    ///档案项目对应
    ///</summary>
    [SugarTable("MapArchiveItem")]
    public class MapArchiveItem
    {
        /// <summary>
        /// 档案项目代码
        /// </summary>        
        [SugarColumn(IsNullable = false, IsPrimaryKey = true, Length = 50)]
        public string ArchiveCode { get; set; }

        /// <summary>
        /// 档案项目名称
        /// </summary>        
        [SugarColumn(IsNullable = false, Length = 255)]
        public string ArchiveName { get; set; }

        /// <summary>
        /// 项目代码
        /// </summary>        
        [SugarColumn(IsNullable = false, Length = 6)]
        public string ItemCode { get; set; }
    }
}