﻿using System;
using Microsoft.Extensions.Caching.Memory;

namespace Peis.Utility.Cache;

public class MemoryCacheAdapter
{
    private readonly IMemoryCache _cache;

    public MemoryCacheAdapter(IMemoryCache cache)
    {
        _cache = cache;
    }

    public T Get<T>(string key)
    {
        return _cache.TryGetValue(key, out T value) ? value : default;
    }

    public void Remove(string key)
    {
        _cache.Remove(key);
    }

    public void Set(ProviderConfig cfg)
    {
        _cache.Set(cfg.Key, cfg.Refresher.Refresh(), TimeSpan.FromSeconds(cfg.Time));
    }

    public object GetOrCreate(ProviderConfig cfg)
    {
        return _cache.GetOrCreate(cfg.Key, entry =>
        {
            entry.AbsoluteExpirationRelativeToNow = TimeSpan.FromSeconds(cfg.Time);
            return cfg.Refresher.Refresh();
        });
    }
}
