﻿using Microsoft.AspNetCore.Mvc;
using Peis.Model.DataBaseEntity.Occupation;
using Peis.Service.IService.Occupation.BasicCode;
using Peis.Utility.Helper;
using Peis.Utility.PeUser;
using System;
using System.Collections.Generic;

namespace Peis.API.Controllers.Occupation
{
    /// <summary>
    /// 代码对应
    /// </summary>
    [Route("api/Occupation/[controller]")]
    [ApiController]
    public class CodeMappingController : BaseApiController
    {
        private readonly IOccupationalCodeMappingService _zybCodeMappingService;
        private readonly IHttpContextUser _httpContextUser;
        public CodeMappingController(IOccupationalCodeMappingService zybCodeMappingService, IHttpContextUser httpContextUser)
        {
            _zybCodeMappingService = zybCodeMappingService;
            _httpContextUser = httpContextUser;
        }
        #region 婚姻代码对应信息 MapOccupationalMarryStatus
        /// <summary>
        /// 获取婚姻代码对应信息
        /// </summary>
        /// <param name="statusCode"></param>
        /// <returns></returns>
        [HttpPost("Query_MapMarryStatus")]
        public IActionResult Query_MapMarryStatus([FromQuery] string statusCode)
        {
            try
            {
                result.ReturnData = _zybCodeMappingService.ReadMapMarryStatus(statusCode);
            }
            catch (Exception e)
            {
                result.Success = false;
                result.ReturnMsg = $"查询数据时发生异常:{e.Message}";
            }

            return Ok(result);
        }

        /// <summary>
        /// /CD_MapMarryStatus/Create 新增婚姻代码对应信息
        /// /CD_MapMarryStatus/Delete 删除婚姻代码对应信息
        /// </summary>
        /// <param name="type"></param>
        /// <param name="mapOccupationalMarryStatus"></param>
        /// <returns></returns>
        [HttpPost("CD_MapMarryStatus/{type}")]
        public IActionResult CD_MapMarryStatus([FromRoute] string type, [FromBody] List<MapOccupationalMarryStatus> mapOccupationalMarryStatus)
        {
            string msg = string.Empty;
            switch (type.ToLower())
            {
                case "create":
                    result.Success = _zybCodeMappingService.CreateMapMarryStatus(mapOccupationalMarryStatus, ref msg);
                    break;
                case "delete":
                    result.Success = _zybCodeMappingService.DeleteMapMarryStatus(mapOccupationalMarryStatus, ref msg);
                    break;
                default:
                    return new BadRequestResult();
            }

            result.ReturnMsg = msg;
            return Ok(result);
        }

        #endregion

        #region 证件类型对应信息 MapOccupationalCardType
        /// <summary>
        /// 获取证件类型对应信息
        /// </summary>
        /// <param name="cardTypeCode"></param>
        /// <returns></returns>
        [HttpPost("Query_MapCardType")]
        public IActionResult Query_MapCardType([FromQuery] string cardTypeCode)
        {
            try
            {
                result.ReturnData = _zybCodeMappingService.ReadMapCardType(cardTypeCode);
            }
            catch (Exception e)
            {
                result.Success = false;
                result.ReturnMsg = $"查询数据时发生异常:{e.Message}";
            }

            return Ok(result);
        }

        /// <summary>
        /// /CD_MapCardType/Create 新增证件类型对应信息
        /// /CD_MapCardType/Delete 删除证件类型对应信息
        /// </summary>
        /// <param name="type"></param>
        /// <param name="mapOccupationalCardType"></param>
        /// <returns></returns>
        [HttpPost("CD_MapCardType/{type}")]
        public IActionResult CD_MapCardType([FromRoute] string type, [FromBody] List<MapOccupationalCardType> mapOccupationalCardType)
        {
            string msg = string.Empty;
            switch (type.ToLower())
            {
                case "create":
                    result.Success = _zybCodeMappingService.CreateMapCardType(mapOccupationalCardType, ref msg);
                    break;
                case "delete":
                    result.Success = _zybCodeMappingService.DeleteMapCardType(mapOccupationalCardType, ref msg);
                    break;
                default:
                    return new BadRequestResult();
            }

            result.ReturnMsg = msg;
            return Ok(result);
        }

        #endregion

        #region 职业字典-体检项目信息 MapOccupationalItemPeItem
        /// <summary>
        /// 获取职业字典-体检项目信息
        /// </summary>
        /// <param name="itemCode"></param>
        /// <returns></returns>
        [HttpPost("Query_MapItemPeItem")]
        public IActionResult Query_MapItemPeItem([FromQuery] string itemCode)
        {
            try
            {
                result.ReturnData = _zybCodeMappingService.ReadMapItemPeItem(itemCode);
            }
            catch (Exception e)
            {
                result.Success = false;
                result.ReturnMsg = $"查询数据时发生异常:{e.Message}";
            }

            return Ok(result);
        }

        /// <summary>
        /// /CD_MapItemPeItem/Create 新增职业字典-体检项目信息
        /// /CD_MapItemPeItem/Delete 删除职业字典-体检项目信息
        /// </summary>
        /// <param name="type"></param>
        /// <param name="mapOccupationalItemPeItem"></param>
        /// <returns></returns>
        [HttpPost("CD_MapItemPeItem/{type}")]
        public IActionResult CD_MapItemPeItem([FromRoute] string type, [FromBody] List<MapOccupationalItemPeItem> mapOccupationalItemPeItem)
        {
            string msg = string.Empty;
            switch (type.ToLower())
            {
                case "create":
                    result.Success = _zybCodeMappingService.CreateMapItemPeItem(mapOccupationalItemPeItem, ref msg);
                    break;
                case "delete":
                    result.Success = _zybCodeMappingService.DeleteMapItemPeItem(mapOccupationalItemPeItem, ref msg);
                    break;
                default:
                    return new BadRequestResult();
            }

            result.ReturnMsg = msg;
            return Ok(result);
        }

        #endregion

        #region 项目-单位对应信息 MapOccupationalItemUnit
        /// <summary>
        /// 获取项目-单位对应信息
        /// </summary>
        /// <param name="itemCode"></param>
        /// <returns></returns>
        [HttpPost("Query_MapItemUnit")]
        public IActionResult Query_MapItemUnit([FromQuery] string itemCode)
        {
            try
            {
                result.ReturnData = _zybCodeMappingService.ReadMapItemUnit(itemCode);
            }
            catch (Exception e)
            {
                result.Success = false;
                result.ReturnMsg = $"查询数据时发生异常:{e.Message}";
            }

            return Ok(result);
        }

        /// <summary>
        /// /CD_MapItemUnit/Create 新增项目-单位对应信息
        /// /CD_MapItemUnit/Delete 删除项目-单位对应信息
        /// </summary>
        /// <param name="type"></param>
        /// <param name="mapOccupationalItemUnit"></param>
        /// <returns></returns>
        [HttpPost("CD_MapItemUnit/{type}")]
        public IActionResult CD_MapItemUnit([FromRoute] string type, [FromBody] List<MapOccupationalItemUnit> mapOccupationalItemUnit)
        {
            string msg = string.Empty;
            switch (type.ToLower())
            {
                case "create":
                    result.Success = _zybCodeMappingService.CreateMapItemUnit(mapOccupationalItemUnit, ref msg);
                    break;
                case "delete":
                    result.Success = _zybCodeMappingService.DeleteMapItemUnit(mapOccupationalItemUnit, ref msg);
                    break;
                default:
                    return new BadRequestResult();
            }

            result.ReturnMsg = msg;
            return Ok(result);
        }

        #endregion

        #region 报告模块-体检项目对应信息 MapOccupationalReportModelItem
        /// <summary>
        /// 获取报告模块-体检项目对应信息
        /// </summary>
        /// <param name="itemCode"></param>
        /// <returns></returns>
        [HttpPost("Query_MapReportModelItem")]
        public IActionResult Query_MapReportModelItem([FromQuery] string itemCode)
        {
            try
            {
                result.ReturnData = _zybCodeMappingService.ReadMapReportModelItem(itemCode);
            }
            catch (Exception e)
            {
                result.Success = false;
                result.ReturnMsg = $"查询数据时发生异常:{e.Message}";
            }

            return Ok(result);
        }

        /// <summary>
        /// /CD_MapReportModelItem/Create 新增报告模块-体检项目对应信息
        /// /CD_MapReportModelItem/Delete 删除报告模块-体检项目对应信息
        /// </summary>
        /// <param name="type"></param>
        /// <param name="mapOccupationalReportModelItem"></param>
        /// <returns></returns>
        [HttpPost("CD_MapReportModelItem/{type}")]
        public IActionResult CD_MapReportModelItem([FromRoute] string type, [FromBody] List<MapOccupationalReportModelItem> mapOccupationalReportModelItem)
        {
            string msg = string.Empty;
            switch (type.ToLower())
            {
                case "create":
                    result.Success = _zybCodeMappingService.CreateMapReportModelItem(mapOccupationalReportModelItem, ref msg);
                    break;
                case "delete":
                    result.Success = _zybCodeMappingService.DeleteMapReportModelItem(mapOccupationalReportModelItem, ref msg);
                    break;
                default:
                    return new BadRequestResult();
            }

            result.ReturnMsg = msg;
            return Ok(result);
        }

        #endregion

        #region 症状询问-体检项目信息 MapOccupationalItemPeItem
        /// <summary>
        /// 获取症状询问-体检项目信息
        /// </summary>
        /// <param name="symptomCode"></param>
        /// <returns></returns>
        [HttpPost("Query_MapSymptomPeItem")]
        public IActionResult Query_MapSymptomPeItem([FromQuery] string symptomCode)
        {
            try
            {
                result.ReturnData = _zybCodeMappingService.ReadMapSymptomPeItem(symptomCode);
            }
            catch (Exception e)
            {
                result.Success = false;
                result.ReturnMsg = $"查询数据时发生异常:{e.Message}";
            }

            return Ok(result);
        }

        /// <summary>
        /// /CD_MapOccupationalSymptomPeItem/Create 新增症状询问-体检项目信息
        /// /CD_MapOccupationalSymptomPeItem/Delete 删除症状询问-体检项目信息
        /// </summary>
        /// <param name="type"></param>
        /// <param name="mapOccupationalSymptomPeItems"></param>
        /// <returns></returns>
        [HttpPost("CD_MapOccupationalSymptomPeItem/{type}")]
        public IActionResult CD_MapOccupationalSymptomPeItem([FromRoute] string type, [FromBody] List<MapOccupationalSymptomPeItem> mapOccupationalSymptomPeItems)
        {
            string msg = string.Empty;
            switch (type.ToLower())
            {
                case "create":
                    result.Success = _zybCodeMappingService.CreateMapSymptomPeItem(mapOccupationalSymptomPeItems, ref msg);
                    break;
                case "delete":
                    result.Success = _zybCodeMappingService.DeleteMapSymptomPeItem(mapOccupationalSymptomPeItems, ref msg);
                    break;
                default:
                    return new BadRequestResult();
            }

            result.ReturnMsg = msg;
            return Ok(result);
        }

        #endregion

        #region 危害因素推荐组合对应关系 MapOccupationalItemPeItem
        /// <summary>
        /// 获取危害因素推荐组合对应关系
        /// </summary>
        /// <param name="hazardousCode"></param>
        /// <param name="statusCode"></param>
        /// <returns></returns>
        [HttpPost("Query_MapHazardousComb")]
        public IActionResult Query_MapHazardousComb([FromQuery] string hazardousCode, [FromQuery] string statusCode)
        {
            try
            {
                result.ReturnData = _zybCodeMappingService.ReadMapHazardousComb(hazardousCode, statusCode);
            }
            catch (Exception e)
            {
                result.Success = false;
                result.ReturnMsg = $"查询数据时发生异常:{e.Message}";
            }

            return Ok(result);
        }

        /// <summary>
        /// /CD_MapOccupationHazardousComb/Create 新增危害因素推荐组合对应关系
        /// /CD_MapOccupationHazardousComb/Delete 删除危害因素推荐组合对应关系
        /// </summary>
        /// <param name="type"></param>
        /// <param name="mapOccupationHazardousCombs"></param>
        /// <returns></returns>
        [HttpPost("CD_MapOccupationHazardousComb/{type}")]
        public IActionResult CD_MapOccupationHazardousComb([FromRoute] string type, [FromBody] List<MapOccupationHazardousComb> mapOccupationHazardousCombs)
        {
            string msg = string.Empty;
            mapOccupationHazardousCombs.BatchUpdate(x => x.HospCode = _httpContextUser.HospCode);
            switch (type.ToLower())
            {
                case "create":
                    result.Success = _zybCodeMappingService.CreateMapHazardousComb(mapOccupationHazardousCombs, ref msg);
                    break;
                case "delete":
                    result.Success = _zybCodeMappingService.DeleteMapHazardousComb(mapOccupationHazardousCombs, ref msg);
                    break;
                default:
                    return new BadRequestResult();
            }

            result.ReturnMsg = msg;
            return Ok(result);
        }

        #endregion
    }
}
