﻿using SqlSugar;

namespace Peis.Model.DataBaseEntity
{
    ///<summary>
    ///科室常用词
    ///</summary>
    [SugarTable("CodeDeptWord")]
    public class CodeDeptWord
    {
        /// <summary>
        /// 科室编码
        /// </summary>        
        [SugarColumn(IsNullable = false, IsPrimaryKey = true, Length = 6)]
        public string DeptCode { get; set; }

        /// <summary>
        /// 常用词
        /// </summary>        
        [SugarColumn(IsNullable = false, IsPrimaryKey = true, Length = 50)]
        public string Words { get; set; }
    }
}