﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Peis.Model.DataBaseEntity;
using Peis.Service.IService;
using Peis.Utility.Helper;
using System.Collections.Generic;

namespace Peis.API.Controllers.HardwareComm
{
    /// <summary>
    /// 硬件通信的更新程序
    /// </summary>
    [Route("HardwareComm")]
    [ApiController]
    [AllowAnonymous]
    public class HardwareCommController : BaseApiController
    {
        private readonly IHardwareCommService _hardwareCommService;

        public HardwareCommController(IHardwareCommService hardwareCommService)
        {
            _hardwareCommService = hardwareCommService;
        }

        #region 上传更新文件
        /// <summary>
        /// 上传更新文件
        /// </summary>
        /// <param name="updatedFiles">更新的文件</param>
        /// <returns></returns>
        [HttpPost("UploadFile")]
        public IActionResult UploadFile(List<UpdatedFile> updatedFiles)
        {
            var msg = string.Empty;

            result.Success = _hardwareCommService.UploadFile(updatedFiles, ref msg);
            return Ok(result);
        }
        #endregion

        #region 获取上传更新的文件信息
        /// <summary>
        /// 获取上传更新的文件信息
        /// </summary>
        /// <returns></returns>
        [HttpPost("GetUploadFileInfo")]
        [ProducesResponseType(typeof(List<FolderAndFileInfo>), 200)]
        public IActionResult GetUploadFileInfo()
        {
            result.ReturnData = _hardwareCommService.GetUpdatedFileInfo();
            return Ok(result);
        }
        #endregion

        #region 是否更新/更新版本号
        /// <summary>
        /// 是否更新
        /// </summary>
        /// <param name="macCode">物理地址00:00...</param>
        /// <returns></returns>
        [HttpPost("IsUpdate")]
        public IActionResult IsUpdate(string macCode)
        {
            result.ReturnData = _hardwareCommService.IsUpdate(macCode);
            return Ok(result);
        }

        /// <summary>
        /// 更新版本号
        /// </summary>
        /// <param name="macCode">物理地址00:00...</param>
        /// <returns></returns>
        [HttpPost("UpdateVersionNo")]
        public IActionResult UpdateVersionNo(string macCode)
        {
            var msg = string.Empty;

            result.Success = _hardwareCommService.UpdateVersionNo(macCode, ref msg);

            return Ok(result);
        }
        #endregion

        #region 下载更新的文件
        /// <summary>
        /// 下载更新的文件
        /// </summary>
        /// <returns></returns>
        [HttpPost("DownloadFile")]
        [ProducesResponseType(typeof(List<UpdatedFile>), 200)]
        public IActionResult DownloadFile()
        {
            result.ReturnData = _hardwareCommService.DownloadFile();
            return Ok(result);
        }
        #endregion

        #region 配置
        /// <summary>
        /// 设置Mac配置
        /// </summary>
        /// <param name="codeMacConfig"></param>
        /// <returns></returns>
        [HttpPost("SetMacConfig")]
        public IActionResult SetMacConfig(CodeMacConfig codeMacConfig)
        {
            result.ReturnData = _hardwareCommService.SetMacConfig(codeMacConfig);
            return Ok(result);
        }

        /// <summary>
        /// 获取Mac配置
        /// </summary>
        /// <param name="macCode"></param>
        /// <returns></returns>
        [HttpPost("GetMacConfig")]
        [ProducesResponseType(typeof(CodeMacConfig), 200)]
        public IActionResult GetMacConfig(string macCode)
        {
            result.ReturnData = _hardwareCommService.GetMacConfig(macCode);
            return Ok(result);
        }
        #endregion
    }
}
