﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>Exe</OutputType>
    <TargetFramework>net6.0</TargetFramework>
    <StartupObject></StartupObject>
    <Platforms>AnyCPU;x86;x64;ARM64</Platforms>
  </PropertyGroup>

  <ItemGroup>
    <Content Include="..\Peis.API\appsettings.json" Link="appsettings.json">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Flee" Version="2.0.0" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\Peis.Model\Peis.Model.csproj" />
    <ProjectReference Include="..\Peis.Utility\Peis.Utility.csproj" />
  </ItemGroup>

  <ProjectExtensions><VisualStudio><UserProperties /></VisualStudio></ProjectExtensions>

</Project>
