﻿namespace Peis.Model.DataBaseEntity.Occupation;

/// <summary>
/// 既往病史
/// </summary>
public class PeRecordAnamneses
{
    /// <summary>
    /// 体检号
    /// </summary>
    [SugarColumn(IsPrimaryKey = true, Length = 12)]
    public string RegNo { get; set; }
    /// <summary>
    /// 排序
    /// </summary>
    [SugarColumn(IsPrimaryKey = true)]
    public int SortIndex { get; set; }
    /// <summary>
    /// 疾病名称
    /// </summary>
    [SugarColumn(IsNullable = false, Length = 100)]
    public string DiseaseName { get; set; }
    /// <summary>
    /// 诊断日期 yyyy-MM-dd
    /// </summary>
    [SugarColumn(IsNullable = true)]
    public DateTime? DiagnosisDate { get; set; }
    /// <summary>
    /// 诊断单位
    /// </summary>
    [SugarColumn(IsNullable = true, Length = 100)]
    public string DiagnosticUnit { get; set; }
    /// <summary>
    /// 治疗经过
    /// </summary>
    [SugarColumn(IsNullable = true, Length = 255)]
    public string TreatmentProcess { get; set; }
    /// <summary>
    /// 转归
    /// </summary>
    [SugarColumn(IsNullable = true, Length = 50)]
    public string Conversion { get; set; }
}
