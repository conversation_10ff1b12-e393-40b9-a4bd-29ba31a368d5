﻿using Peis.Utility.Helper;
using System.Collections.Generic;
using System.Data;
using System.Linq;

namespace Peis.Model.DTO.CompanyStatistics;

/// <summary>
/// 单位年度报告分析
/// </summary>
public class CompanyYearAnalyseReport
{
    /// <summary>
    /// 单位名称
    /// </summary>
    public string CompanyName { get; set; }

    /// <summary>
    /// 医院名称
    /// </summary>
    public string HospName { get; set; }

    /// <summary>
    /// 年度
    /// </summary>
    public int Year { get; set; }

    /// <summary>
    /// 年度中文
    /// </summary>
    public string YearOfChinese => NumberHelper.ToCNMap(Year);

    /// <summary>
    /// 月份
    /// </summary>
    public int Month { get; set; }

    /// <summary>
    /// 月份中文
    /// </summary>
    public string MonthOfChinese => NumberHelper.ToCNMap(Month);

    /// <summary>
    /// 前几项分析，默认5，不足按前面取
    /// </summary>
    public int TopAnalyseNumber => (SummaryTopDiseAnalyseList ?? new()).Count;

    /// <summary>
    /// 前几项分析-中文
    /// </summary>
    public string TopAnalyseNumberOfChinese => NumberHelper.ToCNMap(TopAnalyseNumber);


    #region 基本情况
    /// <summary>
    /// 体检时间-开始
    /// </summary>
    public DateTime PhyExamDateOfStart { get; set; }

    /// <summary>
    /// 体检时间-开始
    /// </summary>
    public string PhyExamDateOfStartStr => PhyExamDateOfStart.ToString("yyyy年MM月dd日");

    /// <summary>
    /// 体检时间-结束
    /// </summary>
    public DateTime PhyExamDateOfEnd { get; set; }

    /// <summary>
    /// 体检时间-结束
    /// </summary>
    public string PhyExamDateOfEndStr => PhyExamDateOfStart.ToString("yyyy年MM月dd日");

    /// <summary>
    /// 应参加体检的员工人数
    /// </summary>
    public int TotalCount { get; set; }

    /// <summary>
    /// 实检员工人数
    /// </summary>
    public int TotalCountOfActual { get; set; }

    /// <summary>
    /// 实检率
    /// </summary>
    public double PercentOfTotalCount => TotalCount > 0 ? Math.Round((double)TotalCountOfActual / TotalCount, 4, MidpointRounding.AwayFromZero) * 100 : default;

    /// <summary>
    /// 男性
    /// </summary>
    public int TotalManCount { get; set; }

    /// <summary>
    /// 男性-占体检人数百分率
    /// </summary>
    public double PercentOfTotalManCount => TotalCountOfActual > 0 ? Math.Round((double)TotalManCount / TotalCountOfActual, 4, MidpointRounding.AwayFromZero) * 100 : default;

    /// <summary>
    /// 女性
    /// </summary>
    public int TotalWomanCount { get; set; }

    /// <summary>
    /// 女性-占体检人数百分率
    /// </summary>
    public double PercentOfTotalWomanCount => TotalCountOfActual > 0 ? Math.Round((double)TotalWomanCount / TotalCountOfActual, 4, MidpointRounding.AwayFromZero) * 100 : default;

    #region 各年龄段统计
    /// <summary>
    /// 各年龄段统计表
    /// </summary>
    public List<CompanySumDiseaseAge> SummaryAgeList { get; set; } = new()
    {
        new CompanySumDiseaseAge
        {
            SortIndex = 1,
            AgeOfStart = 0,
            AgeOfEnd   = 29,
            ItemName   = "<30 岁",
            ItemNameOfChinese   = "30 岁以下",
        },
        new CompanySumDiseaseAge
        {
            SortIndex = 2,
            AgeOfStart = 30,
            AgeOfEnd   = 40,
            ItemName   = "30-40 岁",
            ItemNameOfChinese   = "30-40 岁",
        },
        new CompanySumDiseaseAge
        {
            SortIndex = 3,
            AgeOfStart = 41,
            AgeOfEnd   = 200,
            ItemName   = ">40 岁",
            ItemNameOfChinese   = "大于 40 岁",
        }
    };

    #endregion

    #endregion


    #region 体检结果分析
    /// <summary>
    /// 异常人数
    /// </summary>
    public int TotalCountOfDisease { get; set; }

    /// <summary>
    /// 异常人数-占实检人数百分率
    /// </summary>
    public double PercentOfDisease => TotalCountOfActual > 0 ? Math.Round((double)TotalCountOfDisease / TotalCountOfActual, 4, MidpointRounding.AwayFromZero) * 100 : default;


    #region 体检情况一览表
    /// <summary>
    /// 体检情况一览表
    /// </summary>
    public List<CompanySumDiseaseCls> SummaryDiseList { get; set; } = new();

    #endregion

    #region 前几项分析
    /// <summary>
    /// 前几项分析表
    /// </summary>
    public List<CompanySumDiseaseCls> SummaryTopDiseAnalyseList { get; set; } = new();
    #endregion


    #region 各年龄段统计拆分统计明细
    /// <summary>
    /// 年龄段主内容
    /// </summary>
    public List<CompanySumDiseaseAge> SummaryTopDiseOfAgeList { get; set; } = new();

    /// <summary>
    /// 各年龄段统计明细表
    /// </summary>
    public List<CompanySumDiseaseCls> SummaryTopDiseDetailList { get; set; } = new();
    #endregion

    #endregion


    #region 第三部分 流行病学分析及健康教育对策
    /// <summary>
    /// 前几项项目内容
    /// </summary>
    public string SummaryTopDiseContent => string.Join("、", (SummaryTopDiseAnalyseList ?? new()).Select(x => x.DiseaseClsName));

    /// <summary>
    /// 前几项异常项目科普与健康教育
    /// </summary>
    public List<CompanySumTopDiseHealthEdu> SummaryTopDiseHealthEduList { get; set; } = new();
    #endregion
}

/// <summary>
/// 各年龄段人数及百分比
/// </summary>
public class CompanySumDiseaseAge
{
    /// <summary>
    /// 序号
    /// </summary>
    public int SortIndex { get; set; }

    /// <summary>
    /// 年龄段名称
    /// </summary>
    public string ItemName { get; set; }

    /// <summary>
    /// 年龄段名称
    /// </summary>
    public string ItemNameOfChinese { get; set; }

    /// <summary>
    /// 年龄-开始
    /// </summary>
    public int AgeOfStart { get; set; }

    /// <summary>
    /// 年龄-结束
    /// </summary>
    public int AgeOfEnd { get; set; }

    /// <summary>
    /// 实检员工总人数
    /// </summary>
    public int TotalCountOfActual { get; set; }

    /// <summary>
    /// 该年龄段人数
    /// </summary>
    public int TotalCount { get; set; }

    /// <summary>
    /// 百分比
    /// </summary>
    public double Percent => TotalCountOfActual > 0 ? Math.Round((double)TotalCount / TotalCountOfActual, 4, MidpointRounding.AwayFromZero) * 100 : default;

    /// <summary>
    /// 异常项目内容
    /// </summary>
    public List<CompanySumDiseaseCls> DiseaseDetailList { get; set; } = new();

    /// <summary>
    /// 前几项分析
    /// </summary>
    public int TopAnalyseNumber => (DiseaseDetailList ?? new()).Count;

    /// <summary>
    /// 前几项分析-中文
    /// </summary>
    public string TopAnalyseNumberOfChinese => NumberHelper.ToCNMap(TopAnalyseNumber);
}

/// <summary>
/// 体检情况一览表内容
/// </summary>
public class CompanySumDiseaseCls
{
    /// <summary>
    /// 序号
    /// </summary>
    public int SortIndex { get; set; }

    /// <summary>
    /// 体检异常项目
    /// </summary>
    public string DiseaseClsName { get; set; }

    /// <summary>
    /// 体检人数
    /// </summary>
    public int TotalCount { get; set; }

    /// <summary>
    /// 检查异常人数
    /// </summary>
    public int TotalCountOfDisease { get; set; }

    /// <summary>
    /// 异常率
    /// </summary>
    public double Percent => TotalCount > 0 ? Math.Round((double)TotalCountOfDisease / TotalCount, 4, MidpointRounding.AwayFromZero) * 100 : default;

    #region Ext
    /// <summary>
    /// 体检异常编码
    /// </summary>
    public string DiseaseClsCode { get; set; }

    /// <summary>
    /// 是否参与异常分析报告前几排行
    /// </summary>
    public bool DiseaseClsIsEnableTop { get; set; }
    #endregion
}

/// <summary>
/// 前五项异常项目科普与健康教育
/// </summary>
public class CompanySumTopDiseHealthEdu
{
    /// <summary>
    /// 序号
    /// </summary>
    public int SortIndex { get; set; }

    /// <summary>
    /// 体检异常项目
    /// </summary>
    public string DiseaseClsName { get; set; }

    /// <summary>
    /// 科普
    /// </summary>
    public string PopularScience { get; set; }

    /// <summary>
    /// 健康教育和对策
    /// </summary>
    public string Suggestion { get; set; }
}