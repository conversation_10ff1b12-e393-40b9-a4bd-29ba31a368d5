﻿using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Peis.Model.DataBaseEntity;
using Peis.Model.DataBaseEntity.Occupation;
using Peis.Model.DTO.DataQuery;
using Peis.Model.DTO.Occupation.BasicCode;
using Peis.Model.Other.PeEnum.Occupation;
using Peis.Repository.Repository.TransactionAttribute;
using Peis.Service.IService;
using Peis.Service.IService.Helper;
using Peis.Service.IService.Occupation.BasicCode;
using Peis.Utility.PeUser;
using System;
using System.Collections.Generic;

namespace Peis.API.Controllers.Occupation
{
    /// <summary>
    /// 职业病基础代码
    /// </summary>
    [Route("api/Occupation/[controller]")]
    [ApiController]
    public class BasicCodeController : BaseApiController
    {
        private readonly IOccupationalBasicCodeService _zybBasicCodeService;
        private readonly IBasicCodeService _basicCodeService;
        private readonly IHttpContextUser _httpContextUser;
        private readonly INoGeneration _noGeneration;

        public BasicCodeController(IOccupationalBasicCodeService zybBasicCodeService,
            IBasicCodeService basicCodeService,
            IHttpContextUser httpContextUser,
            INoGeneration noGeneration)
        {
            _zybBasicCodeService = zybBasicCodeService;
            _basicCodeService = basicCodeService;
            _httpContextUser = httpContextUser;
            _noGeneration = noGeneration;
        }

        #region 地区代码信息 CodeOccupationalAddress
        /// <summary>
        /// /CU_CodeOccupationalAddress/Create 新增地区代码信息
        /// /CU_CodeOccupationalAddress/Update 更新地区代码信息
        /// </summary>
        /// <param name="type"></param>
        /// <param name="codeOccupationalAddress"></param>
        /// <returns></returns>
        [HttpPost("CU_CodeOccupationalAddress/{type}")]
        public IActionResult CU_CodeOccupationalAddress([FromRoute] string type, [FromBody] CodeOccupationalAddress codeOccupationalAddress)
        {
            try
            {
                string msg = string.Empty;

                switch (type.ToLower())
                {
                    case "create":
                        result.Success = _zybBasicCodeService.CreateCodeOccupationalAddress(codeOccupationalAddress, ref msg);
                        break;
                    case "update":
                        result.Success = _zybBasicCodeService.UpdateCodeOccupationalAddress(codeOccupationalAddress, ref msg);
                        break;
                    default:
                        return new BadRequestResult();
                }
                result.ReturnMsg = msg;
            }
            catch (Exception e)
            {
                result.Success = false;
                result.ReturnMsg = e.Message;
            }

            return Ok(result);
        }

        /// <summary>
        /// /PageQuery_CodeOccupationalAddress 分页查询地区代码信息
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        [HttpPost("PageQuery_CodeOccupationalAddress")]
        public IActionResult PageQuery_CodeOccupationalAddress([FromBody] AddressPageQuery query)
        {
            try
            {
                int totalNumber = 0;
                int totalPage = 0;

                result.ReturnData = _zybBasicCodeService.ReadCodeOccupationalAddress(query, ref totalNumber, ref totalPage);

                result.TotalNumber = totalNumber;
                result.TotalPage = totalPage;
            }
            catch (Exception e)
            {
                result.Success = false;
                result.ReturnMsg = $"查询数据时发生异常:{e.Message}";
            }

            return Ok(result);
        }

        /// <summary>
        /// /Delete_CodeItem 删除地区代码信息
        /// </summary>
        /// <param name="addressCodes"></param>
        /// <returns></returns>
        [HttpPost("Delete_CodeOccupationalAddress")]
        public IActionResult Delete_CodeOccupationalAddress([FromBody] string[] addressCodes)
        {
            try
            {
                string msg = string.Empty;
                var flag = _zybBasicCodeService.DeleteCodeOccupationalAddress(addressCodes, ref msg);
                if (!flag)
                {
                    result.Success = false;
                    result.ReturnMsg = msg;
                    return Ok(result);
                }
            }
            catch (Exception e)
            {
                result.Success = false;
                result.ReturnMsg = $"删除数据时发生异常:{e.Message}";
            }
            return Ok(result);
        }
        #endregion

        #region 经济类型信息 CodeOccupationalEconomicType
        /// <summary>
        /// /CU_CodeOccupationalEconomicType/Create 新增经济类型信息
        /// /CU_CodeOccupationalEconomicType/Update 更新经济类型信息
        /// </summary>
        /// <param name="type"></param>
        /// <param name="codeOccupationalEconomicType"></param>
        /// <returns></returns>
        [HttpPost("CU_CodeOccupationalEconomicType/{type}")]
        public IActionResult CU_CodeOccupationalEconomicType([FromRoute] string type, [FromBody] CodeOccupationalEconomicType codeOccupationalEconomicType)
        {
            try
            {
                string msg = string.Empty;

                switch (type.ToLower())
                {
                    case "create":
                        result.Success = _zybBasicCodeService.CreateCodeOccupationalEconomicType(codeOccupationalEconomicType, ref msg);
                        break;
                    case "update":
                        result.Success = _zybBasicCodeService.UpdateCodeOccupationalEconomicType(codeOccupationalEconomicType, ref msg);
                        break;
                    default:
                        return new BadRequestResult();
                }
                result.ReturnMsg = msg;
            }
            catch (Exception e)
            {
                result.Success = false;
                result.ReturnMsg = e.Message;
            }

            return Ok(result);
        }

        /// <summary>
        /// /RD_CodeOccupationalEconomicType/Read   获取经济类型信息
        /// /RD_CodeOccupationalEconomicType/Delete 删除经济类型信息
        /// </summary>
        /// <param name="type"></param>
        /// <param name="economicCode"></param>
        /// <returns></returns>
        [HttpPost("RD_CodeOccupationalEconomicType/{type}")]
        public IActionResult RD_CodeOccupationalEconomicType([FromRoute] string type, [FromBody] string[] economicCode)
        {
            try
            {
                string msg = string.Empty;

                switch (type.ToLower())
                {
                    case "read":
                        result.ReturnData = _zybBasicCodeService.ReadCodeOccupationalEconomicType();
                        break;
                    case "delete":
                        result.Success = _zybBasicCodeService.DeleteCodeOccupationalEconomicType(economicCode, ref msg);
                        break;
                    default:
                        return new BadRequestResult();
                }
            }
            catch (Exception e)
            {
                result.Success = false;
                result.ReturnMsg = e.Message;
            }

            return Ok(result);
        }
        #endregion

        #region 危害因素信息 CodeOccupationalHazardous
        /// <summary>
        /// /CU_CodeOccupationalHazardous/Create 新增危害因素信息
        /// /CU_CodeOccupationalHazardous/Update 更新危害因素信息
        /// </summary>
        /// <param name="type"></param>
        /// <param name="codeOccupationalHazardous"></param>
        /// <returns></returns>
        [HttpPost("CU_CodeOccupationalHazardous/{type}")]
        public IActionResult CU_CodeOccupationalHazardous([FromRoute] string type, [FromBody] CodeOccupationalHazardous codeOccupationalHazardous)
        {
            try
            {
                string msg = string.Empty;

                switch (type.ToLower())
                {
                    case "create":
                        result.Success = _zybBasicCodeService.CreateCodeOccupationalHazardous(codeOccupationalHazardous, ref msg);
                        break;
                    case "update":
                        result.Success = _zybBasicCodeService.UpdateCodeOccupationalHazardous(codeOccupationalHazardous, ref msg);
                        break;
                    default:
                        return new BadRequestResult();
                }
                result.ReturnMsg = msg;
            }
            catch (Exception e)
            {
                result.Success = false;
                result.ReturnMsg = e.Message;
            }

            return Ok(result);
        }

        /// <summary>
        /// /PageQuery_CodeOccupationalHazardous 分页查询危害因素信息
        /// </summary>
        /// <param name="query"></param>

        /// <returns></returns>
        [HttpPost("PageQuery_CodeOccupationalHazardous")]
        public IActionResult PageQuery_CodeOccupationalHazardous([FromBody] HazardousPageQuery query)
        {
            try
            {
                int totalNumber = 0;
                int totalPage = 0;

                result.ReturnData = _zybBasicCodeService.ReadCodeOccupationalHazardous(query, ref totalNumber, ref totalPage);

                result.TotalNumber = totalNumber;
                result.TotalPage = totalPage;
            }
            catch (Exception e)
            {
                result.Success = false;
                result.ReturnMsg = $"查询数据时发生异常:{e.Message}";
            }

            return Ok(result);
        }

        /// <summary>
        /// /Delete_CodeItem 删除危害因素信息
        /// </summary>
        /// <param name="hazardousCodes"></param>
        /// <returns></returns>
        [HttpPost("Delete_CodeOccupationalHazardous")]
        public IActionResult Delete_CodeOccupationalHazardous([FromBody] string[] hazardousCodes)
        {
            try
            {
                string msg = string.Empty;
                var flag = _zybBasicCodeService.DeleteCodeOccupationalHazardous(hazardousCodes, ref msg);
                if (!flag)
                {
                    result.Success = false;
                    result.ReturnMsg = msg;
                    return Ok(result);
                }
            }
            catch (Exception e)
            {
                result.Success = false;
                result.ReturnMsg = $"删除数据时发生异常:{e.Message}";
            }
            return Ok(result);
        }

        #endregion

        #region 行业分类信息 CodeOccupationalIndustry
        /// <summary>
        /// /CU_CodeOccupationalIndustry/Create 新增行业分类信息
        /// /CU_CodeOccupationalIndustry/Update 更新行业分类信息
        /// </summary>
        /// <param name="type"></param>
        /// <param name="codeOccupationalIndustry"></param>
        /// <returns></returns>
        [HttpPost("CU_CodeOccupationalIndustry/{type}")]
        public IActionResult CU_CodeOccupationalIndustry([FromRoute] string type, [FromBody] CodeOccupationalIndustry codeOccupationalIndustry)
        {
            try
            {
                string msg = string.Empty;

                switch (type.ToLower())
                {
                    case "create":
                        result.Success = _zybBasicCodeService.CreateCodeOccupationalIndustry(codeOccupationalIndustry, ref msg);
                        break;
                    case "update":
                        result.Success = _zybBasicCodeService.UpdateCodeOccupationalIndustry(codeOccupationalIndustry, ref msg);
                        break;
                    default:
                        return new BadRequestResult();
                }
                result.ReturnMsg = msg;
            }
            catch (Exception e)
            {
                result.Success = false;
                result.ReturnMsg = e.Message;
            }

            return Ok(result);
        }

        /// <summary>
        /// /PageQuery_CodeOccupationalIndustry 分页查询行业分类信息
        /// </summary>
        /// <param name="query"></param>

        /// <returns></returns>
        [HttpPost("PageQuery_CodeOccupationalIndustry")]
        public IActionResult PageQuery_CodeOccupationalIndustry([FromBody] PageQuery query)
        {
            try
            {
                int totalNumber = 0;
                int totalPage = 0;

                result.ReturnData = _zybBasicCodeService.ReadCodeOccupationalIndustry(query, ref totalNumber, ref totalPage);

                result.TotalNumber = totalNumber;
                result.TotalPage = totalPage;
            }
            catch (Exception e)
            {
                result.Success = false;
                result.ReturnMsg = $"查询数据时发生异常:{e.Message}";
            }

            return Ok(result);
        }

        /// <summary>
        /// /Delete_CodeItem 删除行业分类信息
        /// </summary>
        /// <param name="industryCodes"></param>
        /// <returns></returns>
        [HttpPost("Delete_CodeOccupationalIndustry")]
        public IActionResult Delete_CodeOccupationalIndustry([FromBody] string[] industryCodes)
        {
            try
            {
                string msg = string.Empty;
                var flag = _zybBasicCodeService.DeleteCodeOccupationalIndustry(industryCodes, ref msg);
                if (!flag)
                {
                    result.Success = false;
                    result.ReturnMsg = msg;
                    return Ok(result);
                }
            }
            catch (Exception e)
            {
                result.Success = false;
                result.ReturnMsg = $"删除数据时发生异常:{e.Message}";
            }
            return Ok(result);
        }
        #endregion

        #region 职业病证件类型信息 CodeOccupationalCardType
        /// <summary>
        /// /CU_CodeOccupationalCardType/Create 新增职业病证件类型信息
        /// /CU_CodeOccupationalCardType/Update 更新职业病证件类型信息
        /// </summary>
        /// <param name="type"></param>
        /// <param name="codeOccupationalCardType"></param>
        /// <returns></returns>
        [HttpPost("CU_CodeOccupationalCardType/{type}")]
        public IActionResult CU_CodeOccupationalCardType([FromRoute] string type, [FromBody] CodeOccupationalCardType codeOccupationalCardType)
        {
            try
            {
                string msg = string.Empty;

                switch (type.ToLower())
                {
                    case "create":
                        result.Success = _zybBasicCodeService.CreateCodeOccupationalCardType(codeOccupationalCardType, ref msg);
                        break;
                    case "update":
                        result.Success = _zybBasicCodeService.UpdateCodeOccupationalCardType(codeOccupationalCardType, ref msg);
                        break;
                    default:
                        return new BadRequestResult();
                }
                result.ReturnMsg = msg;
            }
            catch (Exception e)
            {
                result.Success = false;
                result.ReturnMsg = e.Message;
            }

            return Ok(result);
        }

        /// <summary>
        /// /RD_CodeOccupationalCardType/Read   获取职业病证件类型信息
        /// /RD_CodeOccupationalCardType/Delete 删除职业病证件类型信息
        /// </summary>
        /// <param name="type"></param>
        /// <param name="cardTypeCode"></param>
        /// <returns></returns>
        [HttpPost("RD_CodeOccupationalCardType/{type}")]
        public IActionResult RD_CodeOccupationalCardType([FromRoute] string type, [FromBody] string[] cardTypeCode)
        {
            try
            {
                string msg = string.Empty;

                switch (type.ToLower())
                {
                    case "read":
                        result.ReturnData = _zybBasicCodeService.ReadCodeOccupationalCardType();
                        break;
                    case "delete":
                        result.Success = _zybBasicCodeService.DeleteCodeOccupationalCardType(cardTypeCode, ref msg);
                        break;
                    default:
                        return new BadRequestResult();
                }
            }
            catch (Exception e)
            {
                result.Success = false;
                result.ReturnMsg = e.Message;
            }

            return Ok(result);
        }
        #endregion

        #region 职业禁忌证信息 CodeOccupationalContraindication
        /// <summary>
        /// /CU_CodeOccupationalContraindication/Create 新增职业禁忌证信息
        /// /CU_CodeOccupationalContraindication/Update 更新职业禁忌证信息
        /// </summary>
        /// <param name="type"></param>
        /// <param name="codeOccupationalContraindication"></param>
        /// <returns></returns>
        [HttpPost("CU_CodeOccupationalContraindication/{type}")]
        public IActionResult CU_CodeOccupationalContraindication([FromRoute] string type, [FromBody] CodeOccupationalContraindication codeOccupationalContraindication)
        {
            try
            {
                string msg = string.Empty;

                switch (type.ToLower())
                {
                    case "create":
                        result.Success = _zybBasicCodeService.CreateCodeOccupationalContraindication(codeOccupationalContraindication, ref msg);
                        break;
                    case "update":
                        result.Success = _zybBasicCodeService.UpdateCodeOccupationalContraindication(codeOccupationalContraindication, ref msg);
                        break;
                    default:
                        return new BadRequestResult();
                }
                result.ReturnMsg = msg;
            }
            catch (Exception e)
            {
                result.Success = false;
                result.ReturnMsg = e.Message;
            }

            return Ok(result);
        }

        /// <summary>
        /// /PageQuery_CodeOccupationalContraindication 分页查询职业禁忌证信息
        /// </summary>
        /// <param name="query"></param>

        /// <returns></returns>
        [HttpPost("PageQuery_CodeOccupationalContraindication")]
        public IActionResult PageQuery_CodeOccupationalContraindication([FromBody] PageQuery query)
        {
            try
            {
                int totalNumber = 0;
                int totalPage = 0;

                result.ReturnData = _zybBasicCodeService.ReadCodeOccupationalContraindication(query, ref totalNumber, ref totalPage);

                result.TotalNumber = totalNumber;
                result.TotalPage = totalPage;
            }
            catch (Exception e)
            {
                result.Success = false;
                result.ReturnMsg = $"查询数据时发生异常:{e.Message}";
            }

            return Ok(result);
        }

        /// <summary>
        /// /Delete_CodeItem 删除职业禁忌证信息
        /// </summary>
        /// <param name="contraindicationCodes"></param>
        /// <returns></returns>
        [HttpPost("Delete_CodeOccupationalContraindication")]
        public IActionResult Delete_CodeOccupationalContraindication([FromBody] string[] contraindicationCodes)
        {
            try
            {
                string msg = string.Empty;
                var flag = _zybBasicCodeService.DeleteCodeOccupationalContraindication(contraindicationCodes, ref msg);
                if (!flag)
                {
                    result.Success = false;
                    result.ReturnMsg = msg;
                    return Ok(result);
                }
            }
            catch (Exception e)
            {
                result.Success = false;
                result.ReturnMsg = $"删除数据时发生异常:{e.Message}";
            }
            return Ok(result);
        }
        #endregion

        #region 疑似职业病信息 CodeOccupationalDisease
        /// <summary>
        /// /CU_CodeOccupationalDisease/Create 新增疑似职业病信息
        /// /CU_CodeOccupationalDisease/Update 更新疑似职业病信息
        /// </summary>
        /// <param name="type"></param>
        /// <param name="codeOccupationalDisease"></param>
        /// <returns></returns>
        [HttpPost("CU_CodeOccupationalDisease/{type}")]
        public IActionResult CU_CodeOccupationalDisease([FromRoute] string type, [FromBody] CodeOccupationalDisease codeOccupationalDisease)
        {
            try
            {
                string msg = string.Empty;

                switch (type.ToLower())
                {
                    case "create":
                        result.Success = _zybBasicCodeService.CreateCodeOccupationalDisease(codeOccupationalDisease, ref msg);
                        break;
                    case "update":
                        result.Success = _zybBasicCodeService.UpdateCodeOccupationalDisease(codeOccupationalDisease, ref msg);
                        break;
                    default:
                        return new BadRequestResult();
                }
                result.ReturnMsg = msg;
            }
            catch (Exception e)
            {
                result.Success = false;
                result.ReturnMsg = e.Message;
            }

            return Ok(result);
        }

        /// <summary>
        /// /PageQuery_CodeOccupationalDisease 分页查询疑似职业病信息
        /// </summary>
        /// <param name="query"></param>

        /// <returns></returns>
        [HttpPost("PageQuery_CodeOccupationalDisease")]
        public IActionResult PageQuery_CodeOccupationalDisease([FromBody] PageQuery query)
        {
            try
            {
                int totalNumber = 0;
                int totalPage = 0;

                result.ReturnData = _zybBasicCodeService.ReadCodeOccupationalDisease(query, ref totalNumber, ref totalPage);

                result.TotalNumber = totalNumber;
                result.TotalPage = totalPage;
            }
            catch (Exception e)
            {
                result.Success = false;
                result.ReturnMsg = $"查询数据时发生异常:{e.Message}";
            }

            return Ok(result);
        }

        /// <summary>
        /// /Delete_CodeItem 删除疑似职业病信息
        /// </summary>
        /// <param name="diseaseCodes"></param>
        /// <returns></returns>
        [HttpPost("Delete_CodeOccupationalDisease")]
        public IActionResult Delete_CodeOccupationalDisease([FromBody] string[] diseaseCodes)
        {
            try
            {
                string msg = string.Empty;
                var flag = _zybBasicCodeService.DeleteCodeOccupationalDisease(diseaseCodes, ref msg);
                if (!flag)
                {
                    result.Success = false;
                    result.ReturnMsg = msg;
                    return Ok(result);
                }
            }
            catch (Exception e)
            {
                result.Success = false;
                result.ReturnMsg = $"删除数据时发生异常:{e.Message}";
            }
            return Ok(result);
        }
        #endregion

        #region 职业病体检项目信息 CodeOccupationalItem
        /// <summary>
        /// /CU_CodeOccupationalItem/Create 新增职业病体检项目信息
        /// /CU_CodeOccupationalItem/Update 更新职业病体检项目信息
        /// </summary>
        /// <param name="type"></param>
        /// <param name="codeOccupationalItem"></param>
        /// <returns></returns>
        [HttpPost("CU_CodeOccupationalItem/{type}")]
        public IActionResult CU_CodeOccupationalItem([FromRoute] string type, [FromBody] CodeOccupationalItem codeOccupationalItem)
        {
            try
            {
                string msg = string.Empty;

                switch (type.ToLower())
                {
                    case "create":
                        result.Success = _zybBasicCodeService.CreateCodeOccupationalItem(codeOccupationalItem, ref msg);
                        break;
                    case "update":
                        result.Success = _zybBasicCodeService.UpdateCodeOccupationalItem(codeOccupationalItem, ref msg);
                        break;
                    default:
                        return new BadRequestResult();
                }
                result.ReturnMsg = msg;
            }
            catch (Exception e)
            {
                result.Success = false;
                result.ReturnMsg = e.Message;
            }

            return Ok(result);
        }

        /// <summary>
        /// /PageQuery_CodeOccupationalItem 分页查询职业病体检项目信息
        /// </summary>
        /// <param name="query"></param>

        /// <returns></returns>
        [HttpPost("PageQuery_CodeOccupationalItem")]
        public IActionResult PageQuery_CodeOccupationalItem([FromBody] PageQuery query)
        {
            try
            {
                int totalNumber = 0;
                int totalPage = 0;

                result.ReturnData = _zybBasicCodeService.ReadCodeOccupationalItem(query, ref totalNumber, ref totalPage);

                result.TotalNumber = totalNumber;
                result.TotalPage = totalPage;
            }
            catch (Exception e)
            {
                result.Success = false;
                result.ReturnMsg = $"查询数据时发生异常:{e.Message}";
            }

            return Ok(result);
        }

        /// <summary>
        /// /Delete_CodeItem 删除职业病体检项目信息
        /// </summary>
        /// <param name="itemCodes"></param>
        /// <returns></returns>
        [HttpPost("Delete_CodeOccupationalItem")]
        public IActionResult Delete_CodeOccupationalItem([FromBody] string[] itemCodes)
        {
            try
            {
                string msg = string.Empty;
                var flag = _zybBasicCodeService.DeleteCodeOccupationalItem(itemCodes, ref msg);
                if (!flag)
                {
                    result.Success = false;
                    result.ReturnMsg = msg;
                    return Ok(result);
                }
            }
            catch (Exception e)
            {
                result.Success = false;
                result.ReturnMsg = $"删除数据时发生异常:{e.Message}";
            }
            return Ok(result);
        }
        #endregion

        #region 检查项目限定信息 CodeOccupationalItemLimit
        /// <summary>
        /// /CU_CodeOccupationalItemLimit/Create 新增检查项目限定信息
        /// /CU_CodeOccupationalItemLimit/Update 更新检查项目限定信息
        /// </summary>
        /// <param name="type"></param>
        /// <param name="codeOccupationalItemLimit"></param>
        /// <returns></returns>
        [HttpPost("CU_CodeOccupationalItemLimit/{type}")]
        public IActionResult CU_CodeOccupationalItemLimit([FromRoute] string type, [FromBody] CodeOccupationalItemLimit codeOccupationalItemLimit)
        {
            try
            {
                string msg = string.Empty;

                switch (type.ToLower())
                {
                    case "create":
                        result.Success = _zybBasicCodeService.CreateCodeOccupationalItemLimit(codeOccupationalItemLimit, ref msg);
                        break;
                    case "update":
                        result.Success = _zybBasicCodeService.UpdateCodeOccupationalItemLimit(codeOccupationalItemLimit, ref msg);
                        break;
                    default:
                        return new BadRequestResult();
                }
                result.ReturnMsg = msg;
            }
            catch (Exception e)
            {
                result.Success = false;
                result.ReturnMsg = e.Message;
            }

            return Ok(result);
        }

        /// <summary>
        /// /RD_CodeOccupationalItemLimit/Read   获取检查项目限定信息
        /// /RD_CodeOccupationalItemLimit/Delete 删除检查项目限定信息
        /// </summary>
        /// <param name="type"></param>
        /// <param name="itemCodes"></param>
        /// <returns></returns>
        [HttpPost("RD_CodeOccupationalItemLimit/{type}")]
        public IActionResult RD_CodeOccupationalItemLimit([FromRoute] string type, [FromBody] string[] itemCodes)
        {
            try
            {
                string msg = string.Empty;

                switch (type.ToLower())
                {
                    case "read":
                        result.ReturnData = _zybBasicCodeService.ReadCodeOccupationalItemLimit();
                        break;
                    case "delete":
                        result.Success = _zybBasicCodeService.DeleteCodeOccupationalItemLimit(itemCodes, ref msg);
                        break;
                    default:
                        return new BadRequestResult();
                }
            }
            catch (Exception e)
            {
                result.Success = false;
                result.ReturnMsg = e.Message;
            }

            return Ok(result);
        }
        #endregion

        #region 计量单位编码信息 CodeOccupationalItemUnit
        /// <summary>
        /// /CU_CodeOccupationalItemUnit/Create 新增计量单位编码信息
        /// /CU_CodeOccupationalItemUnit/Update 更新计量单位编码信息
        /// </summary>
        /// <param name="type"></param>
        /// <param name="codeOccupationalItemUnit"></param>
        /// <returns></returns>
        [HttpPost("CU_CodeOccupationalItemUnit/{type}")]
        public IActionResult CU_CodeOccupationalItemUnit([FromRoute] string type, [FromBody] CodeOccupationalItemUnit codeOccupationalItemUnit)
        {
            try
            {
                string msg = string.Empty;

                switch (type.ToLower())
                {
                    case "create":
                        result.Success = _zybBasicCodeService.CreateCodeOccupationalItemUnit(codeOccupationalItemUnit, ref msg);
                        break;
                    case "update":
                        result.Success = _zybBasicCodeService.UpdateCodeOccupationalItemUnit(codeOccupationalItemUnit, ref msg);
                        break;
                    default:
                        return new BadRequestResult();
                }
                result.ReturnMsg = msg;
            }
            catch (Exception e)
            {
                result.Success = false;
                result.ReturnMsg = e.Message;
            }

            return Ok(result);
        }

        /// <summary>
        /// /PageQuery_CodeOccupationalItemUnit 分页查询计量单位编码信息
        /// </summary>
        /// <param name="query"></param>

        /// <returns></returns>
        [HttpPost("PageQuery_CodeOccupationalItemUnit")]
        public IActionResult PageQuery_CodeOccupationalItemUnit([FromBody] PageQuery query)
        {
            try
            {
                int totalNumber = 0;
                int totalPage = 0;

                result.ReturnData = _zybBasicCodeService.ReadCodeOccupationalItemUnit(query, ref totalNumber, ref totalPage);

                result.TotalNumber = totalNumber;
                result.TotalPage = totalPage;
            }
            catch (Exception e)
            {
                result.Success = false;
                result.ReturnMsg = $"查询数据时发生异常:{e.Message}";
            }

            return Ok(result);
        }

        /// <summary>
        /// /Delete_CodeItem 删除计量单位编码信息
        /// </summary>
        /// <param name="unitCodes"></param>
        /// <returns></returns>
        [HttpPost("Delete_CodeOccupationalItemUnit")]
        public IActionResult Delete_CodeOccupationalItemUnit([FromBody] string[] unitCodes)
        {
            try
            {
                string msg = string.Empty;
                var flag = _zybBasicCodeService.DeleteCodeOccupationalItemUnit(unitCodes, ref msg);
                if (!flag)
                {
                    result.Success = false;
                    result.ReturnMsg = msg;
                    return Ok(result);
                }
            }
            catch (Exception e)
            {
                result.Success = false;
                result.ReturnMsg = $"删除数据时发生异常:{e.Message}";
            }
            return Ok(result);
        }
        #endregion

        #region 婚姻状况信息 CodeOccupationalMarryStatus
        /// <summary>
        /// /CU_CodeOccupationalMarryStatus/Create 新增婚姻状况信息
        /// /CU_CodeOccupationalMarryStatus/Update 更新婚姻状况信息
        /// </summary>
        /// <param name="type"></param>
        /// <param name="codeOccupationalMarryStatus"></param>
        /// <returns></returns>
        [HttpPost("CU_CodeOccupationalMarryStatus/{type}")]
        public IActionResult CU_CodeOccupationalMarryStatus([FromRoute] string type, [FromBody] CodeOccupationalMarryStatus codeOccupationalMarryStatus)
        {
            try
            {
                string msg = string.Empty;

                switch (type.ToLower())
                {
                    case "create":
                        result.Success = _zybBasicCodeService.CreateCodeOccupationalMarryStatus(codeOccupationalMarryStatus, ref msg);
                        break;
                    case "update":
                        result.Success = _zybBasicCodeService.UpdateCodeOccupationalMarryStatus(codeOccupationalMarryStatus, ref msg);
                        break;
                    default:
                        return new BadRequestResult();
                }
                result.ReturnMsg = msg;
            }
            catch (Exception e)
            {
                result.Success = false;
                result.ReturnMsg = e.Message;
            }

            return Ok(result);
        }

        /// <summary>
        /// /RD_CodeOccupationalMarryStatus/Read   获取婚姻状况信息
        /// /RD_CodeOccupationalMarryStatus/Delete 删除婚姻状况信息
        /// </summary>
        /// <param name="type"></param>
        /// <param name="statusCodes"></param>
        /// <returns></returns>
        [HttpPost("RD_CodeOccupationalMarryStatus/{type}")]
        public IActionResult RD_CodeOccupationalMarryStatus([FromRoute] string type, [FromBody] string[] statusCodes)
        {
            try
            {
                string msg = string.Empty;

                switch (type.ToLower())
                {
                    case "read":
                        result.ReturnData = _zybBasicCodeService.ReadCodeOccupationalMarryStatus();
                        break;
                    case "delete":
                        result.Success = _zybBasicCodeService.DeleteCodeOccupationalMarryStatus(statusCodes, ref msg);
                        break;
                    default:
                        return new BadRequestResult();
                }
            }
            catch (Exception e)
            {
                result.Success = false;
                result.ReturnMsg = e.Message;
            }

            return Ok(result);
        }
        #endregion

        #region 岗位状态信息 CodeOccupationalPositionStatus
        /// <summary>
        /// /CU_CodeOccupationalPositionStatus/Create 新增岗位状态信息
        /// /CU_CodeOccupationalPositionStatus/Update 更新岗位状态信息
        /// </summary>
        /// <param name="type"></param>
        /// <param name="codeOccupationalPositionStatus"></param>
        /// <returns></returns>
        [HttpPost("CU_CodeOccupationalPositionStatus/{type}")]
        public IActionResult CU_CodeOccupationalPositionStatus([FromRoute] string type, [FromBody] CodeOccupationalPositionStatus codeOccupationalPositionStatus)
        {
            try
            {
                string msg = string.Empty;

                switch (type.ToLower())
                {
                    case "create":
                        result.Success = _zybBasicCodeService.CreateCodeOccupationalPositionStatus(codeOccupationalPositionStatus, ref msg);
                        break;
                    case "update":
                        result.Success = _zybBasicCodeService.UpdateCodeOccupationalPositionStatus(codeOccupationalPositionStatus, ref msg);
                        break;
                    default:
                        return new BadRequestResult();
                }
                result.ReturnMsg = msg;
            }
            catch (Exception e)
            {
                result.Success = false;
                result.ReturnMsg = e.Message;
            }

            return Ok(result);
        }

        /// <summary>
        /// /RD_CodeOccupationalPositionStatus/Read   获取岗位状态信息
        /// /RD_CodeOccupationalPositionStatus/Delete 删除岗位状态信息
        /// </summary>
        /// <param name="type"></param>
        /// <param name="statusCodes"></param>
        /// <returns></returns>
        [HttpPost("RD_CodeOccupationalPositionStatus/{type}")]
        public IActionResult RD_CodeOccupationalPositionStatus([FromRoute] string type, [FromBody] string[] statusCodes)
        {
            try
            {
                string msg = string.Empty;

                switch (type.ToLower())
                {
                    case "read":
                        result.ReturnData = _zybBasicCodeService.ReadCodeOccupationalPositionStatus();
                        break;
                    case "delete":
                        result.Success = _zybBasicCodeService.DeleteCodeOccupationalPositionStatus(statusCodes, ref msg);
                        break;
                    default:
                        return new BadRequestResult();
                }
            }
            catch (Exception e)
            {
                result.Success = false;
                result.ReturnMsg = e.Message;
            }

            return Ok(result);
        }
        #endregion

        #region 报告模块
        /// <summary>
        /// 获取报告模块
        /// </summary>
        [HttpPost("ReadReportModel")]
        public IActionResult ReadReportModel()
        {
            try
            {
                result.ReturnData = _zybBasicCodeService.ReadReportModel();
            }
            catch (Exception e)
            {
                result.Success = false;
                result.ReturnMsg = e.Message;
            }

            return Ok(result);
        }
        #endregion

        #region 职业病体检结论 CodeOccupationalConclusion
        /// <summary>
        /// /CU_CodeOccupationalConclusion/Create 新增职业病体检结论
        /// /CU_CodeOccupationalConclusion/Update 更新职业病体检结论
        /// </summary>
        /// <param name="type"></param>
        /// <param name="codeOccupationalConclusion"></param>
        /// <returns></returns>
        [HttpPost("CU_CodeOccupationalConclusion/{type}")]
        public IActionResult CU_CodeOccupationalConclusion([FromRoute] string type, [FromBody] CodeOccupationalConclusion codeOccupationalConclusion)
        {
            try
            {
                string msg = string.Empty;

                switch (type.ToLower())
                {
                    case "create":
                        result.Success = _zybBasicCodeService.CreateCodeOccupationalConclusion(codeOccupationalConclusion, ref msg);
                        break;
                    case "update":
                        result.Success = _zybBasicCodeService.UpdateCodeOccupationalConclusion(codeOccupationalConclusion, ref msg);
                        break;
                    default:
                        return new BadRequestResult();
                }
                result.ReturnMsg = msg;
            }
            catch (Exception e)
            {
                result.Success = false;
                result.ReturnMsg = e.Message;
            }

            return Ok(result);
        }

        /// <summary>
        /// /RD_CodeOccupationalConclusion/Read   获取职业病体检结论
        /// /RD_CodeOccupationalConclusion/Delete 删除职业病体检结论
        /// </summary>
        /// <param name="type"></param>
        /// <param name="statusCodes"></param>
        /// <returns></returns>
        [HttpPost("RD_CodeOccupationalConclusion/{type}")]
        public IActionResult RD_CodeOccupationalConclusion([FromRoute] string type, [FromBody] string[] statusCodes)
        {
            try
            {
                string msg = string.Empty;

                switch (type.ToLower())
                {
                    case "read":
                        result.ReturnData = _zybBasicCodeService.ReadCodeOccupationalConclusion();
                        break;
                    case "delete":
                        result.Success = _zybBasicCodeService.DeleteCodeOccupationalConclusion(statusCodes, ref msg);
                        break;
                    default:
                        return new BadRequestResult();
                }
            }
            catch (Exception e)
            {
                result.Success = false;
                result.ReturnMsg = e.Message;
            }

            return Ok(result);
        }
        #endregion

        #region 职业病工种 CodeOccupationalJob
        /// <summary>
        /// /CU_CodeOccupationalJob/Create 新增职业病工种
        /// /CU_CodeOccupationalJob/Update 更新职业病工种
        /// </summary>
        /// <param name="type"></param>
        /// <param name="codeOccupationalJob"></param>
        /// <returns></returns>
        [HttpPost("CU_CodeOccupationalJob/{type}")]
        public IActionResult CU_CodeOccupationalJob([FromRoute] string type, [FromBody] CodeOccupationalJob codeOccupationalJob)
        {
            try
            {
                string msg = string.Empty;

                switch (type.ToLower())
                {
                    case "create":
                        result.Success = _zybBasicCodeService.CreateCodeOccupationalJob(codeOccupationalJob, ref msg);
                        break;
                    case "update":
                        result.Success = _zybBasicCodeService.UpdateCodeOccupationalJob(codeOccupationalJob, ref msg);
                        break;
                    default:
                        return new BadRequestResult();
                }
                result.ReturnMsg = msg;
            }
            catch (Exception e)
            {
                result.Success = false;
                result.ReturnMsg = e.Message;
            }

            return Ok(result);
        }

        /// <summary>
        /// /PageQuery_CodeOccupationalJob 分页查询职业病工种
        /// </summary>
        /// <param name="query"></param>

        /// <returns></returns>
        [HttpPost("PageQuery_CodeOccupationalJob")]
        public IActionResult PageQuery_CodeOccupationalJob([FromBody] PageQuery query)
        {
            try
            {
                int totalNumber = 0;
                int totalPage = 0;

                result.ReturnData = _zybBasicCodeService.ReadCodeOccupationalJob(query, ref totalNumber, ref totalPage);

                result.TotalNumber = totalNumber;
                result.TotalPage = totalPage;
            }
            catch (Exception e)
            {
                result.Success = false;
                result.ReturnMsg = $"查询数据时发生异常:{e.Message}";
            }

            return Ok(result);
        }

        /// <summary>
        /// /Delete_CodeItem 删除职业病工种
        /// </summary>
        /// <param name="itemCodes"></param>
        /// <returns></returns>
        [HttpPost("Delete_CodeOccupationalJob")]
        public IActionResult Delete_CodeOccupationalJob([FromBody] string[] itemCodes)
        {
            try
            {
                string msg = string.Empty;
                var flag = _zybBasicCodeService.DeleteCodeOccupationalJob(itemCodes, ref msg);
                if (!flag)
                {
                    result.Success = false;
                    result.ReturnMsg = msg;
                    return Ok(result);
                }
            }
            catch (Exception e)
            {
                result.Success = false;
                result.ReturnMsg = $"删除数据时发生异常:{e.Message}";
            }
            return Ok(result);
        }
        #endregion

        #region 职业病症状询问 CodeOccupationalSymptom
        /// <summary>
        /// /CU_CodeOccupationalSymptom/Create 新增职业病症状询问
        /// /CU_CodeOccupationalSymptom/Update 更新职业病症状询问
        /// </summary>
        /// <param name="type"></param>
        /// <param name="codeOccupationalSymptom"></param>
        /// <returns></returns>
        [HttpPost("CU_CodeOccupationalSymptom/{type}")]
        public IActionResult CU_CodeOccupationalSymptom([FromRoute] string type, [FromBody] CodeOccupationalSymptom codeOccupationalSymptom)
        {
            try
            {
                string msg = string.Empty;

                switch (type.ToLower())
                {
                    case "create":
                        result.Success = _zybBasicCodeService.CreateCodeOccupationalSymptom(codeOccupationalSymptom, ref msg);
                        break;
                    case "update":
                        result.Success = _zybBasicCodeService.UpdateCodeOccupationalSymptom(codeOccupationalSymptom, ref msg);
                        break;
                    default:
                        return new BadRequestResult();
                }
                result.ReturnMsg = msg;
            }
            catch (Exception e)
            {
                result.Success = false;
                result.ReturnMsg = e.Message;
            }

            return Ok(result);
        }

        /// <summary>
        /// /RD_CodeOccupationalSymptom/Read   获取职业病症状询问
        /// /RD_CodeOccupationalSymptom/Delete 删除职业病症状询问
        /// </summary>
        /// <param name="type"></param>
        /// <param name="statusCodes"></param>
        /// <returns></returns>
        [HttpPost("RD_CodeOccupationalSymptom/{type}")]
        public IActionResult RD_CodeOccupationalSymptom([FromRoute] string type, [FromBody] string[] statusCodes)
        {
            try
            {
                string msg = string.Empty;

                switch (type.ToLower())
                {
                    case "read":
                        result.ReturnData = _zybBasicCodeService.ReadCodeOccupationalSymptom();
                        break;
                    case "delete":
                        result.Success = _zybBasicCodeService.DeleteCodeOccupationalSymptom(statusCodes, ref msg);
                        break;
                    default:
                        return new BadRequestResult();
                }
            }
            catch (Exception e)
            {
                result.Success = false;
                result.ReturnMsg = e.Message;
            }

            return Ok(result);
        }
        #endregion

        #region 职业病套餐 
        /// <summary>
        /// /CU_CodeCluster/Create 新增职业病套餐信息
        /// /CU_CodeCluster/Update 更新职业病套餐信息
        /// </summary>
        /// <param name="type"></param>
        /// <param name="codeCluster"></param>
        /// <returns></returns>
        [HttpPost("CU_CodeCluster/{type}")]
        public IActionResult CU_CodeCluster([FromRoute] string type, [FromBody] CodeCluster codeCluster)
        {
            try
            {
                string msg = string.Empty;
                codeCluster.HospCode = _httpContextUser.HospCode;
                codeCluster.IsOccupation = true;
                switch (type.ToLower())
                {
                    case "create":
                        codeCluster.ClusCode = _noGeneration.NextClusterNo(_httpContextUser.HospCode)[0];
                        result.Success = _basicCodeService.CreateCodeCluster(codeCluster);
                        result.ReturnData = codeCluster.ClusCode;
                        break;
                    case "update":
                        result.Success = _basicCodeService.UpdateCodeCluster(codeCluster);
                        break;
                    default:
                        return new BadRequestResult();
                }
                result.ReturnMsg = msg;
            }
            catch (Exception e)
            {
                result.Success = false;
                result.ReturnMsg = e.Message;
            }

            return Ok(result);
        }

        /// <summary>
        /// /RD_CodeCluster/Read   获取职业病套餐信息
        /// /RD_CodeCluster/Delete 删除职业病套餐信息
        /// </summary>
        /// <param name="type"></param>
        /// <param name="clusCodes"></param>
        /// <returns></returns>
        [HttpPost("RD_CodeCluster/{type}")]
        [UnitOfWork]
        public IActionResult RD_CodeCluster([FromRoute] string type, [FromBody] string[] clusCodes)
        {
            try
            {
                string msg = string.Empty;

                switch (type.ToLower())
                {
                    case "read":
                        result.ReturnData = _basicCodeService.ReadCodeCluster(true);
                        break;
                    case "delete":
                        result.Success = _basicCodeService.DeleteCodeCluster(clusCodes);
                        break;
                    default:
                        return new BadRequestResult();
                }
            }
            catch (Exception e)
            {
                result.Success = false;
                result.ReturnMsg = e.Message;
            }
            return Ok(result);
        }

        /// <summary>
        /// 获取套餐及其明细内容
        /// </summary>
        /// <returns></returns>
        [HttpPost("GetCodeClusterWithDetails")]
        [ProducesResponseType(typeof(List<CodeCluster>), StatusCodes.Status200OK)]
        public IActionResult GetCodeClusterWithDetails()
        {
            result.ReturnData = _basicCodeService.GetCodeClusterWithDetails(true);
            return Ok(result);
        }
        #endregion

        #region 听力校正值 CodeOccupationalAuditoryCorrectedValue
        /// <summary>
        /// /CU_CodeOccupationalAuditoryCorrectedValue/Create 新增听力校正值
        /// /CU_CodeOccupationalAuditoryCorrectedValue/Update 更新听力校正值
        /// </summary>
        /// <param name="type"></param>
        /// <param name="codeOccupationalAuditoryCorrectedValue"></param>
        /// <returns></returns>
        [HttpPost("CU_CodeOccupationalAuditoryCorrectedValue/{type}")]
        public IActionResult CU_CodeOccupationalAuditoryCorrectedValue([FromRoute] string type, [FromBody] CodeOccupationalAuditoryCorrectedValue codeOccupationalAuditoryCorrectedValue)
        {
            try
            {
                string msg = string.Empty;

                switch (type.ToLower())
                {
                    case "create":
                        result.Success = _zybBasicCodeService.CreateCodeOccupationalAuditoryCorrectedValue(codeOccupationalAuditoryCorrectedValue, ref msg);
                        break;
                    case "update":
                        result.Success = _zybBasicCodeService.UpdateCodeOccupationalAuditoryCorrectedValue(codeOccupationalAuditoryCorrectedValue, ref msg);
                        break;
                    default:
                        return new BadRequestResult();
                }
                result.ReturnMsg = msg;
            }
            catch (Exception e)
            {
                result.Success = false;
                result.ReturnMsg = e.Message;
            }

            return Ok(result);
        }

        /// <summary>
        /// /RD_CodeOccupationalAuditoryCorrectedValue/Read   获取听力校正值
        /// /RD_CodeOccupationalAuditoryCorrectedValue/Delete 删除听力校正值
        /// </summary>
        /// <param name="type"></param>
        /// <param name="statusCodes"></param>
        /// <returns></returns>
        [HttpPost("RD_CodeOccupationalAuditoryCorrectedValue/{type}")]
        public IActionResult RD_CodeOccupationalAuditoryCorrectedValue([FromRoute] string type, [FromBody] string[] statusCodes)
        {
            try
            {
                string msg = string.Empty;

                switch (type.ToLower())
                {
                    case "read":
                        result.ReturnData = _zybBasicCodeService.ReadCodeOccupationalAuditoryCorrectedValue();
                        break;
                    case "delete":
                        result.Success = _zybBasicCodeService.DeleteCodeOccupationalAuditoryCorrectedValue(statusCodes, ref msg);
                        break;
                    default:
                        return new BadRequestResult();
                }
            }
            catch (Exception e)
            {
                result.Success = false;
                result.ReturnMsg = e.Message;
            }

            return Ok(result);
        }
        #endregion

        #region 重点监测项目 CodeOccupationalImportantMonitorItem

        /// <summary>
        /// 保存重点监测项目
        /// </summary>
        /// <param name="codeOccupationalImportantMonitorItem"></param>
        /// <returns></returns>
        [HttpPost("SaveCodeOccupationalImportantMonitorItem")]
        public IActionResult SaveCodeOccupationalImportantMonitorItem([FromBody] CodeOccupationalImportantMonitorItem codeOccupationalImportantMonitorItem)
        {
            string msg = string.Empty;
            result.Success = _zybBasicCodeService.SaveCodeOccupationalImportantMonitorItem(codeOccupationalImportantMonitorItem, ref msg);
            result.ReturnMsg = msg;
            return Ok(result);
        }

        /// <summary>
        /// 删除重点监测项目
        /// </summary>
        /// <param name="keys"></param>
        /// <returns></returns>
        [HttpPost("DeleteCodeOccupationalImportantMonitorItem")]
        public IActionResult DeleteCodeOccupationalImportantMonitorItem([FromBody] ImportItemKey[] keys)
        {
            string msg = string.Empty;
            result.Success = _zybBasicCodeService.DeleteCodeOccupationalImportantMonitorItem(keys, ref msg);
            result.ReturnMsg = msg;
            return Ok(result);
        }

        /// <summary>
        /// 获取重点监测项目
        /// </summary>
        /// <returns></returns>
        [HttpPost("ReadCodeOccupationalImportantMonitorItem")]
        [ProducesDefaultResponseType(typeof(List<CodeOccupationalImportantMonitorItem>))]
        public IActionResult ReadCodeOccupationalImportantMonitorItem()
        {
            result.ReturnData = _zybBasicCodeService.ReadCodeOccupationalImportantMonitorItem();
            result.Success = true;
            return Ok(result);
        }
        #endregion
    }
}
