﻿using Peis.Model.DTO.External.DataMaintenances;

namespace Peis.Model.DataBaseEntity.External
{
    [SugarTable(nameof(MapCompanyPersonCombChargeItem), TableDescription = "团体人员体检组合与His收费/药品项目关系中间表")]
    [SugarIndex("index_MapCompanyPersonCombChargeItem_RegNo_IsInCluster", nameof(RegNo), OrderByType.Asc, nameof(IsInCluster), OrderByType.Asc)]
    public class MapCompanyPersonCombChargeItem
    {
        /// <summary>
        /// 体检号
        /// </summary>
        [SugarColumn(IsPrimaryKey = true, Length = 12)]
        public string RegNo { get; set; }
        /// <summary>
        /// 组合代码
        /// </summary>        
        [SugarColumn(IsPrimaryKey = true, Length = 8)]
        public string CombCode { get; set; }
        /// <summary>
        /// His-收费/药品项目代码
        /// </summary>        
        [SugarColumn(IsPrimaryKey = true, Length = 64)]
        public string HisChargeItemCode { get; set; }

        /// <summary>
        /// 类型：0-检查，1-药品
        /// </summary>
        [SugarColumn(IsPrimaryKey = true)]
        public EnumCombHisItemType ItemType { get; set; }

        /// <summary>
        /// 组合名称
        /// </summary>        
        [SugarColumn(IsNullable = false, ColumnDataType = "nvarchar(256)")]
        public string CombName { get; set; }

        /// <summary>
        /// His-收费/药品项目ID
        /// </summary>        
        [SugarColumn(Length = 64)]
        public string HisChargeItemId { get; set; }

        /// <summary>
        /// His-收费/药品项目中文名称
        /// </summary>        
        [SugarColumn(IsNullable = false, ColumnDataType = "nvarchar(256)")]
        public string HisChargeItemCNName { get; set; }

        /// <summary>
        /// His-收费项目价格
        /// </summary>        
        [SugarColumn(Length = 10, DecimalDigits = 2)]
        public decimal HisChargeItemPrice { get; set; }

        /// <summary>
        /// His项目/药品数量
        /// </summary>        
        [SugarColumn(Length = 10, DecimalDigits = 2)]
        public decimal HisChargeItemCount { get; set; }

        /// <summary>
        /// His项目/药品折扣
        /// </summary>        
        [SugarColumn(IsNullable = false, Length = 8, DecimalDigits = 2)]
        public decimal HisChargeItemDiscount { get; set; }
        /// <summary>
        /// 是否套餐内
        /// </summary>
        [SugarColumn(IsNullable = false)]
        public bool IsInCluster { get; set; }
    }
}
