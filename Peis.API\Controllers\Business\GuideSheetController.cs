﻿using Microsoft.AspNetCore.Mvc;
using Peis.Model.DTO.DoctorStation;
using Peis.Model.DTO.GuideSheet;
using Peis.Model.Other.Input.DoctorStation;
using Peis.Model.Other.Input.GuideSheet;
using Peis.Service.IService;
using System.Collections.Generic;

namespace Peis.API.Controllers.Business
{
    /// <summary>
    /// 指引单
    /// </summary>
    [Route("api/[controller]")]
    [ApiController]
    public class GuideSheetController : BaseApiController
    {
        private readonly IGuideSheetService _guideSheetService;
        private readonly IRecordNewService _recordService;

        public GuideSheetController(IGuideSheetService guideSheetService, IRecordNewService recordService)
        {
            _guideSheetService = guideSheetService;
            _recordService = recordService;
        }

        /// <summary>
        /// 回收指引单查询
        /// </summary>
        /// <returns></returns>
        [HttpPost("RecycleGuidanceQuery")]
        [ProducesResponseType(typeof(List<RecycleGuidance>), 200)]
        public IActionResult RecycleGuidanceQuery([FromBody] RecycleGuidanceQuery recycleQuery)
        {
            int totalNumber = 0;
            int totalPage = 0;
            result.ReturnData = _guideSheetService.RecycleGuidanceQuery(recycleQuery, ref totalNumber, ref totalPage);
            result.TotalNumber = totalNumber;
            result.TotalPage = totalPage;
            return Ok(result);
        }

        /// <summary>
        /// /WhetherRecycleGuidance/recycle   回收指引单
        /// /WhetherRecycleGuidance/norecycle 取消回收指引单
        /// </summary>
        /// <param name="type"></param>
        /// <param name="whetherRecycle"></param>
        /// <returns></returns>
        [HttpPost("WhetherRecycleGuidance/{type}")]
        public IActionResult WhetherRecycleGuidance([FromRoute] string type, [FromBody] WhetherRecycle whetherRecycle)
        {
            string msg = string.Empty;

            switch (type.ToLower())
            {
                case "recycle":
                    result.Success = _guideSheetService.RecycleGuidance(whetherRecycle, ref msg);
                    break;
                case "norecycle":
                    result.Success = _guideSheetService.NoRecycleGuidance(whetherRecycle, ref msg);
                    break;
                default:
                    return new BadRequestResult();
            }

            result.ReturnMsg = msg;
            return Ok(result);
        }

        /// <summary>
        /// 获取组合情况
        /// </summary>
        /// <param name="regNo">体检号</param>
        /// <returns></returns>
        [HttpPost("ReadCombInfo")]
        [ProducesResponseType(typeof(CombsInfo), 200)]
        public IActionResult ReadCombInfo([FromQuery] string regNo)
        {
            string msg = string.Empty;
            result.ReturnData = _recordService.ReadCombInfo(regNo, ref msg);
            result.ReturnMsg = msg;
            return Ok(result);
        }

        /// <summary>
        /// 弃检组合
        /// </summary>
        /// <param name="abandonComb"></param>
        /// <returns></returns>
        [HttpPost("AbandonComb")]
        public IActionResult AbandonComb([FromBody] AbandonCombData abandonComb)
        {
            string msg = string.Empty;
            result.Success = _recordService.AbandonComb(abandonComb, ref msg);
            result.ReturnMsg = msg;
            return Ok(result);
        }

        /// <summary>
        /// 取消 弃检组合
        /// </summary>
        /// <param name="regNo">体检号</param>
        /// <param name="regCombId">登记组合Id</param>
        /// <returns></returns>
        [HttpPost("CancleAbandonComb")]
        public IActionResult CancleAbandonComb([FromQuery] string regNo, [FromQuery] long regCombId)
        {
            string msg = string.Empty;
            result.Success = _recordService.CancleAbandonComb(regNo, regCombId, ref msg);
            result.ReturnMsg = msg;
            return Ok(result);
        }

        /// <summary>
        /// 根据化验类型弃检组合
        /// </summary>
        /// <param name="abandonComb"></param>
        /// <returns></returns>
        [HttpPost("RefuseCombsByAssayType")]
        public IActionResult RefuseCombsByAssayType([FromBody] AbandonCombData abandonComb)
        {
            string msg = string.Empty;
            result.Success = _recordService.RefuseCombsByAssayType(abandonComb, ref msg);
            result.ReturnMsg = msg;
            return Ok(result);
        }

    }
}
