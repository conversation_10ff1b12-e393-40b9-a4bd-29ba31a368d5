﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Peis.Model.DTO.ReportDataModel;
using Peis.Service.IService;

namespace Peis.API.Controllers.Business
{
    /// <summary>
    /// 报表数据模型配置维护
    /// </summary>
    [Route("api/[controller]")]
    [ApiController]
    [AllowAnonymous]
    public class ReportDataModelController : BaseApiController
    {
        private readonly IReportDataModelService _reportDataModelService;

        public ReportDataModelController(IReportDataModelService reportDataModelService)
        {
            _reportDataModelService = reportDataModelService;
        }

        /// <summary>
        /// 获取数据模型列表
        /// </summary>
        /// <returns></returns>
        [HttpGet("List")]
        [ProducesResponseType(typeof(ReportDataModel[]), 200)]

        public IActionResult GetReportDataModelList()
        {
            result.ReturnData = _reportDataModelService.GetReportDataModelList();
            return Ok(result);
        }

        /// <summary>
        /// 创建数据模型
        /// </summary>
        /// <param name="create">数据模型</param>
        /// <returns></returns>
        [HttpPost("Create")]
        [ProducesResponseType(typeof(ReportDataModel), 200)]

        public IActionResult CreateReportDataModel([FromBody] ReportDataModelToCreate create)
        {
            _reportDataModelService.CreateReportDataModel(create);
            return Ok(result);
        }

        /// <summary>
        /// 更新数据模型
        /// </summary>
        /// <param name="dataModel">数据模型</param>
        /// <returns></returns>
        [HttpPost("Update")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        public IActionResult UpdateReportDataModel([FromBody] ReportDataModel dataModel)
        {
            _reportDataModelService.UpdateReportDataModel(dataModel);
            return Ok(result);
        }

        /// <summary>
        /// 删除数据模型
        /// </summary>
        /// <param name="dataModel">数据模型</param>
        /// <returns></returns>
        [HttpPost("Delete")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        public IActionResult DeleteReportDataModel([FromBody] ReportDataModel dataModel)
        {
            _reportDataModelService.DeleteReportDataModel(dataModel);
            return Ok(result);
        }

        /// <summary>
        /// 获取数据模型的结果集列表
        /// </summary>
        /// <param name="modelCode">模型代码</param>
        /// <returns></returns>
        [HttpGet("ResultSet/List")]
        [ProducesResponseType(typeof(ReportDataModelResultSet[]), 200)]
        public IActionResult GetResultSetList([FromQuery] string modelCode)
        {
            result.ReturnData = _reportDataModelService.GetResultSetList(modelCode);
            return Ok(result);
        }

        /// <summary>
        /// 创建数据模型的结果集
        /// </summary>
        /// <param name="modelCode">模型代码</param>
        /// <param name="create">结果集</param>
        /// <returns></returns>
        [HttpPost("ResultSet/Create")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        public IActionResult CreateResultSet([FromQuery] string modelCode, [FromBody] ReportDataModelResultSet create)
        {
            _reportDataModelService.CreateResultSet(modelCode, create);
            return Ok(result);
        }

        /// <summary>
        /// 更新数据模型的结果集
        /// </summary>
        /// <param name="modelCode">模型代码</param>
        /// <param name="resultSet">结果集</param>
        /// <returns></returns>
        [HttpPost("ResultSet/Update")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        public IActionResult UpdateResultSet([FromQuery] string modelCode, [FromBody] ReportDataModelResultSet resultSet)
        {
            _reportDataModelService.UpdateResultSet(modelCode, resultSet);
            return Ok(result);
        }

        /// <summary>
        ///删除数据模型的结果集
        /// </summary>
        /// <param name="modelCode">模型代码</param>
        /// <param name="resultSet">结果集</param>
        /// <returns></returns>
        [HttpPost("ResultSet/Delete")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        public IActionResult DeleteResultSet([FromQuery] string modelCode, [FromBody] ReportDataModelResultSet resultSet)
        {
            _reportDataModelService.DeleteResultSet(modelCode, resultSet);
            return Ok(result);
        }
    }
}
