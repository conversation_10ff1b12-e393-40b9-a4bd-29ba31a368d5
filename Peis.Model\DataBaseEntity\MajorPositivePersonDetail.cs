﻿using Peis.Model.Other.PeEnum;
using SqlSugar;

namespace Peis.Model.DataBaseEntity
{
    ///<summary>
    ///重大阳性人员详细表
    ///</summary>
    [SugarTable("MajorPositivePersonDetail")]
    public class MajorPositivePersonDetail
    {
        /// <summary>
        /// 体检号
        /// </summary>        
        [SugarColumn(IsNullable = false, IsPrimaryKey = true, Length = 12)]
        public string RegNo { get; set; }

        /// <summary>
        /// 重大阳性代码
        /// </summary>        
        [SugarColumn(IsNullable = false, IsPrimaryKey = true, Length = 8)]
        public string PositiveCode { get; set; }

        /// <summary>
        /// 重大阳性名称
        /// </summary>        
        [SugarColumn(IsNullable = false, Length = 200)]
        public string PositiveName { get; set; }

        /// <summary>
        /// 重大阳性类型 1:A类 2:B类
        /// </summary>        
        [SugarColumn(IsNullable = false)]
        public PositiveType PositiveType { get; set; }
    }
}