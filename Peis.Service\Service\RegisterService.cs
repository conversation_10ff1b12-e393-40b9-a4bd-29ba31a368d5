﻿using MediatR;
using NUglify.Helpers;
using Peis.Model.Constant;
using Peis.Model.DTO;
using Peis.Model.DTO.BasicCode;
using Peis.Model.DTO.Register;
using Peis.Model.DTO.Sample;
using Peis.Model.Other.Input;
using Peis.Model.Other.PeEnum;
using Peis.Repository.IRepository;
using Peis.Service.IService;
using Peis.Service.IService.ExternalSystem;
using Peis.Service.IService.Helper;
using Peis.Service.Service.MediatR;
using Peis.Utility.PeIM.Core;
using Peis.Utility.PeUser;

namespace Peis.Service.Service;

public class RegisterService : IRegisterService
{
    private readonly IHttpContextUser _httpContextUser;
    private readonly IPeimClients _peimClients;
    private readonly IBasicCodeRepository _basicCodeRepository;
    private readonly IRegisterRepository _registerRepository;
    private readonly IFeeRepository _feeRepository;
    private readonly IDataTranRepository _dataTranRepository;
    private readonly IClusterCombRepository _clusterCombRepository;
    private readonly ICompanyRepository _companyRepository;
    private readonly INoGeneration _noGeneration;
    private readonly IDataRepository<PeRegister> _peRegisterRepository;
    private readonly IDataRepository<DeletePeRegister> _delRegisterRepository;
    private readonly IDataRepository<PeRegisterCluster> _peRegisterClusterRepository;
    private readonly ISplitTableRepository<PeRegisterComb> _peRegisterCombRepository;
    private readonly IDataRepository<WeChatBookQuestion> _weChatBookQuestionRepository;
    private readonly ILogBusinessService _logBusinessService;
    private readonly ISystemParameterService _systemParameterService;
    private readonly IExternalSystemOrderService _externalSystemService;
    private readonly ISampleService _sampleService;
    private readonly ISampleRepository _sampleRepository;
    private readonly ISystemSettingRepository _settingRepository;
    private readonly IExternalSystemCompanySettlementService _externalSystemCompanyService;
    private readonly IMediator _mediator;
    private readonly ICacheRepository _cacheRepository;
    private readonly IDataRepository<MapCompanySettlementPerson> _mapCompanySettlementPersonRepository;

    public RegisterService(
        IHttpContextUser httpContextUser,
        IPeimClients peimClients,
        IBasicCodeRepository basicCodeRepository,
        IRegisterRepository registerRepository,
        IFeeRepository feeRepository,
        IDataTranRepository dataTranRepository,
        IClusterCombRepository clusterCombRepository,
        ICompanyRepository companyRepository,
        INoGeneration noGeneration,
        IDataRepository<PeRegister> peRegisterRepository,
        IDataRepository<DeletePeRegister> delRegisterRepository,
        IDataRepository<PeRegisterCluster> peRegisterClusterRepository,
        ISplitTableRepository<PeRegisterComb> peRegisterCombRepository,
        IDataRepository<WeChatBookQuestion> weChatBookQuestionRepository,
        ILogBusinessService logBusinessService,
        ISystemParameterService systemParameterService,
        IExternalSystemOrderService externalSystemService,
        ISampleService sampleService,
        ISampleRepository sampleRepository,
        ISystemSettingRepository settingRepository,
        IExternalSystemCompanySettlementService externalSystemCompanyService,
        IMediator mediator,
        ICacheRepository cacheRepository,
        IDataRepository<MapCompanySettlementPerson> mapCompanySettlementPersonRepository)
    {
        _httpContextUser = httpContextUser;
        _peimClients = peimClients;
        _basicCodeRepository = basicCodeRepository;
        _registerRepository = registerRepository;
        _feeRepository = feeRepository;
        _dataTranRepository = dataTranRepository;
        _clusterCombRepository = clusterCombRepository;
        _companyRepository = companyRepository;
        _noGeneration = noGeneration;
        _peRegisterRepository = peRegisterRepository;
        _delRegisterRepository = delRegisterRepository;
        _peRegisterClusterRepository = peRegisterClusterRepository;
        _peRegisterCombRepository = peRegisterCombRepository;
        _weChatBookQuestionRepository = weChatBookQuestionRepository;
        _logBusinessService = logBusinessService;
        _systemParameterService = systemParameterService;
        _externalSystemService = externalSystemService;
        _sampleService = sampleService;
        _sampleRepository = sampleRepository;
        _settingRepository = settingRepository;
        _externalSystemCompanyService = externalSystemCompanyService;
        _mediator = mediator;
        _cacheRepository = cacheRepository;
        _mapCompanySettlementPersonRepository = mapCompanySettlementPersonRepository;
    }

    public CandidateCluster[] ReadCandidateCluster()
    {
        var candidateClusters = _clusterCombRepository.ReadEnabledCluster()
            .Select(x => new CandidateCluster
            {
                ClusCode = x.ClusCode,
                ClusName = x.ClusName,
                Price = x.Price,
                PeCls = x.PeCls,
                Sex = x.Sex,
                LowerAgeLimit = x.LowerAgeLimit,
                UpperAgeLimit = x.UpperAgeLimit,
                GuidanceType = x.GuidanceType,
                ReportType = x.ReportType,
                PinYinCode = x.PinYinCode,
                WuBiCode = x.WuBiCode
            }).ToArray();

        var dictClusters = candidateClusters.ToDictionary(x => x.ClusCode);

        #region 设置套餐绑定的组合
        var bindCombByClusGroups = _clusterCombRepository
            .ReadClusterBindComb(false)
            .Where((clusComb, comb) => comb.IsEnabled)
            .OrderBy((clusComb, comb, cls) => cls.SortIndex)
            .OrderBy((clusComb, comb) => comb.SortIndex)
            .Select((clusComb, comb) => new
            {
                ClusCode = clusComb.ClusCode,
                CombCode = comb.CombCode,
                Price = comb.Price,
            })
            .ToArray()
            .GroupBy(clusComb => clusComb.ClusCode);

        foreach (var bindCombs in bindCombByClusGroups)
        {
            if (dictClusters.ContainsKey(bindCombs.Key))
                dictClusters[bindCombs.Key].BindCombs = bindCombs.Select(x => new CandidateClusterBindComb
                {
                    CombCode = x.CombCode,
                    Discount = 1,
                    Price = x.Price,
                    OriginalPrice = x.Price,
                }).ToArray();
        }
        #endregion

        #region 设置套餐互斥的组合
        var mutexCombByClusGroups = _clusterCombRepository.ReadClusterMutexComb()
            .ToArray().GroupBy(x => x.ClusCode, x => x.CombCode);

        foreach (var mutexCombs in mutexCombByClusGroups)
        {
            if (dictClusters.ContainsKey(mutexCombs.Key))
                dictClusters[mutexCombs.Key].MutexCombs = mutexCombs.ToArray();
        }
        #endregion

        return candidateClusters;
    }

    public CandidateCluster[] ReadCompanyCandidateCluster(string companyCode, int companyTimes)
    {
        var candidateClusters = _companyRepository.ReadCompanyCluster(companyCode, companyTimes)
            .Select(x => new CandidateCluster
            {
                ClusCode = x.ClusterCode,
                ClusName = x.ClusterName,
                Price = x.Price,
                PeCls = PeCls.健康体检,
                Sex = x.Sex,
                LowerAgeLimit = x.LowerAgeLimit,
                UpperAgeLimit = x.UpperAgeLimit,
                GuidanceType = x.GuidanceType,
                ReportType = x.ReportType,
                PinYinCode = string.Empty,
                WuBiCode = string.Empty
            }).ToArray();

        #region 设置套餐绑定的组合
        foreach (var cluster in candidateClusters)
        {
            cluster.BindCombs = _companyRepository
                .ReadCompanyClusterBindComb(cluster.ClusCode)
                .Select((clusComb, comb) =>
                new CandidateClusterBindComb
                {
                    CombCode = comb.CombCode,
                    Discount = clusComb.Discount,
                    Price = clusComb.Price,
                    OriginalPrice = clusComb.OriginalPrice,
                }).ToArray();
        }
        ;
        #endregion

        return candidateClusters;
    }

    public CandidateComb[] ReadCandidateComb()
    {
        var barCombs = _clusterCombRepository.ReadBarcodeTypeFeeComb()
            .GroupBy((barType, comb) => barType.FeeCombCode)
            .Select((barType, comb) => barType.FeeCombCode)
            .ToArray();

        var candidateCombs = _clusterCombRepository.ReadEnabledComb()
            .Select(x => new CandidateComb
            {
                CombCode = x.CombCode,
                CombName = x.CombName,
                ShortName = x.ShortName,
                CheckCls = x.CheckCls,
                Sex = x.Sex,
                ClsCode = x.ClsCode,
                ExamDeptCode = x.ExamDeptCode,
                DiscountAllow = x.DiscountAllow,
                Price = x.Price,
                PinYinCode = x.PinYinCode,
                WuBiCode = x.WuBiCode
            })
            .Where(x => SqlFunc.Subqueryable<MapItemComb>().Where(sq => sq.CombCode == x.CombCode).Any())
            .Where(x => !SqlFunc.ContainsArray(barCombs, x.CombCode)) //过滤条码类型材料
            .ToArray();

        var dictCombs = candidateCombs.ToDictionary(x => x.CombCode);

        #region 设置组合绑定的组合
        var bindCombGroups = _clusterCombRepository.ReadCombBindComb()
            .ToArray().GroupBy(x => x.CombCode);

        foreach (var bindCombs in bindCombGroups)
        {
            if (dictCombs.ContainsKey(bindCombs.Key))
                dictCombs[bindCombs.Key].BindCombs = bindCombs.Select(x => x.AppendCombCode).ToArray();
        }
        #endregion

        #region 设置组合互斥的组合
        var combMutexCombs = _clusterCombRepository.ReadCombMutexComb()
            .Select(x => new
            {
                x.MutexCode,
                x.CombCode
            }).ToArray();

        var mutexGroups = combMutexCombs.GroupBy(x => x.MutexCode, x => x.CombCode);

        foreach (var mutexCombs in mutexGroups)
        {
            var combs = mutexCombs.ToArray();
            for (int a = 0; a < combs.Length; a++)
            {
                for (int b = 0; b < combs.Length; b++)
                {
                    if (b == a)
                        continue;

                    if (dictCombs.ContainsKey(combs[a]))
                    {
                        if (!dictCombs[combs[a]].MutexCombs.Contains(combs[b]))
                            dictCombs[combs[a]].MutexCombs.Add(combs[b]);
                    }
                }
            }
        }
        #endregion

        return candidateCombs;
    }

    public CandidateItem[] ReadCandidateCombItems(string combCode)
    {
        return _clusterCombRepository.ReadCombBindItem(combCode)
            .Where((map, comb, item) => item.IsEnabled)
            .OrderBy((map, comb, item) => item.SortIndex)
            .OrderBy((map, comb, item) => item.ItemCode)
            .Select((map, comb, item) => new CandidateItem
            {
                ItemCode = item.ItemCode,
                ItemName = item.ItemName
            })
            .ToArray();
    }

    public object NewRegisterOrder(RegisterOrder order, ref bool success, ref string errMsg)
    {
        var newRegInfo = CreatePeRegister(order.IsCompany, order.Patient, ref errMsg);
        if (newRegInfo == null)
            return null;

        var newRegClusters = CreatePeRegisterCluster(newRegInfo.RegNo, order.Clusters);
        var newRegCombs = CreatePeRegisterComb(newRegInfo, order.Combs);

        _dataTranRepository.ExecTran(() =>
        {
            // 登记信息 插入
            _peRegisterRepository.Insert(newRegInfo);

            // 登记套餐 插入
            if (newRegClusters.Length > 0)
                _peRegisterClusterRepository.Insert(newRegClusters);

            if (newRegCombs.Length > 0)
                // 登记组合 插入
                _peRegisterCombRepository.SplitTableInsert(newRegCombs);

            var clusterCode = order.Clusters.Length > 0 ? order.Clusters[0].ClusCode : null;
            //登记组合生成试管材料
            if (!order.IsCompany)
            {
                InsertTestTubeToRegisterComb(newRegInfo);
                //生成采血费用
                InsertVenousBloodComb(newRegInfo.RegNo, !newRegInfo.IsCompanyCheck);
                //生成所有条码
                _sampleService.SyncSampleBindBarCodeAll(newRegInfo.RegNo);
            }
            else
            {
                InsertTestTubeToRegisterComb(newRegInfo, clusterCode);
                //生成采血费用
                InsertVenousBloodComb(newRegInfo.RegNo, !newRegInfo.IsCompanyCheck, clusterCode);
                //生成所有条码
                _sampleService.SyncSampleBindBarCodeAll(newRegInfo.RegNo, clusterCode);
            }
        });

        _mediator.Publish(new SyncPeRegisterOrderHandle.Data(new string[] { newRegInfo.RegNo }));
        // 同步订单到外部系统
        //if (newRegInfo.IsActive || order.Combs.Length > 0)
        _externalSystemService.SyncOrder(newRegInfo);
        _externalSystemCompanyService.SavePersonExtraChargeCombs(newRegInfo);
        success = true;
        return new
        {
            newRegInfo.RegNo,
            newRegInfo.GuidanceType
        };
    }

    public object AlterRegisterOrder(RegisterOrder order, ref bool success, ref string errMsg)
    {
        //修改前的登记信息
        var peRegisterOriginal = new PeRegister();

        //获取修改后的登记信息
        var altRegister = GetRegisterChanged(order.IsCompany, order.Patient, order.Clusters, ref peRegisterOriginal, ref errMsg);
        if (altRegister == null)
            return null;

        if (peRegisterOriginal.PeStatus == PeStatus.已总检 || peRegisterOriginal.PeStatus == PeStatus.已审核)
        {
            errMsg = "已总检/审核，不能新增组合";
            return null;
        }
        if (_mapCompanySettlementPersonRepository.Any(x => x.RegNo == order.Patient.RegNo))
        {
            errMsg = "已进入结算阶段，不能新增组合";
            return null;
        }
        //获取修改后的登记套餐
        GetRegisterClusterChanged(
            altRegister.RegNo,
            order.Clusters,
            out List<PeRegisterCluster> deleteClusters,
            out PeRegisterCluster[] addClusters
        );

        //获取修改后的登记组合
        GetRegisterCombChanged
        (
            altRegister,
            order.Combs,
            out List<PeRegisterComb> delCombs,
            out List<PeRegisterComb> altCombs,
            out PeRegisterComb[] addCombs
        );

        if (delCombs.Count > 0 && delCombs.Any(x => x.PayStatus == PayStatus.收费 || x.PayStatus == PayStatus.冲销))
        {
            errMsg = "不能删除已支付的组合";
            return null;
        }

        _dataTranRepository.ExecTran(() =>
        {
            //登记信息 更新
            _peRegisterRepository.Update(altRegister);

            //登记套餐 删除、插入
            if (deleteClusters.Count > 0)
            {
                _peRegisterClusterRepository.Delete(deleteClusters);
                _logBusinessService.Write(altRegister.RegNo, "删除了套餐：" + string.Join(",", deleteClusters.Select(x => x.ClusName)));
            }
            if (addClusters.Length > 0)
                _peRegisterClusterRepository.Insert(addClusters);

            //登记组合 删除、更新、插入
            if (delCombs.Count > 0)
            {
                _peRegisterCombRepository.SplitTableDelete(delCombs);
                _logBusinessService.Write(altRegister.RegNo, "删除了组合：" + string.Join(",", delCombs.Select(x => x.CombName)));
            }
            if (altCombs.Count > 0)
                _peRegisterCombRepository.SplitTableUpdate(altCombs);
            if (addCombs.Length > 0)
                _peRegisterCombRepository.SplitTableInsert(addCombs);

            var clusterCode = order.Clusters.Length > 0 ? order.Clusters[0].ClusCode : null;
            if (!order.IsCompany)
            {
                //登记组合生成试管材料
                InsertTestTubeToRegisterComb(altRegister);
                //生成采血费用
                InsertVenousBloodComb(altRegister.RegNo, !altRegister.IsCompanyCheck);
                //生成所有条码
                _sampleService.SyncSampleBindBarCodeAll(altRegister.RegNo);
            }
            else
            {
                //登记组合生成试管材料
                InsertTestTubeToRegisterComb(altRegister, clusterCode);
                //生成采血费用
                InsertVenousBloodComb(altRegister.RegNo, !altRegister.IsCompanyCheck, clusterCode);
                //生成所有条码
                _sampleService.SyncSampleBindBarCodeAll(altRegister.RegNo, clusterCode);
            }
        });

        _mediator.Publish(new SyncPeRegisterOrderHandle.Data(new string[] { altRegister.RegNo }));
        // 同步订单到外部系统
        //if (altRegister.IsActive)
        _externalSystemService.SyncOrder(altRegister);
        _externalSystemCompanyService.SavePersonExtraChargeCombs(altRegister);
        success = true;
        return new
        {
            altRegister.RegNo,
            altRegister.GuidanceType
        };
    }

    public bool AlterRegisterPaitent(bool isCompanyCheck, RegisterPatient registerPatient, ref string errMsg)
    {
        //修改前的登记信息
        var peRegisterOriginal = new PeRegister();

        //获取修改后的登记信息
        var altRegister = GetRegisterChanged(isCompanyCheck, registerPatient, null, ref peRegisterOriginal, ref errMsg);

        if (altRegister == null)
            return false;

        _dataTranRepository.ExecTran(() =>
        {
            _peRegisterRepository.Update(altRegister);
            //登记信息 更新
        });

        #region 写入业务日志
        //需要记录日志的字段(体检登记：姓名、性别、单位、电话、身份证、地址、出生日期、年龄、婚姻状况)
        var properties = new string[]
        {
            nameof(peRegisterOriginal.Name),
            nameof(peRegisterOriginal.Sex),
            nameof(peRegisterOriginal.CompanyCode),
            nameof(peRegisterOriginal.Tel),
            nameof(peRegisterOriginal.CardNo),
            nameof(peRegisterOriginal.Address),
            nameof(peRegisterOriginal.Birthday),
            nameof(peRegisterOriginal.Age),
            nameof(peRegisterOriginal.MarryStatus),
        };

        var message = _logBusinessService.GetMessage(peRegisterOriginal, altRegister, properties);

        if (!string.IsNullOrEmpty(message))
        {
            _logBusinessService.Write(altRegister.RegNo, message);
        }
        #endregion

        return true;
    }

    public object ReadHistoryArchives(string queryType, string queryValue)
    {
        if (string.IsNullOrEmpty(queryValue))
            return Array.Empty<object>();

        bool queryName = false;
        bool queryCardNo = false;
        bool queryTel = false;

        switch (queryType.ToLower())
        {
            case "name":
                queryName = true;
                break;
            case "cardno":
                queryCardNo = true;
                break;
            case "tel":
                queryTel = true;
                break;
            default:
                throw new Exception("参数不正确");
        }

        //查询数据
        var queryable = _registerRepository.ReadRegister(queryValue, false, false, queryName, queryCardNo, queryTel, x => new HistoryArchives
        {
            Index = SqlFunc.RowNumber($"{x.RegisterTime} desc", $"{x.CardNo}, {x.Name}"),
            PatCode = x.PatCode,
            Name = x.Name,
            Sex = x.Sex,
            Age = x.Age,
            AgeUnit = x.AgeUnit,
            Birthday = x.Birthday,
            NativePlace = x.NativePlace,
            MarryStatus = x.MarryStatus,
            CardType = x.CardType,
            CardNo = x.CardNo,
            Tel = x.Tel,
            Address = x.Address,
            JobCode = x.JobCode,
            MedicalHistory = x.MedicalHistory,
            RegisterTime = x.RegisterTime,
            ActiveTime = x.ActiveTime
        });

        return queryable
            .MergeTable()
            .Where(x => x.Index == 1)
            .ToArray();
    }

    /// <summary>
    /// 登记订单多条件查询
    /// </summary>
    /// <param name="query">查询条件</param>
    /// <param name="totalNumber">返回总数</param>
    /// <param name="totalPage">返回页码</param>
    /// <returns>RegisterRecord</returns>
    public RegisterRecord ReadRegistersByMultipleFilter(RegisterMultipleFilters query, ref int totalNumber, ref int totalPage)
    {
        var regList = new RegisterRecord();
        ISugarQueryable<PeRegister> queryable = null;
        if (!string.IsNullOrEmpty(query.RegNo))
        {
            queryable = _registerRepository.ReadRegister().Where(reg => reg.RegNo == query.RegNo);
        }
        else
        {
            var startTime = query.StartTime.Value.Date;
            var endTime = query.EndTime.Value.Date.Add(new TimeSpan(23, 59, 59));
            queryable = _registerRepository.ReadRegister()
                //个检还是团检
                .Where(reg => reg.IsCompanyCheck == query.IsCompanyCheck)
                //档案号
                .WhereIF(!string.IsNullOrEmpty(query.PatCode),
                    reg => reg.PatCode == query.PatCode)
                //姓名
                .WhereIF(!string.IsNullOrEmpty(query.Name),
                    reg => reg.Name.Contains(query.Name))
                //性别，查全部前端默认传-1
                .WhereIF(Enum.IsDefined(typeof(Sex), query.Sex),
                    reg => reg.Sex == query.Sex)
                //证件号
                .WhereIF(!string.IsNullOrEmpty(query.CardNo),
                    reg => reg.CardNo == query.CardNo)
                //登记时间
                .WhereIF(query.QueryType == 1 && query.EndTime != null,
                    reg => SqlFunc.Between(reg.RegisterTime, startTime, endTime))
                //激活时间(报到时间)
                .WhereIF(query.QueryType == 2 && query.EndTime != null,
                    reg => SqlFunc.Between(reg.ActiveTime, startTime, endTime))
                //预约时间
                .WhereIF(query.QueryType == 3 && query.EndTime != null,
                    reg => SqlFunc.Between(reg.BookBeginTime, startTime, endTime))
                //团体查体检状态
                .WhereIF(query.IsCompanyCheck && Enum.IsDefined(typeof(PeStatus), query.PeStatus),
                    reg => reg.PeStatus == query.PeStatus)
                //个人 查现场登记订单
                .WhereIF(!query.IsCompanyCheck && query.BookType == (int)BookType.现场登记, reg => reg.BookType == (int)BookType.现场登记)
                //个人 查预约订单
                .WhereIF(query.BookType != (int)BookType.现场登记 && Enum.IsDefined(typeof(BookType), query.BookType),
                    reg => reg.BookType != (int)BookType.现场登记)
                .WhereIF(query.IsActive.HasValue, reg => reg.IsActive == query.IsActive);

            //单位码
            if (!string.IsNullOrEmpty(query.CompanyCode))
            {
                queryable
                    .Where(reg => reg.CompanyCode == query.CompanyCode)
                    //单位次数 默认值前端传-1
                    .WhereIF(query.CompanyTimes > 0, reg => reg.CompanyTimes == query.CompanyTimes)
                    //单位部门
                    .WhereIF(!string.IsNullOrEmpty(query.CompanyDeptCode), reg => reg.CompanyDeptCode == query.CompanyDeptCode);
            }
            if (!string.IsNullOrEmpty(query.Keyword))
            {
                // 如果query.KeyWord是体检号则优先查
                var regNoFlag = CheckHelper.VerifyRegNo(query.Keyword, out _);
                queryable
                    .WhereIF(regNoFlag, reg => reg.RegNo == query.Keyword)
                    .WhereIF(!regNoFlag, reg => reg.Name.Contains(query.Keyword));
            }
            if (!query.ClusCode.IsNullOrEmpty())
            {
                // 套餐码
                queryable.Where(reg => SqlFunc.Subqueryable<PeRegisterCluster>().Where(z => z.ClusCode == query.ClusCode && z.RegNo == reg.RegNo).Any());
            }
        }

        var countList = queryable.Select(x => new
        {
            x.RegNo,
            x.IsActive,
            x.PeStatus,
            x.ReportPrinted
        }).ToList();
        if (countList.IsNullOrEmpty())
        {
            regList.Record = Array.Empty<RecordPatient>();
            regList.RecordStatus = new();
            return regList; // 没有数据 
        }

        regList.RecordStatus = new RecordStatus
        {
            TotalCount = countList.Count,
            InActiveCount = countList.Count(x => x.IsActive == false),
            UnCheckedCount = countList.Count(x => x.PeStatus == PeStatus.未检查),
            IsCheckingCount = countList.Count(x => x.PeStatus == PeStatus.正在检查),
            CheckedCount = countList.Count(x => x.PeStatus == PeStatus.已检完),
            ApprovedCount = countList.Count(x => x.PeStatus == PeStatus.已审核),
            IssuedReportCount = countList.Count(x => x.ReportPrinted)
        };

        var selectQueryable = queryable.Select(reg => new RecordPatient
        {
            RegNo = reg.RegNo,
            PatCode = reg.PatCode,
            PeStatus = reg.PeStatus,
            Name = reg.Name,
            Sex = reg.Sex,
            Age = reg.Age,
            AgeUnit = reg.AgeUnit,
            IsActive = reg.IsActive,
            IsCompanyCheck = reg.IsCompanyCheck,
            IsVIP = reg.IsVIP,
            PeCls = reg.PeCls,
            GuidanceType = reg.GuidanceType,
            ReportPrinted = reg.ReportPrinted,
            BookType = reg.BookType.ToString(),
            ClusPrice = reg.TotalPrice,
            PayStatus = reg.PayStatus,
            ClusName = SqlFunc.Subqueryable<PeRegisterCluster>().Where(z => z.RegNo == reg.RegNo).SelectStringJoin(z => z.ClusName, "+"),
            CompanyCode = reg.CompanyCode,
            CompanyName = SqlFunc.Subqueryable<CodeCompany>().Where(x => x.CompanyCode == reg.CompanyCode).Select(x => x.CompanyName),
            DeptName = SqlFunc.Subqueryable<CodeCompanyDepartment>().Where(x => x.DeptCode == reg.CompanyDeptCode).Select(x => x.DeptName),
        });

        totalNumber = countList.Count;
        if (query.PageSize > 0)
        {
            regList.Record = selectQueryable.OrderBy(query.OrderByList).ToPageList(query.PageNumber, query.PageSize).ToArray();
            totalPage = (int)Math.Ceiling(totalNumber * 1.0 / query.PageSize);
        }
        else
        {
            regList.Record = selectQueryable.ToArray();
            totalPage = 1;
        }

        return regList;
    }

    /// <summary>
    /// 获取登记资料列表
    /// </summary>
    /// <param name="isCompanyCheck"></param>
    /// <param name="queryValue"></param>
    /// <param name="success"></param>
    /// <param name="msg"></param>
    /// <returns></returns>
    public object ReadRegisterByQueryType(bool isCompanyCheck, string queryValue, out bool success, ref string msg)
    {
        if (string.IsNullOrWhiteSpace(queryValue))
        {
            success = false;
            msg = "查询值不能为空";
            return null;
        }

        queryValue = queryValue.Trim();

        success = true;
        return _registerRepository.ReadRegister(isCompanyCheck, queryValue, true, true, true, true, true,
            x => new RegisterByQueryTypeResult
            {
                PeStatus = x.PeStatus,
                PatCode = x.PatCode,
                RegNo = x.RegNo,
                Name = x.Name,
                Sex = x.Sex,
                Age = x.Age,
                AgeUnit = x.AgeUnit,
                IsActive = x.IsActive,
                IsCompanyCheck = x.IsCompanyCheck,
                IsVIP = x.IsVIP,
                PeCls = x.PeCls,
                CompanyCode = x.CompanyCode,
                CompanyDeptCode = x.CompanyDeptCode,
                RegisterTime = x.RegisterTime
            })
            .OrderByDescending(x => x.RegisterTime)
            .ToArray();
    }

    public RegisterOrder GetRegisterOrder(string regNo, out bool success, ref string msg)
    {
        //资料
        var hisCard = _externalSystemService.GetHisCard(regNo);
        var regInfo = _registerRepository.ReadRegister(regNo)
        .Select(reg => new RegisterPatient
        {
            RegNo = reg.RegNo,
            PatCode = reg.PatCode,
            Name = reg.Name,
            Sex = reg.Sex,
            Age = reg.Age,
            AgeUnit = reg.AgeUnit,
            Birthday = reg.Birthday,
            CardType = reg.CardType,
            CardNo = reg.CardNo,
            Tel = reg.Tel,
            NativePlace = reg.NativePlace,
            Address = reg.Address,
            MarryStatus = reg.MarryStatus,
            PhotoUrl = reg.PhotoUrl,
            RegisterTime = reg.RegisterTime,
            IsActive = reg.IsActive,
            PeCls = reg.PeCls,
            GuidanceType = reg.GuidanceType,
            ReportType = reg.ReportType,
            CompanyCode = reg.CompanyCode,
            CompanyTimes = reg.CompanyTimes,
            CompanyDeptCode = reg.CompanyDeptCode,
            JobCode = reg.JobCode,
            JobHistory = reg.JobHistory,
            MedicalHistory = reg.MedicalHistory,
            FamilyMedicalHistory = reg.FamilyMedicalHistory,
            IsVIP = reg.IsVIP,
            IsLeader = reg.IsLeader,
            IsConstitution = reg.IsConstitution,
            IsRecheck = SqlFunc.HasValue(reg.RecheckNo),
            RecheckNo = reg.RecheckNo,
            OperatorCode = reg.OperatorCode,
            Introducer = reg.Introducer,
            Note = reg.Note,
            ChargeModel = reg.ChargeModel,
            HisCard = hisCard
        }).First();

        if (regInfo == null)
        {
            success = false;
            msg = "该体检号不存在";
            return null;
        }

        if (!string.IsNullOrWhiteSpace(regInfo.PhotoUrl))
            regInfo.PhotoUrl = Appsettings.GetSectionValue("WebserviceHub:FileServiceUrl") + regInfo.PhotoUrl;

        //套餐
        var regClus = _registerRepository.ReadRegisterClusters(regNo)
            .Select(x => new RegisterCluster
            {
                ClusCode = x.ClusCode,
                ClusName = x.ClusName,
                Price = x.Price,
                IsMain = x.IsMain
            }).ToArray();
        //所有组合
        var regCombQuery = _registerRepository.ReadRegisterCombs(regNo)
             .InnerJoin<CodeItemComb>((regComb, comb) => regComb.CombCode == comb.CombCode)
             .OrderBy(regComb => regComb.ClsSortIndex)
             .OrderBy(regComb => regComb.CombSortIndex)
             .Select((regComb, comb) => new RegisterComb
             {
                 Id = regComb.Id,
                 CombCode = regComb.CombCode,
                 CombName = regComb.CombName,
                 OriginalPrice = regComb.OriginalPrice,
                 Price = regComb.Price,
                 Discount = regComb.Discount,
                 IsPayBySelf = regComb.IsPayBySelf,
                 PayStatus = regComb.PayStatus,
                 ApplicantCode = regComb.ApplicantCode,
                 ApplicantName = regComb.ApplicantName,
                 DiscountOperCode = regComb.DiscountOperCode,
                 DiscountOperName = regComb.DiscountOperName,
                 BeFrom = regComb.BeFrom,
                 IsExamComb = regComb.BeFrom == null ? true : false
             }).ToArray();

        success = true;

        return new RegisterOrder()
        {
            Patient = regInfo,
            Clusters = regClus,
            Combs = regCombQuery.Where(x => x.BeFrom.IsNullOrEmpty()).ToArray(),//普通组合
            TestTubes = regCombQuery.Where(x => !x.BeFrom.IsNullOrEmpty()).ToArray(),//试管材料
            FeeList = _feeRepository.GetRegisterFeeList(regNo)
        };
    }

    public RegisterOrder GetRegisterClusterAndComb(string regNo)
    {
        //套餐
        var regClus = _registerRepository.ReadRegisterClusters(regNo)
            .Select(x => new RegisterCluster
            {
                ClusCode = x.ClusCode,
                ClusName = x.ClusName,
                Price = x.Price,
                IsMain = x.IsMain
            }).ToArray();

        //组合
        var regCombQuery = _registerRepository.ReadRegisterCombs(regNo)
            .InnerJoin<CodeItemComb>((regComb, comb) => regComb.CombCode == comb.CombCode)
             .Select((regComb, comb) => new RegisterComb
             {
                 Id = regComb.Id,
                 CombCode = regComb.CombCode,
                 CombName = regComb.CombName,
                 OriginalPrice = regComb.OriginalPrice,
                 Price = regComb.Price,
                 Discount = regComb.Discount,
                 IsPayBySelf = regComb.IsPayBySelf,
                 PayStatus = regComb.PayStatus,
                 ApplicantCode = regComb.ApplicantCode,
                 ApplicantName = regComb.ApplicantName,
                 DiscountOperCode = regComb.DiscountOperCode,
                 DiscountOperName = regComb.DiscountOperName,
                 BeFrom = regComb.BeFrom,
                 IsExamComb = regComb.BeFrom == null ? true : false
             }).ToArray();

        return new RegisterOrder()
        {
            Clusters = regClus,
            Combs = regCombQuery.Where(x => x.BeFrom.IsNullOrEmpty()).ToArray(),
            TestTubes = regCombQuery.Where(x => !x.BeFrom.IsNullOrEmpty()).ToArray(),
            FeeList = _feeRepository.GetRegisterFeeList(regNo)
        };
    }

    /// <summary>
    /// 获取订单信息集合
    /// </summary>
    /// <param name="query">查询条件</param>
    /// <param name="totalNumber">总记录数</param>
    /// <param name="totalPage">总页数</param>
    /// <returns>RegisterOrders</returns>
    public List<RegisterOrder> GetRegisterOrders(RegisterOrderQuery query, ref int totalNumber, ref int totalPage)
    {
        var res = new List<RegisterOrder>();
        // 登记信息
        var regs = _registerRepository.ReadRegister()
            .WhereIF(!query.CardNo.IsNullOrEmpty(), x => x.CardNo == query.CardNo)
            .WhereIF(!query.PeStatuses.IsNullOrEmpty(), x => query.PeStatuses.Contains(x.PeStatus))
            .Select(x => new RegisterPatient
            {
                RegNo = x.RegNo.SelectAll()
            })
            .OrderBy(query.OrderByList)
            .ToMyPageList(query.PageNumber, query.PageSize, ref totalNumber, ref totalPage);

        foreach (var item in regs)
        {
            var order = new RegisterOrder();
            order.Patient = item;
            order.Patient.OperatorName = _settingRepository.GetUser(order.Patient.OperatorCode).First()?.Name ?? "";
            order.Clusters = _registerRepository.ReadRegisterClusters(item.RegNo)
                            .Select(x => new RegisterCluster
                            {
                                ClusCode = x.ClusCode.SelectAll()
                            }).ToArray();
            res.Add(order);
        }

        return res;
    }

    /// <summary>
    /// 获取体检人、组合信息
    /// </summary>
    /// <param name="regNo">体检号</param>
    /// <returns>PeRegister、PeRegisterCombs</returns>
    public Tuple<PeRegister, PeRegisterComb[]> GetRegisterWithComb(string regNo)
    {
        // 体检人信息
        var register = _registerRepository.ReadRegister(regNo).First();
        //组合
        var regCombs = _registerRepository.ReadRegisterCombs(regNo).ToArray();

        return new Tuple<PeRegister, PeRegisterComb[]>(register, regCombs);
    }

    public string GetPatCodeByCardNo(string cardNo)
    {
        if (string.IsNullOrEmpty(cardNo))
            return _noGeneration.NextPatCode()[0];

        var patCode = _registerRepository.ReadRegister()
                .Where(x => x.CardNo == cardNo)
                .OrderBy(x => x.RegisterTime, OrderByType.Desc)
                .Select(x => x.PatCode)
                .First();

        return patCode;
    }

    #region 材料费
    public decimal ReadTestTubeMaterialFees(string[] combCodes)
    {
        //获取组合的试管绑定的材料组合
        var barcodeFeeCombs = _clusterCombRepository.ReadCombBarcodeType(combCodes)
            .Select((comb, barType) => barType.FeeCombCode)
            .ToArray();

        if (barcodeFeeCombs.Length == 0)
            return 0;

        return _clusterCombRepository.ReadComb(barcodeFeeCombs)
            .Sum(x => x.Price);
    }

    private void InsertTestTubeToRegisterComb(PeRegister register, string clusterCode = null)
    {
        PeRegisterComb[] peRegisterCombs = _registerRepository.ReadRegisterCombs(register.RegNo).ToArray();
        PeRegisterComb[] regCombs;
        //获取个人未收费/团体未采集的登记组合
        if (register.IsCompanyCheck)
        {
            var gather = _sampleRepository.ReadSample().Where(x => x.RegNo == register.RegNo && x.GatherTime != null).Select(x => x.RegCombId).ToArray();
            regCombs = peRegisterCombs.Where(x => !gather.Any(t => t == x.Id)).ToArray();
            regCombs = regCombs.Except(regCombs.Where(x => x.BeFrom != null && x.BeFrom.All(item => gather.Select(t => t.ToString()).ToArray().Contains(item)))).ToArray();
        }
        else
        {
            regCombs = peRegisterCombs.Where(x => x.PayStatus == PayStatus.未收费).ToArray();
        }
        if (regCombs.Length == 0)
            return;
        var combDict = _cacheRepository.DictComb();
        var testTubeBefrom = _sampleService.CalculateTestTubeBefrom(regCombs.Select(x => x.CombCode).ToArray(), clusterCode);

        List<PeRegisterComb> newRegCombs = new();
        List<PeRegisterComb> updateCombs = new();
        List<PeRegisterComb> deleteCombs = new();

        var allTestTube = _clusterCombRepository.ReadBarcodeTypeFeeComb().Select((barType, comb) => barType.FeeCombCode).Distinct().ToArray();
        var oldRegTestTube = regCombs.Where(x => allTestTube.Contains(x.CombCode)).ToList();

        #region 计算试管数量及从属关系
        foreach (var tube in testTubeBefrom.Where(x => x.TubeCombCode != _systemParameterService.VenousBloodComb))
        {
            var befrom = regCombs.Where(x => SqlFunc.ContainsArray(tube.BeFrom, x.CombCode)).Select(x => x.Id.ToString()).ToArray();
            if (befrom.Length == 0)
                continue;
            var tubecomb = oldRegTestTube.Where(x => x.CombCode == tube.TubeCombCode
                        && (x.BeFrom.All(item => befrom.Contains(item) || befrom.All(item => x.BeFrom.Contains(item))))).SingleOrDefault();
            if (tubecomb == null)
            {
                if (tube.TubeCombCode.IsNullOrEmpty())
                    continue;
                var combInfo = combDict[tube.TubeCombCode].ItemComb;
                if (combInfo == null) //试管组合不存在
                    continue;
                //新试管组合
                var newRegComb = new PeRegisterComb
                {
                    Id = _noGeneration.NextSnowflakeId(),
                    RegNo = register.RegNo,
                    CombCode = combInfo.CombCode,
                    CombName = combInfo.CombName,
                    ExamDeptCode = combInfo.ExamDeptCode,
                    CheckCls = combInfo.CheckCls,
                    ClsCode = combInfo.ClsCode,
                    ClsName = string.Empty,
                    OriginalPrice = combInfo.Price,
                    Price = tube.Price,
                    ReportShow = combInfo.ReportShow,
                    Discount = tube.Discount,
                    IsPayBySelf = regCombs.Any(g => g.IsPayBySelf) ? true : false, //
                    PayStatus = PayStatus.未收费,
                    BeFrom = befrom,
                    Surcharges = 0,
                    ApplicantCode = string.Empty,
                    ApplicantName = string.Empty,
                    CreateTime = DateTime.Now,
                    RegisterTime = regCombs[0].RegisterTime,
                    CombSortIndex = combInfo.SortIndex,
                    ClsSortIndex = combInfo.SortIndex,
                };
                newRegCombs.Add(newRegComb);
            }
            else
            {
                tubecomb.BeFrom = befrom.ToArray();
                updateCombs.Add(tubecomb);
            }
        }
        #endregion

        if (oldRegTestTube.Any(x => !x.BeFrom.All(item => regCombs.Select(x => x.Id.ToString()).ToArray().Contains(item))))
        {
            deleteCombs.AddRange(oldRegTestTube.Where(x => !x.BeFrom.All(item => regCombs.Select(x => x.Id.ToString()).ToArray().Contains(item))).ToArray());
            deleteCombs = deleteCombs.Except(updateCombs).ToList();
        }

        if (deleteCombs.Count > 0)
            _peRegisterCombRepository.SplitTableDelete(deleteCombs);
        if (updateCombs.Count > 0)
            _peRegisterCombRepository.SplitTableUpdate(updateCombs);
        if (newRegCombs.Count > 0)
            _peRegisterCombRepository.SplitTableInsert(newRegCombs);
    }
    #endregion

    #region 回收记录/恢复记录
    public bool RecycleRegisterOrder(string[] regNoArray, ref string msg)
    {
        try
        {
            //查询即将删除的数据
            var regList = _peRegisterRepository.FindAll(x => SqlFunc.ContainsArray(regNoArray, x.RegNo)).ToList();
            if (regList.Any(x => x.IsActive))
            {
                msg = "存在已激活的数据,无法回收!";
                return false;
            }

            //转换为DeletePeRegister
            var delRegList = JsonConvert.DeserializeObject<List<DeletePeRegister>>(JsonConvert.SerializeObject(regList));
            foreach (var item in delRegList)
                item.DeleteTime = DateTime.Now;

            var regCombs = _registerRepository.ReadRegisterCombs(regNoArray)
                            .Where(x => SqlFunc.ContainsArray(regNoArray, x.RegNo))
                            .ToList();

            if (regList.Count <= 0)
            {
                msg = "选中的数据不存在,请刷新页面查看";
                return false;
            }

            //已总检的不能删除
            if (regList.Any(x => x.PeStatus != PeStatus.未检查))
            {
                msg = "已检查的订单不能删除";
                return false;
            }

            //已收费的订单不能删除
            if (regCombs.Any(x => x.PayStatus == PayStatus.收费))
            {
                msg = "已收费的订单不能删除";
                return false;
            }

            //登记表删除,删除登记表添加
            _dataTranRepository.ExecTran(() =>
            {
                _delRegisterRepository.Insert(delRegList);
                _peRegisterRepository.Delete(regList);
            });

            return true;
        }
        catch (Exception ex)
        {
            msg = ex.Message;
            return false;
        }
    }

    public DeletedOrder[] GetRecycleRegisterOrder(bool isCompanyCheck, DelOrderQuery delQuery)
    {
        delQuery.DelEndTime = delQuery.DelEndTime.Value.AddDays(1);
        return _registerRepository.ReadDeleteRegister()
            .WhereIF(!string.IsNullOrEmpty(delQuery.QueryValue),
                    (reg) => SqlFunc.Contains(reg.Name, delQuery.QueryValue) ||
                     reg.CardNo == delQuery.QueryValue || reg.Tel == delQuery.QueryValue)
            .Where(reg => SqlFunc.Between(reg.DeleteTime, delQuery.DelStartTime, delQuery.DelEndTime))
            .Where(reg => reg.IsCompanyCheck == isCompanyCheck)
            .OrderBy(reg => reg.DeleteTime)
            .Select(reg => new DeletedOrder
            {
                PeStatus = reg.PeStatus,
                PatCode = reg.PatCode,
                RegNo = reg.RegNo,
                Name = reg.Name,
                Sex = reg.Sex,
                Age = reg.Age,
                AgeUnit = reg.AgeUnit,
                IsActive = reg.IsActive,
                IsCompanyCheck = reg.IsCompanyCheck,
                IsVIP = reg.IsVIP,
                PeCls = reg.PeCls,
                CompanyName = SqlFunc.Subqueryable<CodeCompany>().Where(x => x.CompanyCode == reg.CompanyCode).Select(x => x.CompanyName),
                DeptName = SqlFunc.Subqueryable<CodeDepartment>().Where(x => x.DeptCode == reg.CompanyDeptCode).Select(x => x.DeptName)
            })
            .ToArray();
    }

    public bool DeleteRegisterOrder(string[] regNoArray, ref string msg)
    {
        try
        {
            if (_peRegisterRepository.Any(x => SqlFunc.ContainsArray(regNoArray, x.RegNo) &&
                        x.PeStatus != PeStatus.未检查))
            {
                msg = "状态为未检查的记录才能删除!";
                return false;
            }

            var regInfo = _registerRepository.ReadDeleteRegister()
                         .Where(x => SqlFunc.ContainsArray(regNoArray, x.RegNo))
                         .Select(x => new
                         {
                             startTime = SqlFunc.AggregateMin(x.RegisterTime),
                             endTime = SqlFunc.AggregateMax(x.RegisterTime),
                         }).First();

            //登记表删除,删除登记表添加
            _dataTranRepository.ExecTran(() =>
            {
                _peRegisterCombRepository.SplitTableDelete(regInfo.startTime, regInfo.endTime, x => SqlFunc.ContainsArray(regNoArray, x.RegNo));
                _peRegisterClusterRepository.Delete(x => SqlFunc.ContainsArray(regNoArray, x.RegNo));
                _delRegisterRepository.DeleteByIds(regNoArray);
            });
        }
        catch (Exception ex)
        {
            msg = ex.Message;
            return false;
        }
        return true;
    }

    public bool RestoreRegisterOrder(string[] regNoArray, ref string msg)
    {
        try
        {
            //查询需要恢复的数据
            var delRegList = _delRegisterRepository.FindAll(x => SqlFunc.ContainsArray(regNoArray, x.RegNo)).ToList();
            //转换为登记数据
            var regList = JsonConvert.DeserializeObject<List<PeRegister>>(JsonConvert.SerializeObject(delRegList));
            //登记表删除,删除登记表添加
            _dataTranRepository.ExecTran(() =>
            {
                _peRegisterRepository.Insert(regList);
                _delRegisterRepository.Delete(delRegList);
            });
        }
        catch (Exception ex)
        {
            msg = ex.Message;
            return false;
        }
        return true;
    }
    #endregion

    #region 体检激活
    public ActiveRecord[] ReadActiveRecord(ActiveQuery activeQuery)
    {
        var sugarQueryable = _registerRepository.ReadRegister();

        // 体检号优先
        if (!string.IsNullOrWhiteSpace(activeQuery.RegNo))
        {
            sugarQueryable.Where(reg => reg.RegNo == activeQuery.RegNo);
        }
        else if (!string.IsNullOrWhiteSpace(activeQuery.CardNo))
        {
            sugarQueryable.Where(reg => reg.CardNo == activeQuery.CardNo);
        }
        else
        {
            if (activeQuery.RegStartTime == null || activeQuery.RegEndTime == null)
                return Array.Empty<ActiveRecord>();

            activeQuery.RegStartTime = activeQuery.RegStartTime?.Date;
            activeQuery.RegEndTime = activeQuery.RegEndTime?.Date.Add(new TimeSpan(23, 59, 59));

            sugarQueryable
                .Where(reg => SqlFunc.Between(reg.RegisterTime, activeQuery.RegStartTime, activeQuery.RegEndTime))
                // 姓名
                .WhereIF(!string.IsNullOrEmpty(activeQuery.Name), reg => SqlFunc.Contains(reg.Name, activeQuery.Name))
                // 单位
                .WhereIF(!string.IsNullOrEmpty(activeQuery.CompanyCode), reg => reg.CompanyCode == activeQuery.CompanyCode && reg.CompanyTimes == activeQuery.CompanyTimes)
                // 档案卡号
                .WhereIF(!string.IsNullOrEmpty(activeQuery.PatCode), reg => reg.PatCode == activeQuery.PatCode);
        }

        return sugarQueryable
           .Where(reg => SqlFunc.ContainsArray(new PeStatus[] { PeStatus.未检查, PeStatus.正在检查 }, reg.PeStatus))
           .Select(reg => new ActiveRecord
           {
               RegNo = reg.RegNo,
               Name = reg.Name,
               Sex = reg.Sex,
               Age = reg.Age,
               AgeUnit = reg.AgeUnit,
               CardNo = reg.CardNo,
               MarryStatus = reg.MarryStatus,
               PeStatus = reg.PeStatus,
               PeCls = reg.PeCls,
               GuidanceType = reg.GuidanceType,
               IsActive = reg.IsActive,
               IsVIP = reg.IsVIP,
               IsCompanyCheck = reg.IsCompanyCheck,
               CompanyName = SqlFunc.Subqueryable<CodeCompany>().Where(x => x.CompanyCode == reg.CompanyCode).Select(x => x.CompanyName),
               DeptName = SqlFunc.Subqueryable<CodeCompanyDepartment>().Where(x => x.DeptCode == reg.CompanyDeptCode).Select(x => x.DeptName),
           }).ToArray();
    }

    public bool ActivationOrCancel(bool isActive, ActiveRegister activeRegister, ref string msg)
    {
        //如果是激活,激活时间为空则返回信息
        if (isActive && activeRegister.ActiveTime == null)
        {
            msg = "激活时间不能为空!";
            return false;
        }

        var regList = _peRegisterRepository.FindAll(x => SqlFunc.ContainsArray(activeRegister.RegNoArray, x.RegNo)).ToList();
        if (isActive && regList.Any(x => x.IsActive))
        {
            msg = "存在已经激活的数据,请勿重复激活!";
            return false;
        }
        if (regList.Any(x => x.PeStatus != PeStatus.未检查) || regList.Any(x => x.ReportPrinted) || regList.Any(x => x.PayStatus == PaymentStatus.Paid))
        {
            msg = "当前订单状态不支持操作!";
            return false;
        }

        for (int i = 0; i < activeRegister.RegNoArray.Length; i++)
        {
            ExecActivationOrCancel(activeRegister.RegNoArray[i]);
            SendProgressMsg(activeRegister.RegNoArray.Length, i + 1);
        }

        return true;

        #region 本地方法
        void ExecActivationOrCancel(string regNo)
        {
            _dataTranRepository.ExecTran(() =>
            {
                if (!isActive)
                {
                    activeRegister.ActiveTime = null;
                    activeRegister.Activator = null;
                }
                else
                    activeRegister.Activator = _httpContextUser.UserId.IsNullOrEmpty() ? ConstantSystem.UserCodeDirector : _httpContextUser.UserId;

                _peRegisterRepository.Update(
                    x => new PeRegister()
                    {
                        IsActive = isActive,
                        ActiveTime = activeRegister.ActiveTime,
                        Activator = activeRegister.Activator
                    },
                    x => x.RegNo == regNo);
                bool isCompanyCheck = regList.First(x => x.RegNo == regNo).IsCompanyCheck;
                if (isActive && !isCompanyCheck)
                    _sampleService.SyncSampleBindBarCodeAll(regNo);
            });
            if (isActive)
                _externalSystemService.SyncOrder(regNo);
            else
                _externalSystemService.ClearOrder(regNo);
        }

        void SendProgressMsg(int totalCount, int completedCount)
        {
            if (_httpContextUser.UserId.IsNullOrEmpty()) return;

            var msg = new
            {
                MsgCode = "ActivationOrCancelForBatch",
                MsgData = new
                {
                    Message = $"已处理数量 {completedCount}/{totalCount}",
                    CurrentProgressPercentage = Math.Truncate(((float)completedCount / totalCount) * 100)// 当前进度百分比
                }
            };
            _peimClients.User(_httpContextUser.UserId).SendMsg(msg.ToJson()).Wait();
        }
        #endregion
    }
    #endregion

    #region 批修改个人属性信息(领导标识，VIP标识)
    public BatchUpdateRecord[] ReadBatchUpdateRecord(BatchUpdateQuery batchQuery)
    {
        return _registerRepository.ReadRegister()
           //体检号
           .WhereIF(!string.IsNullOrEmpty(batchQuery.RegNo), x => x.RegNo == batchQuery.RegNo)
           //只查出团体
           .Where(x => x.IsCompanyCheck)
           //姓名
           .WhereIF(!string.IsNullOrEmpty(batchQuery.Name), x => x.Name == batchQuery.Name)
           //档案卡号
           .WhereIF(!string.IsNullOrEmpty(batchQuery.PatCode), x => x.PatCode == batchQuery.PatCode)
            //工作单位
            .WhereIF(!string.IsNullOrEmpty(batchQuery.CompanyCode), x => x.CompanyCode == batchQuery.CompanyCode)
            //登记时间
            .WhereIF(batchQuery.QueryType == 1 && batchQuery.EndTime != null,
                reg => SqlFunc.Between(reg.RegisterTime.Date, batchQuery.StartTime.Value.Date, batchQuery.EndTime.Value.Date))
            //激活时间（报到时间）
            .WhereIF(batchQuery.QueryType == 2 && batchQuery.EndTime != null,
                reg => SqlFunc.Between(reg.ActiveTime.Value.Date, batchQuery.StartTime.Value.Date, batchQuery.EndTime.Value.Date))
           .Where(x => SqlFunc.ContainsArray(new int[] { 0, 1, 2 }, x.PeStatus))
           .Select(x => new BatchUpdateRecord
           {
               PatCode = x.PatCode,
               RegNo = x.RegNo,
               PeStatus = x.PeStatus,
               Name = x.Name,
               IsActive = x.IsActive,
               IsCompanyCheck = x.IsCompanyCheck,
               IsVIP = x.IsVIP,
               PeCls = x.PeCls,
               Sex = x.Sex,
               Age = x.Age,
               AgeUnit = x.AgeUnit,
               MarryStatus = x.MarryStatus,
               CardNo = x.CardNo,
               IsLeader = SqlFunc.IsNull(x.IsLeader, false)
           }).ToArray();
    }

    public bool BatchUpdateRegisterOrder(BatchUpdateArray batchUpdateArray, ref string msg)
    {
        var regList = _peRegisterRepository.FindAll(x => SqlFunc.ContainsArray(batchUpdateArray.RegNoArray, x.RegNo)).ToList();
        if (regList.Any(x => x.PeStatus == PeStatus.已审核) || regList.Any(x => x.ReportPrinted))
        {
            msg = "存在已审核/已发报告的订单,不支持批修改";
            return false;
        }

        foreach (var item in regList)
        {
            item.PeCls = !Enum.IsDefined(typeof(PeCls), batchUpdateArray.PeCls) ? item.PeCls : batchUpdateArray.PeCls;
            item.IsVIP = batchUpdateArray.IsVIP == null ? item.IsVIP : batchUpdateArray.IsVIP;
            item.IsLeader = batchUpdateArray.IsLeader == null ? item.IsLeader : batchUpdateArray.IsLeader;
        }

        _peRegisterRepository.Update(regList);
        return true;
    }
    #endregion

    #region 批增加/批删除(组合)
    public BatchAddOrDeleteRecord[] ReadBatchAddOrDeleteOrder(BatchAddOrDeleteQuery batchQuery)
    {
        if (string.IsNullOrWhiteSpace(batchQuery.CompanyCode) || batchQuery.CompanyTimes == 0)
            return Array.Empty<BatchAddOrDeleteRecord>();

        return _registerRepository.ReadRegisterOrder()
            .Where((reg, clus) => reg.CompanyCode == batchQuery.CompanyCode && reg.CompanyTimes == batchQuery.CompanyTimes)
            .WhereIF(!string.IsNullOrEmpty(batchQuery.ClusterCode), (reg, clus) => clus.ClusCode == batchQuery.ClusterCode)
            .WhereIF(!string.IsNullOrEmpty(batchQuery.CompanyDeptCode), (reg, clus) => reg.CompanyDeptCode == batchQuery.CompanyDeptCode)
            .Where(reg => SqlFunc.ContainsArray(new int[] { 0, 1, 2 }, reg.PeStatus))
            .Select((reg, clus) => new BatchAddOrDeleteRecord
            {
                RegisterTime = reg.RegisterTime,
                RegNo = reg.RegNo,
                Name = reg.Name,
                Sex = reg.Sex,
                Age = reg.Age,
                AgeUnit = reg.AgeUnit,
                CompanyName = SqlFunc.Subqueryable<CodeCompany>().Where(x => x.CompanyCode == reg.CompanyCode).Select(x => x.CompanyName),
            })
            .ToArray();
    }

    public bool BatchAddComb(BatchAddOrDeleteArray batchArray, ref string msg)
    {
        // 查出需要增加的组合
        var itemCombList = _basicCodeRepository.ReadCodeItemComb(Array.Empty<string>())
            .Where(x => SqlFunc.ContainsArray(batchArray.CombCodeArray, x.CombCode))
            .Select(x => new CodeItemCombDTO
            {
                CombCode = x.CombCode.SelectAll(),
                ClsCodeName = SqlFunc.Subqueryable<CodeItemCls>().Where(y => y.ClsCode == x.ClsCode).Select(y => y.ClsName),
                ClsSortIndex = SqlFunc.Subqueryable<CodeItemCls>().Where(y => y.ClsCode == x.ClsCode).Select(y => y.SortIndex)
            }).ToList();

        var totalCount = batchArray.RegNoArray.Length;
        var completedCount = 0;
        foreach (var regNo in batchArray.RegNoArray)
        {
            var regInfo = _registerRepository.ReadRegister(regNo).First();
            if (regInfo == null)
                continue;

            var newCombList = itemCombList
                .Where(combData => !_peRegisterCombRepository.SplitTableAny(regInfo.RegisterTime, x => x.RegNo == regNo && x.CombCode == combData.CombCode))
                .Select(combData => new PeRegisterComb
                {
                    Id = _noGeneration.NextSnowflakeId(),
                    RegNo = regNo,
                    CombCode = combData.CombCode,
                    CombName = combData.CombName,
                    CombSortIndex = combData.SortIndex,
                    ExamDeptCode = combData.ExamDeptCode,
                    CheckCls = combData.CheckCls,
                    ClsCode = combData.ClsCode,
                    ClsName = combData.ClsCodeName ?? "",
                    ClsSortIndex = combData.ClsSortIndex,
                    OriginalPrice = combData.Price,
                    Price = combData.Price,
                    ReportShow = combData.ReportShow,
                    IsPayBySelf = false,
                    PayStatus = PayStatus.未收费,
                    Discount = 1,
                    CreateTime = DateTime.Now,
                    RegisterTime = regInfo.RegisterTime
                })
                .ToList();

            // 保存到表中
            _peRegisterCombRepository.SplitTableInsert(newCombList);
            var clusterCode = _registerRepository.ReadRegisterClusters(regNo).First()?.ClusCode;
            InsertTestTubeToRegisterComb(regInfo, clusterCode);
            InsertVenousBloodComb(regInfo.RegNo, !regInfo.IsCompanyCheck, clusterCode);
            _sampleService.SyncSampleBindBarCodeAll(regNo, clusterCode);
            _mediator.Publish(new SyncPeRegisterOrderHandle.Data(new string[] { regNo }));
            // 如果激活就同步订单到外部系统
            if (regInfo.IsActive)
                _externalSystemService.SyncOrder(regNo);

            completedCount++;
            SendProgressMsg(totalCount, completedCount);
        }

        return true;

        void SendProgressMsg(int totalCount, int completedCount)
        {
            var msg = new
            {
                MsgCode = "CompanyBatchAddOrDelete",
                MsgData = new
                {
                    Message = $"已处理数量 {completedCount}/{totalCount}",
                    CurrentProgressPercentage = Math.Truncate(((float)completedCount / totalCount) * 100)// 当前进度百分比
                }
            };
            _peimClients.User(_httpContextUser.UserId).SendMsg(msg.ToJson()).Wait();

            DebugHelper.WriteLine(msg.ToJson(), ConsoleColor.Green);
        }
    }

    public bool BatchDeleteComb(BatchAddOrDeleteArray batchArray, ref string msg)
    {
        _dataTranRepository.ExecTran(() =>
        {
            for (int i = 0; i < batchArray.RegNoArray.Length; i++)
            {
                var regNo = batchArray.RegNoArray[i];
                var regInfo = _registerRepository.ReadRegister(regNo).First();
                if (regInfo == null)
                    continue;
                var peRegisterComb = _registerRepository.ReadRegisterCombs(regNo, batchArray.CombCodeArray, PayStatus.未收费).ToArray();
                _peRegisterCombRepository.SplitTableDelete(peRegisterComb);
                var clusterCode = _registerRepository.ReadRegisterClusters(regNo).First()?.ClusCode;
                //条码，材料费
                InsertTestTubeToRegisterComb(regInfo, clusterCode);
                InsertVenousBloodComb(regInfo.RegNo, !regInfo.IsCompanyCheck, clusterCode);
                _sampleService.SyncSampleBindBarCodeAll(regNo, clusterCode);
                // 同步订单到外部系统
                _externalSystemService.SyncOrder(regNo);
                SendProgressMsg(batchArray.RegNoArray.Length, i + 1);
            }
        });
        _mediator.Publish(new SyncPeRegisterOrderHandle.Data(batchArray.RegNoArray));
        return true;

        void SendProgressMsg(int totalCount, int completedCount)
        {
            var msg = new
            {
                MsgCode = "CompanyBatchAddOrDelete",
                MsgData = new
                {
                    Message = $"已处理数量 {completedCount}/{totalCount}",
                    CurrentProgressPercentage = Math.Truncate(((float)completedCount / totalCount) * 100)// 当前进度百分比
                }
            };
            _peimClients.User(_httpContextUser.UserId).SendMsg(msg.ToJson()).Wait();

            DebugHelper.WriteLine(msg.ToJson(), ConsoleColor.Green);
        }
    }
    #endregion

    /// <summary>
    /// 查询可打印（指引单、报告、体检标签、检验条码、采血检验条码、非采血检验条码）
    /// </summary>
    /// <param name="query"></param>
    /// <returns></returns>
    public List<string> CheckPrintables(PrintQuery query)
    {
        var register = _peRegisterRepository.First(x => x.RegNo == query.RegNo);
        if (register == null)
            return new();

        return query.FilterList.Where(type => HasPrintable(type, register)).Select(type => type.ToString()).ToList();

        #region 本地函数
        bool HasPrintable(PrintFileType type, PeRegister register) => type switch
        {
            PrintFileType.Guidance => register.IsActive || register.IsCompanyCheck,                     // 指引单
            PrintFileType.Report => register.IsActive && HasPrintableReport(register.RegNo),          // 报告
            PrintFileType.PeLabel => register.IsActive && HasPrintablePeLabel(register.RegNo),         // 体检标签
            PrintFileType.Barcode => register.IsActive && HasPrintableBarcode(register.RegNo),         // 检验条码
            PrintFileType.BloodBarcode => register.IsActive && HasPrintableBloodBarcode(register.RegNo),    // 检验采血条码
            PrintFileType.NonBloodBarcode => register.IsActive && HasPrintableNonBloodBarcode(register.RegNo), // 检验非采血条码
            PrintFileType.Gastroscope => register.IsActive && HasPrintableGastroscope(register.RegNo),     // 胃镜申请
            PrintFileType.Colonoscopy => register.IsActive && HasPrintableColonoscopy(register.RegNo),     // 肠镜申请
            PrintFileType.AnesthesiaCost => register.IsActive && HasPrintableAnesthesiaCost(register.RegNo),  // 麻醉费用申请
            _ => false
        };

        bool HasPrintableReport(string regNo)
            => _registerRepository.ReadRegisterNoHosp().Any(x => x.RegNo == regNo && (x.PeStatus == PeStatus.已总检 || x.PeStatus == PeStatus.已审核));

        bool HasPrintablePeLabel(string regNo)
            => _registerRepository.ReadRegCombsItemCls(regNo)
            .Any((regComb, itemCls) => (regComb.PayStatus == PayStatus.收费 || !regComb.IsPayBySelf) && (itemCls.CombPrintTimes > 0 || itemCls.ClsPrintTimes > 0));

        bool HasPrintableBarcode(string regNo)
            => _sampleRepository.ReadPrintableAndPaidSample(regNo, BarcodePrintType.Barcode).Any();

        bool HasPrintableBloodBarcode(string regNo)
            => _sampleRepository.ReadPrintableAndPaidSample(regNo, BarcodePrintType.BloodBarcode).Any();

        bool HasPrintableNonBloodBarcode(string regNo)
            => _sampleRepository.ReadPrintableAndPaidSample(regNo, BarcodePrintType.NonBloodBarcode).Any();

        bool HasPrintableGastroscope(string regNo)
            => _registerRepository.ReadRegisterCombs(regNo).Any(x => x.ClsCode == "23");

        bool HasPrintableColonoscopy(string regNo)
            => _registerRepository.ReadRegisterCombs(regNo).Any(x => x.ClsCode == "24");

        bool HasPrintableAnesthesiaCost(string regNo)
            => _registerRepository.ReadRegisterCombs(regNo).Any(x => x.CombCode == "1806");
        #endregion
    }

    /// <summary>
    /// 通过体检号获取问卷数据
    /// </summary>
    /// <param name="regNo">体检号</param>
    /// <returns></returns>
    public WeChatBookQuestion GetQuestionDataByRegNo(string regNo)
    {
        return _weChatBookQuestionRepository.First(x => x.RegNo == regNo);
    }

    public void InsertTestTubeToRegisterComb(string regNo)
    {
        //暂时空置函数
    }

    /// <summary>
    /// 获取未审核的体检订单
    /// </summary>
    /// <param name="query">查询条件</param>
    /// <param name="totalNumber">返回总数</param>
    /// <param name="totalPage">返回总页数</param>
    /// <returns>PeRegisters</returns>
    public List<PeRegister> GetRegisterForNotAudits(RegisterNotAuditQuery query, ref int totalNumber, ref int totalPage)
    {
        return _registerRepository.ReadRegisterNoHosp()
            .Where(x => x.PeStatus <= PeStatus.已总检)
            .WhereIF(query.ActiveTimeStart.HasValue, x => x.ActiveTime >= query.ActiveTimeStart)
            .WhereIF(query.ActiveTimeEnd.HasValue, x => x.ActiveTime <= query.ActiveTimeEnd)
            .ToMyPageList(query.PageNumber, query.PageSize, ref totalNumber, ref totalPage);
    }

    /// <summary>
    /// 修改登记指引单相关内容信息
    /// </summary>
    /// <param name="guidance">指引单内容</param>
    /// <param name="msg">消息</param>
    /// <returns>bool</returns>
    public bool ModifyRegisterGuidance(RegisterGuidanceDto guidance, ref string msg)
    {
        var register = _registerRepository.ReadRegister(guidance.RegNo).First();
        if (register.IsNullOrEmpty())
        {
            msg = ResxCommon.NotExistRecord;
            return false;
        }

        if (!guidance.GuidanceType.IsNullOrEmpty())
            register.GuidanceType = guidance.GuidanceType;
        if (guidance.GuidancePrinted.HasValue)
            register.GuidancePrinted = guidance.GuidancePrinted;
        if (guidance.GuidancePrintTime.HasValue)
            register.GuidancePrintTime = guidance.GuidancePrintTime;
        if (!guidance.GuidanceRecycler.IsNullOrEmpty())
            register.GuidanceRecycler = guidance.GuidanceRecycler;
        if (guidance.GuidanceRecyclyTime.HasValue)
            register.GuidanceRecyclyTime = guidance.GuidanceRecyclyTime;

        _peRegisterRepository.Update(register);
        msg = ResxCommon.Success;
        return true;
    }

    #region 本地方法
    /// <summary>
    /// 创建登记信息
    /// </summary>
    /// <param name="isCompanyCheck"></param>
    /// <param name="patient"></param>
    /// <param name="errMsg"></param>
    /// <returns></returns>
    private PeRegister CreatePeRegister(bool isCompanyCheck, RegisterPatient patient, ref string errMsg)
    {
        if (!patient.PeCls.HasValue)
        {
            errMsg = "体检分类不能为空";
            return null;
        }
        // 读取或尝试匹配最后一次体检订单记录
        PeRegister lastRegister = null;
        if (string.IsNullOrEmpty(patient.PatCode))
        {
            // 同名同身份证号一样不能创建新的
            lastRegister = _registerRepository.ReadRegisterNoHosp()
                .Where(x => x.CardNo == patient.CardNo && x.Name == patient.Name)
                .OrderByDescending(x => x.RegisterTimes)
                .First();
        }
        else
        {
            lastRegister = _registerRepository.ReadRegisterNoHosp()
                .Where(x => x.PatCode == patient.PatCode)
                .OrderByDescending(x => x.RegisterTimes)
                .First();
        }

        // ini
        var patCode = lastRegister != null ? lastRegister.PatCode : _noGeneration.NextPatCode()[0];
        var regTimes = lastRegister != null ? lastRegister.RegisterTimes + 1 : 1;

        //// 限制同体检类型多个订单
        //if (regTimes > 1)
        //{
        //    var peStatuses = new PeStatus[] { PeStatus.未检查, PeStatus.未检查, PeStatus.正在检查 };

        //    //检查该体检类型是否存在已激活未总检的记录
        //    bool isExis = _registerRepository.ReadRegister()
        //        .Where(x => x.PatCode == patient.PatCode && x.PeCls == patient.PeCls && x.IsActive)
        //        .Where(x => SqlFunc.ContainsArray(peStatuses, x.PeStatus))
        //        .Any();
        //    if (isExis)
        //    {
        //        errMsg = "存在已激活且未完成的体检记录";
        //        return null;
        //    }
        //}

        // 设置默认指引单格式
        if (string.IsNullOrEmpty(patient.GuidanceType))
            patient.GuidanceType = _systemParameterService.DefaultGuidanceType;

        // 设置默认报告格式
        if (string.IsNullOrEmpty(patient.ReportType))
            patient.ReportType = _systemParameterService.DefaultReportType;

        return new PeRegister()
        {
            RegNo = _noGeneration.NextRegNo()[0],
            PatCode = patCode,
            Name = patient.Name,
            Sex = patient.Sex,
            Age = patient.Age,
            AgeUnit = patient.AgeUnit,
            Birthday = patient.Birthday,
            CardType = patient.CardType,
            CardNo = patient.CardNo,
            Tel = patient.Tel,
            NativePlace = patient.NativePlace,
            Address = patient.Address,
            MarryStatus = patient.MarryStatus,
            //PhotoUrl           = patient.PhotoUrl,
            RegisterTime = DateTime.Now,
            RegisterTimes = regTimes,
            IsActive = patient.IsActive,
            ActiveTime = patient.IsActive ? DateTime.Now : null,
            Activator = patient.IsActive ? _httpContextUser.UserId : null,
            PeCls = patient.PeCls.Value,
            PeStatus = PeStatus.未检查,
            GuidanceType = patient.GuidanceType,
            GuidancePrinted = false,
            GuidancePrintTime = null,
            GuidanceRecycler = string.Empty,
            GuidanceRecyclyTime = null,
            ReportType = patient.ReportType,
            ReportPrinted = false,
            IsCompanyCheck = isCompanyCheck,
            CompanyCode = patient.CompanyCode,
            CompanyTimes = patient.CompanyTimes,
            CompanyDeptCode = patient.CompanyDeptCode,
            BookType = BookType.现场登记,
            BookNo = string.Empty,
            BookBeginTime = null,
            BookEndTime = null,
            //JobStatus          = patient.JobStatus,
            JobCode = patient.JobCode,
            JobHistory = patient.JobHistory,
            MedicalHistory = patient.MedicalHistory,
            FamilyMedicalHistory = patient.FamilyMedicalHistory,
            IsVIP = patient.IsVIP,
            IsLeader = patient.IsLeader,
            IsConstitution = patient.IsConstitution,
            RecheckNo = patient.RecheckNo,
            HealthUploaded = false,
            OutsideUploaded = false,
            OperatorCode = patient.OperatorCode,
            Introducer = patient.Introducer,
            Note = patient.Note,
            ChargeModel = patient.ChargeModel,
            HospCode = _httpContextUser.HospCode,
            IsOrdinary = true,
            IsOccupation = false
        };
    }

    /// <summary>
    /// 获取修改后的登记信息
    /// </summary>
    /// <param name="isCompanyCheck"></param>
    /// <param name="patient"></param>
    /// <param name="peRegisterOriginal"></param>
    /// <param name="errMsg"></param>
    /// <returns></returns>
    private PeRegister GetRegisterChanged(bool isCompanyCheck, RegisterPatient patient, RegisterCluster[] clusters, ref PeRegister peRegisterOriginal, ref string errMsg)
    {
        if (string.IsNullOrWhiteSpace(patient.RegNo))
        {
            errMsg = "不能修改体检号为空的订单信息";
            return null;
        }
        if (!patient.PeCls.HasValue)
        {
            errMsg = "体检分类不能为空";
            return null;
        }
        var regQuery = _registerRepository.ReadRegister(patient.RegNo).First();

        if (regQuery == null)
        {
            errMsg = "找不到订单";
            return null;
        }

        if (regQuery.IsCompanyCheck != isCompanyCheck)
        {
            errMsg = "团检标识不能修改";
            return null;
        }
        if (regQuery.CompanyCode != patient.CompanyCode)
        {
            errMsg = "单位代码不能修改";
            return null;
        }
        if (regQuery.IsActive != patient.IsActive)
        {
            regQuery.IsActive = patient.IsActive;
            regQuery.ActiveTime = patient.IsActive ? DateTime.Now : null;
        }

        if (string.IsNullOrEmpty(patient.GuidanceType) || clusters?.Length == 0)
            patient.GuidanceType = _systemParameterService.DefaultGuidanceType;

        if (string.IsNullOrEmpty(patient.ReportType) || clusters?.Length == 0)
            patient.ReportType = _systemParameterService.DefaultReportType;

        #region 深复制原登记信息，用于记录日志
        peRegisterOriginal = TransExp<PeRegister, PeRegister>.Trans(regQuery);
        #endregion

        //todo:只更新修改的字段，关键字段修改写日志
        regQuery.Name = patient.Name;
        regQuery.Sex = patient.Sex;
        regQuery.Age = patient.Age;
        regQuery.AgeUnit = patient.AgeUnit;
        regQuery.Birthday = patient.Birthday;
        regQuery.CardType = patient.CardType;
        regQuery.CardNo = patient.CardNo;
        regQuery.Tel = patient.Tel;
        regQuery.NativePlace = patient.NativePlace;
        regQuery.Address = patient.Address;
        regQuery.MarryStatus = patient.MarryStatus;
        //regQuery.PhotoUrl           = patient.PhotoUrl;
        //regQuery.RegisterTime       = patinet.RegisterTime;
        regQuery.PeCls = patient.PeCls.Value;
        regQuery.GuidanceType = patient.GuidanceType;
        regQuery.ReportType = patient.ReportType;
        regQuery.CompanyCode = patient.CompanyCode;
        regQuery.CompanyTimes = patient.CompanyTimes;
        regQuery.CompanyDeptCode = patient.CompanyDeptCode;
        //regQuery.JobStatus          = patient.JobStatus;
        regQuery.JobCode = patient.JobCode;
        regQuery.JobHistory = patient.JobHistory;
        regQuery.MedicalHistory = patient.MedicalHistory;
        regQuery.FamilyMedicalHistory = patient.FamilyMedicalHistory;
        regQuery.IsVIP = patient.IsVIP;
        regQuery.IsLeader = patient.IsLeader;
        regQuery.IsConstitution = patient.IsConstitution;
        regQuery.RecheckNo = patient.RecheckNo;
        regQuery.Introducer = patient.Introducer;
        regQuery.Note = patient.Note;
        regQuery.IsOrdinary = true;
        regQuery.IsOccupation = false;

        // 微信预约，如果传值是护士组的账号，开单医生默认不修改
        //if (!regQuery.BookType.Equals(BookType.微信预约) || 
        //    !_settingRepository.AnyRole(patient.OperatorCode, _systemParameterService.DefaultRoleCodeNurse))
        //{
        //开单工号非医生角色，修改工号为医生角色则覆盖
        if (!_settingRepository.AnyRole(regQuery.OperatorCode, _systemParameterService.DefaultRoleCodeDoctor)
            && _settingRepository.AnyRole(patient.OperatorCode, _systemParameterService.DefaultRoleCodeDoctor))
        {
            regQuery.OperatorCode = patient.OperatorCode;// 目前作为开单医生
        }
        //}

        return regQuery;
    }

    /// <summary>
    /// 创建登记套餐信息
    /// </summary>
    /// <param name="regNo"></param>
    /// <param name="clusters"></param>
    /// <returns></returns>
    private PeRegisterCluster[] CreatePeRegisterCluster(string regNo, RegisterCluster[] addClusters)
    {
        if (addClusters.Length == 0)
            return Array.Empty<PeRegisterCluster>();

        return addClusters.Select(clus => new PeRegisterCluster
        {
            RegNo = regNo,
            ClusCode = clus.ClusCode,
            ClusName = clus.ClusName,
            Price = clus.Price,
            IsMain = clus.IsMain
        }).ToArray();
    }

    /// <summary>
    /// 获取修改后的登记套餐信息
    /// </summary>
    /// <param name="regNo"></param>
    /// <param name="current"></param>
    /// <param name="dels"></param>
    /// <param name="adds"></param>
    private void GetRegisterClusterChanged(
            string regNo,
            RegisterCluster[] current,
        out List<PeRegisterCluster> dels,
        out PeRegisterCluster[] adds)
    {
        var oldRegClus = _registerRepository.ReadRegisterClusters(regNo).ToArray();

        dels =
            (
                from old in oldRegClus
                join cur in current on old.ClusCode equals cur.ClusCode into tmp
                from cur2 in tmp.DefaultIfEmpty()
                where cur2 == null
                select old
             ).ToList();

        RegisterCluster[] newClus =
            (
                from cur in current
                join old in oldRegClus on cur.ClusCode equals old.ClusCode into tmp
                from old2 in tmp.DefaultIfEmpty()
                where old2 == null
                select cur
             ).ToArray();

        adds = CreatePeRegisterCluster(regNo, newClus);
    }

    /// <summary>
    /// 创建登记组合信息（除试管外，试管另单独处理）
    /// </summary>
    /// <param name="regNo"></param>
    /// <param name="registerTime"></param>
    /// <param name="combs"></param>
    /// <returns></returns>
    private PeRegisterComb[] CreatePeRegisterComb(PeRegister register, RegisterComb[] addCombs)
    {
        if (addCombs.Length == 0)
            return Array.Empty<PeRegisterComb>();

        var codeCombs = _clusterCombRepository
                            .ReadItemCombCls(addCombs.Select(x => x.CombCode).ToArray())
                            .Select((comb, cls) => new
                            {
                                comb.CombCode,
                                comb.CombName,
                                comb.Sex,
                                comb.Price,
                                comb.ExamDeptCode,
                                comb.CheckCls,
                                comb.ClsCode,
                                comb.ReportShow,
                                cls.ClsName,
                                CombSortIndex = comb.SortIndex,
                                ClsSortIndex = cls.SortIndex,
                            }).ToArray();

        var illegalCombs = codeCombs
            .Where(x => x.Sex != Sex.通用 && x.Sex != register.Sex)
            .Select(x => $"[{x.CombName}]")
            .ToArray();
        if (illegalCombs.Any())
            throw new Exception("性别冲突的组合：" + string.Join(' ', illegalCombs));

        return (from comb in codeCombs
                join regCombs in addCombs on comb.CombCode equals regCombs.CombCode
                select new PeRegisterComb()
                {
                    Id = _noGeneration.NextSnowflakeId(),
                    RegNo = register.RegNo,
                    CombCode = comb.CombCode,
                    CombName = comb.CombName,
                    ExamDeptCode = comb.ExamDeptCode,
                    CheckCls = comb.CheckCls,
                    ClsCode = comb.ClsCode,
                    ClsName = comb.ClsName ?? "",
                    OriginalPrice = regCombs.OriginalPrice,
                    Price = regCombs.Price,
                    ReportShow = comb.ReportShow,
                    Surcharges = 0.0M,
                    Discount = regCombs.Discount,
                    IsPayBySelf = !register.IsCompanyCheck || regCombs.IsPayBySelf, //个检直接自费，团体读项目自费勾选状态
                    PayStatus = PayStatus.未收费,
                    ApplicantCode = regCombs.ApplicantCode,
                    ApplicantName = regCombs.ApplicantName,
                    CreateTime = DateTime.Now,
                    RegisterTime = register.RegisterTime,
                    CombSortIndex = comb.CombSortIndex,
                    ClsSortIndex = comb.ClsSortIndex,
                    IsOrdinary = true,
                    IsOccupation = false,
                }).ToArray();
    }

    /// <summary>
    /// 获取修改后的登记组合信息（除试管外，试管另单独处理）
    /// </summary>
    /// <param name="regInfo"></param>
    /// <param name="current"></param>
    /// <param name="dels"></param>
    /// <param name="alts"></param>
    /// <param name="adds"></param>
    private void GetRegisterCombChanged(
            PeRegister regInfo,
            RegisterComb[] current,
        out List<PeRegisterComb> dels,
        out List<PeRegisterComb> alts,
        out PeRegisterComb[] adds)
    {
        var oldRegCombs = _registerRepository.ReadRegisterCombs(regInfo.RegNo)
            .Where(x => x.BeFrom == null)//不包括条码材料
            .ToArray();

        dels =
            (
                from old in oldRegCombs
                join cur in current on old.Id equals cur.Id into tmp
                from cur2 in tmp.DefaultIfEmpty()
                where cur2 == null
                select old
            ).ToList();

        alts = new List<PeRegisterComb>();

        //团体登记可以修改自费标识
        //if (regInfo.IsCompanyCheck)
        //{
        var modifyCombs = oldRegCombs
            .Join(current, oldComb => oldComb.Id, curComb => curComb.Id, (oldComb, curComb) => new { oldComb, curComb })
            .Where(x => x.oldComb.IsPayBySelf != x.curComb.IsPayBySelf
                                || x.oldComb.Discount != x.curComb.Discount
                                || x.oldComb.Price != x.curComb.Price);

        foreach (var comb in modifyCombs)
        {
            comb.oldComb.IsPayBySelf = comb.curComb.IsPayBySelf;
            comb.oldComb.Discount = comb.curComb.Discount;
            comb.oldComb.Price = comb.curComb.Price;
            alts.Add(comb.oldComb);
        }
        //}

        RegisterComb[] newCombs =
            (
                from cur in current
                join old in oldRegCombs on cur.Id equals old.Id into tmp
                from old2 in tmp.DefaultIfEmpty()
                where old2 == null
                select cur
            ).ToArray();

        adds = CreatePeRegisterComb(regInfo, newCombs);
    }

    /// <summary>
    /// 插入采血收费组合
    /// </summary>
    /// <param name="regNo"></param>
    /// <param name="isPayBySelf"></param>
    /// <param name="regCombs"></param>
    private void InsertVenousBloodComb(string regNo, bool isPayBySelf, string clusterCode = null)
    {
        //获取采血收费组合代码
        var combcode = _systemParameterService.VenousBloodComb;
        if (string.IsNullOrEmpty(combcode))
        {
            return;
        }
        //判断是否有采血组合，无则删除采血组合
        var regCombs = _registerRepository.ReadRegisterCombs(regNo).Where(x => x.BeFrom != null).ToArray();
        if (!regCombs.Any(x => x.CombCode != combcode))
        {
            DeleteVenousBloodComb();
            return;
        }
        var befrom = new List<string>();
        regCombs.Where(x => x.CombCode != combcode).ForEach(x => befrom.AddRange(x.BeFrom));
        //已有采血则更新从属关系
        if (regCombs.Any(x => x.CombCode == combcode))
        {
            var venousBloodComb = regCombs.Where(x => x.CombCode == combcode).First();
            if (venousBloodComb.PayStatus == PayStatus.收费)
            {
                return;
            }
            venousBloodComb.BeFrom = befrom.ToArray();
            _peRegisterCombRepository.SplitTableUpdate(venousBloodComb);
            return;
        }
        var bloodComb = _cacheRepository.DictComb().TryGetValue(combcode, out var comb) ? comb : null;
        if (bloodComb == null) //组合不存在
            return;
        var combInfo = bloodComb.ItemComb;
        decimal price, discount;
        if (clusterCode.IsNullOrEmpty())
        {
            price = combInfo.Price;
            discount = 1;
        }
        else
        {
            _cacheRepository.DictCompanyClusterTestTube().TryGetValue(clusterCode, out var tube);
            //团体获取组合价格，折扣
            if (tube == null)
            {
                price = combInfo.Price;
                discount = 1;
            }
            else
            {
                var a = tube.Where(x => x.CombCode == combcode).First();
                price = a == null ? combInfo.Price : a.Price;
                discount = a == null ? 1 : a.Discount;
            }
        }

        //采血组合
        var newRegComb = new PeRegisterComb
        {
            Id = _noGeneration.NextSnowflakeId(),
            RegNo = regNo,
            CombCode = combInfo.CombCode,
            CombName = combInfo.CombName,
            ExamDeptCode = combInfo.ExamDeptCode,
            CheckCls = combInfo.CheckCls,
            ClsCode = combInfo.ClsCode,
            ClsName = string.Empty,
            OriginalPrice = combInfo.Price,
            Price = price,
            ReportShow = combInfo.ReportShow,
            Discount = discount,
            IsPayBySelf = isPayBySelf, // 
            PayStatus = PayStatus.未收费,
            BeFrom = befrom.ToArray(),
            Surcharges = 0,
            ApplicantCode = string.Empty,
            ApplicantName = string.Empty,
            CreateTime = DateTime.Now,
            RegisterTime = regCombs[0].RegisterTime,
            CombSortIndex = combInfo.SortIndex,
            ClsSortIndex = combInfo.SortIndex,
        };
        _peRegisterCombRepository.SplitTableInsert(newRegComb);

        void DeleteVenousBloodComb()
        {
            if (!regCombs.Any(x => x.CombCode == combcode))
            {
                return;
            }
            if (regCombs.Any(x => x.CombCode == combcode && x.PayStatus == PayStatus.收费))
            {
                return;
            }
            var deleteComb = _registerRepository.ReadRegisterCombs(regNo).Where(x => x.CombCode == combcode).ToArray();
            _peRegisterCombRepository.SplitTableDelete(deleteComb);
        }
    }
    #endregion
}