﻿using Peis.Model.DataBaseViewModel.External;
using Peis.Model.DTO.External.HATM;
using Peis.Model.DTO.External.HisApply;
using Peis.Model.DTO.External.Permission;
using Peis.Model.DTO.External.Report;
using Peis.Model.DTO.External.ShortMessage;
using Peis.Model.DTO.Register;
using Peis.Utility.Helper;

namespace Peis.Model.AutoMapperProfile;

/// <summary>
/// 继承自Profile类
/// </summary>
public class ExternalProfile : Profile
{
    /// <summary>
    /// 构造函数中实现映射
    /// </summary>
    public ExternalProfile()
    {
        CreateMap<CodeHisChargeItem, CodeHisChargeItemDto>();
        CreateMap<CodeHisChargeItemDto, CodeHisChargeItem>();

        CreateMap<CodeHisOrderItem, CodeHisOrderItemDto>();
        CreateMap<CodeHisOrderItemDto, CodeHisOrderItem>();

        CreateMap<CodeHisOrderItemCharge, CodeHisOrderItemChargeDto>();
        CreateMap<CodeHisOrderItemChargeDto, CodeHisOrderItemCharge>();

        CreateMap<MapCodeItemCombHisChargeItem, MapCodeItemCombHisChargeItemDto>();
        CreateMap<MapCodeItemCombHisChargeItemDto, MapCodeItemCombHisChargeItem>();

        CreateMap<PeReportFile, PeReportFileDto>();
        CreateMap<PeReportFileDto, PeReportFile>();

        CreateMap<SysShortMsgTemplate, SysShortMsgTemplateDto>();
        CreateMap<SysShortMsgTemplateDto, SysShortMsgTemplate>();

        CreateMap<PeSendShortMsgRecord, PeSendShortMsgRecordDto>();
        CreateMap<PeSendShortMsgRecordDto, PeSendShortMsgRecord>();

        CreateMap<v_lis_item, CodeLisItemDto>();
        CreateMap<MapCodeLisItem, CodeLisItemDto>();

        CreateMap<CodeHisDrugItem, CodeHisDrugItemDto>();
        CreateMap<CodeHisDrugItemDto, CodeHisDrugItem>();

        CreateMap<CodeDiseaseEntry, CodeDiseaseEntryDto>();
        CreateMap<CodeDiseaseEntryDto, CodeDiseaseEntry>();

        CreateMap<CodeDisease, CodeDiseaseBackup>();
        CreateMap<CodeDiseaseBackup, CodeDisease>();

        CreateMap<UserInfoExternalDto, UserInfoResponseDto>()
            .ForMember(dest => dest.UserName, opt => opt.MapFrom(src => src.UserCode));

        CreateMap<ExtHisApplyComb, ExtHisApplyCombDto>();
        CreateMap<ExtHisApplyCombDto, ExtHisApplyComb>();

        CreateMap<HATMOrderListQuery, RegisterOrderQuery>();
        CreateMap<RegisterPatient, HATMOrderOutputDto>()
           .ForMember(dest => dest.PeClsName, opt => opt.MapFrom(src => src.PeCls.GetDescription()))
           .ForMember(dest => dest.IsActiveName, opt => opt.MapFrom(src => src.IsActive ? "已激活" : "未激活"));

        CreateMap<HATMPrintInputDto, RegisterGuidanceDto>()
           .ForMember(dest => dest.GuidancePrintTime, opt => opt.MapFrom(src => src.PrintedTime));
    }
}
