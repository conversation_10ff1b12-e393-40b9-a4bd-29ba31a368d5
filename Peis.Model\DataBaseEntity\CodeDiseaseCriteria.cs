﻿using SqlSugar;

namespace Peis.Model.DataBaseEntity
{
    ///<summary>
    ///疾病审核条件表
    ///</summary>
    [SugarTable("CodeDiseaseCriteria")]
    public class CodeDiseaseCriteria
    {
        /// <summary>
        /// 疾病码
        /// </summary>        
        [SugarColumn(IsNullable = false, IsPrimaryKey = true, Length = 8)]
        public string DiseaseCode { get; set; }

        /// <summary>
        /// 关联逻辑（1:And 2:Or）
        /// </summary>        
        [SugarColumn(IsNullable = false)]
        public int Logic { get; set; }
    }
}