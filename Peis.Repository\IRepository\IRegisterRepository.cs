﻿using Peis.Model.DataBaseEntity;
using Peis.Model.DataBaseEntity.Occupation;
using Peis.Model.DTO;
using Peis.Model.Other.PeEnum;
using SqlSugar;
using System;
using System.Collections.Generic;
using System.Linq.Expressions;

namespace Peis.Repository.IRepository
{
    public interface IRegisterRepository
    {
        /// <summary>
        /// 获取订单
        /// </summary>
        /// <param name="regNo"></param>
        /// <returns></returns>
        PeRegister GetByNo(string regNo);

        /// <summary>
        /// 获取未总检订单
        /// </summary>
        /// <param name="patCode"></param>
        /// <param name="cls"></param>
        /// <returns></returns>
        bool HasNotYetSummarized(string patCode, PeCls cls);


        /// <summary>
        /// 获取订单的资料
        /// </summary>
        /// <returns></returns>
        ISugarQueryable<PeRegister> ReadRegister();

        /// <summary>
        /// 获取订单的资料（不按院区过滤）
        /// </summary>
        /// <returns></returns>
        ISugarQueryable<PeRegister> ReadRegisterNoHosp();

        /// <summary>
        /// 获取订单的资料
        /// </summary>
        /// <param name="regNo"></param>
        /// <returns></returns>
        ISugarQueryable<PeRegister> ReadRegister(string regNo);

        /// <summary>
        /// 获取订单的资料（不按院区过滤）
        /// </summary>
        /// <param name="regNo"></param>
        /// <returns></returns>
        ISugarQueryable<PeRegister> ReadRegisterNoHosp(string regNo);

        /// <summary>
        /// 获取回收订单的资料
        /// </summary>
        /// <param name="regNo"></param>
        /// <returns></returns>
        ISugarQueryable<DeletePeRegister> ReadDeleteRegister();

        /// <summary>
        /// 获取职业病信息
        /// </summary>
        /// <returns></returns>
        ISugarQueryable<PeRegisterOccupation> ReadRegisterOccupation();

        /// <summary>
        /// 获取职业病危害因素
        /// </summary>
        /// <returns></returns>
        ISugarQueryable<PeRegisterOccupationHazard> ReadRegisterOccupationHazard();

        /// <summary>
        /// 获取订单资料
        /// </summary>
        /// <param name="companyCode"></param>
        /// <param name="companyTimes"></param>
        /// <param name="clusterCode"></param>
        /// <returns></returns>
        ISugarQueryable<PeRegister> ReadRegister(string companyCode, int companyTimes, string clusterCode);

        /// <summary>
        /// 根据身份证号获取登记记录
        /// </summary>
        /// <param name="cardNos">身份证号码</param>
        /// <returns></returns>
        ISugarQueryable<PeRegister> ReadRegister(params string[] cardNos);

        /// <summary>
        /// 获取订单的资料(聚合查询)
        /// </summary>
        /// <typeparam name="TResult"></typeparam>
        /// <param name="queryValue"></param>
        /// <param name="queryPatNo"></param>
        /// <param name="queryRegNo"></param>
        /// <param name="queryName"></param>
        /// <param name="queryCardNo"></param>
        /// <param name="queryTel"></param>
        /// <param name="selectColumns"></param>
        /// <returns></returns>
        ISugarQueryable<TResult> ReadRegister<TResult>(
            string queryValue,
            bool queryPatNo,
            bool queryRegNo,
            bool queryName,
            bool queryCardNo,
            bool queryTel,
            Expression<Func<PeRegister, TResult>> selectColumns) where TResult : class, new();

        /// <summary>
        /// 获取订单的资料(聚合查询)
        /// </summary>
        /// <typeparam name="TResult"></typeparam>
        /// <param name="isCompanyCheck"></param>
        /// <param name="queryValue"></param>
        /// <param name="queryPatNo"></param>
        /// <param name="queryRegNo"></param>
        /// <param name="queryName"></param>
        /// <param name="queryCardNo"></param>
        /// <param name="queryTel"></param>
        /// <param name="selectColumns"></param>
        /// <returns></returns>
        ISugarQueryable<TResult> ReadRegister<TResult>(
            bool isCompanyCheck,
            string queryValue,
            bool queryPatNo,
            bool queryRegNo,
            bool queryName,
            bool queryCardNo,
            bool queryTel,
            Expression<Func<PeRegister, TResult>> selectColumns) where TResult : class, new();

        /// <summary>
        /// 获取登记时间
        /// </summary>
        /// <param name="regNo"></param>
        /// <returns></returns>
        DateTime? ReadRegisterTime(string regNo);

        /// <summary>
        /// 获取登记时间
        /// </summary>
        /// <param name="regNos"></param>
        /// <param name="startTime"></param>
        /// <param name="endTime"></param>
        /// <returns></returns>
        bool ReadRegisterTime(string[] regNos, out DateTime startTime, out DateTime endTime);

        /// <summary>
        /// 获取登记时间区间
        /// </summary>
        /// <param name="regNo">体检号</param>
        /// <returns>RegisterDateRange</returns>
        RegisterDateRange GetRegisterDateRange(string regNo);

        /// <summary>
        /// 根据激活日期范围获取登记日期范围
        /// </summary>
        /// <param name="activeStartDate"></param>
        /// <param name="activeEndDate"></param>
        /// <returns></returns>
        RegisterDateRange ReadRegDateRangeByActiveTime(DateTime activeStartDate, DateTime activeEndDate);

        /// <summary>
        /// 获取订单数据及套餐
        /// </summary>
        /// <returns></returns>
        ISugarQueryable<PeRegister, PeRegisterCluster> ReadRegisterAndMainCluster(string regNo = "");

        /// <summary>
        /// 获取订单的资料
        /// </summary>
        /// <returns></returns>
        ISugarQueryable<PeRegister, PeRegisterCluster> ReadRegisterOrder();

        /// <summary>
        /// 获取订单的套餐
        /// </summary>
        /// <returns></returns>
        ISugarQueryable<PeRegisterCluster> ReadRegisterClusters();

        /// <summary>
        /// 获取订单的套餐
        /// </summary>
        /// <param name="regNo"></param>
        ISugarQueryable<PeRegisterCluster> ReadRegisterClusters(string regNo);

        /// <summary>
        /// 获取订单的组合
        /// </summary>
        /// <param name="regNo"></param>
        /// <returns></returns>
        ISugarQueryable<PeRegisterComb> ReadRegisterCombs(string regNo);

        /// <summary>
        /// 获取订单的组合
        /// </summary>
        /// <param name="regNo"></param>
        /// <param name="regItemIds"></param>
        /// <returns></returns>
        ISugarQueryable<PeRegisterComb> ReadRegisterCombs(string regNo, params long[] regItemIds);

        /// <summary>
        /// 获取订单的组合
        /// </summary>
        /// <param name="regNo"></param>
        /// <param name="combCodes"></param>
        /// <returns></returns>
        ISugarQueryable<PeRegisterComb> ReadRegisterCombs(string regNo, params string[] combCodes);

        /// <summary>
        /// 获取订单的组合（收费状态过滤）
        /// </summary>
        /// <param name="regNo"></param>
        /// <param name="payStatus"></param>
        /// <returns></returns> 
        ISugarQueryable<PeRegisterComb> ReadRegisterCombs(string regNo, PayStatus payStatus, params long[] regItemIds);

        /// <summary>
        /// 获取订单的组合（自费的组合）
        /// </summary>
        /// <param name="regNo"></param>
        /// <param name="payStatus"></param>
        /// <returns></returns> 
        ISugarQueryable<PeRegisterComb> ReadRegisterCombs(string regNo, PayStatus payStatus);

        /// <summary>
        /// 获取订单的组合（收费状态、组合码过滤）
        /// </summary>
        /// <param name="regNo"></param>
        /// <param name="comCodes"></param>
        /// <param name="payStatus"></param>
        /// <returns></returns>
        ISugarQueryable<PeRegisterComb> ReadRegisterCombs(string regNo, string[] comCodes, PayStatus payStatus);

        /// <summary>
        /// 获取订单的组合
        /// </summary>
        /// <param name="regNos"></param>
        /// <returns></returns>
        ISugarQueryable<PeRegisterComb> ReadRegisterCombs(string[] regNos);

        /// <summary>
        /// 获取订单的组合
        /// </summary>
        /// <param name="start"></param>
        /// <param name="end"></param>
        /// <returns></returns>
        ISugarQueryable<PeRegisterComb> ReadRegisterCombs(DateTime start, DateTime end);
        /// <summary>
        /// 获取订单的组合
        /// </summary>
        /// <param name="start"></param>
        /// <param name="end"></param>
        /// <param name="whereExpression"></param>
        /// <returns></returns>
        ISugarQueryable<PeRegisterComb> ReadRegisterCombs(DateTime start, DateTime end, Expression<Func<PeRegisterComb, bool>> whereExpression);

        ISugarQueryable<PeRegisterComb, SingleColumnEntity<string>> ReadRegisterCombs2(string[] regNos);

        /// <summary>
        /// 获取订单的组合费用分类
        /// </summary>
        /// <param name="regNos"></param>
        /// <returns></returns>
        ISugarQueryable<CodeItemComb, PeRegisterComb> ReadRegisterCombsFeeCls(string[] regNos);

        /// <summary>
        /// 更新试管组合的归属关系
        /// </summary>
        /// <param name="testTubes"></param>
        void UpdateTestTubeBeFrom(List<PeRegisterComb> testTubes);

        /// <summary>
        /// 更新组合
        /// </summary>
        /// <param name="regNo"></param>
        /// <returns></returns>
        IUpdateable<PeRegisterComb> UpdateRegisterComb(string regNo);

        /// <summary>
        /// 更新组合
        /// </summary>
        /// <param name="combs"></param>
        /// <param name="updateColumns"></param>
        void UpdateColumnsRegisterComb(PeRegisterComb[] combs, Expression<Func<PeRegisterComb, object>> updateColumns);

        /// <summary>
        /// 删除登记组合项目
        /// </summary>
        /// <param name="startTime"></param>
        /// <param name="endTime"></param>
        /// <param name="regNos"></param>
        void DeleteRegisterComb(DateTime startTime, DateTime endTime, string[] regNos);

        /// <summary>
        /// 添加登记组合项目
        /// </summary>
        /// <param name="peRegisterCombs"></param>
        void InsertRegisterComb(List<PeRegisterComb> peRegisterCombs);

        /// <summary>
        /// 获取订单数据及组合
        /// </summary>
        /// <param name="registerTime"></param>
        /// <returns></returns>
        ISugarQueryable<PeRegister, PeRegisterComb> ReadRegisterOrder(DateTime registerTime);

        /// <summary>
        /// 获取订单数据
        /// </summary>
        /// <param name="beginDate"></param>
        /// <param name="endDate"></param>
        /// <returns></returns>
        ISugarQueryable<PeRegister, PeRegisterComb> ReadRegisterOrder(DateTime beginDate, DateTime endDate);

        /// <summary>
        /// 获取登记组合明细
        /// </summary>
        /// <param name="startTime">登记时间-开始</param>
        /// <param name="endTime">登记时间-结束</param>
        /// <returns>ISugarQueryable</returns>
        ISugarQueryable<PeRegister, PeRegisterComb> QueryPeRegCombs(DateTime startTime, DateTime endTime);

        /// <summary>
        /// 读取组合与费用分类明细
        /// </summary>
        /// <param name="regNo"></param>
        /// <returns></returns>
        ISugarQueryable<PeRegisterComb, CodeItemComb, CodeFeeCls> ReadCombAndFeeClsDetails(string regNo);

        /// <summary>
        /// 读取登记套餐组合数据
        /// </summary>
        /// <param name="beginDate"></param>
        /// <param name="endDate"></param>
        /// <returns></returns>
        ISugarQueryable<PeRegister, PeRegisterCluster, MapClusterComb, PeRegisterComb> ReadClusterAndComb(DateTime beginDate, DateTime endDate);

        /// <summary>
        /// 体检人员费用列表使用
        /// </summary>
        /// <param name="beginDate"></param>
        /// <param name="endDate"></param>
        /// <returns></returns>
        ISugarQueryable<PeRegister, PeRegisterComb, PeRegisterCluster, MapClusterComb> ReadPersonnelFeeList(DateTime beginDate, DateTime endDate);

        /// <summary>
        /// 获取检查项目的组合明细信息（非材料、治疗费等）
        /// </summary>
        /// <param name="regNo"></param>
        /// <param name="reportShow"></param>
        /// <returns></returns>
        ISugarQueryable<PeRegisterComb, CodeItemComb, CodeBarcodeType> ReadExamTypeCombDetail(string regNo, bool reportShow = true);

        /// <summary>
        /// 获取登记组合的项目分类
        /// </summary>
        /// <param name="regNo"></param>
        /// <returns></returns>
        ISugarQueryable<PeRegisterComb, CodeItemCls> ReadRegCombsItemCls(string regNo);

        /// <summary>
        /// 获取单位人员登记数据
        /// </summary>
        /// <returns></returns>
        ISugarQueryable<PeRegister, PeRegisterCluster, CodeCompany> ReadCompanyRegData();

        /// <summary>
        /// 获取上级复查订单数据
        /// </summary>
        /// <param name="regNo"></param>
        /// <returns></returns>
        string ReadSuperiorRegNo(string regNo);
    }
}
