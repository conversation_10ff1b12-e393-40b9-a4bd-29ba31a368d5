﻿using Peis.Model.Other.PeEnum;
using Peis.Model.TableFilter;

namespace Peis.Model.DataBaseEntity
{
    ///<summary>
    ///组合代码
    ///</summary>
    [SugarTable("CodeItemComb")]
    public class CodeItemComb: IHospCodeFilter
    {
        /// <summary>
        /// 组合代码
        /// </summary>        
        [SugarColumn(IsNullable = false, IsPrimaryKey = true, Length = 8)]
        public string CombCode { get; set; }

        /// <summary>
        /// 组合名称
        /// </summary>        
        [SugarColumn(IsNullable = false, Length = 200)]
        public string CombName { get; set; }

        /// <summary>
        /// 组合简称
        /// </summary>        
        [SugarColumn(IsNullable = true, Length = 100)]
        public string ShortName { get; set; }

        /// <summary>
        /// 性别（0 通用 1 男  2 女）
        /// </summary>        
        [SugarColumn(IsNullable = false)]
        public Sex Sex { get; set; }

        /// <summary>
        /// 项目分类
        /// </summary>        
        [SugarColumn(IsNullable = false, Length = 6)]
        public string ClsCode { get; set; }

        /// <summary>
        /// 基础分类
        /// </summary>        
        [SugarColumn(IsNullable = true, Length = 4)]
        public string FeeBasicCls { get; set; }

        /// <summary>
        /// 检查分类
        /// </summary>        
        [SugarColumn(IsNullable = false)]
        public CheckCls CheckCls { get; set; }

        /// <summary>
        /// 提示信息
        /// </summary>        
        [SugarColumn(IsNullable = true, Length = 200)]
        public string Hint { get; set; }

        /// <summary>
        /// 意义说明(备注)
        /// </summary>        
        [SugarColumn(IsNullable = true, Length = 255)]
        public string Note { get; set; }

        /// <summary>
        /// 单价
        /// </summary>        
        [SugarColumn(IsNullable = false)]
        public decimal Price { get; set; }

        /// <summary>
        /// 折扣标识
        /// </summary>        
        [SugarColumn(IsNullable = false)]
        public bool DiscountAllow { get; set; }

        /// <summary>
        /// 条码类型
        /// </summary>        
        [SugarColumn(IsNullable = true, Length = 4)]
        public string BarCodeType { get; set; }

        /// <summary>
        /// 检查限制（1无限制2餐前3餐后）
        /// </summary>        
        [SugarColumn(IsNullable = false)]
        public CheckLimit CheckLimit { get; set; }

        /// <summary>
        /// 检查科室
        /// </summary>        
        [SugarColumn(IsNullable = false, Length = 6)]
        public string ExamDeptCode { get; set; }

        /// <summary>
        /// 采集地点（如：采血）
        /// </summary>        
        [SugarColumn(IsNullable = true, Length = 6)]
        public string GatherPlace { get; set; }

        /// <summary>
        /// 报告中显示
        /// </summary>        
        [SugarColumn(IsNullable = false)]
        public bool ReportShow { get; set; }

        /// <summary>
        /// His码
        /// </summary>        
        [SugarColumn(IsNullable = true, Length = 20)]
        public string HisCode { get; set; }

        /// <summary>
        /// 检查部位
        /// </summary>        
        [SugarColumn(IsNullable = true, Length = 20)]
        public string BwName { get; set; }

        /// <summary>
        /// 检查部位大类
        /// </summary>        
        [SugarColumn(IsNullable = true, Length = 20)]
        public string BwDlName { get; set; }

        /// <summary>
        /// 微信加项允许标识
        /// </summary>        
        [SugarColumn(IsNullable = false)]
        public bool WeChatAddAllow { get; set; }

        /// <summary>
        /// 不适宜人群
        /// </summary>        
        [SugarColumn(IsNullable = true, Length = 200)]
        public string UnsuitableCrowd { get; set; }

        /// <summary>
        /// 注意事项
        /// </summary>        
        [SugarColumn(IsNullable = true, Length = 200)]
        public string Attention { get; set; }

        /// <summary>
        /// 显示顺序
        /// </summary>        
        [SugarColumn(IsNullable = false)]
        public int SortIndex { get; set; }

        /// <summary>
        /// 启用标识
        /// </summary>        
        [SugarColumn(IsNullable = false)]
        public bool IsEnabled { get; set; }

        /// <summary>
        /// 拼音码
        /// </summary>        
        [SugarColumn(IsNullable = true, Length = 10)]
        public string PinYinCode { get; set; }

        /// <summary>
        /// 五笔码
        /// </summary>        
        [SugarColumn(IsNullable = true, Length = 10)]
        public string WuBiCode { get; set; }

        /// <summary>
        /// 并管标志
        /// </summary>
        [SugarColumn(IsNullable = false)]
        public bool IsMergeTube { get; set; }

        /// <summary>
        /// 是否合并体检标签
        /// </summary>
        [SugarColumn(IsNullable = false)]
        public bool IsMergePeLabel { get; set; }

        /// <summary>
        /// 是否打印体检标签
        /// </summary>
        [SugarColumn(IsNullable = false)]
        public bool IsPeLabelPrintable { get; set; }

        /// <summary>
        /// 院区编码
        /// </summary>        
        [SugarColumn(IsNullable = false, Length = 1)]
        public string HospCode { get; set; }

        /// <summary>
        /// 组合扩展
        /// </summary>
        [SugarColumn(IsNullable = true, Length = 255, IsJson = true)]
        public CombExtension[] CombExtensions { get; set; }

        #region 非数据库字段
        /// <summary>
        /// His-医嘱项目CODE
        /// </summary>        
        [SugarColumn(IsIgnore = true)]
        public string HisOrderCode { get; set; }

        /// <summary>
        /// His-医嘱项目中文名称
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public string HisOrderCNName { get; set; }

        /// <summary>
        /// 是否体检专用（用于公告提示过滤）
        /// </summary>        
        [SugarColumn(IsIgnore = true)]
        public bool IsDedicatePies { get; init; }

        [SugarColumn(IsIgnore = true)]
        public List<MapItemComb> MapItemCombs { get; set; }

        /// <summary>
        /// 项目分类名称
        /// </summary>        
        [SugarColumn(IsIgnore = true)]
        public string ClsName { get; set; }

        /// <summary>
        /// 项目分类排序
        /// </summary>        
        [SugarColumn(IsIgnore = true)]
        public int ClsSortIndex { get; set; }
        #endregion
    }
}