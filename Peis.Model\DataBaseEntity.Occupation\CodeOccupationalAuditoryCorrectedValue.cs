﻿using Peis.Model.Other.PeEnum;

namespace Peis.Model.DataBaseEntity.Occupation
{
    /// <summary>
    /// 听力校正值维护表
    /// </summary>
    [SugarTable]
    public class CodeOccupationalAuditoryCorrectedValue
    {
        /// <summary>
        /// 编码
        /// </summary>
        [SugarColumn(Length =6,IsPrimaryKey =true)]
        public String CorrectedValueCode { get; set; }
        /// <summary>
        /// 性别
        /// </summary>
        [SugarColumn(IsNullable =false)]
        public Sex Sex { get; set; }
        /// <summary>
        /// 下限
        /// </summary>        
        [SugarColumn(IsNullable = false)]
        public int AgeLowerLimit { get; set; }
        /// <summary>
        /// 上限
        /// </summary>        
        [SugarColumn(IsNullable = false)]
        public int AgeUpperLimit { get; set; }
        /// <summary>
        /// 500频率校正值
        /// </summary>
        [SugarColumn(IsNullable = false)]
        public int CorrectedValue500 { get; set; }
        /// <summary>
        /// 1000频率校正值
        /// </summary>
        [SugarColumn(IsNullable = false)]
        public int CorrectedValue1000 { get; set; }
        /// <summary>
        /// 2000频率校正值
        /// </summary>
        [SugarColumn(IsNullable = false)]
        public int CorrectedValue2000 { get; set; }
        /// <summary>
        /// 3000频率校正值
        /// </summary>
        [SugarColumn(IsNullable = false)]
        public int CorrectedValue3000 { get; set; }
        /// <summary>
        /// 4000频率校正值
        /// </summary>
        [SugarColumn(IsNullable = false)]
        public int CorrectedValue4000 { get; set; }
        /// <summary>
        /// 6000频率校正值
        /// </summary>
        [SugarColumn(IsNullable = false)]
        public int CorrectedValue6000 { get; set; }
    }
}
