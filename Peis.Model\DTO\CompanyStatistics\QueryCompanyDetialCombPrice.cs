﻿using Peis.Model.Other.PeEnum;

namespace Peis.Model.DTO.CompanyStatistics;

public class QueryCompanyDetialCombPrice : StatisticalQueryBase
{
    /// <summary>
    /// 单位次数
    /// </summary>
    public int CompanyTimes { get; set; }

    /// <summary>
    /// 单位部门编码
    /// </summary>        
    public string CompanyDeptCode { get; set; }

    /// <summary>
    /// 0 按全部组合 1 按实际组合
    /// </summary>
    public int Type { get; set; }
    /// <summary>
    /// 套餐代码
    /// </summary>
    public string ClusterCode { get; set; }

    /// <summary>
    /// 时间类型
    /// </summary>
    public TimeType TimeType { get; set; }
}
