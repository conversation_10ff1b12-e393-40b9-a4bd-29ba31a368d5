﻿using Microsoft.AspNetCore.Mvc;
using Peis.Model.DataBaseEntity;
using Peis.Model.DTO;
using Peis.Model.DTO.External.His;
using Peis.Model.Other.Input;
using Peis.Service.IService;
using Peis.Service.IService.ExternalSystem;
using System;

namespace Peis.API.Controllers.Business
{
    /// <summary>
    /// 收银台
    /// </summary>
    [Route("api/[controller]")]
    [ApiController]
    public class CheckStandController : BaseApiController
    {
        private readonly IFeeService _feeService;
        private readonly IExternalSystemOrderService _externalSystemOrderService;

        public CheckStandController(IFeeService checkStandService, 
            IExternalSystemOrderService externalSystemOrderService)
        {
            _feeService = checkStandService;
            _externalSystemOrderService = externalSystemOrderService;
        }

        /// <summary>
        /// 获取个人信息列表
        /// </summary>
        /// <param name="name"></param>
        /// <param name="cardNo"></param>
        /// <returns></returns>
        [HttpPost("GetPersonInfo")]
        [ProducesResponseType(typeof(RegisterInfo[]), 200)]
        public IActionResult GetPersonInfo([FromQuery] string name, [FromQuery] string cardNo)
        {
            try
            {
                string msg = string.Empty;
                result.ReturnData = _feeService.GetPersonInfo(name, cardNo, ref msg);
            }
            catch (Exception e)
            {
                result.Success = false;
                result.ReturnMsg = e.Message;
            }
            return Ok(result);
        }

        /// <summary>
        /// 获取组合/材料费(折扣管理)
        /// </summary>
        /// <param name="regNo"></param>
        /// <returns></returns>
        [HttpPost("GetRegisterInfoAndComb")]
        [ProducesResponseType(typeof(RegisterData), 200)]
        public IActionResult GetRegisterInfoAndComb([FromQuery] string regNo)
        {
            try
            {
                string msg = string.Empty;
                result.ReturnData = _feeService.GetRegisterInfoAndComb(regNo, ref msg);
            }
            catch (Exception e)
            {
                result.Success = false;
                result.ReturnMsg = e.Message;
            }
            return Ok(result);
        }

        /// <summary>
        /// 固定金额(一口价)
        /// </summary>
        /// <param name="reductioPrice"></param>
        /// <returns></returns>
        [HttpPost("FixedPrice")]
        public IActionResult FixedPrice([FromBody] FixPrice reductioPrice)
        {
            try
            {
                string msg = string.Empty;
                var flag = _feeService.ProjectsFixedPrice(reductioPrice, ref msg);
                if (!flag)
                {
                    result.Success = false;
                    result.ReturnMsg = msg;
                }
            }
            catch (Exception e)
            {
                result.Success = false;
                result.ReturnMsg = e.Message;
            }
            return Ok(result);
        }

        /// <summary>
        /// 减免项目金额(折扣)
        /// </summary>
        /// <param name="reductioPrice">Discount:折扣率</param>
        /// <returns></returns>
        [HttpPost("ReductioProjectsPrice")]
        public IActionResult ReductioProjectsPrice([FromBody] DiscountPrice reductioPrice)
        {
            try
            {
                string msg = string.Empty;
                var flag = _feeService.ReductioProjectsPrice(reductioPrice, ref msg);
                if (!flag)
                {
                    result.Success = false;
                    result.ReturnMsg = msg;
                }
            }
            catch (Exception e)
            {
                result.Success = false;
                result.ReturnMsg = e.Message;
            }
            return Ok(result);
        }

        /// <summary>
        /// 恢复项目单价
        /// </summary>
        /// <param name="reductioPrice">只需要传regno,Ids项目集合</param>
        /// <returns></returns>
        [HttpPost("RecoverCombPrice")]
        public IActionResult RecoverCombPrice([FromBody] RecoverPrice reductioPrice)
        {
            try
            {
                string msg = string.Empty;
                var flag = _feeService.RecoverCombPrice(reductioPrice, ref msg);
                if (!flag)
                {
                    result.Success = false;
                    result.ReturnMsg = msg;
                }
            }
            catch (Exception e)
            {
                result.Success = false;
                result.ReturnMsg = e.Message;
            }
            return Ok(result);
        }

        /// <summary>
        /// 查询发票段
        /// </summary>
        /// <param name="operCode"></param>
        /// <returns></returns>
        [HttpPost("ReadInvoiceSegment")]
        [ProducesResponseType(typeof(FeeInvoiceAllocate[]), 200)]
        public IActionResult ReadInvoiceSegment([FromQuery] string operCode)
        {
            string msg = string.Empty;
            result.ReturnData = _feeService.ReadInvoiceData("InvoiceSegment", operCode, ref msg);
            result.ReturnMsg = msg;
            return Ok(result);
        }

        /// <summary>
        /// 根据操作员查询当前发票号
        /// </summary>
        /// <param name="operCode"></param>
        /// <returns></returns>
        [HttpPost("QueryCurrentInvoice")]
        [ProducesResponseType(typeof(FeeInvoiceAllocate), 200)]
        public IActionResult QueryCurrentInvoice([FromQuery] string operCode)
        {
            string msg = string.Empty;

            result.ReturnData = _feeService.ReadInvoiceData("CurrentInvoice", operCode, ref msg);
            result.Success = string.IsNullOrEmpty(msg);
            result.ReturnMsg = msg;
            return Ok(result);
        }

        /// <summary>
        /// 确认新发票号(收费首页验证发票号是否正确)
        /// </summary>
        /// <param name="newInvoice"></param>
        /// <returns></returns>
        [HttpPost("ConfirmNewInvoice")]
        public IActionResult ConfirmNewInvoice([FromBody] ConfirmNewInvoice newInvoice)
        {
            string msg = string.Empty;

            result.ReturnData = _feeService.ConfirmNewInvoice(newInvoice, ref msg);
            result.Success = string.IsNullOrEmpty(msg);
            result.ReturnMsg = msg;
            return Ok(result);
        }

        /// <summary>
        /// 个人收费---获取未缴费列表
        /// </summary>
        /// <param name="registerTime">登记日期registerTime</param>
        /// <param name="regNo">体检号regNo</param>
        /// <returns></returns>
        [HttpPost("ReadUnpaidList")]
        [ProducesResponseType(typeof(UnpaidPersonInfo[]), 200)]
        public IActionResult ReadUnpaidList([FromQuery] DateTime? registerTime, [FromQuery] string regNo)
        {
            var data = Array.Empty<UnpaidPersonInfo>();
            if (!_feeService.ReadUnpaidList(registerTime, regNo, ref data))
            {
                result.Success = false;
                result.ReturnMsg = "暂无数据";
                return Ok(result);
            }
            result.ReturnData = data;
            return Ok(result);
        }

        /// <summary>
        /// 个人收费---获取未缴费组合及基础分类
        /// </summary>
        /// <param name="regNo"></param>
        /// <returns></returns>
        [HttpPost("ReadUnpaidCombsAndBasicCls")]
        [ProducesResponseType(typeof(UnpaidCombsAndBasicCls), 200)]
        public IActionResult ReadUnpaidCombsAndBasicCls([FromQuery] string regNo)
        {
            result.ReturnData = _feeService.ReadUnpaidCombsAndBasicCls(regNo);
            return Ok(result);
        }

        /// <summary>
        /// 获取结算记录列表(多筛查条件)
        /// </summary>
        /// <param name="filters"></param>
        /// <response code="200">执行成功</response>
        /// <returns></returns>
        [HttpPost("ReadSettlementRecordsByMultipleFilter")]
        [ProducesResponseType(typeof(SettlementRecords), 200)]
        public IActionResult ReadSettlementRecordsByMultipleFilter([FromBody] SettlementRecordsMultipleFilters filters)
        {
            var record = new SettlementRecords();
            var msg = string.Empty;
            if (!_feeService.ReadSettlementRecordsByMultipleFilter(filters, ref record, ref msg))
            {
                result.Success = false;
                result.ReturnMsg = msg;
                return Ok(result);
            }

            result.ReturnData = record;
            return Ok(result);
        }

        /// <summary>
        /// 获取发票号信息，用于修改发票号
        /// </summary>
        /// <param name="invoiceNoQuery"></param>
        /// <returns></returns>
        [HttpPost("GetInvoiceNoInfo")]
        [ProducesResponseType(typeof(InvoiceNoInfo[]), 200)]
        public IActionResult GetInvoiceNoInfo([FromBody] InvoiceNoQuery invoiceNoQuery)
        {
            result.ReturnData = _feeService.GetInvoiceNoInfo(invoiceNoQuery);

            return Ok(result);
        }

        /// <summary>
        /// 修改发票
        /// </summary>
        /// <param name="invoiceNoUpdate"></param>
        /// <returns></returns>
        [HttpPost("UpdateInvoiceNo")]
        public IActionResult UpdateInvoiceNo([FromBody] InvoiceNoUpdate invoiceNoUpdate)
        {
            result.Success = _feeService.UpdateInvoiceNo(invoiceNoUpdate, out string msg);

            result.ReturnMsg = msg;
            return Ok(result);
        }

        /// <summary>
        /// 获取结算组合
        /// </summary>
        /// <param name="settlementNo">结算号</param>
        /// <response code="200">执行成功</response>
        /// <returns></returns>
        [HttpPost("ReadSettlementCombs")]
        [ProducesResponseType(typeof(SettlementComb), 200)]
        public IActionResult ReadSettlementCombs([FromQuery] string settlementNo)
        {
            result.ReturnData = _feeService.ReadSettlementCombs(settlementNo);
            return Ok(result);
        }

        /// <summary>
        /// 创建结算
        /// </summary>
        /// <param name="regNo">体检号</param>
        /// <returns>返回所有支付方式以及已支付状态</returns>
        [HttpPost("CreateSettlement")]
        [ProducesResponseType(typeof(SettlementPlan), 200)]
        public IActionResult CreateSettlement([FromQuery] string regNo)
        {
            result.Success = _feeService.CreateSettlement(regNo, out SettlementPlan settlement, out string msg);
            result.ReturnData = settlement;
            result.ReturnMsg = msg;
            return Ok(result);
        }

        /// <summary>
        /// 单个支付
        /// </summary>
        /// <param name="pay">支付信息</param>
        /// <returns>支付状态信息</returns>
        [HttpPost("SinglePay")]
        [ProducesResponseType(typeof(PaymentPlan), 200)]
        public IActionResult SinglePay([FromBody] Payment pay)
        {
            result.Success = _feeService.SinglePay(pay, out PaymentPlan payed, out string msg);
            result.ReturnData = payed;
            result.ReturnMsg = msg;
            return Ok(result);
        }

        /// <summary>
        /// 完成结算
        /// </summary>
        /// <param name="settle">结算信息</param>
        /// <returns></returns>
        [HttpPost("FinishSettlement")]
        public IActionResult FinishSettlement([FromBody] Settlement settle)
        {
            result.Success = _feeService.FinishSettlement(settle, out string msg);
            result.ReturnMsg = msg;
            return Ok(result);
        }

        /// <summary>
        /// 取消所有支付
        /// </summary>
        /// <param name="settleNo">结算号</param>
        /// <returns></returns>
        [HttpPost("CancelPayAll")]
        public IActionResult CancelPayAll([FromQuery] string settleNo)
        {
            result.Success = _feeService.CancelPayAll(settleNo, out string msg);
            result.ReturnMsg = msg;
            return Ok(result);
        }

        /// <summary>
        /// 取消支付
        /// </summary>
        /// <param name="settleNo">结算号</param>
        /// <param name="payID">支付ID</param>
        /// <returns></returns>
        [HttpPost("CancelPaySingle")]
        public IActionResult CancelPaySingle([FromQuery] string settleNo, [FromQuery] int payID)
        {
            result.Success = _feeService.CancelPaySingle(settleNo, payID, out string msg);
            result.ReturnMsg = msg;
            return Ok(result);
        }

        /// <summary>
        /// 退款
        /// </summary>
        /// <param name="settleNo"></param>
        /// <returns></returns>
        [HttpPost("Refund")]
        public IActionResult Refund([FromQuery] string settleNo)
        {
            result.Success = _feeService.Refund(settleNo, out string msg);
            result.ReturnMsg = msg;
            return Ok(result);
        }

        /// <summary>
        /// 发送HIS退费申请
        /// </summary>
        /// <param name="hisRefund"></param>
        /// <returns></returns>
        [HttpPost("HisRefund")]
        public IActionResult HisRefund([FromBody] HisRefund hisRefund)
        {
            string msg;
            result.Success = _externalSystemOrderService.OrderRefund(hisRefund.RegNo, hisRefund.combs, out msg);
            result.ReturnMsg = msg;
            return Ok(result);
        }

        /// <summary>
        /// 撤销HIS退费申请
        /// </summary>
        /// <param name="regNo"></param>
        /// <returns></returns>
        [HttpPost("CancelHisRefund")]
        public IActionResult CancelHisRefund([FromQuery] string regNo)
        {
            string msg;
            result.Success = _externalSystemOrderService.CancelOrderRefund(regNo, out msg);
            result.ReturnMsg = msg;
            return Ok(result);
        }

        #region 日结
        /// <summary>
        /// 获取日结报表
        /// </summary>
        /// <param name="settlementDailyQuery"></param>
        /// <returns></returns>
        [HttpPost("GetReportSettlementDaily")]
        [ProducesResponseType(typeof(ReportSettlementDaily), 200)]
        public IActionResult GetReportSettlementDaily([FromBody] SettlementDailyQuery settlementDailyQuery)
        {
            result.ReturnData = _feeService.GetReportSettlementDaily(settlementDailyQuery);
            return Ok(result);
        }

        /// <summary>
        /// 保存日结记录
        /// </summary>
        /// <param name="settlementDailyQuery"></param>
        /// <returns></returns>
        [HttpPost("SaveSettlementDaily")]
        public IActionResult SaveSettlementDaily([FromBody] SettlementDailyQuery settlementDailyQuery)
        {
            result.Success = _feeService.SaveSettlementDaily(settlementDailyQuery, out string msg);
            result.ReturnMsg = msg;
            return Ok(result);
        }

        /// <summary>
        /// 获取日结记录
        /// </summary>
        /// <param name="beginOperateDate">开始操作日期</param>
        /// <param name="endOperateDate">结束操作日期</param>
        /// <param name="operatorCode">操作员编码</param>
        /// <returns></returns>
        [HttpPost("GetSettlementDaily")]
        public IActionResult GetSettlementDaily([FromQuery] DateTime beginOperateDate, [FromQuery] DateTime endOperateDate, [FromQuery] string operatorCode)
        {
            result.ReturnData = _feeService.GetSettlementDaily(beginOperateDate, endOperateDate, operatorCode);
            return Ok(result);
        }

        /// <summary>
        /// 删除日结记录
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpPost("DeleteSettlementDaily")]
        public IActionResult DeleteSettlementDaily([FromQuery] int id)
        {
            result.Success = _feeService.DeleteSettlementDaily(id, out string msg);
            result.ReturnMsg = msg;
            return Ok(result);
        }
        #endregion
    }
}
