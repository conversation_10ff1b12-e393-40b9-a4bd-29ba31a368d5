﻿using SqlSugar;

namespace Peis.Model.DataBaseEntity
{
    ///<summary>
    ///支付方式信息（收费类代码）
    ///</summary>
    [SugarTable("CodePayment")]
    public class CodePayment
    {
        /// <summary>
        /// 支付代码
        /// </summary>        
        [SugarColumn(IsNullable = false, IsPrimaryKey = true, Length = 10)]
        public string PayCode { get; set; }

        /// <summary>
        /// 支付名称
        /// </summary>        
        [SugarColumn(IsNullable = false, Length = 40)]
        public string PayName { get; set; }

        /// <summary>
        /// 是否本位币
        /// </summary>        
        [SugarColumn(IsNullable = false)]
        public bool IsBaseCurrency { get; set; }

        /// <summary>
        /// 级别
        /// </summary>        
        [SugarColumn(IsNullable = false)]
        public int Grade { get; set; }

        /// <summary>
        /// 父代码
        /// </summary>        
        [SugarColumn(IsNullable = false, Length = 10)]
        public string Parent { get; set; }

        /// <summary>
        /// 汇率
        /// </summary>        
        [SugarColumn(IsNullable = true, Length = 10, DecimalDigits = 4)]
        public decimal? Rate { get; set; }

        /// <summary>
        /// 是否记账
        /// </summary>        
        [SugarColumn(IsNullable = false)]
        public bool IsAcc { get; set; }

        /// <summary>
        /// 是否末节
        /// </summary>        
        [SugarColumn(IsNullable = false)]
        public bool IsTerminal { get; set; }
    }
}