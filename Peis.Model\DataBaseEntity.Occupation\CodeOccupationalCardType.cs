﻿namespace Peis.Model.DataBaseEntity.Occupation
{
    /// <summary>
    /// 职业病证件类型字典
    /// </summary>
    [SugarTable]
    public class CodeOccupationalCardType
    {
        /// <summary>
        /// 证件类型代码
        /// </summary>
        [SugarColumn(Length =2,IsPrimaryKey = true)]
        public string CardTypeCode { get; set; }
        /// <summary>
        /// Peis.Model.DataBaseEntity.ZYB
        /// </summary>
        [SugarColumn(Length =30,IsNullable = false)]
        public string CardTypeName { get; set; }
    }
}
