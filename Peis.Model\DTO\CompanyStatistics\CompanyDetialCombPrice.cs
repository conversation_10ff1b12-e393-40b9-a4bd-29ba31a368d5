﻿using Peis.Model.Other.PeEnum;
using System.Text.Json.Serialization;

namespace Peis.Model.DTO.CompanyStatistics
{
    public class CompanyDetialCombPrice
    {
        /// <summary>
        /// 体检号
        /// </summary>
        public string RegNo { get; set; }

        /// <summary>
        /// 姓名
        /// </summary>
        public string Name { get; set; }

        /// <summary>
        /// 性别
        /// </summary>
        public Sex? Sex { get; set; }

        /// <summary>
        /// 年龄
        /// </summary>
        public int? Age { get; set; }

        /// <summary>
        /// 套餐名
        /// </summary>
        public string ClusterName { get; set; }

        /// <summary>
        /// 组合代码
        /// </summary>
        public string CombCode { get; set; }

        /// <summary>
        /// 组合名称
        /// </summary>
        public string CombName { get; set; }

        /// <summary>
        /// 组合金额
        /// </summary>
        public decimal Price { get; set; }

        [JsonIgnore]
        public long Id { get; set; }

        [SugarColumn(IsJson = true)]
        public string[] Befrom { get; set; }
        [JsonIgnore]
        public string ClsCode { get; set; }
    }
}
