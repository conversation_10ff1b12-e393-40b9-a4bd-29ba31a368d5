﻿using Peis.Model.DTO.External.BaiHui;

namespace Peis.External.Platform.Service.Service.IService.BaiHui;

/// <summary>
/// 百慧平台：基础服务接口
/// </summary>
public interface IBHBasicService
{
    /// <summary>
    /// 获取Token
    /// </summary>
    /// <returns>string</returns>
    string GetToken();

    /// <summary>
    /// 清除缓存中的token
    /// </summary>
    void ClearToken();

    /// <summary>
    /// PostMsgTransfer请求
    /// </summary>
    /// <param name="requestMsg">BHRequestMsg-入参内容</param>
    /// <returns>BHApiReturn</returns>
    BHApiReturn<T> PostMsgTransfer<T>(BHRequestMsg requestMsg);

    /// <summary>
    /// PostMsgTransfer 分页数据请求
    /// </summary>
    /// <param name="requestMsg">BHRequestMsg-入参内容</param>
    /// <returns>BHApiPageResult</returns>
    BHApiPageResult<BHApiPageData<T>> PostMsgTransferPage<T>(BHRequestMsg requestMsg);

    /// <summary>
    /// 发起Opss接口Post请求
    /// </summary>
    /// <typeparam name="T">请求参数</typeparam>
    /// <param name="reqMsg">请求内容</param>
    /// <returns>业务级BHApiOpssContent</returns>
    /// <exception cref="BusinessException"></exception>
    BHApiOpssContent PostOpss<T>(BHApiOpssReqMsg<T> reqMsg);
}
