using Peis.Model.Other.PeEnum;
using SqlSugar;
using System;

namespace Peis.Model.DataBaseEntity
{
    ///<summary>
    ///登记组合表
    ///</summary>
    [SplitTable(SplitType.Year)]
    [SugarTable("PeRegisterComb_{yyyy}", "登记组合表")]
    [SugarIndex("index_RegNo_", nameof(RegNo), OrderByType.Asc)]
    public class PeRegisterComb
    {
        /// <summary>
        /// 雪花Id
        /// </summary>        
        [SugarColumn(IsNullable = false, IsPrimaryKey = true)]
        public long Id { get; set; }

        /// <summary>
        /// 体检号
        /// </summary>        
        [SugarColumn(IsNullable = false, Length = 12)]
        public string RegNo { get; set; }

        /// <summary>
        /// 组合代码
        /// </summary>        
        [SugarColumn(IsNullable = false, Length = 8)]
        public string CombCode { get; set; }

        /// <summary>
        /// 组合名称
        /// </summary>        
        [SugarColumn(IsNullable = false, Length = 200)]
        public string CombName { get; set; }

        /// <summary>
        /// 组合排序
        /// </summary>        
        [SugarColumn(IsNullable = false)]
        public int CombSortIndex { get; set; }

        /// <summary>
        /// 检查科室代码
        /// </summary>        
        [SugarColumn(IsNullable = false, Length = 6)]
        public string ExamDeptCode { get; set; }

        /// <summary>
        /// 检查分类
        /// </summary>        
        [SugarColumn(IsNullable = false)]
        public CheckCls CheckCls { get; set; }

        /// <summary>
        /// 项目分类
        /// </summary>        
        [SugarColumn(IsNullable = false, Length = 6)]
        public string ClsCode { get; set; }

        /// <summary>
        /// 项目分类名称
        /// </summary>        
        [SugarColumn(IsNullable = false, Length = 30)]
        public string ClsName { get; set; }

        /// <summary>
        /// 项目分类组合排序
        /// </summary>        
        [SugarColumn(IsNullable = false)]
        public int ClsSortIndex { get; set; }

        /// <summary>
        /// 报告中显示
        /// </summary>        
        [SugarColumn(IsNullable = false)]
        public bool ReportShow { get; set; }

        /// <summary>
        /// 原始单价
        /// </summary>        
        [SugarColumn(IsNullable = false, Length = 12, DecimalDigits = 2)]
        public decimal OriginalPrice { get; set; }

        /// <summary>
        /// 单价
        /// </summary>        
        [SugarColumn(IsNullable = false, Length = 12, DecimalDigits = 2)]
        public decimal Price { get; set; }

        /// <summary>
        /// 折扣
        /// </summary>        
        [SugarColumn(IsNullable = false, Length = 8, DecimalDigits = 2)]
        public decimal Discount { get; set; }

        /// <summary>
        /// 自费标识
        /// </summary>        
        [SugarColumn(IsNullable = false)]
        public bool IsPayBySelf { get; set; }

        /// <summary>
        /// 支付状态（0未收费 1收费 2冲销 3已退费）
        /// </summary>        
        [SugarColumn(IsNullable = false)]
        public PayStatus PayStatus { get; set; }

        /// <summary>
        /// 附加费
        /// </summary>        
        [SugarColumn(IsNullable = false, Length = 12, DecimalDigits = 2)]
        public decimal Surcharges { get; set; }

        /// <summary>
        /// 开单人代码
        /// </summary>        
        [SugarColumn(IsNullable = true, Length = 10)]
        public string ApplicantCode { get; set; }

        /// <summary>
        /// 开单人名字
        /// </summary>        
        [SugarColumn(IsNullable = true, Length = 50)]
        public string ApplicantName { get; set; }

        /// <summary>
        /// 折扣人代码
        /// </summary>        
        [SugarColumn(IsNullable = true, Length = 10)]
        public string DiscountOperCode { get; set; }

        /// <summary>
        /// 折扣人名称
        /// </summary>        
        [SugarColumn(IsNullable = true, Length = 50)]
        public string DiscountOperName { get; set; }

        /// <summary>
        /// 归属关系
        /// </summary>        
        [SugarColumn(IsNullable = true, ColumnDataType = StaticConfig.CodeFirst_BigString,IsJson =true)]
        public string[] BeFrom { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>        
        [SugarColumn(IsNullable = false)]
        public DateTime CreateTime { get; set; }

        /// <summary>
        /// 体检登记时间（分表依据）
        /// </summary>        
        [SplitField]
        [SugarColumn(IsNullable = false)]
        public DateTime RegisterTime { get; set; }

        /// <summary>
        /// 职检标识
        /// </summary>
        [SugarColumn(IsNullable = false)]
        public bool IsOccupation { get; set; }

        /// <summary>
        /// 普检标识
        /// </summary>
        [SugarColumn(IsNullable = false)]
        public bool IsOrdinary { get; set; }
        /// <summary>
        /// 是否自选
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public bool IsSelfSelected { get; set; }
    }
}