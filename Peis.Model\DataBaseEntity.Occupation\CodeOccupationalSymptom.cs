﻿namespace Peis.Model.DataBaseEntity.Occupation
{
    /// <summary>
    /// 职业病症状询问
    /// </summary>
    [SugarTable]
    public class CodeOccupationalSymptom
    {
        /// <summary>
        /// 症状代码
        /// </summary>
        [SugarColumn(Length =10,IsPrimaryKey = true)]
        public string SymptomCode {  get; set; }
        /// <summary>
        /// 症状名称
        /// </summary>
        [SugarColumn(Length =255,IsNullable = false)]   
        public string SymptomName { get; set; }
    }
}
