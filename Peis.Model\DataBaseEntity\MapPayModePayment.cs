﻿using SqlSugar;

namespace Peis.Model.DataBaseEntity
{
    ///<summary>
    ///结账模式-结账类型对应信息（收费类代码）
    ///</summary>
    [SugarTable("MapPayModePayment")]
    public class MapPayModePayment
    {
        /// <summary>
        /// 结账模式代码
        /// </summary>        
        [SugarColumn(IsNullable = false, IsPrimaryKey = true, Length = 10)]
        public string PayModeCode { get; set; }

        /// <summary>
        /// 结账模式名称
        /// </summary>        
        [SugarColumn(IsNullable = false, Length = 40)]
        public string PayModeName { get; set; }

        /// <summary>
        /// 结账类型代码
        /// </summary>        
        [SugarColumn(IsNullable = false, IsPrimaryKey = true, Length = 10)]
        public string PayCode { get; set; }

        /// <summary>
        /// 结账类型名称
        /// </summary>        
        [SugarColumn(IsNullable = false, Length = 40)]
        public string PayName { get; set; }
    }
}