﻿using Peis.Model.Other.PeEnum;
using SqlSugar;

namespace Peis.External.Wx.Service.Model.FromBody
{
    /// <summary>
    /// 微信个人订单同步数据
    /// </summary>
    public class SyncPersonOrder
    {
        /// <summary>
        /// 姓名
        /// </summary>
        public string Name { get; set; }

        /// <summary>
        /// 性别
        /// </summary>
        public Sex Sex { get; set; }

        /// <summary>
        /// 年龄
        /// </summary>
        public int Age { get; set; }

        /// <summary>
        /// 出生日期
        /// </summary>
        public string Birthday { get; set; }

        /// <summary>
        /// 证件号
        /// </summary>
        public string CardNo { get; set; }

        /// <summary>
        /// 证件类型
        /// </summary>
        public CardType CardType { get; set; }

        /// <summary>
        /// 手机号
        /// </summary>
        public string Tel { get; set; }

        /// <summary>
        /// 体检分类
        /// </summary>
        public PeCls PeCls { get; set; }

        /// <summary>
        /// 预约号(微信订单号)
        /// </summary>
        public string BookNo { get; set; }

        /// <summary>
        /// 体检日期
        /// </summary>
        public string BeginTime { get; set; }

        /// <summary>
        /// 套餐编码
        /// </summary>
        public string ClusCode { get; set; }

        /// <summary>
        /// 团检标识
        /// </summary>
        public bool IsCompanyCheck { get; set; }

        /// <summary>
        /// 院区编码
        /// </summary>
        public string HospCode { get; set; }

        /// <summary>
        /// 开单医生工号
        /// </summary>
        public string OperatorCode { get; set; }

        /// <summary>
        /// 开单医生名称
        /// </summary>
        public string OperatorName { get; set; }

        /// <summary>
        /// 家族史
        /// </summary>        
        public string? FamilyMedicalHistory { get; set; }

        /// <summary>
        /// 既往病史
        /// </summary>        
        public string? PastMedicalHistory { get; set; }

        /// <summary>
        /// 手术状况
        /// </summary>        
        public string? OperationStatus { get; set; }

        /// <summary>
        /// 吸烟习惯
        /// </summary>        
        public string SmokingHabit { get; set; }

        /// <summary>
        /// 喝酒习惯
        /// </summary>        
        public string? DrinkingHabit { get; set; }

        /// <summary>
        /// 生活习惯
        /// </summary>        
        public string? LivingHabit { get; set; }

        /// <summary>
        /// 现在病况
        /// </summary>        
        public string? CurrentCondition { get; set; }

        /// <summary>
        /// 问卷答案
        /// </summary>        
        public string? QuestionnaireAnswer { get; set; }
    }
}
