﻿//------------------------------------------------------------------------------
// <auto-generated>
//     此代码由工具生成。
//     运行时版本:4.0.30319.42000
//
//     对此文件的更改可能会导致不正确的行为，并且如果
//     重新生成代码，这些更改将会丢失。
// </auto-generated>
//------------------------------------------------------------------------------

namespace Peis.Model {
    using System;
    
    
    /// <summary>
    ///   一个强类型的资源类，用于查找本地化的字符串等。
    /// </summary>
    // 此类是由 StronglyTypedResourceBuilder
    // 类通过类似于 ResGen 或 Visual Studio 的工具自动生成的。
    // 若要添加或移除成员，请编辑 .ResX 文件，然后重新运行 ResGen
    // (以 /str 作为命令选项)，或重新生成 VS 项目。
    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Resources.Tools.StronglyTypedResourceBuilder", "17.0.0.0")]
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
    [global::System.Runtime.CompilerServices.CompilerGeneratedAttribute()]
    public class ResxExternal {
        
        private static global::System.Resources.ResourceManager resourceMan;
        
        private static global::System.Globalization.CultureInfo resourceCulture;
        
        [global::System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal ResxExternal() {
        }
        
        /// <summary>
        ///   返回此类使用的缓存的 ResourceManager 实例。
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        public static global::System.Resources.ResourceManager ResourceManager {
            get {
                if (object.ReferenceEquals(resourceMan, null)) {
                    global::System.Resources.ResourceManager temp = new global::System.Resources.ResourceManager("Peis.Model.ResxExternal", typeof(ResxExternal).Assembly);
                    resourceMan = temp;
                }
                return resourceMan;
            }
        }
        
        /// <summary>
        ///   重写当前线程的 CurrentUICulture 属性，对
        ///   使用此强类型资源类的所有资源查找执行重写。
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        public static global::System.Globalization.CultureInfo Culture {
            get {
                return resourceCulture;
            }
            set {
                resourceCulture = value;
            }
        }
        
        /// <summary>
        ///   查找类似 &lt;message&gt;
        ///    &lt;epKey&gt;{0}&lt;/epKey&gt;
        ///&lt;/message&gt; 的本地化字符串。
        /// </summary>
        public static string BHApiGetTokenMsg {
            get {
                return ResourceManager.GetString("BHApiGetTokenMsg", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 F4C78775-79E7-3195-EA0C-27BA63EDD7FD 的本地化字符串。
        /// </summary>
        public static string BHApiKey {
            get {
                return ResourceManager.GetString("BHApiKey", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 MDM-queryChargeItems 的本地化字符串。
        /// </summary>
        public static string BHApiMDM_queryChargeItems {
            get {
                return ResourceManager.GetString("BHApiMDM_queryChargeItems", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 MDM-queryDrugs 的本地化字符串。
        /// </summary>
        public static string BHApiMDM_queryDrugs {
            get {
                return ResourceManager.GetString("BHApiMDM_queryDrugs", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 MDM-queryOrderItems 的本地化字符串。
        /// </summary>
        public static string BHApiMDM_queryOrderItems {
            get {
                return ResourceManager.GetString("BHApiMDM_queryOrderItems", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 medicalRecord 的本地化字符串。
        /// </summary>
        public static string BHApiMedicalRecord {
            get {
                return ResourceManager.GetString("BHApiMedicalRecord", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 KangRuang 的本地化字符串。
        /// </summary>
        public static string BHApiOpssChannel {
            get {
                return ResourceManager.GetString("BHApiOpssChannel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 100201 的本地化字符串。
        /// </summary>
        public static string BHApiOpssHospitalId {
            get {
                return ResourceManager.GetString("BHApiOpssHospitalId", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 sendShortMessage001 的本地化字符串。
        /// </summary>
        public static string BHApiOpssId_SendMsg {
            get {
                return ResourceManager.GetString("BHApiOpssId_SendMsg", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 eaf41e2218f2445886edb44be5cd248a 的本地化字符串。
        /// </summary>
        public static string BHApiOpssKey {
            get {
                return ResourceManager.GetString("BHApiOpssKey", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 https://gdbyway-hlwyy.sschospital.cn/opss-gateway/doRpc/opss 的本地化字符串。
        /// </summary>
        public static string BHApiOpssUrl {
            get {
                return ResourceManager.GetString("BHApiOpssUrl", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 queryClinicCardInfo01Q006 的本地化字符串。
        /// </summary>
        public static string BHApiQueryCardInfo {
            get {
                return ResourceManager.GetString("BHApiQueryCardInfo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 queryTokenInfo98Q001J 的本地化字符串。
        /// </summary>
        public static string BHApiQueryTokenInfo {
            get {
                return ResourceManager.GetString("BHApiQueryTokenInfo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 registPat 的本地化字符串。
        /// </summary>
        public static string BHApiRegistPat {
            get {
                return ResourceManager.GetString("BHApiRegistPat", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 synchPEModifyNotice 的本地化字符串。
        /// </summary>
        public static string BHApiSynchPEModifyNotice {
            get {
                return ResourceManager.GetString("BHApiSynchPEModifyNotice", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 synchPEReportInfo 的本地化字符串。
        /// </summary>
        public static string BHApiSynchPEReportInfo {
            get {
                return ResourceManager.GetString("BHApiSynchPEReportInfo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 https://gdbyway-platform-cluster.sschospital.cn/hie/srvmgr/hieSrvMgr/getToken 的本地化字符串。
        /// </summary>
        public static string BHApiUrlGetToken {
            get {
                return ResourceManager.GetString("BHApiUrlGetToken", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 https://gdbyway-platform-cluster.sschospital.cn/hie/services/hieService/msgTransfer 的本地化字符串。
        /// </summary>
        public static string BHApiUrlMsgTransfer {
            get {
                return ResourceManager.GetString("BHApiUrlMsgTransfer", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 pacsImages 的本地化字符串。
        /// </summary>
        public static string FolderExamReport {
            get {
                return ResourceManager.GetString("FolderExamReport", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 PeisReports 的本地化字符串。
        /// </summary>
        public static string FolderPeisReport {
            get {
                return ResourceManager.GetString("FolderPeisReport", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 MB01 的本地化字符串。
        /// </summary>
        public static string SysShortMsgTemplate_MB01 {
            get {
                return ResourceManager.GetString("SysShortMsgTemplate_MB01", resourceCulture);
            }
        }
    }
}
