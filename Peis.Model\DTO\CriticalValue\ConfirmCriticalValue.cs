﻿using Peis.Model.Other.PeEnum;

namespace Peis.Model.DTO.CriticalValue
{
    /// <summary>
    /// 确认危急值
    /// </summary>
    public class ConfirmCriticalValue
    {
        /// <summary>
        /// 体检号
        /// </summary>
        public string RegNo { get; set; }

        /// <summary>
        /// 类型（危急值、重大阳性）
        /// </summary>
        public CriticalValueType Type { get; set; }

        /// <summary>
        /// 确认人
        /// </summary>
        public string Confirmer { get; set; }

        /// <summary>
        /// 重大异常项目列表
        /// </summary>
        public ConfirmCriticalValueItem[] Items { get; set; }

        /// <summary>
        /// 确认日期
        /// </summary>
        public DateTime ConfirmTime { get; set; }
    }
}
