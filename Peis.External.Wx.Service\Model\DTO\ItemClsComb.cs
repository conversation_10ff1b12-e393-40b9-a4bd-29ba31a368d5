﻿namespace Peis.External.Wx.Service.Model.DTO
{
    /// <summary>
    /// 项目分类下的组合
    /// </summary>
    public class ItemClsComb
    {
        /// <summary>
        /// 项目分类代码
        /// </summary>
        public string ClsCode { get; set; }

        /// <summary>
        /// 项目分类名称
        /// </summary>
        public string ClsName { get; set; }

        public Children[] Children { get; set; }
    }

    /// <summary>
    /// 分类下的组合
    /// </summary>
    public class Children
    {
        /// <summary>
        /// 组合代码
        /// </summary>
        public string CombCode { get; set; }

        /// <summary>
        /// 组合名称
        /// </summary>
        public string CombName { get; set; }

        /// <summary>
        /// 意义说明(备注)
        /// </summary>
        public string Note { get; set; }

        /// <summary>
        /// 单价
        /// </summary>
        public decimal Price { get; set; }
    }
}
