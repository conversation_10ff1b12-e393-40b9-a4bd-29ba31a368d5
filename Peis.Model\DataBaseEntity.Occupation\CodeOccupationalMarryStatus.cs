﻿namespace Peis.Model.DataBaseEntity.Occupation
{
    /// <summary>
    /// 职业病婚姻状况字典表
    /// </summary>
    [SugarTable]
    public class CodeOccupationalMarryStatus
    {
        /// <summary>
        /// 代码
        /// </summary>
        [SugarColumn(Length =1,IsPrimaryKey = true)]
        public string StatusCode { get; set; }
        /// <summary>
        /// 名称
        /// </summary>
        [SugarColumn(Length =10,IsNullable =false)]
        public string StatusName { get; set;}
    }
}
