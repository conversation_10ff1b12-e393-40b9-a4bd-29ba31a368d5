﻿using MediatR;
using NUglify.Helpers;
using Peis.Model.DataBaseEntity.Occupation;
using Peis.Model.DTO;
using Peis.Model.DTO.BasicCode;
using Peis.Model.DTO.Register;
using Peis.Model.DTO.Sample;
using Peis.Model.Other.Input;
using Peis.Model.Other.PeEnum;
using Peis.Repository.IRepository;
using Peis.Service.IService;
using Peis.Service.IService.ExternalSystem;
using Peis.Service.IService.Helper;
using Peis.Service.Service.MediatR;
using Peis.Utility.PeIM.Core;
using Peis.Utility.PeUser;
using System.Text.RegularExpressions;

namespace Peis.Service.Service
{
    public class RegisterNewService : IRegisterNewService
    {
        private readonly IHttpContextUser _httpContextUser;
        private readonly IPeimClients _peimClients;
        private readonly IBasicCodeRepository _basicCodeRepository;
        private readonly IRegisterRepository _registerRepository;
        private readonly IFeeRepository _feeRepository;
        private readonly IDataTranRepository _dataTranRepository;
        private readonly IClusterCombRepository _clusterCombRepository;
        private readonly ICompanyRepository _companyRepository;
        private readonly INoGeneration _noGeneration;
        private readonly IDataRepository<PeRegister> _peRegisterRepository;
        private readonly IDataRepository<DeletePeRegister> _delRegisterRepository;
        private readonly IDataRepository<PeRegisterCluster> _peRegisterClusterRepository;
        private readonly IDataRepository<PeRegisterOccupation> _peRegisterOccupationRepository;
        private readonly IDataRepository<PeRegisterOccupationHazard> _peRegisterOccupationHazardRepository;
        private readonly ISplitTableRepository<PeRegisterComb> _peRegisterCombRepository;
        private readonly IDataRepository<WeChatBookQuestion> _weChatBookQuestionRepository;
        private readonly ISystemParameterService _systemParameterService;
        private readonly IExternalSystemOrderService _externalSystemService;
        private readonly ISampleService _sampleService;
        private readonly ISampleRepository _sampleRepository;
        private readonly ISystemSettingRepository _settingRepository;
        private readonly IExternalSystemCompanySettlementService _externalSystemCompanyService;
        private readonly ICacheRepository _cacheRepository;
        private readonly IMediator _mediator;
        private readonly IRecordRepository _recordRepository;
        private readonly IDataRepository<MapCompanySettlementPerson> _mapCompanySettlementPersonRepository;
        private readonly IDataRepository<PeRecheckComb> _peRecheckCombRepository;
        private readonly IMapper _mapper;
        private readonly IDataRepository<PeRegisterQuoteComb> _peRegisterQuoteCombRepository;
        private readonly IRecordNewService _recordNewService;
        private readonly ILogBusinessNewService _logBusinessNewService;

        public RegisterNewService(
            IHttpContextUser httpContextUser,
            IPeimClients peimClients,
            IBasicCodeRepository basicCodeRepository,
            IRegisterRepository registerRepository,
            IFeeRepository feeRepository,
            IDataTranRepository dataTranRepository,
            IClusterCombRepository clusterCombRepository,
            ICompanyRepository companyRepository,
            INoGeneration noGeneration,
            IDataRepository<PeRegister> peRegisterRepository,
            IDataRepository<DeletePeRegister> delRegisterRepository,
            IDataRepository<PeRegisterCluster> peRegisterClusterRepository,
            IDataRepository<PeRegisterOccupation> peRegisterOccupationRepository,
            IDataRepository<PeRegisterOccupationHazard> peRegisterOccupationHazardRepository,
            ISplitTableRepository<PeRegisterComb> peRegisterCombRepository,
            IDataRepository<WeChatBookQuestion> weChatBookQuestionRepository,
            ISystemParameterService systemParameterService,
            IExternalSystemOrderService externalSystemService,
            ISampleService sampleService,
            ISampleRepository sampleRepository,
            ISystemSettingRepository settingRepository,
            IExternalSystemCompanySettlementService externalSystemCompanyService,
            ICacheRepository cacheRepository,
            IMediator mediator,
            IRecordRepository recordRepository,
            IDataRepository<MapCompanySettlementPerson> mapCompanySettlementPersonRepository,
            IDataRepository<PeRecheckComb> peRecheckCombRepository,
            IMapper mapper,
            IDataRepository<PeRegisterQuoteComb> peRegisterQuoteCombRepository,
            IRecordNewService recordNewService,
            ILogBusinessNewService logBusinessNewService)
        {
            _httpContextUser = httpContextUser;
            _peimClients = peimClients;
            _basicCodeRepository = basicCodeRepository;
            _registerRepository = registerRepository;
            _feeRepository = feeRepository;
            _dataTranRepository = dataTranRepository;
            _clusterCombRepository = clusterCombRepository;
            _companyRepository = companyRepository;
            _noGeneration = noGeneration;
            _peRegisterRepository = peRegisterRepository;
            _delRegisterRepository = delRegisterRepository;
            _peRegisterClusterRepository = peRegisterClusterRepository;
            _peRegisterOccupationRepository = peRegisterOccupationRepository;
            _peRegisterOccupationHazardRepository = peRegisterOccupationHazardRepository;
            _peRegisterCombRepository = peRegisterCombRepository;
            _weChatBookQuestionRepository = weChatBookQuestionRepository;
            _systemParameterService = systemParameterService;
            _externalSystemService = externalSystemService;
            _sampleService = sampleService;
            _sampleRepository = sampleRepository;
            _settingRepository = settingRepository;
            _externalSystemCompanyService = externalSystemCompanyService;
            _cacheRepository = cacheRepository;
            _mediator = mediator;
            _recordRepository = recordRepository;
            _mapCompanySettlementPersonRepository = mapCompanySettlementPersonRepository;
            _peRecheckCombRepository = peRecheckCombRepository;
            _mapper = mapper;
            _peRegisterQuoteCombRepository = peRegisterQuoteCombRepository;
            _recordNewService = recordNewService;
            _logBusinessNewService = logBusinessNewService;
        }

        public CandidateCluster[] ReadCandidateCluster()
        {
            var candidateClusters = _clusterCombRepository.ReadEnabledCluster()
                .Where(x => !x.IsOccupation)
                .Select(x => new CandidateCluster
                {
                    ClusCode = x.ClusCode,
                    ClusName = x.ClusName,
                    Price = x.Price,
                    PeCls = x.PeCls,
                    Sex = x.Sex,
                    LowerAgeLimit = x.LowerAgeLimit,
                    UpperAgeLimit = x.UpperAgeLimit,
                    GuidanceType = x.GuidanceType,
                    ReportType = x.ReportType,
                    PinYinCode = x.PinYinCode,
                    WuBiCode = x.WuBiCode
                }).ToArray();

            var dictClusters = _cacheRepository.DictCluster();

            // 设置套餐绑定的组合
            foreach (var item in candidateClusters)
            {
                if (dictClusters.ContainsKey(item.ClusCode))
                {
                    item.BindCombs = dictClusters[item.ClusCode].ClusCombs;
                    item.MutexCombs = dictClusters[item.ClusCode].MutexCombs;
                }
            }
            return candidateClusters;
        }

        public CandidateCluster[] ReadCompanyCandidateCluster(string companyCode, int companyTimes, PeClsType type)
        {
            var candidateClusters = _companyRepository.ReadCompanyCluster(companyCode, companyTimes)
                .WhereIF(type == PeClsType.仅普通, x => x.IsOccupation == false)
                .WhereIF(type == PeClsType.仅职业, x => x.IsOccupation == true)
                .Select(x => new CandidateCluster
                {
                    ClusCode = x.ClusterCode,
                    ClusName = x.ClusterName,
                    Price = x.Price,
                    PeCls = PeCls.健康体检,
                    Sex = x.Sex,
                    LowerAgeLimit = x.LowerAgeLimit,
                    UpperAgeLimit = x.UpperAgeLimit,
                    GuidanceType = x.GuidanceType,
                    ReportType = x.ReportType,
                    PinYinCode = string.Empty,
                    WuBiCode = string.Empty,
                    JobStatus = x.JobStatus,
                    HazardFactors = x.HazardFactors,
                }).ToArray();
            var dictClusters = _cacheRepository.DictCompanyCluster();
            var hazardDict = _cacheRepository.DictHazardFactor();
            //设置危害因素
            candidateClusters.BatchUpdate(x =>
            {
                if (!x.HazardFactors.IsNullOrEmpty())
                    x.BindHazardFactors = x.HazardFactors.ToDictionary(h => h, h => hazardDict[h].HazardousName);
            }
            );
            // 设置套餐绑定的组合
            foreach (var item in candidateClusters)
            {
                if (dictClusters.ContainsKey(item.ClusCode))
                    item.BindCombs = dictClusters[item.ClusCode].ClusCombs;
            }
            ;
            return candidateClusters;
        }

        public CandidateCluster[] ReadOccupationalCandidateCluster()
        {
            var candidateClusters = _clusterCombRepository.ReadEnabledCluster()
                .Where(x => x.IsOccupation)
                .Select(x => new CandidateCluster
                {
                    ClusCode = x.ClusCode,
                    ClusName = x.ClusName,
                    Price = x.Price,
                    PeCls = x.PeCls,
                    Sex = x.Sex,
                    LowerAgeLimit = x.LowerAgeLimit,
                    UpperAgeLimit = x.UpperAgeLimit,
                    GuidanceType = x.GuidanceType,
                    ReportType = x.ReportType,
                    PinYinCode = x.PinYinCode,
                    WuBiCode = x.WuBiCode,
                    HazardFactors = x.HazardFactors,
                    JobStatus = x.JobStatus
                }).ToArray();

            var dictClusters = _cacheRepository.DictCluster();
            var hazardDict = _cacheRepository.DictHazardFactor();
            //设置危害因素
            candidateClusters.BatchUpdate(x =>
            {
                if (!x.HazardFactors.IsNullOrEmpty())
                    x.BindHazardFactors = x.HazardFactors.ToDictionary(h => h, h => hazardDict[h].HazardousName);
            }
            );
            //设置套餐绑定的组合
            foreach (var item in candidateClusters)
            {
                if (dictClusters.ContainsKey(item.ClusCode))
                {
                    item.BindCombs = dictClusters[item.ClusCode].ClusCombs;
                    item.MutexCombs = dictClusters[item.ClusCode].MutexCombs;
                }
            }
            return candidateClusters;
        }

        public CandidateComb[] ReadCandidateComb()
        {
            var barCombs = _clusterCombRepository.ReadBarcodeTypeFeeComb()
                .GroupBy((barType, comb) => barType.FeeCombCode)
                .Select((barType, comb) => barType.FeeCombCode)
                .ToArray();

            var candidateCombs = _clusterCombRepository.ReadEnabledComb()
                .Select(x => new CandidateComb
                {
                    CombCode = x.CombCode,
                    CombName = x.CombName,
                    ShortName = x.ShortName,
                    CheckCls = x.CheckCls,
                    Sex = x.Sex,
                    ClsCode = x.ClsCode,
                    ExamDeptCode = x.ExamDeptCode,
                    DiscountAllow = x.DiscountAllow,
                    Price = x.Price,
                    PinYinCode = x.PinYinCode,
                    WuBiCode = x.WuBiCode
                })
                .Where(x => SqlFunc.Subqueryable<MapItemComb>().Where(sq => sq.CombCode == x.CombCode).Any())
                .Where(x => !SqlFunc.ContainsArray(barCombs, x.CombCode)) //过滤条码类型材料
                .ToArray();

            var dictCombs = candidateCombs.ToDictionary(x => x.CombCode);

            #region 设置组合绑定的组合
            var bindCombGroups = _clusterCombRepository.ReadCombBindComb()
                .ToArray().GroupBy(x => x.CombCode);

            foreach (var bindCombs in bindCombGroups)
            {
                if (dictCombs.ContainsKey(bindCombs.Key))
                    dictCombs[bindCombs.Key].BindCombs = bindCombs.Select(x => x.AppendCombCode).ToArray();
            }
            #endregion

            #region 设置组合互斥的组合
            var combMutexCombs = _clusterCombRepository.ReadCombMutexComb()
                .Select(x => new
                {
                    x.MutexCode,
                    x.CombCode
                }).ToArray();

            var mutexGroups = combMutexCombs.GroupBy(x => x.MutexCode, x => x.CombCode);

            foreach (var mutexCombs in mutexGroups)
            {
                var combs = mutexCombs.ToArray();
                for (int a = 0; a < combs.Length; a++)
                {
                    for (int b = 0; b < combs.Length; b++)
                    {
                        if (b == a)
                            continue;

                        if (dictCombs.ContainsKey(combs[a]))
                        {
                            if (!dictCombs[combs[a]].MutexCombs.Contains(combs[b]))
                                dictCombs[combs[a]].MutexCombs.Add(combs[b]);
                        }
                    }
                }
            }
            #endregion

            return candidateCombs;
        }

        public CandidateItem[] ReadCandidateCombItems(string combCode)
        {
            return _clusterCombRepository.ReadCombBindItem(combCode)
                .Where((map, comb, item) => item.IsEnabled)
                .OrderBy((map, comb, item) => item.SortIndex)
                .OrderBy((map, comb, item) => item.ItemCode)
                .Select((map, comb, item) => new CandidateItem
                {
                    ItemCode = item.ItemCode,
                    ItemName = item.ItemName
                })
                .ToArray();
        }

        public object NewRegisterOrder(RegisterNewOrderInfo order, ref bool success)
        {
            var newRegInfo = CreatePeRegister(order.RegisterOrder.IsCompany, order.RegisterOrder.Patient, order.RegisterOrder.Combs);
            if (newRegInfo == null)
                return null;

            // 赋值登记组合ID
            order.RegisterOrder.Combs.Where(x => x.Id < 10000).BatchUpdate(x => x.Id = _noGeneration.NextSnowflakeId());

            var newTestTubes = CalculateTestTube(new CalculateTestTubeInput
            {
                Clusters = order.RegisterOrder.Clusters.Select(x => new CalculateCluster { ClusCode = x.ClusCode }).ToList(),
                Combs = order.RegisterOrder.Combs.ToList(),
                IsCompanyCheck = order.RegisterOrder.IsCompany,
                TestTubes = order.RegisterOrder.TestTubes.ToList()
            });

            var newRegClusters = CreatePeRegisterCluster(newRegInfo.RegNo, order.RegisterOrder.Clusters);
            var newRegCombs = CreatePeRegisterComb(newRegInfo, order.RegisterOrder.Combs.Concat(newTestTubes).ToArray());

            _dataTranRepository.ExecTran(() =>
            {
                // 登记信息 插入
                _peRegisterRepository.Insert(newRegInfo);

                // 单位、单位部门
                _companyRepository.SaveCompany(newRegInfo.CodeCompany);
                _companyRepository.SaveCompanyDept(newRegInfo.CodeCompanyDepartment);

                // 登记套餐 插入
                if (newRegClusters.Length > 0)
                    _peRegisterClusterRepository.Insert(newRegClusters);

                if (newRegCombs.Length > 0)
                    // 登记组合 插入
                    _peRegisterCombRepository.SplitTableInsert(newRegCombs);

                //职检标识
                if (order.RegisterOrder.Patient.IsOccupation)
                {
                    //职业病信息
                    order.OccupationOrder.RegNo = newRegInfo.RegNo;
                    SavePeRegisterOccupation(true, order.OccupationOrder);
                }

                //生成所有条码
                _sampleService.GenerateAllSampleNew(newRegInfo.RegNo, order.RegisterOrder.Combs, order.RegisterOrder.Clusters.Select(x => x.ClusCode).FirstOrDefault());
                _logBusinessNewService.RegisterNewLog(newRegInfo.RegNo, "现场登记");
                _logBusinessNewService.SaveLogs();
            });

            _mediator.Publish(new SyncPeRegisterOrderHandle.Data(new string[] { newRegInfo.RegNo }));
            // 同步订单到外部系统
            //if (newRegInfo.IsActive || order.Combs.Length > 0)
            _externalSystemService.SyncOrder(newRegInfo);
            if (newRegClusters.Length > 0)
            {
                _externalSystemCompanyService.SavePersonClusterChargeItemEntry(newRegInfo, order.RegisterOrder.Clusters.Select(x => x.ClusCode).FirstOrDefault());
            }
            _externalSystemCompanyService.SavePersonExtraChargeCombs(newRegInfo);

            success = true;
            return new { newRegInfo.RegNo, newRegInfo.GuidanceType };
        }

        public object AlterRegisterOrder(RegisterNewOrderInfo order, ref bool success)
        {
            //修改前的登记信息
            var peRegisterOriginal = new PeRegister();

            //获取修改后的登记信息
            var altRegister = GetRegisterChanged(order.RegisterOrder.IsCompany, order.RegisterOrder.Patient, order.RegisterOrder.Clusters, order.RegisterOrder.Combs, ref peRegisterOriginal);
            if (altRegister == null)
                return null;

            if (peRegisterOriginal.PeStatus == PeStatus.已总检 || peRegisterOriginal.PeStatus == PeStatus.已审核)
                throw new BusinessException("已总检/审核，不能修改组合");

            if (order.RegisterOrder.IsCompany && _mapCompanySettlementPersonRepository.Any(x => x.RegNo == order.RegisterOrder.Patient.RegNo))
                throw new BusinessException("已进入结算阶段，不能修改组合");

            // 赋值登记组合ID
            order.RegisterOrder.Combs.Where(x => x.Id < 10000).BatchUpdate(x => x.Id = _noGeneration.NextSnowflakeId());

            var newTestTubes = CalculateTestTube(new CalculateTestTubeInput
            {
                Clusters = order.RegisterOrder.Clusters.Select(x => new CalculateCluster { ClusCode = x.ClusCode }).ToList(),
                Combs = order.RegisterOrder.Combs.ToList(),
                IsCompanyCheck = order.RegisterOrder.IsCompany,
                TestTubes = order.RegisterOrder.TestTubes.ToList()
            });
            //获取修改后的登记套餐
            GetRegisterClusterChanged(
                altRegister.RegNo,
                order.RegisterOrder.Clusters,
                out List<PeRegisterCluster> deleteClusters,
                out PeRegisterCluster[] addClusters
            );

            //获取修改后的登记组合
            GetRegisterCombChanged
            (
                altRegister,
                order.RegisterOrder.Combs.Concat(newTestTubes).ToArray(),
                out List<PeRegisterComb> delCombs,
                out List<PeRegisterComb> altCombs,
                out PeRegisterComb[] addCombs
            );

            if (delCombs.Count > 0 && delCombs.Any(x => x.PayStatus == PayStatus.收费 || x.PayStatus == PayStatus.冲销))
                throw new BusinessException("不能删除已支付的组合");

            _dataTranRepository.ExecTran(() =>
            {
                //登记信息 更新
                _peRegisterRepository.Update(altRegister);

                // 单位、单位部门
                _companyRepository.SaveCompany(altRegister.CodeCompany);
                _companyRepository.SaveCompanyDept(altRegister.CodeCompanyDepartment);

                //登记套餐 删除、插入
                if (deleteClusters.Count > 0)
                    _peRegisterClusterRepository.Delete(deleteClusters);
                if (addClusters.Length > 0)
                    _peRegisterClusterRepository.Insert(addClusters);

                //登记组合 删除、更新、插入
                if (delCombs.Count > 0)
                    _peRegisterCombRepository.SplitTableDelete(delCombs);
                if (altCombs.Count > 0)
                    _peRegisterCombRepository.SplitTableUpdate(altCombs);
                if (addCombs.Length > 0)
                    _peRegisterCombRepository.SplitTableInsert(addCombs);

                //职检标识
                if (order.RegisterOrder.Patient.IsOccupation)
                {
                    order.OccupationOrder.RegNo = altRegister.RegNo;
                    //职业病信息
                    SavePeRegisterOccupation(!peRegisterOriginal.IsOccupation, order.OccupationOrder);
                }
                else
                {
                    if (peRegisterOriginal.IsOccupation)
                    {
                        DeletePeRegisterOccupation(altRegister.RegNo);
                    }
                }
                _recordNewService.UpdatePeStatus(altRegister);
                //生成所有条码
                _sampleService.GenerateAllSampleNew(altRegister.RegNo, order.RegisterOrder.Combs, order.RegisterOrder.Clusters.Select(x => x.ClusCode).FirstOrDefault());
                _logBusinessNewService.SaveLogs();
            });

            _mediator.Publish(new SyncPeRegisterOrderHandle.Data(new string[] { altRegister.RegNo }));
            // 同步订单到外部系统
            //if (altRegister.IsActive)
            _externalSystemService.SyncOrder(altRegister);
            if (deleteClusters.Count > 0 || addClusters.Length > 0)
            {
                _externalSystemCompanyService.SavePersonClusterChargeItemEntry(altRegister, order.RegisterOrder.Clusters.Select(x => x.ClusCode).FirstOrDefault());
            }
            _externalSystemCompanyService.SavePersonExtraChargeCombs(altRegister);

            success = true;
            return new { altRegister.RegNo, altRegister.GuidanceType };
        }

        public bool AlterRegisterPaitent(bool isCompanyCheck, AlterRegisterPatient AlterRegisterPatient)
        {
            //修改前的登记信息
            var peRegisterOriginal = new PeRegister();
            var peRegisterOccupationOriginal = new PeRegisterOccupation();
            var alterOccupation = new PeRegisterOccupation();
            List<PeRegisterOccupationHazard> alterOccHazards = default;
            //获取修改后的登记信息
            var altRegister = GetRegisterChanged(isCompanyCheck, AlterRegisterPatient.RegisterPatient, null, null, ref peRegisterOriginal);
            if (altRegister == null)
                return false;
            if (altRegister.IsOccupation != peRegisterOriginal.IsOccupation)
            {
                throw new BusinessException("更新功能不用于职业病信息新增/删除");
            }
            if (peRegisterOriginal.CompanyCode != AlterRegisterPatient.RegisterPatient.CompanyCode)
            {
                throw new BusinessException("更新功能不允许修改单位");
            }
            if (altRegister.IsOccupation)
            {
                alterOccupation = GetRegisterOccupationChanged(AlterRegisterPatient.OccupationOrder);
                alterOccHazards = GetRegisterOccHazardChanged(AlterRegisterPatient.OccupationOrder);
            }

            _dataTranRepository.ExecTran(() =>
            {
                //登记信息 更新
                _peRegisterRepository.Update(altRegister);
                if (altRegister.IsOccupation)
                    _peRegisterOccupationRepository.Update(alterOccupation);

                // 危害因素
                if (!alterOccHazards.IsNullOrEmpty())
                    _peRegisterOccupationHazardRepository.Update(alterOccHazards, x => new { x.StartDateOfHazards, x.YearsOfHazards, x.MonthsOfHazards });

                _logBusinessNewService.SaveLogs();
            });

            _mediator.Send(new SavePeReportFileByRegNoHandle.Data(altRegister.RegNo));

            return true;
        }

        public object ReadHistoryArchives(string queryType, string queryValue)
        {
            if (string.IsNullOrEmpty(queryValue))
                return Array.Empty<object>();

            bool queryName = false;
            bool queryCardNo = false;
            bool queryTel = false;

            switch (queryType.ToLower())
            {
                case "name":
                    queryName = true;
                    break;
                case "cardno":
                    queryCardNo = true;
                    break;
                case "tel":
                    queryTel = true;
                    break;
                default:
                    throw new BusinessException("参数不正确");
            }
            //查询数据
            var queryable = _registerRepository.ReadRegister(queryValue, false, false, queryName, queryCardNo, queryTel, x => new HistoryArchives
            {
                Index = SqlFunc.RowNumber($"{x.RegisterTime} desc", $"{x.CardNo}, {x.Name}"),
                PatCode = x.PatCode,
                Name = x.Name,
                Sex = x.Sex,
                Age = x.Age,
                AgeUnit = x.AgeUnit,
                Birthday = x.Birthday,
                NativePlace = x.NativePlace,
                MarryStatus = x.MarryStatus,
                CardType = x.CardType,
                CardNo = x.CardNo,
                Tel = x.Tel,
                Address = x.Address,
                JobCode = x.JobCode,
                MedicalHistory = x.MedicalHistory,
                RegisterTime = x.RegisterTime,
                ActiveTime = x.ActiveTime,
                PhotoUrl = x.PhotoUrl,
            }).MergeTable().OrderBy(x => x.Index).ToArray();
            foreach (var item in queryable.GroupBy(x => new { x.CardNo, x.Name }))
            {
                var regList = item.ToArray();
                var firstReg = regList.First();
                if (string.IsNullOrWhiteSpace(firstReg.PhotoUrl))
                {
                    if (regList.Any(x => !string.IsNullOrWhiteSpace(x.PhotoUrl)))
                    {
                        firstReg.PhotoUrl = regList.Where(x => !string.IsNullOrWhiteSpace(x.PhotoUrl)).First().PhotoUrl;
                    }
                }
            }
            queryable = queryable
                .Where(x => x.Index == 1)
                .ToArray();
            queryable.BatchUpdate(x =>
            {
                if (!string.IsNullOrWhiteSpace(x.PhotoUrl))
                    x.PhotoUrl = Appsettings.GetSectionValue("WebserviceHub:FileServiceUrl") + x.PhotoUrl;
            });
            return queryable;
        }

        public RegisterRecord ReadRegistersByMultipleFilter(RegisterMultipleFilters filters, ref int totalNumber, ref int totalPage)
        {
            var regList = new RegisterRecord();
            ISugarQueryable<PeRegister> queryable = null;
            if (!string.IsNullOrEmpty(filters.RegNo))
            {
                queryable = _registerRepository.ReadRegister().Where(reg => reg.RegNo == filters.RegNo)
                    .Where(reg => reg.IsCompanyCheck == filters.IsCompanyCheck);
            }
            else
            {
                var startTime = filters.StartTime.Value.Date;
                var endTime = filters.EndTime.Value.Date.Add(new TimeSpan(23, 59, 59));

                queryable = _registerRepository.ReadRegister()
                    //个检还是团检
                    .Where(reg => reg.IsCompanyCheck == filters.IsCompanyCheck)
                    //档案号
                    .WhereIF(!string.IsNullOrEmpty(filters.PatCode),
                        reg => reg.PatCode == filters.PatCode)
                    //姓名
                    .WhereIF(!string.IsNullOrEmpty(filters.Name),
                        reg => reg.Name.Contains(filters.Name))
                    //性别，查全部前端默认传-1
                    .WhereIF(Enum.IsDefined(typeof(Sex), filters.Sex),
                        reg => reg.Sex == filters.Sex)
                    //证件号
                    .WhereIF(!string.IsNullOrEmpty(filters.CardNo),
                        reg => reg.CardNo == filters.CardNo)
                    //登记时间
                    .WhereIF(filters.QueryType == 1 && filters.EndTime != null,
                        reg => SqlFunc.Between(reg.RegisterTime, startTime, endTime))
                    //激活时间(报到时间)
                    .WhereIF(filters.QueryType == 2 && filters.EndTime != null,
                        reg => SqlFunc.Between(reg.ActiveTime, startTime, endTime))
                    //预约时间
                    .WhereIF(filters.QueryType == 3 && filters.EndTime != null,
                        reg => SqlFunc.Between(reg.BookBeginTime, startTime, endTime))
                    //团体查体检状态
                    .WhereIF(Enum.IsDefined(typeof(PeStatus), filters.PeStatus),
                        reg => reg.PeStatus == filters.PeStatus)
                    //个人 查预约订单
                    .WhereIF(Enum.IsDefined(typeof(BookType), filters.BookType),
                        reg => reg.BookType == filters.BookType)
                    //复查
                    .WhereIF(filters.IsRecheck.HasValue, reg => SqlFunc.HasValue(reg.RecheckNo) == filters.IsRecheck)
                    //兼容职业病普通
                    .WhereIF(!filters.IsOccupation, reg => reg.IsOrdinary == true)
                    .WhereIF(filters.IsActive.HasValue, reg => reg.IsActive == filters.IsActive)
                    .WhereIF(Enum.IsDefined<PeCls>(filters.PeCls), reg => reg.PeCls == filters.PeCls)
                    .WhereIF(filters.GuidanceStatus.HasValue && filters.GuidanceStatus.Value == 1, reg => reg.GuidancePrinted == true)
                    .WhereIF(filters.GuidanceStatus.HasValue && filters.GuidanceStatus.Value == 2, reg => reg.GuidancePrinted == false);

                //单位码
                if (!filters.CompanyCode.IsNullOrEmpty())
                {
                    queryable
                        .Where(reg => reg.CompanyCode == filters.CompanyCode)
                        //单位次数 默认值前端传-1
                        .WhereIF(filters.CompanyTimes > 0, reg => reg.CompanyTimes == filters.CompanyTimes)
                        //单位部门
                        .WhereIF(!string.IsNullOrEmpty(filters.CompanyDeptCode), reg => reg.CompanyDeptCode == filters.CompanyDeptCode);
                }
                if (!filters.Keyword.IsNullOrEmpty())
                {
                    queryable
                        .WhereIF(Regex.IsMatch(filters.Keyword, @"^[\\u4e00-\\u9fa5]{0,}"), reg => reg.Name.Contains(filters.Keyword))
                        .WhereIF(Regex.IsMatch(filters.Keyword, @"\d{12}") && filters.Keyword.Length == 11, reg => reg.Tel == filters.Keyword)
                        .WhereIF(Regex.IsMatch(filters.Keyword, @"\d{12}") && filters.Keyword.Length == 18, reg => reg.CardNo == filters.Keyword);
                }
                if (!filters.ClusCode.IsNullOrEmpty())
                {
                    // 套餐码
                    queryable.Where(reg => SqlFunc.Subqueryable<PeRegisterCluster>().Where(z => z.ClusCode == filters.ClusCode && z.RegNo == reg.RegNo).Any());
                }
                if (!filters.CombCode.IsNullOrEmpty())
                {
                    // 组合码
                    DateTime startTimeOfSplit = startTime, endTimeOfSplit = endTime;
                    if (filters.QueryType == 2)
                    {
                        var timeRange = _registerRepository.ReadRegDateRangeByActiveTime(startTime, endTime);
                        startTimeOfSplit = timeRange.MinRegTime;
                        endTimeOfSplit = timeRange.MaxRegTime;
                    }

                    var combQuery = _registerRepository.ReadRegisterCombs(startTimeOfSplit, endTimeOfSplit,
                        comb => SqlFunc.Between(comb.RegisterTime, startTime, endTime) && comb.CombCode == filters.CombCode);
                    queryable = queryable
                        .InnerJoin(combQuery, (reg, comb) => reg.RegNo == comb.RegNo)
                        .Select((reg, comb) => reg)
                        .Distinct();
                }
            }

            var countList = queryable.Select(reg => new
            {
                reg.RegNo,
                reg.IsActive,
                reg.PeStatus,
                reg.ReportPrinted
            }).ToList();
            if (countList.IsNullOrEmpty())
            {
                regList.Record = Array.Empty<RecordPatient>();
                regList.RecordStatus = new();
                return regList; // 没有数据 
            }

            regList.RecordStatus = new RecordStatus
            {
                TotalCount = countList.Count,
                InActiveCount = countList.Count(x => x.IsActive == false),
                UnCheckedCount = countList.Count(x => x.PeStatus == PeStatus.未检查),
                IsCheckingCount = countList.Count(x => x.PeStatus == PeStatus.正在检查),
                CheckedCount = countList.Count(x => x.PeStatus == PeStatus.已检完),
                ApprovedCount = countList.Count(x => x.PeStatus == PeStatus.已审核),
                IssuedReportCount = countList.Count(x => x.ReportPrinted)
            };

            var registerData = queryable.Select(reg => new
            {
                RegNo = reg.RegNo,
                PatCode = reg.PatCode,
                PeStatus = reg.PeStatus,
                Name = reg.Name,
                Sex = reg.Sex,
                Age = reg.Age,
                AgeUnit = reg.AgeUnit,
                IsActive = reg.IsActive,
                IsCompanyCheck = reg.IsCompanyCheck,
                IsVIP = reg.IsVIP,
                PeCls = reg.PeCls,
                GuidanceType = reg.GuidanceType,
                ReportPrinted = reg.ReportPrinted,
                BookType = reg.BookType,
                TotalPrice = reg.TotalPrice,
                PayStatus = reg.PayStatus,
                CompanyCode = reg.CompanyCode,
                CompanyDeptCode = reg.CompanyDeptCode,
                GuidancePrinted = reg.GuidancePrinted,
                GuidancePrintTime = reg.GuidancePrintTime,
                RecheckNo = reg.RecheckNo
            }).ToArray();

            // 获取缓存数据
            var companyDict = _cacheRepository.DictCompany();
            var deptDict = _cacheRepository.DictCompanyDepartment();

            // 获取套餐名称字典
            var regNos = registerData.Select(x => x.RegNo).ToArray();
            var clusterDict = _peRegisterClusterRepository.FindAll().Where(x => regNos.Contains(x.RegNo))
                .GroupBy(x => x.RegNo)
                .ToDictionary(g => g.Key, g => string.Join("+", g.Select(x => x.ClusName)));

            var selectQueryable = registerData.Select(reg => new RecordPatient
            {
                RegNo = reg.RegNo,
                PatCode = reg.PatCode,
                PeStatus = reg.PeStatus,
                Name = reg.Name,
                Sex = reg.Sex,
                Age = reg.Age,
                AgeUnit = reg.AgeUnit,
                IsActive = reg.IsActive,
                IsCompanyCheck = reg.IsCompanyCheck,
                IsVIP = reg.IsVIP,
                PeCls = reg.PeCls,
                GuidanceType = reg.GuidanceType,
                ReportPrinted = reg.ReportPrinted,
                BookType = reg.BookType.ToString(),
                ClusPrice = reg.TotalPrice,
                PayStatus = reg.PayStatus,
                ClusName = clusterDict.TryGetValue(reg.RegNo, out var clusName) ? clusName : "",
                CompanyCode = reg.CompanyCode,
                CompanyName = companyDict.TryGetValue(reg.CompanyCode ?? "", out var company) ? company.CompanyName : "",
                DeptName = deptDict.TryGetValue(reg.CompanyDeptCode ?? "", out var dept) ? dept.DeptName : "",
                GuidancePrinted = reg.GuidancePrinted,
                GuidancePrintTime = reg.GuidancePrintTime,
                RecheckNo = reg.RecheckNo,
                IsRecheck = !string.IsNullOrEmpty(reg.RecheckNo),
            });

            totalNumber = countList.Count;
            if (filters.PageSize > 0)
            {
                // 先转换为数组，然后使用 LINQ 排序
                var orderedData = selectQueryable.ToArray();
                if (filters.OrderByList?.Count > 0)
                {
                    // 这里需要根据 OrderByList 的具体实现来排序
                    // 暂时使用默认排序
                    orderedData = orderedData.OrderBy(x => x.RegNo).ToArray();
                }

                var skip = (filters.PageNumber - 1) * filters.PageSize;
                regList.Record = orderedData.Skip(skip).Take(filters.PageSize).ToArray();
                totalPage = (int)Math.Ceiling(totalNumber * 1.0 / filters.PageSize);
            }
            else
            {
                var orderedData = selectQueryable.ToArray();
                if (filters.OrderByList?.Count > 0)
                {
                    orderedData = orderedData.OrderBy(x => x.RegNo).ToArray();
                }
                regList.Record = orderedData;
                totalPage = 1;
            }
            return regList;
        }

        /// <summary>
        /// 获取登记资料列表
        /// </summary>
        /// <param name="isCompanyCheck"></param>
        /// <param name="queryValue"></param>
        /// <param name="success"></param>
        /// <param name="msg"></param>
        /// <returns></returns>
        public object ReadRegisterByQueryType(bool isCompanyCheck, string queryValue, out bool success, ref string msg)
        {
            if (string.IsNullOrWhiteSpace(queryValue))
            {
                success = false;
                msg = "查询值不能为空";
                return null;
            }

            queryValue = queryValue.Trim();

            success = true;
            return _registerRepository.ReadRegister(isCompanyCheck, queryValue, true, true, true, true, true,
                x => new RegisterByQueryTypeResult
                {
                    PeStatus = x.PeStatus,
                    PatCode = x.PatCode,
                    RegNo = x.RegNo,
                    Name = x.Name,
                    Sex = x.Sex,
                    Age = x.Age,
                    AgeUnit = x.AgeUnit,
                    IsActive = x.IsActive,
                    IsCompanyCheck = x.IsCompanyCheck,
                    IsVIP = x.IsVIP,
                    PeCls = x.PeCls,
                    CompanyCode = x.CompanyCode,
                    CompanyDeptCode = x.CompanyDeptCode,
                    RegisterTime = x.RegisterTime,
                    CardNo = x.CardNo
                })
                .OrderByDescending(x => x.RegisterTime)
                .ToArray();
        }

        public RegisterNewOrderInfo GetRegisterOrder(string regNo)
        {
            //资料
            var occupationOrder = new OccupationOrderPatient();
            var hisCard = _externalSystemService.GetHisCard(regNo);
            var register = _registerRepository.ReadRegister(regNo).First() ?? throw new BusinessException("该体检号不存在");
            var regInfo = _mapper.Map<RegisterPatient>(register);
            regInfo.HisCard = hisCard;

            if (regInfo.IsOccupation)
            {
                occupationOrder = _registerRepository.ReadRegisterOccupation()
                    .Where(x => x.RegNo == regNo)
                    .Select(x => new OccupationOrderPatient
                    {
                        RegNo = x.RegNo,
                        JobId = x.JobId,
                        JobType = x.JobType,
                        JobStatus = x.JobStatus,
                        TotalYearsOfWork = x.TotalYearsOfWork,
                        TotalMonthsOfWork = x.TotalMonthsOfWork,
                        MonitoringType = x.MonitoringType,
                        PeStatus = x.PeStatus,
                        ReportType = x.ReportType,
                        JobName = x.JobName,
                    }).First();

                occupationOrder.HazardInfo = _registerRepository.ReadRegisterOccupationHazard()
                    .Where(x => x.RegNo == regNo)
                    .Select(x => new HazardInfo
                    {
                        HazardousCode = x.HazardousCode,
                        HazardousName = x.HazardousName,
                        StartDateOfHazards = x.StartDateOfHazards,
                        YearsOfHazards = x.YearsOfHazards,
                        MonthsOfHazards = x.MonthsOfHazards
                    }).ToArray();
                occupationOrder.HazardInfo.BatchUpdate(x =>
                {
                    if (_cacheRepository.DictHazardFactor().ContainsKey(x.HazardousCode))
                    {
                        x.IsOtherHazardous = _cacheRepository.DictHazardFactor()[x.HazardousCode].IsOtherHazardous;
                    }
                });
            }

            if (!string.IsNullOrWhiteSpace(regInfo.PhotoUrl))
                regInfo.PhotoUrl = Appsettings.GetSectionValue("WebserviceHub:FileServiceUrl") + regInfo.PhotoUrl;

            //套餐
            var regClus = _registerRepository.ReadRegisterClusters(regNo)
                .Select(x => new RegisterCluster
                {
                    ClusCode = x.ClusCode,
                    ClusName = x.ClusName,
                    Price = x.Price,
                    IsMain = x.IsMain,
                    IsOccupation = x.IsOccupation
                }).ToArray();
            //所有组合
            var regCombQuery =
                  _registerRepository.ReadRegisterCombs(regNo).MergeTable()
                 .InnerJoin<CodeItemComb>((regComb, comb) => regComb.CombCode == comb.CombCode)
                 .LeftJoin<PeSample>((regComb, comb, sample) => regComb.Id == sample.RegCombId)
                 .OrderBy(regComb => regComb.ClsSortIndex)
                 .OrderBy(regComb => regComb.CombSortIndex)
                 .Select((regComb, comb, sample) => new RegisterNewComb
                 {
                     Id = regComb.Id,
                     CombCode = regComb.CombCode,
                     CombName = regComb.CombName,
                     OriginalPrice = regComb.OriginalPrice,
                     Price = regComb.Price,
                     Discount = regComb.Discount,
                     IsPayBySelf = regComb.IsPayBySelf,
                     PayStatus = regComb.PayStatus,
                     ApplicantCode = regComb.ApplicantCode,
                     ApplicantName = regComb.ApplicantName,
                     DiscountOperCode = regComb.DiscountOperCode,
                     DiscountOperName = regComb.DiscountOperName,
                     BeFrom = regComb.BeFrom,
                     IsExamComb = regComb.BeFrom == null ? true : false,
                     IsOccupation = regComb.IsOccupation,
                     IsOrdinary = regComb.IsOrdinary,
                     IsSelfSelected = regComb.IsSelfSelected,
                     IsGathered = SqlFunc.IF(sample.GatherTime != null).Return(true).End(false),
                     Sex = comb.Sex,
                 }).ToArray();
            var checkedCombs = _recordRepository.ReadRecordComb(regNo)
                .Select(recComb => recComb.RegCombId).ToArray();
            regCombQuery.BatchUpdate(x => x.MutexCombs = _cacheRepository.DictComb().ContainsKey(x.CombCode) ? _cacheRepository.DictComb()[x.CombCode].MutexCombs : Array.Empty<string>());
            regCombQuery.BatchUpdate(x => x.IsChecked = checkedCombs.Contains(x.Id));

            return new RegisterNewOrderInfo
            {
                RegisterOrder = new RegisterNewOrder
                {
                    IsCompany = register.IsCompanyCheck,
                    Patient = regInfo,
                    Clusters = regClus,
                    Combs = regCombQuery.Where(x => x.BeFrom.IsNullOrEmpty()).ToArray(),//普通组合
                    TestTubes = regCombQuery.Where(x => !x.BeFrom.IsNullOrEmpty()).ToArray(),//试管材料
                    FeeList = _feeRepository.GetRegisterFeeList(regNo)
                },
                OccupationOrder = occupationOrder
            };
        }

        public RegisterNewOrder GetRegisterClusterAndComb(string regNo)
        {
            //套餐
            var regClus = _registerRepository.ReadRegisterClusters(regNo)
                .Select(x => new RegisterCluster
                {
                    ClusCode = x.ClusCode,
                    ClusName = x.ClusName,
                    Price = x.Price,
                    IsMain = x.IsMain
                }).ToArray();

            //组合
            var regCombQuery = _registerRepository.ReadRegisterCombs(regNo)
                .InnerJoin<CodeItemComb>((regComb, comb) => regComb.CombCode == comb.CombCode)
                 .Select((regComb, comb) => new RegisterNewComb
                 {
                     Id = regComb.Id,
                     CombCode = regComb.CombCode,
                     CombName = regComb.CombName,
                     OriginalPrice = regComb.OriginalPrice,
                     Price = regComb.Price,
                     Discount = regComb.Discount,
                     IsPayBySelf = regComb.IsPayBySelf,
                     PayStatus = regComb.PayStatus,
                     ApplicantCode = regComb.ApplicantCode,
                     ApplicantName = regComb.ApplicantName,
                     DiscountOperCode = regComb.DiscountOperCode,
                     DiscountOperName = regComb.DiscountOperName,
                     BeFrom = regComb.BeFrom,
                     IsExamComb = regComb.BeFrom == null ? true : false,
                     IsOccupation = regComb.IsOccupation,
                     IsOrdinary = regComb.IsOrdinary,
                     IsSelfSelected = regComb.IsSelfSelected,
                 }).ToArray();

            return new RegisterNewOrder()
            {
                Clusters = regClus,
                Combs = regCombQuery.Where(x => x.BeFrom.IsNullOrEmpty()).ToArray(),
                TestTubes = regCombQuery.Where(x => !x.BeFrom.IsNullOrEmpty()).ToArray(),
                FeeList = _feeRepository.GetRegisterFeeList(regNo)
            };
        }

        /// <summary>
        /// 获取体检人、组合信息
        /// </summary>
        /// <param name="regNo">体检号</param>
        /// <returns>PeRegister、PeRegisterCombs</returns>
        public Tuple<PeRegister, PeRegisterComb[]> GetRegisterWithComb(string regNo)
        {
            // 体检人信息
            var register = _registerRepository.ReadRegister(regNo).First();
            //组合
            var regCombs = _registerRepository.ReadRegisterCombs(regNo).ToArray();

            return new Tuple<PeRegister, PeRegisterComb[]>(register, regCombs);
        }

        public string GetPatCodeByCardNo(string cardNo)
        {
            if (string.IsNullOrEmpty(cardNo))
                return _noGeneration.NextPatCode()[0];

            var patCode = _registerRepository.ReadRegister()
                    .Where(x => x.CardNo == cardNo)
                    .OrderBy(x => x.RegisterTime, OrderByType.Desc)
                    .Select(x => x.PatCode)
                    .First();

            return patCode;
        }

        #region 材料费
        public decimal ReadTestTubeMaterialFees(string[] combCodes)
        {
            //获取组合的试管绑定的材料组合
            var barcodeFeeCombs = _clusterCombRepository.ReadCombBarcodeType(combCodes)
                .Select((comb, barType) => barType.FeeCombCode)
                .ToArray();

            if (barcodeFeeCombs.Length == 0)
                return 0;

            return _clusterCombRepository.ReadComb(barcodeFeeCombs)
                .Sum(x => x.Price);
        }

        #endregion

        #region 回收记录/恢复记录
        public bool RecycleRegisterOrder(string[] regNoArray)
        {
            try
            {
                //查询即将删除的数据
                var regList = _peRegisterRepository.FindAll(x => SqlFunc.ContainsArray(regNoArray, x.RegNo)).ToList();
                if (regList.Any(x => x.IsActive))
                {
                    throw new BusinessException("存在已激活的数据,无法回收!");
                }

                //转换为DeletePeRegister
                var delRegList = JsonConvert.DeserializeObject<List<DeletePeRegister>>(JsonConvert.SerializeObject(regList));
                foreach (var item in delRegList)
                    item.DeleteTime = DateTime.Now;

                var regCombs = _registerRepository.ReadRegisterCombs(regNoArray)
                                .Where(x => SqlFunc.ContainsArray(regNoArray, x.RegNo))
                                .ToList();

                if (regList.Count <= 0)
                {
                    throw new BusinessException("选中的数据不存在,请刷新页面查看");
                }

                //已总检的不能删除
                if (regList.Any(x => x.PeStatus != PeStatus.未检查))
                {
                    throw new BusinessException("已检查的订单不能删除");
                }

                //已收费的订单不能删除
                if (regCombs.Any(x => x.PayStatus == PayStatus.收费))
                {
                    throw new BusinessException("已收费的订单不能删除");
                }

                //登记表删除,删除登记表添加
                _dataTranRepository.ExecTran(() =>
                {
                    _delRegisterRepository.Insert(delRegList);
                    _peRegisterRepository.Delete(regList);
                    _logBusinessNewService.RegisterDeleteLog(regNoArray, "回收站");
                    _logBusinessNewService.SaveLogs();
                });
                return true;
            }
            catch (Exception ex)
            {
                throw new BusinessException(ex.Message);
            }
        }

        public DeletedOrder[] GetRecycleRegisterOrder(bool isCompanyCheck, DelOrderQuery delQuery)
        {
            delQuery.DelEndTime = delQuery.DelEndTime.Value.AddDays(1);
            return _registerRepository.ReadDeleteRegister()
                .WhereIF(!string.IsNullOrEmpty(delQuery.QueryValue),
                        (reg) => SqlFunc.Contains(reg.Name, delQuery.QueryValue) ||
                         reg.CardNo == delQuery.QueryValue || reg.Tel == delQuery.QueryValue)
                .WhereIF(!string.IsNullOrEmpty(delQuery.CompanyCode), reg => reg.CompanyCode == delQuery.CompanyCode)
                .WhereIF(!string.IsNullOrEmpty(delQuery.CompanyDeptCode), reg => reg.CompanyDeptCode == delQuery.CompanyDeptCode)
                .Where(reg => SqlFunc.Between(reg.DeleteTime, delQuery.DelStartTime, delQuery.DelEndTime))
                .Where(reg => reg.IsCompanyCheck == isCompanyCheck)
                .OrderBy(reg => reg.DeleteTime)
                .Select(reg => new DeletedOrder
                {
                    PeStatus = reg.PeStatus,
                    PatCode = reg.PatCode,
                    RegNo = reg.RegNo,
                    Name = reg.Name,
                    Sex = reg.Sex,
                    Age = reg.Age,
                    AgeUnit = reg.AgeUnit,
                    IsActive = reg.IsActive,
                    IsCompanyCheck = reg.IsCompanyCheck,
                    IsVIP = reg.IsVIP,
                    PeCls = reg.PeCls,
                    CompanyName = SqlFunc.Subqueryable<CodeCompany>().Where(x => x.CompanyCode == reg.CompanyCode).Select(x => x.CompanyName),
                    DeptName = SqlFunc.Subqueryable<CodeCompanyDepartment>().Where(x => x.DeptCode == reg.CompanyDeptCode).Select(x => x.DeptName),
                    DeleteTime = reg.DeleteTime.Value,
                })
                .ToArray();
        }

        public bool DeleteRegisterOrder(string[] regNoArray, ref string msg)
        {
            try
            {
                if (_peRegisterRepository.Any(x => SqlFunc.ContainsArray(regNoArray, x.RegNo) &&
                            x.PeStatus != PeStatus.未检查))
                {
                    msg = "状态为未检查的记录才能删除!";
                    return false;
                }

                var regInfo = _registerRepository.ReadDeleteRegister()
                             .Where(x => SqlFunc.ContainsArray(regNoArray, x.RegNo))
                             .Select(x => new
                             {
                                 startTime = SqlFunc.AggregateMin(x.RegisterTime),
                                 endTime = SqlFunc.AggregateMax(x.RegisterTime),
                             }).First();

                //登记表删除,删除登记表添加
                _dataTranRepository.ExecTran(() =>
                {
                    _peRegisterCombRepository.SplitTableDelete(regInfo.startTime, regInfo.endTime, x => SqlFunc.ContainsArray(regNoArray, x.RegNo));
                    _peRegisterClusterRepository.Delete(x => SqlFunc.ContainsArray(regNoArray, x.RegNo));
                    _delRegisterRepository.DeleteByIds(regNoArray);
                    _logBusinessNewService.RegisterDeleteLog(regNoArray, "彻底删除");
                    _logBusinessNewService.SaveLogs();
                });
            }
            catch (Exception ex)
            {
                msg = ex.Message;
                return false;
            }
            return true;
        }

        public bool RestoreRegisterOrder(string[] regNoArray, ref string msg)
        {
            try
            {
                //查询需要恢复的数据
                var delRegList = _delRegisterRepository.FindAll(x => SqlFunc.ContainsArray(regNoArray, x.RegNo)).ToList();
                //转换为登记数据
                var regList = JsonConvert.DeserializeObject<List<PeRegister>>(JsonConvert.SerializeObject(delRegList));
                //登记表删除,删除登记表添加
                _dataTranRepository.ExecTran(() =>
                {
                    _peRegisterRepository.Insert(regList);
                    _delRegisterRepository.Delete(delRegList);
                    _logBusinessNewService.RegisterDeleteLog(regNoArray, "恢复记录");
                    _logBusinessNewService.SaveLogs();
                });
            }
            catch (Exception ex)
            {
                msg = ex.Message;
                return false;
            }
            return true;
        }
        #endregion

        #region 体检激活
        public ActiveRecord[] ReadActiveRecord(ActiveQuery activeQuery)
        {
            var sugarQueryable = _registerRepository.ReadRegister();

            // 体检号优先
            if (!string.IsNullOrWhiteSpace(activeQuery.RegNo))
            {
                sugarQueryable.Where(reg => reg.RegNo == activeQuery.RegNo);
            }
            else if (!string.IsNullOrWhiteSpace(activeQuery.CardNo))
            {
                sugarQueryable.Where(reg => reg.CardNo == activeQuery.CardNo);
            }
            else
            {
                if (activeQuery.RegStartTime == null || activeQuery.RegEndTime == null)
                    return Array.Empty<ActiveRecord>();

                activeQuery.RegStartTime = activeQuery.RegStartTime?.Date;
                activeQuery.RegEndTime = activeQuery.RegEndTime?.Date.Add(new TimeSpan(23, 59, 59));

                sugarQueryable
                    .Where(reg => SqlFunc.Between(reg.RegisterTime, activeQuery.RegStartTime, activeQuery.RegEndTime))
                    // 姓名
                    .WhereIF(!string.IsNullOrEmpty(activeQuery.Name), reg => SqlFunc.Contains(reg.Name, activeQuery.Name))
                    // 单位
                    .WhereIF(!string.IsNullOrEmpty(activeQuery.CompanyCode), reg => reg.CompanyCode == activeQuery.CompanyCode && reg.CompanyTimes == activeQuery.CompanyTimes)
                    // 档案卡号
                    .WhereIF(!string.IsNullOrEmpty(activeQuery.PatCode), reg => reg.PatCode == activeQuery.PatCode);
            }

            var activeData = sugarQueryable
               .Where(reg => SqlFunc.ContainsArray(new PeStatus[] { PeStatus.未检查, PeStatus.正在检查 }, reg.PeStatus))
               .Select(reg => new
               {
                   RegNo = reg.RegNo,
                   Name = reg.Name,
                   Sex = reg.Sex,
                   Age = reg.Age,
                   AgeUnit = reg.AgeUnit,
                   CardNo = reg.CardNo,
                   MarryStatus = reg.MarryStatus,
                   PeStatus = reg.PeStatus,
                   PeCls = reg.PeCls,
                   GuidanceType = reg.GuidanceType,
                   IsActive = reg.IsActive,
                   IsVIP = reg.IsVIP,
                   IsCompanyCheck = reg.IsCompanyCheck,
                   CompanyCode = reg.CompanyCode,
                   CompanyDeptCode = reg.CompanyDeptCode
               }).ToArray();

            // 获取缓存数据
            var companyDict = _cacheRepository.DictCompany();
            var deptDict = _cacheRepository.DictCompanyDepartment();

            return activeData.Select(reg => new ActiveRecord
            {
                RegNo = reg.RegNo,
                Name = reg.Name,
                Sex = reg.Sex,
                Age = reg.Age,
                AgeUnit = reg.AgeUnit,
                CardNo = reg.CardNo,
                MarryStatus = reg.MarryStatus,
                PeStatus = reg.PeStatus,
                PeCls = reg.PeCls,
                GuidanceType = reg.GuidanceType,
                IsActive = reg.IsActive,
                IsVIP = reg.IsVIP,
                IsCompanyCheck = reg.IsCompanyCheck,
                CompanyName = companyDict.TryGetValue(reg.CompanyCode ?? "", out var company) ? company.CompanyName : "",
                DeptName = deptDict.TryGetValue(reg.CompanyDeptCode ?? "", out var dept) ? dept.DeptName : "",
            }).ToArray();
        }

        public bool ActivationOrCancel(bool isActive, ActiveRegister activeRegister, ref string msg)
        {
            //如果是激活,激活时间为空则返回信息
            if (isActive && activeRegister.ActiveTime == null)
            {
                msg = "激活时间不能为空!";
                return false;
            }

            var regList = _peRegisterRepository.FindAll(x => SqlFunc.ContainsArray(activeRegister.RegNoArray, x.RegNo)).ToList();
            if (isActive && regList.Any(x => x.IsActive))
            {
                msg = "存在已经激活的数据,请勿重复激活!";
                return false;
            }
            if (regList.Any(x => x.PeStatus != PeStatus.未检查) || regList.Any(x => x.ReportPrinted) || regList.Any(x => x.PayStatus == PaymentStatus.Paid))
            {
                msg = "当前订单状态不支持操作!";
                return false;
            }

            for (int i = 0; i < activeRegister.RegNoArray.Length; i++)
            {
                ExecActivationOrCancel(activeRegister.RegNoArray[i]);
                SendProgressMsg(activeRegister.RegNoArray.Length, i + 1);
            }

            return true;

            #region 本地方法
            void ExecActivationOrCancel(string regNo)
            {
                _dataTranRepository.ExecTran(() =>
                {
                    if (!isActive)
                    {
                        activeRegister.ActiveTime = null;
                        activeRegister.Activator = null;
                    }
                    else
                        activeRegister.Activator = _httpContextUser.UserId;

                    _peRegisterRepository.Update(
                        x => new PeRegister()
                        {
                            IsActive = isActive,
                            ActiveTime = activeRegister.ActiveTime,
                            Activator = activeRegister.Activator
                        },
                        x => x.RegNo == regNo);
                    bool isCompanyCheck = regList.First(x => x.RegNo == regNo).IsCompanyCheck;
                    if (isActive && !isCompanyCheck)
                        _sampleService.GenerateAllSampleNew(regNo, GetRegisterNewCombs(regNo));
                });
                if (isActive)
                    _externalSystemService.SyncOrder(regNo);
                else
                    _externalSystemService.ClearOrder(regNo);
            }

            void SendProgressMsg(int totalCount, int completedCount)
            {
                if (_httpContextUser.UserId.IsNullOrEmpty()) return;

                var msg = new
                {
                    MsgCode = "ActivationOrCancelForBatch",
                    MsgData = new
                    {
                        Message = $"已处理数量 {completedCount}/{totalCount}",
                        CurrentProgressPercentage = Math.Truncate(((float)completedCount / totalCount) * 100)// 当前进度百分比
                    }
                };
                _peimClients.User(_httpContextUser.UserId).SendMsg(msg.ToJson()).Wait();
            }
            #endregion
        }
        #endregion

        #region 批修改个人属性信息(领导标识，VIP标识)
        public BatchUpdateRecord[] ReadBatchUpdateRecord(BatchUpdateQuery batchQuery)
        {
            return _registerRepository.ReadRegister()
               //体检号
               .WhereIF(!string.IsNullOrEmpty(batchQuery.RegNo), x => x.RegNo == batchQuery.RegNo)
               //只查出团体
               .Where(x => x.IsCompanyCheck)
               //姓名
               .WhereIF(!string.IsNullOrEmpty(batchQuery.Name), x => x.Name == batchQuery.Name)
               //档案卡号
               .WhereIF(!string.IsNullOrEmpty(batchQuery.PatCode), x => x.PatCode == batchQuery.PatCode)
                //工作单位
                .WhereIF(!string.IsNullOrEmpty(batchQuery.CompanyCode), x => x.CompanyCode == batchQuery.CompanyCode)
                //登记时间
                .WhereIF(batchQuery.QueryType == 1 && batchQuery.EndTime != null,
                    reg => SqlFunc.Between(reg.RegisterTime.Date, batchQuery.StartTime.Value.Date, batchQuery.EndTime.Value.Date))
                //激活时间（报到时间）
                .WhereIF(batchQuery.QueryType == 2 && batchQuery.EndTime != null,
                    reg => SqlFunc.Between(reg.ActiveTime.Value.Date, batchQuery.StartTime.Value.Date, batchQuery.EndTime.Value.Date))
               .Where(x => SqlFunc.ContainsArray(new int[] { 0, 1, 2 }, x.PeStatus))
               .Select(x => new BatchUpdateRecord
               {
                   PatCode = x.PatCode,
                   RegNo = x.RegNo,
                   PeStatus = x.PeStatus,
                   Name = x.Name,
                   IsActive = x.IsActive,
                   IsCompanyCheck = x.IsCompanyCheck,
                   IsVIP = x.IsVIP,
                   PeCls = x.PeCls,
                   Sex = x.Sex,
                   Age = x.Age,
                   AgeUnit = x.AgeUnit,
                   MarryStatus = x.MarryStatus,
                   CardNo = x.CardNo,
                   IsLeader = SqlFunc.IsNull(x.IsLeader, false)
               }).ToArray();
        }

        public bool BatchUpdateRegisterOrder(BatchUpdateArray batchUpdateArray, ref string msg)
        {
            var regList = _peRegisterRepository.FindAll(x => SqlFunc.ContainsArray(batchUpdateArray.RegNoArray, x.RegNo)).ToList();
            if (regList.Any(x => x.PeStatus == PeStatus.已审核) || regList.Any(x => x.ReportPrinted))
            {
                msg = "存在已审核/已发报告的订单,不支持批修改";
                return false;
            }

            foreach (var item in regList)
            {
                item.PeCls = !Enum.IsDefined(typeof(PeCls), batchUpdateArray.PeCls) ? item.PeCls : batchUpdateArray.PeCls;
                item.IsVIP = batchUpdateArray.IsVIP == null ? item.IsVIP : batchUpdateArray.IsVIP;
                item.IsLeader = batchUpdateArray.IsLeader == null ? item.IsLeader : batchUpdateArray.IsLeader;
            }

            _peRegisterRepository.Update(regList);
            return true;
        }
        #endregion

        #region 批增加/批删除(组合)
        public BatchAddOrDeleteRecord[] ReadBatchAddOrDeleteOrder(BatchAddOrDeleteQuery batchQuery)
        {
            if (string.IsNullOrWhiteSpace(batchQuery.CompanyCode) || batchQuery.CompanyTimes == 0)
                return Array.Empty<BatchAddOrDeleteRecord>();

            return _registerRepository.ReadRegisterOrder()
                .Where((reg, clus) => reg.CompanyCode == batchQuery.CompanyCode && reg.CompanyTimes == batchQuery.CompanyTimes)
                .WhereIF(!string.IsNullOrEmpty(batchQuery.ClusterCode), (reg, clus) => clus.ClusCode == batchQuery.ClusterCode)
                .WhereIF(!string.IsNullOrEmpty(batchQuery.CompanyDeptCode), (reg, clus) => reg.CompanyDeptCode == batchQuery.CompanyDeptCode)
                .Where(reg => SqlFunc.ContainsArray(new int[] { 0, 1, 2 }, reg.PeStatus))
                .Select((reg, clus) => new BatchAddOrDeleteRecord
                {
                    RegisterTime = reg.RegisterTime,
                    RegNo = reg.RegNo,
                    Name = reg.Name,
                    Sex = reg.Sex,
                    Age = reg.Age,
                    AgeUnit = reg.AgeUnit,
                    CompanyName = SqlFunc.Subqueryable<CodeCompany>().Where(x => x.CompanyCode == reg.CompanyCode).Select(x => x.CompanyName),
                })
                .ToArray();
        }

        public bool BatchAddComb(BatchAddOrDeleteArray batchArray, ref string msg)
        {
            // 查出需要增加的组合
            var itemCombList = _basicCodeRepository.ReadCodeItemComb(Array.Empty<string>())
                .Where(x => SqlFunc.ContainsArray(batchArray.CombCodeArray, x.CombCode))
                .Select(x => new CodeItemCombDTO
                {
                    CombCode = x.CombCode.SelectAll(),
                    ClsCodeName = SqlFunc.Subqueryable<CodeItemCls>().Where(y => y.ClsCode == x.ClsCode).Select(y => y.ClsName)
                }).ToList();
            var totalCount = batchArray.RegNoArray.Length;
            var completedCount = 0;
            foreach (var regNo in batchArray.RegNoArray)
            {
                var regInfo = _registerRepository.ReadRegister(regNo).First();
                if (regInfo == null)
                    continue;
                if (regInfo.PeStatus >= PeStatus.已总检)
                    continue;
                //类型不符合则跳过
                if (batchArray.IsOccupation && !regInfo.IsOccupation)
                    continue;
                if (!batchArray.IsOccupation && !regInfo.IsOrdinary)
                    continue;
                if (_mapCompanySettlementPersonRepository.Any(x => x.RegNo == regNo))
                    continue; // 已进入结算阶段，不能修改组合

                var checkedCombs = _recordRepository.ReadRecordComb(regNo)
                    .Select(recComb => recComb.RegCombId).ToArray();
                var alterComb =
                  _registerRepository.ReadRegisterCombs(regNo).MergeTable()
                 .LeftJoin<PeSample>((regComb, sample) => regComb.Id == sample.RegCombId)
                 .WhereIF(batchArray.IsOccupation, regComb => SqlFunc.ContainsArray(batchArray.CombCodeArray, regComb.CombCode) && regComb.IsOccupation == false)
                 .WhereIF(!batchArray.IsOccupation, regComb => SqlFunc.ContainsArray(batchArray.CombCodeArray, regComb.CombCode) && regComb.IsOrdinary == false)
                 .Where((regComb, sample) => sample.GatherTime == null && !SqlFunc.ContainsArray(checkedCombs, regComb.Id))
                 .Select(regComb => regComb).ToList();

                var newCombList = itemCombList
                    .Where(combData => !_peRegisterCombRepository.SplitTableAny(regInfo.RegisterTime, x => x.RegNo == regNo && x.CombCode == combData.CombCode))
                    .Select(combData => new PeRegisterComb
                    {
                        Id = _noGeneration.NextSnowflakeId(),
                        RegNo = regNo,
                        CombCode = combData.CombCode,
                        CombName = combData.CombName,
                        ExamDeptCode = combData.ExamDeptCode,
                        CheckCls = combData.CheckCls,
                        ClsCode = combData.ClsCode,
                        ClsName = combData.ClsCodeName ?? "",
                        OriginalPrice = combData.Price,
                        Price = combData.Price,
                        ReportShow = combData.ReportShow,
                        IsPayBySelf = false,
                        PayStatus = PayStatus.未收费,
                        Discount = 1,
                        CreateTime = DateTime.Now,
                        RegisterTime = regInfo.RegisterTime,
                        IsOrdinary = !batchArray.IsOccupation,
                        IsOccupation = batchArray.IsOccupation,
                        IsSelfSelected = false,
                        ApplicantCode = _httpContextUser.UserId,
                        ApplicantName = _httpContextUser.UserName
                    })
                    .ToList();
                // 保存到表中
                _peRegisterCombRepository.SplitTableInsert(newCombList);
                if (alterComb.Count > 0)
                {
                    if (batchArray.IsOccupation)
                    {
                        alterComb.BatchUpdate(x => x.IsOccupation = true);
                    }
                    else
                    {
                        alterComb.BatchUpdate(x => x.IsOrdinary = true);
                    }
                    _peRegisterCombRepository.SplitTableUpdate(alterComb);
                }
                GenerateTubesAndSamples(regInfo);
                SaveBatchLog(newCombList, alterComb, batchArray.IsOccupation, OperateType.新增);
                _mediator.Publish(new SyncPeRegisterOrderHandle.Data(new string[] { regNo }));
                // 如果激活就同步订单到外部系统
                if (regInfo.IsActive)
                    _externalSystemService.SyncOrder(regNo);

                completedCount++;
                SendProgressMsg(totalCount, completedCount);
            }
            _logBusinessNewService.SaveLogs();

            if (completedCount <= 0)
                SendProgressMsg(totalCount, totalCount); // 无需处理即完成

            return true;

            void SendProgressMsg(int totalCount, int completedCount)
            {
                var msg = new
                {
                    MsgCode = "CompanyBatchAddOrDelete",
                    MsgData = new
                    {
                        Message = $"已处理数量 {completedCount}/{totalCount}",
                        CurrentProgressPercentage = Math.Truncate(((float)completedCount / totalCount) * 100)// 当前进度百分比
                    }
                };
                _peimClients.User(_httpContextUser.UserId).SendMsg(msg.ToJson()).Wait();

                DebugHelper.WriteLine(msg.ToJson(), ConsoleColor.Green);
            }
        }

        public bool BatchDeleteComb(BatchAddOrDeleteArray batchArray, ref string msg)
        {
            var completedCount = 0;
            _dataTranRepository.ExecTran(() =>
            {
                for (int i = 0; i < batchArray.RegNoArray.Length; i++)
                {
                    var regNo = batchArray.RegNoArray[i];
                    var regInfo = _registerRepository.ReadRegister(regNo).First();
                    if (regInfo.PeStatus >= PeStatus.已总检)
                        continue;
                    //类型不符合则跳过
                    if (batchArray.IsOccupation && !regInfo.IsOccupation)
                        continue;
                    if (!batchArray.IsOccupation && !regInfo.IsOrdinary)
                        continue;
                    if (_mapCompanySettlementPersonRepository.Any(x => x.RegNo == regNo))
                        continue; // 已进入结算阶段，不能修改组合
                    var checkedCombs = _recordRepository.ReadRecordComb(regNo)
                    .Select(recComb => recComb.RegCombId).ToArray();
                    var peRegisterComb =
                      _registerRepository.ReadRegisterCombs(regNo).MergeTable()
                     .LeftJoin<PeSample>((regComb, sample) => regComb.Id == sample.RegCombId)
                     .Where(regComb => SqlFunc.ContainsArray(batchArray.CombCodeArray, regComb.CombCode) && !SqlFunc.ContainsArray(checkedCombs, regComb.Id))
                     .Where((regComb, sample) => sample.GatherTime == null)
                     .Select(regComb => regComb).ToArray();

                    var delCombs = new List<PeRegisterComb>();
                    var alterComb = new List<PeRegisterComb>();
                    if (batchArray.IsOccupation)
                    {
                        delCombs.AddRange(peRegisterComb.Where(x => x.IsOccupation == true && x.IsOrdinary == false));
                        alterComb.AddRange(peRegisterComb.Where(x => x.IsOccupation == true && x.IsOrdinary == true));
                        alterComb.BatchUpdate(x => x.IsOccupation = false);
                    }
                    else
                    {
                        delCombs.AddRange(peRegisterComb.Where(x => x.IsOrdinary == true && x.IsOccupation == false));
                        alterComb.AddRange(peRegisterComb.Where(x => x.IsOrdinary == true && x.IsOccupation == true));
                        alterComb.BatchUpdate(x => x.IsOrdinary = false);
                    }
                    if (delCombs.Count > 0)
                        _peRegisterCombRepository.SplitTableDelete(delCombs);
                    if (alterComb.Count > 0)
                        _peRegisterCombRepository.SplitTableUpdate(alterComb);
                    SaveBatchLog(delCombs, alterComb, batchArray.IsOccupation, OperateType.删除);
                    GenerateTubesAndSamples(regNo);
                    // 同步订单到外部系统
                    _externalSystemService.SyncOrder(regNo);

                    completedCount++;
                    SendProgressMsg(batchArray.RegNoArray.Length, i + 1);
                }
                _logBusinessNewService.SaveLogs();
            });
            _mediator.Publish(new SyncPeRegisterOrderHandle.Data(batchArray.RegNoArray));

            if (completedCount <= 0)
                SendProgressMsg(batchArray.RegNoArray.Length, batchArray.RegNoArray.Length); // 无需处理即完成

            return true;

            void SendProgressMsg(int totalCount, int completedCount)
            {
                var msg = new
                {
                    MsgCode = "CompanyBatchAddOrDelete",
                    MsgData = new
                    {
                        Message = $"已处理数量 {completedCount}/{totalCount}",
                        CurrentProgressPercentage = Math.Truncate(((float)completedCount / totalCount) * 100)// 当前进度百分比
                    }
                };
                _peimClients.User(_httpContextUser.UserId).SendMsg(msg.ToJson()).Wait();

                DebugHelper.WriteLine(msg.ToJson(), ConsoleColor.Green);
            }
        }
        #endregion

        /// <summary>
        /// 查询可打印（指引单、报告、体检标签、检验条码、采血检验条码、非采血检验条码）
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        public List<string> CheckPrintables(PrintQuery query)
        {
            var register = _peRegisterRepository.First(x => x.RegNo == query.RegNo);
            if (register == null)
                return new();

            return query.FilterList.Where(type => HasPrintable(type, register)).Select(type => type.ToString()).ToList();

            #region 本地函数
            bool HasPrintable(PrintFileType type, PeRegister register) => type switch
            {
                PrintFileType.Guidance => register.IsActive || register.IsCompanyCheck,                     // 指引单
                PrintFileType.Report => register.IsActive && HasPrintableReport(register.RegNo),          // 报告
                PrintFileType.PeLabel => register.IsActive && HasPrintablePeLabel(register.RegNo),         // 体检标签
                PrintFileType.Barcode => register.IsActive && HasPrintableBarcode(register.RegNo),         // 检验条码
                PrintFileType.BloodBarcode => register.IsActive && HasPrintableBloodBarcode(register.RegNo),    // 检验采血条码
                PrintFileType.NonBloodBarcode => register.IsActive && HasPrintableNonBloodBarcode(register.RegNo), // 检验非采血条码
                PrintFileType.Gastroscope => register.IsActive && HasPrintableGastroscope(register.RegNo),     // 胃镜申请
                PrintFileType.Colonoscopy => register.IsActive && HasPrintableColonoscopy(register.RegNo),     // 肠镜申请
                PrintFileType.AnesthesiaCost => register.IsActive && HasPrintableAnesthesiaCost(register.RegNo),  // 麻醉费用申请
                _ => false
            };

            bool HasPrintableReport(string regNo)
                => _registerRepository.ReadRegisterNoHosp().Any(x => x.RegNo == regNo && (x.PeStatus == PeStatus.已总检 || x.PeStatus == PeStatus.已审核));

            bool HasPrintablePeLabel(string regNo)
                => _registerRepository.ReadRegCombsItemCls(regNo)
                .Any((regComb, itemCls) => (regComb.PayStatus == PayStatus.收费 || !regComb.IsPayBySelf) && (itemCls.CombPrintTimes > 0 || itemCls.ClsPrintTimes > 0));

            bool HasPrintableBarcode(string regNo)
                => _sampleRepository.ReadPrintableAndPaidSample(regNo, BarcodePrintType.Barcode).Any();

            bool HasPrintableBloodBarcode(string regNo)
                => _sampleRepository.ReadPrintableAndPaidSample(regNo, BarcodePrintType.BloodBarcode).Any();

            bool HasPrintableNonBloodBarcode(string regNo)
                => _sampleRepository.ReadPrintableAndPaidSample(regNo, BarcodePrintType.NonBloodBarcode).Any();

            bool HasPrintableGastroscope(string regNo)
                => _registerRepository.ReadRegisterCombs(regNo).Any(x => x.ClsCode == "23");

            bool HasPrintableColonoscopy(string regNo)
                => _registerRepository.ReadRegisterCombs(regNo).Any(x => x.ClsCode == "24");

            bool HasPrintableAnesthesiaCost(string regNo)
                => _registerRepository.ReadRegisterCombs(regNo).Any(x => x.CombCode == "1806");
            #endregion
        }

        /// <summary>
        /// 通过体检号获取问卷数据
        /// </summary>
        /// <param name="regNo">体检号</param>
        /// <returns></returns>
        public WeChatBookQuestion GetQuestionDataByRegNo(string regNo)
        {
            return _weChatBookQuestionRepository.First(x => x.RegNo == regNo);
        }

        /// <summary>
        /// 根据体检号获取套餐组合记录
        /// </summary>
        /// <param name="regNo"></param>
        /// <returns></returns>
        /// <exception cref="BusinessException"></exception>
        public CopyRegisterClusterComb ReadRegisterClusterComb(string regNo)
        {
            var copyClusterCombs = new CopyRegisterClusterComb();
            var cluster = _registerRepository.ReadRegisterClusters(regNo)?.ToList().ToDictionary(x => x.ClusCode, x => x.ClusName);
            copyClusterCombs.Cluster = cluster;
            var combs = _registerRepository.ReadRegisterCombs(regNo)
                                .Where(x => x.BeFrom == null)
                                .ToList()
                                .ToDictionary(x => x.CombCode, x => x.CombName);
            if (combs.Count == 0)
            {
                throw new BusinessException("该体检号没有已选组合！");
            }
            copyClusterCombs.Combs = combs;
            if (_registerRepository.ReadRegister(regNo).First().IsOccupation)
            {
                var peRegisterOccupation = _peRegisterOccupationRepository.First(x => x.RegNo == regNo);
                if (peRegisterOccupation != null)
                {
                    copyClusterCombs.JobStatus = peRegisterOccupation.JobStatus;
                    copyClusterCombs.JobType = peRegisterOccupation.JobType;
                    copyClusterCombs.JobName = peRegisterOccupation.JobName;
                }
                if (_peRegisterOccupationHazardRepository.Any(x => x.RegNo == regNo))
                {
                    copyClusterCombs.Hazards = _peRegisterOccupationHazardRepository.FindAll(x => x.RegNo == regNo)
                                                .ToDictionary(x => x.HazardousCode, x => x.HazardousName);
                }
            }
            return copyClusterCombs;
        }

        /// <summary>
        /// 查询人员列表用于复制组合
        /// </summary>
        /// <param name="query"></param>
        /// <param name="totalNumber"></param>
        /// <param name="totalPage"></param>
        /// <returns></returns>
        public List<RecordPatient> ReadCopyPersonList(CopyPersonQuery query, ref int totalNumber, ref int totalPage)
        {
            query.BeginDate = query.BeginDate.Date;
            query.EndDate = query.EndDate.Date.Add(new TimeSpan(23, 59, 59));
            var queryable = _registerRepository.ReadRegister()
                    .Where(reg => SqlFunc.Between(reg.RegisterTime, query.BeginDate, query.EndDate))
                    .Where(reg => reg.IsCompanyCheck == query.IsCompanyCheck)
                    .WhereIF(!string.IsNullOrEmpty(query.RegNo), reg => reg.RegNo == query.RegNo)
                    .WhereIF(query.IsOccupation, reg => reg.IsOccupation)
                    .WhereIF(!query.IsOccupation, reg => reg.IsOrdinary == true && reg.IsOccupation == false)
                    .WhereIF(!query.CompanyCode.IsNullOrEmpty(), reg => reg.CompanyCode == query.CompanyCode)
                   ;
            return queryable.Select(reg => new RecordPatient
            {
                PeStatus = reg.PeStatus,
                Name = reg.Name,
                Sex = reg.Sex,
                Age = reg.Age,
                AgeUnit = reg.AgeUnit,
                IsActive = reg.IsActive,
                IsCompanyCheck = reg.IsCompanyCheck,
                IsVIP = reg.IsVIP,
                PeCls = reg.PeCls,
                GuidanceType = reg.GuidanceType,
                ClusName = SqlFunc.Subqueryable<PeRegisterCluster>().Where(z => z.RegNo == reg.RegNo).SelectStringJoin(z => z.ClusName, "+"),
                CompanyCode = reg.CompanyCode,
                CompanyName = SqlFunc.Subqueryable<CodeCompany>().Where(x => x.CompanyCode == reg.CompanyCode).Select(x => x.CompanyName),
                DeptName = SqlFunc.Subqueryable<CodeCompanyDepartment>().Where(x => x.DeptCode == reg.CompanyDeptCode).Select(x => x.DeptName),
                RegNo = reg.RegNo,
                PatCode = reg.PatCode,
                ReportPrinted = reg.ReportPrinted,
                BookType = reg.BookType.ToString()
            }).ToMyPageList(query.PageNumber, query.PageSize, ref totalNumber, ref totalPage);
        }

        /// <summary>
        /// 保存复查组合
        /// </summary>
        /// <param name="recheckComb"></param>
        /// <exception cref="BusinessException"></exception>
        public void SavePeRecheckComb(PeRecheckComb recheckComb)
        {
            if (_peRegisterRepository.Any(x => x.RecheckNo == recheckComb.RegNo))
                throw new BusinessException("已存在该体检号的复查订单，无法修改组合！");
            if (_peRecheckCombRepository.Any(recheckComb))
                _peRecheckCombRepository.Delete(recheckComb);
            _peRecheckCombRepository.Insert(recheckComb);
        }

        /// <summary>
        /// 生成复查订单
        /// </summary>
        /// <param name="regNo"></param>
        /// <returns></returns>
        /// <exception cref="BusinessException"></exception>
        public object CreateRecheckOrder(PeRecheckComb recheckComb)
        {
            bool success = false;
            var peStatus = _peRegisterOccupationRepository.First(x => x.RegNo == recheckComb.RegNo).PeStatus;
            if (peStatus < PeStatus.已总检)
            {
                throw new BusinessException("该体检号未完成总检，无法生成复查订单！");
            }
            if (_peRegisterRepository.Any(x => x.RecheckNo == recheckComb.RegNo))
                throw new BusinessException("已存在该体检号的复查订单，无法生成复查订单！");
            var oldorder = GetRegisterOrder(recheckComb.RegNo);
            oldorder.RegisterOrder.Patient.RegNo = string.Empty;
            oldorder.RegisterOrder.Patient.RecheckNo = recheckComb.RegNo;
            oldorder.RegisterOrder.Patient.IsRecheck = true;
            oldorder.RegisterOrder.Patient.IsActive = false;
            oldorder.RegisterOrder.Patient.BookType = BookType.现场登记;
            oldorder.RegisterOrder.Patient.PeStatus = PeStatus.未检查;
            //引用组合ID从上层体检号获取为空时，获取上层体检号引用的组合id
            var quoteCombCodes = oldorder.RegisterOrder.Combs.Where(x => !recheckComb.RecheckCombs.Contains(x.CombCode)).Select(x => x.Id).ToArray();
            if (quoteCombCodes.Length == 0)
                quoteCombCodes = _peRegisterQuoteCombRepository.FindAll(x => x.RegNo == recheckComb.RegNo).Select(x => x.RegCombId).ToArray();
            oldorder.RegisterOrder.Combs = oldorder.RegisterOrder.Combs.Where(x => recheckComb.RecheckCombs.Contains(x.CombCode)).ToArray();
            oldorder.RegisterOrder.Combs.BatchUpdate(x => x.IsOrdinary = false);
            if (oldorder.RegisterOrder.Combs.Length == 0)
            {
                throw new BusinessException("选择的的体检项目不存在！");
            }
            for (int i = 0; i < oldorder.RegisterOrder.Combs.Length; i++)
            {
                oldorder.RegisterOrder.Combs[i].Id = i;
            }
            oldorder.RegisterOrder.Patient.IsOrdinary = false;
            oldorder.RegisterOrder.Patient.PeCls = null;
            oldorder.RegisterOrder.Patient.IsOccupation = true;

            oldorder.RegisterOrder.Clusters = Array.Empty<RegisterCluster>();
            oldorder.RegisterOrder.TestTubes = Array.Empty<RegisterNewComb>();
            object newRegInfo = new();
            _dataTranRepository.ExecTran(() =>
            {
                if (_peRecheckCombRepository.Any(recheckComb))
                    _peRecheckCombRepository.Delete(recheckComb);
                _peRecheckCombRepository.Insert(recheckComb);
                newRegInfo = NewRegisterOrder(oldorder, ref success);
                var newRegNo = (newRegInfo as dynamic).RegNo;
                var quoteList = quoteCombCodes.Select(x => new PeRegisterQuoteComb
                {
                    RegNo = newRegNo,
                    RegCombId = x,
                }).ToList();
                if (quoteList.Count > 0)
                    _peRegisterQuoteCombRepository.Insert(quoteList);
                _logBusinessNewService.RegisterNewLog(newRegNo, "生成复查");
                _logBusinessNewService.SaveLogs();
            });
            return newRegInfo;
        }

        /// <summary>
        /// 获取复查信息
        /// </summary>
        /// <param name="regNo"></param>
        /// <returns></returns>
        public RecheckInfo ReadRecheckInfo(string regNo)
        {
            var recheckOrderNo = _registerRepository.ReadRegisterNoHosp().Where(x => x.RecheckNo == regNo).Select(x => x.RegNo).First();
            var recheckCombs = _peRecheckCombRepository.First(x => x.RegNo == regNo);
            var s = _registerRepository.ReadRegisterCombs(regNo)
                .Where(x => x.ReportShow)
                .Where(x => x.IsOccupation == true)
                .OrderBy(x => x.ClsSortIndex)
                .OrderBy(x => x.CombSortIndex)
                .Select(x => new RecheckCombInfo
                {
                    CombCode = x.CombCode,
                    CombName = x.CombName,
                    isRecheck = false,
                }).ToArray();
            if (!recheckCombs.IsNullOrEmpty())
            {
                s.BatchUpdate(x => x.isRecheck = recheckCombs.RecheckCombs.Contains(x.CombCode));
            }
            return new RecheckInfo
            {
                RecheckOrderNo = recheckOrderNo,
                RecheckCombs = s
            };
        }

        /// <summary>
        /// 撤销复查
        /// </summary>
        /// <param name="regNo"></param>
        /// <returns></returns>
        /// <exception cref="BusinessException"></exception>
        public bool CancelRecheckOrder(string regNo)
        {
            var recheckOrderNo = _registerRepository.ReadRegisterNoHosp().Where(x => x.RecheckNo == regNo).Select(x => x.RegNo).First() ?? throw new BusinessException("未找到对应的复查订单");
            _dataTranRepository.ExecTran(() =>
            {
                var msg = string.Empty;
                RecycleRegisterOrder(new string[] { recheckOrderNo });
                _peRegisterQuoteCombRepository.Delete(x => x.RegNo == recheckOrderNo);
                _peRecheckCombRepository.Delete(x => x.RegNo == regNo);
            });
            return true;
        }

        public List<RegisterOrder> GetRegisterOrders(RegisterOrderQuery query, ref int totalNumber, ref int totalPage)
        {
            var res = new List<RegisterOrder>();
            // 登记信息
            var regs = _registerRepository.ReadRegisterNoHosp()
                .WhereIF(!query.CardNo.IsNullOrEmpty(), x => x.CardNo == query.CardNo)
                .WhereIF(!query.PeStatuses.IsNullOrEmpty(), x => query.PeStatuses.Contains(x.PeStatus))
                .Select(x => new RegisterPatient
                {
                    RegNo = x.RegNo.SelectAll()
                })
                .OrderBy(query.OrderByList)
                .ToMyPageList(query.PageNumber, query.PageSize, ref totalNumber, ref totalPage);

            foreach (var item in regs)
            {
                var order = new RegisterOrder();
                order.Patient = item;
                order.Patient.OperatorName = _settingRepository.GetUser(order.Patient.OperatorCode).First()?.Name ?? "";
                order.Clusters = _registerRepository.ReadRegisterClusters(item.RegNo)
                                .Select(x => new RegisterCluster
                                {
                                    ClusCode = x.ClusCode.SelectAll()
                                }).ToArray();
                res.Add(order);
            }

            return res;
        }

        public List<PeRegister> GetRegisterForNotAudits(RegisterNotAuditQuery query, ref int totalNumber, ref int totalPage)
        {
            return _registerRepository.ReadRegisterNoHosp()
            .Where(x => x.PeStatus <= PeStatus.已总检)
            .WhereIF(query.ActiveTimeStart.HasValue, x => x.ActiveTime >= query.ActiveTimeStart)
            .WhereIF(query.ActiveTimeEnd.HasValue, x => x.ActiveTime <= query.ActiveTimeEnd)
            .ToMyPageList(query.PageNumber, query.PageSize, ref totalNumber, ref totalPage);
        }

        public bool ModifyRegisterGuidance(RegisterGuidanceDto guidance, ref string msg)
        {
            var register = _registerRepository.ReadRegister(guidance.RegNo).First();
            if (register.IsNullOrEmpty())
            {
                msg = ResxCommon.NotExistRecord;
                return false;
            }

            if (!guidance.GuidanceType.IsNullOrEmpty())
                register.GuidanceType = guidance.GuidanceType;
            if (guidance.GuidancePrinted.HasValue)
                register.GuidancePrinted = guidance.GuidancePrinted;
            if (guidance.GuidancePrintTime.HasValue)
                register.GuidancePrintTime = guidance.GuidancePrintTime;
            if (!guidance.GuidanceRecycler.IsNullOrEmpty())
                register.GuidanceRecycler = guidance.GuidanceRecycler;
            if (guidance.GuidanceRecyclyTime.HasValue)
                register.GuidanceRecyclyTime = guidance.GuidanceRecyclyTime;

            _peRegisterRepository.Update(register);
            msg = ResxCommon.Success;
            return true;
        }

        public bool UpdateQuestionData(WeChatBookQuestion question)
        {
            if (_weChatBookQuestionRepository.Any(x => x.RegNo == question.RegNo))
                return _weChatBookQuestionRepository.Update(question);
            else
            {
                _weChatBookQuestionRepository.Insert(question);
                return true;
            }
        }

        #region 本地方法
        /// <summary>
        /// 创建登记信息
        /// </summary>
        /// <param name="isCompanyCheck"></param>
        /// <param name="patient"></param>
        /// <returns></returns>
        private PeRegister CreatePeRegister(bool isCompanyCheck, RegisterPatient patient, RegisterNewComb[] combs)
        {
            if (!patient.PeCls.HasValue)
            {
                if (patient.IsOccupation)
                {
                    patient.PeCls = PeCls.职业病;
                    patient.IsOrdinary = false;
                }
                else
                {
                    throw new BusinessException("体检分类不能为空");
                }
            }
            else
            {
                if (combs.IsNullOrEmpty())
                {
                    patient.IsOrdinary = true;
                }
                else
                {
                    if (!combs.Any(x => x.IsOrdinary))
                    {
                        patient.PeCls = PeCls.职业病;
                        patient.IsOrdinary = false;
                    }
                }
            }
            // 读取或尝试匹配最后一次体检订单记录
            PeRegister lastRegister = null;
            if (string.IsNullOrEmpty(patient.PatCode))
            {
                // 同名同身份证号一样不能创建新的
                lastRegister = _registerRepository.ReadRegisterNoHosp()
                    .Where(x => x.CardNo == patient.CardNo && x.Name == patient.Name)
                    .OrderByDescending(x => x.RegisterTimes)
                    .First();
            }
            else
            {
                lastRegister = _registerRepository.ReadRegisterNoHosp()
                    .Where(x => x.PatCode == patient.PatCode)
                    .OrderByDescending(x => x.RegisterTimes)
                    .First();
            }

            // ini
            var patCode = lastRegister != null ? lastRegister.PatCode : _noGeneration.NextPatCode()[0];
            var regTimes = lastRegister != null ? lastRegister.RegisterTimes + 1 : 1;

            //// 限制同体检类型多个订单
            //if (regTimes > 1)
            //{
            //    var peStatuses = new PeStatus[] { PeStatus.未检查, PeStatus.未检查, PeStatus.正在检查 };

            //    //检查该体检类型是否存在已激活未总检的记录
            //    bool isExis = _registerRepository.ReadRegister()
            //        .Where(x => x.PatCode == patient.PatCode && x.PeCls == patient.PeCls && x.IsActive)
            //        .Where(x => SqlFunc.ContainsArray(peStatuses, x.PeStatus))
            //        .Any();
            //    if (isExis)
            //    {
            //        errMsg = "存在已激活且未完成的体检记录";
            //        return null;
            //    }
            //}

            // 设置默认指引单格式
            if (string.IsNullOrEmpty(patient.GuidanceType))
                patient.GuidanceType = _systemParameterService.DefaultGuidanceType;

            // 设置默认报告格式
            if (string.IsNullOrEmpty(patient.ReportType))
                patient.ReportType = _systemParameterService.DefaultReportType;

            if (!string.IsNullOrEmpty(patient.CompanyCode) && !string.IsNullOrEmpty(patient.CompanyDeptCode))
            {
                if (!_companyRepository.ReadCodeCompanyDepartment(patient.CompanyCode).Any(x => x.DeptCode == patient.CompanyDeptCode))
                {
                    throw new BusinessException($"单位部门代码{patient.CompanyDeptCode}不属于单位{patient.CompanyCode}");
                }
            }

            var reg = new PeRegister()
            {
                RegNo = _noGeneration.NextRegNo()[0],
                PatCode = patCode,
                Name = patient.Name,
                Sex = patient.Sex,
                Age = patient.Age,
                AgeUnit = patient.AgeUnit,
                Birthday = patient.Birthday,
                CardType = patient.CardType,
                CardNo = patient.CardNo,
                Tel = patient.Tel,
                NativePlace = patient.NativePlace,
                Address = patient.Address,
                MarryStatus = patient.MarryStatus,
                PhotoUrl = patient.PhotoUrl,
                RegisterTime = DateTime.Now,
                RegisterTimes = regTimes,
                IsActive = patient.IsActive,
                ActiveTime = patient.IsActive ? DateTime.Now : null,
                Activator = patient.IsActive ? _httpContextUser.UserId : null,
                PeCls = patient.PeCls.Value,
                PeStatus = PeStatus.未检查,
                GuidanceType = patient.GuidanceType,
                GuidancePrinted = false,
                GuidancePrintTime = null,
                GuidanceRecycler = string.Empty,
                GuidanceRecyclyTime = null,
                ReportType = patient.ReportType,
                ReportPrinted = false,
                IsCompanyCheck = isCompanyCheck,
                CompanyCode = patient.CompanyCode,
                CompanyTimes = patient.CompanyTimes,
                CompanyDeptCode = patient.CompanyDeptCode,
                BookType = BookType.现场登记,
                BookNo = string.Empty,
                BookBeginTime = null,
                BookEndTime = null,
                //JobStatus          = patient.JobStatus,
                JobCode = patient.JobCode,
                JobHistory = patient.JobHistory,
                MedicalHistory = patient.MedicalHistory,
                FamilyMedicalHistory = patient.FamilyMedicalHistory,
                IsVIP = patient.IsVIP,
                IsLeader = patient.IsLeader,
                IsConstitution = patient.IsConstitution,
                RecheckNo = patient.RecheckNo,
                HealthUploaded = false,
                OutsideUploaded = false,
                OperatorCode = patient.OperatorCode,
                Introducer = patient.Introducer,
                Note = patient.Note,
                ChargeModel = patient.ChargeModel,
                IsOccupation = patient.IsOccupation,
                IsOrdinary = patient.IsOrdinary,
                HospCode = _httpContextUser.HospCode
            };

            if (!isCompanyCheck)
            {
                ModifyRegisterCompany(reg, patient);
            }

            return reg;
        }

        /// <summary>
        /// 获取修改后的登记信息
        /// </summary>
        /// <param name="isCompanyCheck"></param>
        /// <param name="patient"></param>
        /// <param name="peRegisterOriginal"></param>
        /// <returns></returns>
        private PeRegister GetRegisterChanged(bool isCompanyCheck, RegisterPatient patient, RegisterCluster[] clusters, RegisterNewComb[] combs, ref PeRegister peRegisterOriginal)
        {
            if (string.IsNullOrWhiteSpace(patient.RegNo))
            {
                throw new BusinessException("不能修改体检号为空的订单信息");

            }
            if (!patient.PeCls.HasValue)
            {
                if (patient.IsOccupation)
                {
                    patient.PeCls = PeCls.职业病;
                    patient.IsOrdinary = false;
                }
                else
                {
                    throw new BusinessException("体检分类不能为空");
                }
            }
            else
            {
                if (combs.IsNullOrEmpty())
                {
                    patient.IsOrdinary = true;
                }
                else
                {
                    if (!combs.Any(x => x.IsOrdinary))
                    {
                        patient.PeCls = PeCls.职业病;
                        patient.IsOrdinary = false;
                    }
                }
            }
            var regQuery = _registerRepository.ReadRegister(patient.RegNo).First();

            if (regQuery == null)
            {
                throw new BusinessException("找不到订单");
            }

            if (regQuery.IsCompanyCheck != isCompanyCheck)
            {
                throw new BusinessException("团检标识不能修改");
            }
            if (regQuery.CompanyCode != patient.CompanyCode)
            {
                throw new BusinessException("单位代码不能修改");
            }

            if (regQuery.IsActive != patient.IsActive)
            {
                regQuery.IsActive = patient.IsActive;
                regQuery.ActiveTime = patient.IsActive ? DateTime.Now : null;
            }

            if (string.IsNullOrEmpty(patient.GuidanceType) || clusters?.Length == 0)
                patient.GuidanceType = _systemParameterService.DefaultGuidanceType;

            if (string.IsNullOrEmpty(patient.ReportType) || clusters?.Length == 0)
                patient.ReportType = _systemParameterService.DefaultReportType;

            #region 深复制原登记信息，用于记录日志
            peRegisterOriginal = TransExp<PeRegister, PeRegister>.Trans(regQuery);
            #endregion

            //todo:只更新修改的字段，关键字段修改写日志
            regQuery.Name = patient.Name;
            regQuery.Sex = patient.Sex;
            regQuery.Age = patient.Age;
            regQuery.AgeUnit = patient.AgeUnit;
            regQuery.Birthday = patient.Birthday;
            regQuery.CardType = patient.CardType;
            regQuery.CardNo = patient.CardNo;
            regQuery.Tel = patient.Tel;
            regQuery.NativePlace = patient.NativePlace;
            regQuery.Address = patient.Address;
            regQuery.MarryStatus = patient.MarryStatus;
            regQuery.PhotoUrl = patient.PhotoUrl.Replace(Appsettings.GetSectionValue("WebserviceHub:FileServiceUrl"), string.Empty);
            //regQuery.RegisterTime       = patinet.RegisterTime;
            regQuery.PeCls = patient.PeCls.Value;
            regQuery.GuidanceType = patient.GuidanceType;
            regQuery.ReportType = patient.ReportType;
            regQuery.CompanyCode = patient.CompanyCode;
            regQuery.CompanyTimes = patient.CompanyTimes;
            regQuery.CompanyDeptCode = patient.CompanyDeptCode;
            //regQuery.JobStatus          = patient.JobStatus;
            regQuery.JobCode = patient.JobCode;
            regQuery.JobHistory = patient.JobHistory;
            regQuery.MedicalHistory = patient.MedicalHistory;
            regQuery.FamilyMedicalHistory = patient.FamilyMedicalHistory;
            regQuery.IsVIP = patient.IsVIP;
            regQuery.IsLeader = patient.IsLeader;
            regQuery.IsConstitution = patient.IsConstitution;
            regQuery.RecheckNo = patient.RecheckNo;
            regQuery.Introducer = patient.Introducer;
            regQuery.Note = patient.Note;
            regQuery.IsOccupation = patient.IsOccupation;
            regQuery.IsOrdinary = patient.IsOrdinary;

            //开单工号非医生角色，修改工号为医生角色则覆盖
            if (!_settingRepository.AnyRole(regQuery.OperatorCode, _systemParameterService.DefaultRoleCodeDoctor)
                && _settingRepository.AnyRole(patient.OperatorCode, _systemParameterService.DefaultRoleCodeDoctor))
            {
                regQuery.OperatorCode = patient.OperatorCode;// 目前作为开单医生
            }

            if (!isCompanyCheck)
            {
                ModifyRegisterCompany(regQuery, patient);
            }

            _logBusinessNewService.RegisterModifyLog(peRegisterOriginal, regQuery);
            return regQuery;
        }

        /// <summary>
        /// 创建登记套餐信息
        /// </summary>
        /// <param name="regNo"></param>
        /// <param name="clusters"></param>
        /// <returns></returns>
        private PeRegisterCluster[] CreatePeRegisterCluster(string regNo, RegisterCluster[] addClusters)
        {
            if (addClusters.Length == 0)
                return Array.Empty<PeRegisterCluster>();
            addClusters.BatchUpdate(x => x.IsMain = false);
            addClusters[0].IsMain = true;
            return addClusters.Select(clus => new PeRegisterCluster
            {
                RegNo = regNo,
                ClusCode = clus.ClusCode,
                ClusName = clus.ClusName,
                Price = clus.Price,
                IsMain = clus.IsMain,
                IsOccupation = clus.IsOccupation,
            }).ToArray();
        }

        /// <summary>
        /// 获取修改后的登记套餐信息
        /// </summary>
        /// <param name="regNo"></param>
        /// <param name="current"></param>
        /// <param name="dels"></param>
        /// <param name="adds"></param>
        private void GetRegisterClusterChanged(
                string regNo,
                RegisterCluster[] current,
            out List<PeRegisterCluster> dels,
            out PeRegisterCluster[] adds)
        {
            var oldRegClus = _registerRepository.ReadRegisterClusters(regNo).ToArray();

            dels =
                (
                    from old in oldRegClus
                    join cur in current on old.ClusCode equals cur.ClusCode into tmp
                    from cur2 in tmp.DefaultIfEmpty()
                    where cur2 == null
                    select old
                 ).ToList();

            RegisterCluster[] newClus =
                (
                    from cur in current
                    join old in oldRegClus on cur.ClusCode equals old.ClusCode into tmp
                    from old2 in tmp.DefaultIfEmpty()
                    where old2 == null
                    select cur
                 ).ToArray();

            adds = CreatePeRegisterCluster(regNo, newClus);
            if (dels.Count > 0)
                _logBusinessNewService.RegisterModifyLog(regNo, "删除了套餐：" + string.Join(",", dels.Select(x => x.ClusName)));
            if (adds.Length > 0)
                _logBusinessNewService.RegisterModifyLog(regNo, "新增了套餐：" + string.Join(",", adds.Select(x => x.ClusName)));
        }

        /// <summary>
        /// 创建登记组合信息（除试管外，试管另单独处理）
        /// </summary>
        /// <param name="regNo"></param>
        /// <param name="registerTime"></param>
        /// <param name="combs"></param>
        /// <returns></returns>
        private PeRegisterComb[] CreatePeRegisterComb(PeRegister register, RegisterNewComb[] addCombs)
        {
            if (addCombs.Length == 0)
                return Array.Empty<PeRegisterComb>();

            var codeCombs = _clusterCombRepository.ReadItemCombCls(addCombs.Select(x => x.CombCode).ToArray())
            .Select((comb, cls) => new
            {
                comb.CombCode,
                comb.CombName,
                comb.Sex,
                comb.Price,
                comb.ExamDeptCode,
                comb.CheckCls,
                comb.ClsCode,
                comb.ReportShow,
                cls.ClsName,
                CombSortIndex = comb.SortIndex,
                ClsSortIndex = cls.SortIndex,
            }).ToArray();

            var illegalCombs = codeCombs.Where(x => x.Sex != Sex.通用 && x.Sex != register.Sex).Select(x => $"[{x.CombName}]").ToArray();
            if (illegalCombs.Any())
                throw new BusinessException("性别冲突的组合：" + string.Join(' ', illegalCombs));

            return (from comb in codeCombs
                    join regCombs in addCombs on comb.CombCode equals regCombs.CombCode
                    select new PeRegisterComb()
                    {
                        Id = regCombs.Id,
                        RegNo = register.RegNo,
                        CombCode = comb.CombCode,
                        CombName = comb.CombName,
                        ExamDeptCode = comb.ExamDeptCode,
                        CheckCls = comb.CheckCls,
                        ClsCode = comb.ClsCode,
                        ClsName = comb.ClsName ?? "",
                        OriginalPrice = regCombs.OriginalPrice,
                        Price = regCombs.Price,
                        ReportShow = comb.ReportShow,
                        Surcharges = 0.0M,
                        Discount = regCombs.Discount,
                        IsPayBySelf = !register.IsCompanyCheck || regCombs.IsPayBySelf, //个检直接自费，团体读项目自费勾选状态
                        PayStatus = PayStatus.未收费,
                        ApplicantCode = _httpContextUser.UserId,
                        ApplicantName = _httpContextUser.UserName,
                        CreateTime = DateTime.Now,
                        RegisterTime = register.RegisterTime,
                        CombSortIndex = comb.CombSortIndex,
                        ClsSortIndex = comb.ClsSortIndex,
                        BeFrom = regCombs.BeFrom,
                        IsOccupation = regCombs.IsOccupation,
                        IsOrdinary = regCombs.IsOrdinary,
                        IsSelfSelected = regCombs.IsSelfSelected
                    }).ToArray();
        }

        /// <summary>
        /// 获取修改后的登记组合信息（除试管外，试管另单独处理）
        /// </summary>
        /// <param name="regInfo"></param>
        /// <param name="current"></param>
        /// <param name="dels"></param>
        /// <param name="alts"></param>
        /// <param name="adds"></param>
        private void GetRegisterCombChanged(
                PeRegister regInfo,
                RegisterNewComb[] current,
            out List<PeRegisterComb> dels,
            out List<PeRegisterComb> alts,
            out PeRegisterComb[] adds)
        {
            var oldRegCombs = _registerRepository.ReadRegisterCombs(regInfo.RegNo)
                //.Where(x => x.BeFrom == null)//不包括条码材料
                .ToArray();

            dels =
                (
                    from old in oldRegCombs
                    join cur in current on old.Id equals cur.Id into tmp
                    from cur2 in tmp.DefaultIfEmpty()
                    where cur2 == null
                    select old
                ).ToList();

            alts = new List<PeRegisterComb>();

            //判断自费标识、折扣折后价、普通职业标识变动

            var modifyCombs = oldRegCombs
                .Join(current, oldComb => oldComb.Id, curComb => curComb.Id, (oldComb, curComb) => new { oldComb, curComb })
                .Where(x => x.oldComb.IsPayBySelf != x.curComb.IsPayBySelf
                            || x.oldComb.Discount != x.curComb.Discount
                            || x.oldComb.Price != x.curComb.Price
                            || x.oldComb.IsOrdinary != x.curComb.IsOrdinary
                            || x.oldComb.IsOccupation != x.curComb.IsOccupation);

            foreach (var comb in modifyCombs)
            {
                var originalComb = TransExp<PeRegisterComb, PeRegisterComb>.Trans(comb.oldComb);
                comb.oldComb.IsPayBySelf = comb.curComb.IsPayBySelf;
                comb.oldComb.Discount = comb.curComb.Discount;
                comb.oldComb.Price = comb.curComb.Price;
                comb.oldComb.IsOrdinary = comb.curComb.IsOrdinary;
                comb.oldComb.IsOccupation = comb.curComb.IsOccupation;
                alts.Add(comb.oldComb);
                _logBusinessNewService.CombModifyLog(originalComb, comb.oldComb);
            }

            RegisterNewComb[] newCombs =
                (
                    from cur in current
                    join old in oldRegCombs on cur.Id equals old.Id into tmp
                    from old2 in tmp.DefaultIfEmpty()
                    where old2 == null
                    select cur
                ).ToArray();

            adds = CreatePeRegisterComb(regInfo, newCombs);
            _logBusinessNewService.CombDeleteLog(regInfo.RegNo, dels.Where(x => x.BeFrom == null).Select(x => x.CombCode).ToArray(), "登记");
            _logBusinessNewService.CombAddLog(regInfo.RegNo, adds.Where(x => x.BeFrom == null).Select(x => x.CombCode).ToArray(), "登记");
        }

        /// <summary>
        /// 保存职业病信息
        /// </summary>
        /// <param name="IsAdd"></param>
        /// <param name="order"></param>
        /// <returns></returns>
        private void SavePeRegisterOccupation(bool IsAdd, OccupationOrderPatient order)
        {
            // 设置默认报告格式
            if (IsAdd)
                order.ReportType = _systemParameterService.DefaultOccupaitonalReportType;

            var regOccupation = new PeRegisterOccupation
            {
                RegNo = order.RegNo,
                JobId = order.JobId,
                JobType = order.JobType,
                JobStatus = order.JobStatus,
                TotalYearsOfWork = order.TotalYearsOfWork,
                TotalMonthsOfWork = order.TotalMonthsOfWork,
                MonitoringType = order.MonitoringType,
                ReportType = string.IsNullOrEmpty(order.ReportType) ? _systemParameterService.DefaultOccupaitonalReportType : order.ReportType,
                JobName = order.JobName,
            };

            var regOccupationHazards = order.HazardInfo.Select(x => new PeRegisterOccupationHazard
            {
                RegNo = order.RegNo,
                HazardousCode = x.HazardousCode,
                HazardousName = x.HazardousName,
                StartDateOfHazards = x.StartDateOfHazards,
                YearsOfHazards = x.YearsOfHazards,
                MonthsOfHazards = x.MonthsOfHazards
            }).ToArray();

            //如果是修改操作，先删除后添加
            if (!IsAdd)
            {
                _peRegisterOccupationRepository.Update(regOccupation, x => new
                {
                    x.JobId,
                    x.JobType,
                    x.JobStatus,
                    x.TotalYearsOfWork,
                    x.TotalMonthsOfWork,
                    x.MonitoringType,
                    x.JobName,
                });
                //分拆，有可能是新增的，也有可能是修改的
                var finalHazards = regOccupationHazards.Select(x => x.HazardousCode).ToArray();
                var updates = regOccupationHazards.Where(h => _peRegisterOccupationHazardRepository.Any(x => x.RegNo == h.RegNo && x.HazardousCode == h.HazardousCode)).ToList();
                var adds = regOccupationHazards.Except(updates).ToList();
                //更新
                _peRegisterOccupationHazardRepository.Update(updates, x => new
                {
                    x.HazardousName,
                    x.StartDateOfHazards,
                    x.YearsOfHazards,
                    x.MonthsOfHazards,
                });
                //新增
                if (adds.Count > 0)
                    _peRegisterOccupationHazardRepository.Insert(adds);
                //删除
                _peRegisterOccupationHazardRepository.Delete(x => x.RegNo == order.RegNo && !SqlFunc.ContainsArray(finalHazards, x.HazardousCode));
            }
            else
            {
                if (regOccupation != null)
                    _peRegisterOccupationRepository.Insert(regOccupation);

                if (regOccupationHazards.Length > 0)
                    _peRegisterOccupationHazardRepository.Insert(regOccupationHazards);
            }
        }

        private PeRegisterOccupation GetRegisterOccupationChanged(OccupationOrder occupationOrder)
        {
            var registerOccupation = _peRegisterOccupationRepository.First(x => x.RegNo == occupationOrder.RegNo);
            if (registerOccupation == null)
            {
                throw new BusinessException("找不到职业病登记信息");
            }
            var registerOccupationOriginal = TransExp<PeRegisterOccupation, PeRegisterOccupation>.Trans(registerOccupation);
            registerOccupation.JobId = occupationOrder.JobId;
            registerOccupation.JobType = occupationOrder.JobType;
            registerOccupation.JobStatus = occupationOrder.JobStatus;
            registerOccupation.TotalYearsOfWork = occupationOrder.TotalYearsOfWork;
            registerOccupation.TotalMonthsOfWork = occupationOrder.TotalMonthsOfWork;
            registerOccupation.MonitoringType = occupationOrder.MonitoringType;
            registerOccupation.JobName = occupationOrder.JobName;

            _logBusinessNewService.PeRegisterOccupationModiryLog(registerOccupationOriginal, registerOccupation);
            return registerOccupation;
        }

        /// <summary>
        /// 获取危害因素变更信息
        /// </summary>
        /// <param name="occupationOrder"></param>
        /// <returns></returns>
        private List<PeRegisterOccupationHazard> GetRegisterOccHazardChanged(OccupationOrderPatient occupationOrder)
        {
            if (occupationOrder.HazardInfo.IsNullOrEmpty())
                return default;

            var regOccHazards = _peRegisterOccupationHazardRepository.FindAll(x => x.RegNo == occupationOrder.RegNo).ToList();
            if (regOccHazards.IsNullOrEmpty())
                return default;

            foreach (var item in regOccHazards)
            {
                var alterHazard = occupationOrder.HazardInfo.FirstOrDefault(x => x.HazardousCode == item.HazardousCode);
                if (alterHazard == null)
                    continue;

                item.StartDateOfHazards = alterHazard.StartDateOfHazards;
                item.YearsOfHazards = alterHazard.YearsOfHazards;
                item.MonthsOfHazards = alterHazard.MonthsOfHazards;
            }

            return regOccHazards;
        }

        private void DeletePeRegisterOccupation(string regNo)
        {
            _peRegisterOccupationRepository.Delete(x => x.RegNo == regNo);
            _peRegisterOccupationHazardRepository.Delete(x => x.RegNo == regNo);
        }
        private void GenerateTubesAndSamples(string regNo)
        {
            var register = _registerRepository.ReadRegister(regNo).First();
            if (register == null) return;
            GenerateTubesAndSamples(register);
        }
        private void GenerateTubesAndSamples(PeRegister register)
        {
            var order = GetRegisterOrder(register.RegNo);
            var tubes = CalculateTestTube(new CalculateTestTubeInput
            {
                Clusters = order.RegisterOrder.Clusters.Select(x => new CalculateCluster { ClusCode = x.ClusCode }).ToList(),
                Combs = order.RegisterOrder.Combs.ToList(),
                TestTubes = order.RegisterOrder.TestTubes.ToList(),
                IsCompanyCheck = true,
            });
            GetRegisterCombChanged
            (
            register,
                 order.RegisterOrder.Combs.Concat(tubes).ToArray(),
                 out List<PeRegisterComb> delCombs,
                 out List<PeRegisterComb> altCombs,
                 out PeRegisterComb[] addCombs
             );
            if (delCombs.Count > 0)
            {
                _peRegisterCombRepository.SplitTableDelete(delCombs);
            }
            if (addCombs.Length > 0)
            {
                _peRegisterCombRepository.SplitTableInsert(addCombs);
            }
            //日志减少记录重新计算的材料费
            //var log1 = GetBatchLog(delCombs.Where(x => !addCombs.Any(y => y.CombCode == x.CombCode && y.BeFrom.All(z => x.BeFrom.Contains(z)))).ToList(), new(), false, UploadType.删除);
            //var log2 = GetBatchLog(addCombs.Where(x => !delCombs.Any(y => y.CombCode == x.CombCode && y.BeFrom.All(z => x.BeFrom.Contains(z)))).ToList(), new(), false, UploadType.新增);
            //var log = $"{log1} {log2}";
            //if (!string.IsNullOrWhiteSpace(log))
            //{
            //    _logBusinessService.Write(register.RegNo, log);
            //}
            _sampleService.GenerateAllSampleNew(register.RegNo, order.RegisterOrder.Combs, order.RegisterOrder.Clusters.Select(x => x.ClusCode).FirstOrDefault());
            _recordNewService.UpdatePeStatus(register);
            _externalSystemCompanyService.SavePersonExtraChargeCombs(register);
        }

        private RegisterNewComb[] GetRegisterNewCombs(string regNo)
        {
            return _registerRepository.ReadRegisterCombs(regNo).Select(x => new RegisterNewComb
            {
                Id = x.Id,
                CombCode = x.CombCode,
                CombName = x.CombName,
                Discount = x.Discount,
                DiscountOperCode = x.ApplicantCode,
                DiscountOperName = x.ApplicantName,
                IsPayBySelf = x.IsPayBySelf,
                PayStatus = x.PayStatus,
                ApplicantCode = x.ApplicantCode,
                ApplicantName = x.ApplicantName,
                BeFrom = x.BeFrom,
                IsExamComb = true,
                IsGathered = false,
                Price = x.Price,
                OriginalPrice = x.OriginalPrice,
                IsOccupation = x.IsOccupation,
                IsOrdinary = x.IsOrdinary,
                IsSelfSelected = x.IsSelfSelected,
                MutexCombs = null,
                IsChecked = false,
            }).ToArray();
        }

        /// <summary>
        /// 创建单位信息
        /// </summary>
        /// <param name="name">单位名称</param>
        /// <param name="isNew">是否新建</param>
        /// <returns></returns>
        /// <exception cref="BusinessException"></exception>
        CodeCompany CreateCodeCompany(string name, out bool isNew)
        {
            if (name.IsNullOrEmpty())
            {
                isNew = default;
                return default;
            }

            name = name.Trim();
            var company = _companyRepository.ReadCompany().First(x => x.CompanyName == name);
            if (!company.IsNullOrEmpty())
            {
                company.CodeCompanyTimes = _companyRepository.ReadCodeCompanyTimes(company.CompanyCode)
                    .OrderByDescending(x => x.CompanyTimes)
                    .ToList();
                if (company.CodeCompanyTimes.IsNullOrEmpty())
                {
                    company.CodeCompanyTimes.Add(CreateCodeCompanyTimes(company.CompanyCode));
                }
                isNew = default;
                return company;
            }

            var compCls = _companyRepository.GetCompanyClsOfFirst();
            if (compCls.IsNullOrEmpty())
                throw new BusinessException("创建单位信息失败，请先创建单位分类！");

            var sortIndex = _companyRepository.MaxCompanySortIndex(compCls.CompanyClsCode) + 1;
            company = new CodeCompany
            {
                CompanyCode = _noGeneration.NextCompanyNo(_httpContextUser.HospCode)[0],
                CompanyName = name,
                HospCode = _httpContextUser.HospCode,
                CompanyClsCode = compCls.CompanyClsCode,
                Parent = "",
                SortIndex = sortIndex,
                CodeCompanyTimes = new(),
            };
            company.CodeCompanyTimes.Add(CreateCodeCompanyTimes(company.CompanyCode));

            isNew = true;
            return company;

            CodeCompanyTimes CreateCodeCompanyTimes(string companyCode)
            {
                return new CodeCompanyTimes
                {
                    CompanyCode = company.CompanyCode,
                    CompanyTimes = 1,
                    BeginDate = DateTime.Now,
                    IsOutside = false
                };
            }
        }

        /// <summary>
        /// 创建单位部门
        /// </summary>
        /// <param name="companyCode">单位码</param>
        /// <param name="name">部门名称</param>
        /// <param name="isNew">是否新建</param>
        /// <returns></returns>
        CodeCompanyDepartment CreateCodeCompanyDepartment(string companyCode, string name, out bool isNew)
        {
            if (companyCode.IsNullOrEmpty() || name.IsNullOrEmpty())
            {
                isNew = default;
                return default;
            }

            name = name.Trim();
            var dept = _companyRepository.ReadCodeCompanyDepartment(companyCode)
                .First(a => a.DeptName == name);
            if (!dept.IsNullOrEmpty())
            {
                isNew = default;
                return dept;
            }

            dept = new CodeCompanyDepartment
            {
                DeptCode = _noGeneration.NextCompanyDeptNo()[0],
                DeptName = name,
                CompanyCode = companyCode,
            };

            isNew = true;
            return dept;
        }

        /// <summary>
        /// 更新登记绑定的单位信息
        /// </summary>
        /// <param name="register"></param>
        /// <param name="patient"></param>
        void ModifyRegisterCompany(PeRegister register, RegisterPatient patient)
        {
            // 新建单位
            if (patient.CompanyCode.IsNullOrEmpty() && !patient.CompanyName.IsNullOrEmpty())
            {
                register.CodeCompany = CreateCodeCompany(patient.CompanyName, out _);
                register.CompanyCode = register.CodeCompany?.CompanyCode;
                register.CompanyTimes = register.CodeCompany?.CodeCompanyTimes?.FirstOrDefault()?.CompanyTimes;
            }
            // 新建部门
            if (patient.CompanyDeptCode.IsNullOrEmpty() &&
                !patient.CompanyDeptName.IsNullOrEmpty())
            {
                register.CodeCompanyDepartment = CreateCodeCompanyDepartment(register.CompanyCode, patient.CompanyDeptName, out _);
                register.CompanyDeptCode = register.CodeCompanyDepartment?.DeptCode;
            }
        }
        #endregion

        #region 实时计算
        /// <summary>
        /// 实时计算添加组合
        /// </summary>
        /// <param name="input"></param>
        /// <param name="message"></param>
        /// <returns></returns>
        public CalculateOutput CalculateComb(CalculateCombInput input)
        {
            var returnCombs = new List<RegisterNewComb>();
            var returnTubes = new List<RegisterNewComb>();
            var returnCluster = new List<CalculateCluster>(input.Clusters);
            CheckComb();
            returnCombs.AddRange(input.Combs.Where(x => !input.DelCombIds.Any(a => a == x.Id)));
            returnTubes.AddRange(input.TestTubes.Where(x => x.PayStatus == PayStatus.收费));
            var combInfos = input.NewCombCodes.Select(x => GetCombInfo(x, true, input.IsCompanyCheck, input.IsOccupation)).ToArray();
            return CalculateCombs(input.Clusters, returnCombs, returnTubes, combInfos, input.IsCompanyCheck);
            //校验添加的组合
            void CheckComb()
            {
                var combExtendInfos = _cacheRepository.DictComb();
                var list = new List<string>();
                foreach (var combCode in input.NewCombCodes)
                {
                    if (!combExtendInfos.ContainsKey(combCode))
                        list.Add(combCode);
                }
                if (list.Count > 0)
                {
                    throw new BusinessException($"组合代码{string.Join(",", list)}不存在");
                }
            }
        }

        /// <summary>
        /// 实时计算添加套餐
        /// </summary>
        /// <param name="input"></param>
        /// <param name="message"></param>
        /// <returns></returns>
        public CalculateOutput CalculateCluster(CalculateClusterInput input)
        {
            var clusterExtendInfos = _cacheRepository.DictCluster();
            var companyClusterExtendInfos = _cacheRepository.DictCompanyCluster();
            if (!Enum.IsDefined(typeof(OperateType), input.OperateType))
            {
                throw new BusinessException("OperateType错误");
            }
            if (input.OperateType == OperateType.删除 && !input.Clusters.Any(x => x.ClusCode == input.OperClusterCode))
            {
                throw new BusinessException("删除的套餐代码不存在");
            }
            if (clusterExtendInfos.ContainsKey(input.OperClusterCode))
            {
                return CalculateCoommonCluster(input);
            }
            else if (companyClusterExtendInfos.ContainsKey(input.OperClusterCode))
            {
                return CalculateCompanyCluster(input);
            }
            else
            {
                throw new BusinessException("添加的套餐代码不存在");
            }
        }

        /// <summary>
        /// 实时计算添加危害因素
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        /// <exception cref="BusinessException"></exception>
        public CalculateOutput CalculateHazardFactors(CalculateHazardInput input)
        {
            var hazardFactorExtendInfos = _cacheRepository.DictHazardFactor();
            var returnCluster = new List<CalculateCluster>(input.Clusters ?? new());
            var returnCombs = new List<RegisterNewComb>();
            var returnTubes = new List<RegisterNewComb>();
            returnTubes.AddRange(input.TestTubes.Where(x => x.PayStatus == PayStatus.收费));
            returnCombs.AddRange(input.Combs);
            Check();
            var newHazardCombs = new HashSet<string>();
            var deleteHazardCombs = new HashSet<string>();
            foreach (var item in input.AddHazardousCodes)
            {
                if (hazardFactorExtendInfos[item].CombsByStatus.ContainsKey(input.StatusCode.GetDescription()))
                {
                    hazardFactorExtendInfos[item].CombsByStatus[input.StatusCode.GetDescription()].ForEach(x => newHazardCombs.Add(x));
                }
            }
            foreach (var item in input.DeleteHazardousCodes)
            {
                if (hazardFactorExtendInfos[item].CombsByStatus.ContainsKey(input.StatusCode.GetDescription()))
                {
                    hazardFactorExtendInfos[item].CombsByStatus[input.StatusCode.GetDescription()].ForEach(x => deleteHazardCombs.Add(x));
                }
            }
            //先删除
            input.OldHazardousCodes.RemoveAll(input.DeleteHazardousCodes);
            var oldHazardCombs = new HashSet<string>();//原本危害因素带出，计算出需要保留的
            foreach (var item in input.OldHazardousCodes)
            {
                if (hazardFactorExtendInfos[item].CombsByStatus.ContainsKey(input.StatusCode.GetDescription()))
                {
                    hazardFactorExtendInfos[item].CombsByStatus[input.StatusCode.GetDescription()].ForEach(x => oldHazardCombs.Add(x));
                }
            }
            var deleteComb = input.Combs.Where(x => deleteHazardCombs.Except(oldHazardCombs).Any(n => n == x.CombCode) && !x.IsSelfSelected && x.PayStatus == PayStatus.未收费).Select(x => x.CombCode).ToArray();
            returnCombs.RemoveAll(x => deleteComb.Contains(x.CombCode) && !x.IsSelfSelected);
            //再添加  
            var newCombs = newHazardCombs.Except(returnCombs.Select(x => x.CombCode))
                            .Select(x => GetCombInfo(x, false, input.IsCompanyCheck, true)).ToArray();
            return CalculateCombs(input.Clusters, returnCombs, returnTubes, newCombs, input.IsCompanyCheck);

            void Check()
            {
                var errList = new List<string>();
                foreach (var item in input.OldHazardousCodes)
                {
                    if (!hazardFactorExtendInfos.ContainsKey(item))
                    {
                        errList.Add(item);
                    }
                }
                foreach (var item in input.AddHazardousCodes)
                {
                    if (!hazardFactorExtendInfos.ContainsKey(item))
                    {
                        errList.Add(item);
                    }
                }
                foreach (var item in input.DeleteHazardousCodes)
                {
                    if (!hazardFactorExtendInfos.ContainsKey(item))
                    {
                        errList.Add(item);
                    }
                }
                if (errList.Count > 0)
                    throw new BusinessException($"危害因素代码{string.Join(",", errList)}不存在");
            }
        }

        /// <summary>
        /// 实时计算试管费
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public List<RegisterNewComb> CalculateTestTube(CalculateTestTubeInput input)
        {
            var returnCombs = new List<RegisterNewComb>();
            var returnTubes = new List<RegisterNewComb>();
            var exceptCombs = new List<string>();
            var dictCluster = _cacheRepository.DictCluster();
            var noMaterialCostClusters = _systemParameterService.ClusterWithoutMaterialCost;
            if (noMaterialCostClusters.Length > 0) 
            {
                var clusters = input.Clusters.Where(x => noMaterialCostClusters.Contains(x.ClusCode));
                foreach (var cluster in clusters)
                {
                    exceptCombs.AddRange(dictCluster[cluster.ClusCode].ClusCombs.Select(x => x.CombCode));
                }
            }
            returnCombs.AddRange(input.Combs.Where(x => !exceptCombs.Contains(x.CombCode)));
            returnTubes.AddRange(input.TestTubes.Where(x => x.PayStatus == PayStatus.收费));
            var output = CalculateCombs(input.Clusters, returnCombs, returnTubes, Array.Empty<CalculateCombInfo>(), input.IsCompanyCheck);
            return output.TestTubes;
        }

        private void SaveBatchLog(List<PeRegisterComb> newComb, List<PeRegisterComb> updateComb, bool isOccupation, OperateType uploadType)
        {
            var log = string.Empty;
            var isOccupationName = isOccupation ? "职业" : "普通";
            if (!newComb.IsNullOrEmpty())
            {
                switch (uploadType)
                {
                    case OperateType.新增:
                        _logBusinessNewService.CombAddLog(newComb[0].RegNo, newComb.Select(x => x.CombCode).ToArray(), "批增加/删除");
                        break;
                    case OperateType.删除:
                        _logBusinessNewService.CombDeleteLog(newComb[0].RegNo, newComb.Select(x => x.CombCode).ToArray(), "批增加/删除");
                        break;
                    default:
                        break;
                }
            }
            if (!updateComb.IsNullOrEmpty())
            {
                updateComb.ForEach(x => _logBusinessNewService.CombModifyLog(x.RegNo, x.CombCode, $"{uploadType.ToString()}{isOccupationName}类型"));
            }
        }

        #region 私有
        /// <summary>
        /// 普通套餐
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        /// <exception cref="BusinessException"></exception>
        private CalculateOutput CalculateCoommonCluster(CalculateClusterInput input)
        {
            var clusterExtendInfos = _cacheRepository.DictCluster();
            var returnCluster = new List<CalculateCluster>(input.Clusters ?? new());
            var returnCombs = new List<RegisterNewComb>(input.Combs);
            var returnTubes = new List<RegisterNewComb>();
            returnTubes.AddRange(input.TestTubes.Where(x => x.PayStatus == PayStatus.收费));
            //type 1增加 2删除
            //先判断互斥再判断交叉
            if (input.OperateType == OperateType.新增)
            {
                var mutexComb = input.Combs.Where(x => clusterExtendInfos[input.OperClusterCode].MutexCombs.Contains(x.CombCode)).Select(x => x.CombCode);
                if (mutexComb.Count() > 0)
                {
                    throw new BusinessException($"套餐代码{input.OperClusterCode}与组合代码{string.Join(",", mutexComb)}互斥！");
                }
                returnCluster.Add(new CalculateCluster
                {
                    ClusCode = input.OperClusterCode,
                    ClusName = clusterExtendInfos[input.OperClusterCode].ClusterName,
                    Price = clusterExtendInfos[input.OperClusterCode].Price
                });
                //交叉           
                var clus = clusterExtendInfos[input.OperClusterCode];
                var newCombs = clus.ClusCombs
                    .Where(x => !input.Combs.Select(x => x.CombCode).Contains(x.CombCode))
                    .Select(x => GetCombInfo(x.CombCode, false, input.IsCompanyCheck, clus.IsOccupation)).ToArray();
                return CalculateCombs(returnCluster, returnCombs, returnTubes, newCombs, input.IsCompanyCheck);
            }
            else
            {
                if (!input.Clusters.Any(x => x.ClusCode == input.OperClusterCode))
                {
                    return new CalculateOutput
                    {
                        Clusters = input.Clusters,
                        Combs = input.Combs
                    };
                }
                returnCluster.RemoveAll(x => x.ClusCode == input.OperClusterCode);
                //计算
                HashSet<string> finalCombs = new HashSet<string>();
                foreach (var clus in returnCluster)
                {
                    clusterExtendInfos[clus.ClusCode].ClusCombs.ForEach(x => finalCombs.Add(x.CombCode));
                }
                var deleteComb = clusterExtendInfos[input.OperClusterCode].ClusCombs.Where(x => !finalCombs.Contains(x.CombCode)).Select(x => x.CombCode);
                returnCombs.RemoveAll(x => deleteComb.Contains(x.CombCode) && !x.IsSelfSelected);
                return CalculateCombs(returnCluster, returnCombs, returnTubes, Array.Empty<CalculateCombInfo>(), input.IsCompanyCheck);
            }
        }

        /// <summary>
        /// 团体套餐
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        private CalculateOutput CalculateCompanyCluster(CalculateClusterInput input)
        {
            var companyClusterExtendInfos = _cacheRepository.DictCompanyCluster();
            var returnCluster = new List<CalculateCluster>(input.Clusters ?? new());
            var returnCombs = new List<RegisterNewComb>();
            var returnTubes = new List<RegisterNewComb>();
            returnTubes.AddRange(input.TestTubes.Where(x => x.PayStatus == PayStatus.收费));
            returnCombs.AddRange(input.Combs);
            //type 1增加 2删除
            //先判断互斥再判断交叉
            if (input.OperateType == OperateType.新增)
            {
                returnCluster.Add(new CalculateCluster
                {
                    ClusCode = input.OperClusterCode,
                    ClusName = companyClusterExtendInfos[input.OperClusterCode].ClusterName,
                    Price = companyClusterExtendInfos[input.OperClusterCode].Price
                });
                //交叉           
                var newCombs = companyClusterExtendInfos[input.OperClusterCode].ClusCombs
                    .Where(x => !input.Combs.Select(x => x.CombCode).Contains(x.CombCode)).
                    Select(x => new CalculateCombInfo
                    {
                        CombCode = x.CombCode,
                        Discount = x.Discount,
                        OriginalPrice = x.OriginalPrice,
                        Price = x.Price,
                        IsSelfSelected = false,
                        IsCompanyCheck = true,
                        IsOccupation = false
                    }).ToArray();
                return CalculateCombs(input.Clusters, returnCombs, returnTubes, newCombs, input.IsCompanyCheck);
            }
            else
            {
                if (!input.Clusters.Any(x => x.ClusCode == input.OperClusterCode))
                {
                    return new CalculateOutput
                    {
                        Clusters = input.Clusters,
                        Combs = input.Combs
                    };
                }
                returnCluster.RemoveAll(x => x.ClusCode == input.OperClusterCode);
                //计算
                HashSet<string> finalCombs = new HashSet<string>();
                foreach (var clus in returnCluster)
                {
                    companyClusterExtendInfos[clus.ClusCode].ClusCombs.ForEach(x => finalCombs.Add(x.CombCode));
                }
                var deleteComb = companyClusterExtendInfos[input.OperClusterCode].ClusCombs.Where(x => !finalCombs.Contains(x.CombCode)).Select(x => x.CombCode);
                returnCombs.RemoveAll(x => deleteComb.Contains(x.CombCode) && !x.IsSelfSelected);
                return CalculateCombs(returnCluster, returnCombs, returnTubes, Array.Empty<CalculateCombInfo>(), input.IsCompanyCheck);
            }
        }

        /// <summary>
        /// 添加组合核心计算方法
        /// </summary>
        /// <param name="clusCodes"></param>
        /// <param name="combs"></param>
        /// <param name="tubes"></param>
        /// <param name="newCombs"></param>
        /// <returns></returns>
        private CalculateOutput CalculateCombs(
            List<CalculateCluster> clusCodes, 
            List<RegisterNewComb> combs, 
            List<RegisterNewComb> tubes,
            CalculateCombInfo[] newCombs, 
            bool IsCompanyCheck)
        {
            var combExtendInfos = _cacheRepository.DictComb();
            var clusterExtendInfos = _cacheRepository.DictCluster();
            var returnCombs = new List<RegisterNewComb>(combs);
            var returnTubes = new List<RegisterNewComb>(tubes);
            var returnCluster = new List<CalculateCluster>(clusCodes);

            #region 添加组合
            foreach (var newComb in newCombs)
            {
                var comb = GetAppendComb(returnCluster, returnCombs, newComb, true);
                if (comb != null)
                {
                    returnCombs.Add(comb);
                    //自动加收
                    var bindCombs = combExtendInfos[newComb.CombCode].BindCombs;
                    foreach (var item in bindCombs)
                    {
                        if (returnCombs.Any(x => x.CombCode == item && x.IsExamComb))
                        {
                            continue;
                        }
                        var bindCombInfo = new CalculateCombInfo
                        {
                            CombCode = item,
                            Discount = 1,
                            OriginalPrice = combExtendInfos[item].ItemComb.Price,
                            Price = combExtendInfos[item].ItemComb.Price,
                            IsSelfSelected = false,
                            IsCompanyCheck = newComb.IsCompanyCheck,
                            IsOccupation = newComb.IsOccupation
                        };
                        var bindComb = GetAppendComb(returnCluster, returnCombs, bindCombInfo, true);
                        if (bindComb != null)
                        {
                            returnCombs.Add(comb);
                        }
                    }
                }
            }
            #endregion

            #region 判断套餐完整
            //foreach (var clus in clusCodes)
            //{
            //    if (!clusterExtendInfos[clus.ClusCode].ClusCombs.All(x => returnCombs.Any(r => r.CombCode == x)))
            //        returnCluster.Remove(clus);
            //}
            #endregion

            #region 试管费 及 静脉采血费用
            if (_systemParameterService.MaterialCost == "计算生成")
            {
                var tubeRelation = _sampleService.CalculateTestTubeBefromNew(returnCombs.ToArray(), clusCodes.Count > 0 ? clusCodes[0].ClusCode : null);
                if (tubeRelation.Any())
                {
                    foreach (var item in tubeRelation.Where(x => x.TubeCombCode != _systemParameterService.VenousBloodComb))
                    {
                        if (item.TubeCombCode.IsNullOrEmpty() || item.BeFrom.Length == 0)//未维护加收材料费组合代码 或者团体删除项目排除
                        {
                            continue;
                        }
                        var tubeInfo = GetCombInfo(item.TubeCombCode, false, !item.IsPayBySelf, false);
                        var tube = GetAppendComb(clusCodes, returnCombs, tubeInfo, false);
                        if (tube != null)
                        {
                            tube.BeFrom = item.BeFroms;
                            tube.Price = item.Price;
                            tube.Discount = item.Discount;
                            returnTubes.Add(tube);
                        }
                    }
                }
                var bloodComb = _systemParameterService.VenousBloodComb;
                if (!bloodComb.IsNullOrEmpty())
                {
                    if (tubeRelation.Any(x => !x.TubeCombCode.IsNullOrEmpty()))
                    {
                        var befrom = new List<long>();
                        tubeRelation.Where(x => !x.TubeCombCode.IsNullOrEmpty()).Select(x => x.BeFrom).ForEach(x => befrom.AddRange(x));
                        //不存在任何管也不需要静脉采血费用
                        if (befrom.Count > 0)
                        {
                            //查是否已添加 有就更新befrom 无则新增
                            var comb = returnTubes.Where(x => x.CombCode == bloodComb).FirstOrDefault();
                            //如果是团体套餐，试管关系中包含采血
                            var companyBlood = tubeRelation.Where(x => x.TubeCombCode == bloodComb).FirstOrDefault();
                            if (comb == null)
                            {
                                var bloodInfo = GetCombInfo(bloodComb, false, combs.Any(x => !x.IsPayBySelf), false);
                                comb = GetAppendComb(clusCodes, returnCombs, bloodInfo, true);
                                if (comb != null)
                                {
                                    comb.BeFrom = befrom.Select(x => x.ToString()).ToArray();
                                    if (companyBlood != null)
                                    {
                                        comb.BeFrom = companyBlood.BeFroms;
                                        comb.Price = companyBlood.Price;
                                        comb.Discount = companyBlood.Discount;
                                    }
                                    returnTubes.Add(comb);
                                }
                            }
                        }
                    }
                }
            }
        #endregion

            return new CalculateOutput
            {
                Clusters = returnCluster,
                Combs = returnCombs,
                TestTubes = returnTubes
            };

            #region 本地方法
            RegisterNewComb GetAppendComb(List<CalculateCluster> clusters, List<RegisterNewComb> combs, CalculateCombInfo newComb, bool isExamComb)
            {
                //套餐互斥
                foreach (var item in clusters)
                {
                    var mutexCombs = clusterExtendInfos.ContainsKey(item.ClusCode) ? clusterExtendInfos[item.ClusCode].MutexCombs : Array.Empty<string>();
                    if (mutexCombs.Any(x => x == newComb.CombCode))
                    {
                        throw (new BusinessException($"组合{combExtendInfos[newComb.CombCode].ItemComb.CombName}与套餐{item.ClusName}互斥！"));
                    }
                }
                //组合互斥
                var mutex = combs.Where(x => combExtendInfos[newComb.CombCode].MutexCombs.Contains(x.CombCode)).Select(x => x.CombName);
                if (mutex.Count() > 0)
                {
                    throw (new BusinessException($"组合{combExtendInfos[newComb.CombCode].ItemComb.CombName}与组合{string.Join(",", mutex)}互斥！"));
                }
                var comb = combExtendInfos[newComb.CombCode].ItemComb;
                return new RegisterNewComb
                {
                    Id = _noGeneration.NextSnowflakeId(),
                    CombCode = comb.CombCode,
                    CombName = comb.CombName,
                    OriginalPrice = newComb.OriginalPrice,
                    Price = newComb.Price,
                    Discount = newComb.Discount,
                    IsPayBySelf = !newComb.IsCompanyCheck,
                    PayStatus = PayStatus.未收费,
                    ApplicantCode = _httpContextUser.UserId,
                    ApplicantName = _httpContextUser.UserName,
                    IsExamComb = isExamComb,
                    DiscountOperCode = _httpContextUser.UserId,
                    DiscountOperName = _httpContextUser.UserName,
                    IsOccupation = newComb.IsOccupation,
                    IsOrdinary = !newComb.IsOccupation,
                    IsGathered = false,
                    IsSelfSelected = newComb.IsSelfSelected
                };
            }
            #endregion
        }
        /// <summary>
        /// 获取组合
        /// </summary>
        /// <param name="combCode"></param>
        /// <returns></returns>
        private CalculateCombInfo GetCombInfo(string combCode, bool isSelfSelect, bool IsCompanyCheck, bool IsOccupation)
        {
            var combExtendInfos = _cacheRepository.DictComb();
            return new CalculateCombInfo
            {
                CombCode = combCode,
                Discount = 1,
                OriginalPrice = combExtendInfos[combCode].ItemComb.Price,
                Price = combExtendInfos[combCode].ItemComb.Price,
                IsSelfSelected = isSelfSelect,
                IsCompanyCheck = IsCompanyCheck,
                IsOccupation = IsOccupation
            };
        }
        #endregion

        #endregion
    }
}