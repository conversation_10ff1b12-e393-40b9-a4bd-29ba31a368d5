﻿using Peis.Model.TableFilter;
using SqlSugar;

namespace Peis.Model.DataBaseEntity
{
    ///<summary>
    ///角色代码表
    ///</summary>
    [SugarTable("SysRole")]
    public class SysRole: IHospCodeFilter
    {
        /// <summary>
        /// 代码
        /// </summary>        
        [SugarColumn(IsNullable = false, IsPrimaryKey = true, Length = 10)]
        public string RoleCode { get; set; }

        /// <summary>
        /// 名称
        /// </summary>        
        [SugarColumn(IsNullable = false, Length = 50)]
        public string Name { get; set; }

        /// <summary>
        /// 状态
        /// </summary>        
        [SugarColumn(IsNullable = false)]
        public bool IsEnabled { get; set; }

        /// <summary>
        /// 备注
        /// </summary>        
        [SugarColumn(IsNullable = true, Length = 200)]
        public string Note { get; set; }

        /// <summary>
        /// 院区编码
        /// </summary>        
        [SugarColumn(IsNullable = false, Length = 1)]
        public string HospCode { get; set; }
    }
}