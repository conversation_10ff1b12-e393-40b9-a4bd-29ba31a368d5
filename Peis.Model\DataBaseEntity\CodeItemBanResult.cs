﻿using SqlSugar;

namespace Peis.Model.DataBaseEntity
{
    ///<summary>
    ///项目结果禁止规则
    ///</summary>
    [SugarTable("CodeItemBanResult")]
    public class CodeItemBanResult
    {
        /// <summary>
        /// 项目代码
        /// </summary>        
        [SugarColumn(IsNullable = false, IsPrimaryKey = true, Length = 6)]
        public string ItemCode { get; set; }

        /// <summary>
        /// 序号
        /// </summary>        
        [SugarColumn(IsNullable = false, IsPrimaryKey = true)]
        public int SerNo { get; set; }

        /// <summary>
        /// 结果
        /// </summary>        
        [SugarColumn(IsNullable = false, ColumnDataType = StaticConfig.CodeFirst_BigString)]
        public string Result { get; set; }
    }
}