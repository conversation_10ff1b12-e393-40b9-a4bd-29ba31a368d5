﻿<Project Sdk="Microsoft.NET.Sdk">

	<PropertyGroup>
		<TargetFramework>net6.0</TargetFramework>
		<Platforms>AnyCPU;x86;x64;ARM64</Platforms>
	</PropertyGroup>

	<ItemGroup>
		<FrameworkReference Include="Microsoft.AspNetCore.App" />
		<PackageReference Include="Aspose.Words" Version="25.2.0" />
		<PackageReference Include="Crane.MethodHook" Version="1.1.1" />
		<PackageReference Include="Docnet.Core" Version="2.6.0" />
		<PackageReference Include="DocumentFormat.OpenXml" Version="3.1.0" />
		<PackageReference Include="Flee" Version="2.0.0" />
		<PackageReference Include="Microsoft.Extensions.Caching.Abstractions" Version="7.0.0" />
		<PackageReference Include="Microsoft.Extensions.Configuration.Json" Version="7.0.0" />
		<PackageReference Include="Microsoft.Extensions.Http" Version="7.0.0" />
		<PackageReference Include="MiniExcel" Version="1.36.0" />
		<PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
		<PackageReference Include="Portable.BouncyCastle" Version="1.9.0" />
		<PackageReference Include="Serilog.AspNetCore" Version="8.0.0" />
		<PackageReference Include="Serilog.Sinks.Async" Version="1.5.0" />
		<PackageReference Include="Serilog.Sinks.Seq" Version="7.0.0" />
		<PackageReference Include="SixLabors.ImageSharp" Version="3.1.10" />
		<PackageReference Include="System.Drawing.Common" Version="8.0.0" />
		<PackageReference Include="System.Runtime.Caching" Version="9.0.6" />
		<PackageReference Include="Volo.Abp.AspNetCore" Version="6.0.3" />
		<PackageReference Include="Volo.Abp.Autofac" Version="6.0.3" />
		<PackageReference Include="SqlSugarCoreNoDrive" Version="5.1.4.165" />
		<PackageReference Include="AutoMapper.Extensions.Microsoft.DependencyInjection" Version="12.0.0" />
	</ItemGroup>

</Project>
