﻿namespace Peis.Model.DataBaseEntity;

/// <summary>
/// 随访处理情况模板
/// </summary>
[SugarTable(tableName: nameof(CodeFollowUpResultTemplate), tableDescription: "随访处理情况模板")]
public class CodeFollowUpResultTemplate
{
    /// <summary>
    /// 模板Id
    /// </summary>        
    [SugarColumn(IsPrimaryKey = true)]
    public long Id { get; set; }

    /// <summary>
    /// 模板序号
    /// </summary>        
    public int SortIndex { get; set; }

    /// <summary>
    /// 模板内容
    /// </summary>
    [SugarColumn(ColumnDataType = StaticConfig.CodeFirst_BigString)]
    public string Content { get; set; }
}
