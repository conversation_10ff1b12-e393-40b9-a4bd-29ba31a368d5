﻿using SqlSugar;
using System;

namespace Peis.Model.DataBaseEntity
{
    ///<summary>
    ///图像记录表
    ///</summary>
    [SugarTable("PeRecordImage")]
    public class PeRecordImage
    {
        /// <summary>
        /// 图像记录id
        /// </summary>        
        [SugarColumn(IsNullable = false, IsPrimaryKey = true)]
        public long Id { get; set; }

        /// <summary>
        /// 体检号
        /// </summary>        
        [SugarColumn(IsNullable = false, Length = 12)]
        public string RegNo { get; set; }

        /// <summary>
        /// 登记组合id
        /// </summary>        
        [SugarColumn(IsNullable = false)]
        public long RegCombId { get; set; }

        /// <summary>
        /// 组合码
        /// </summary>        
        [SugarColumn(IsNullable = false, Length = 8)]
        public string CombCode { get; set; }

        /// <summary>
        /// 图像路径
        /// </summary>        
        [SugarColumn(IsNullable = false, Length = 250)]
        public string ImagePath { get; set; }

        /// <summary>
        /// 操作员代码
        /// </summary>        
        [SugarColumn(IsNullable = false, Length = 10)]
        public string OperatorCode { get; set; }

        /// <summary>
        /// 操作时间
        /// </summary>        
        [SugarColumn(IsNullable = false)]
        public DateTime OperateTime { get; set; }
    }
}