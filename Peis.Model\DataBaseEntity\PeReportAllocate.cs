﻿using SqlSugar;
using System;

namespace Peis.Model.DataBaseEntity
{
    ///<summary>
    ///体检分配主检表
    ///</summary>
    [SugarTable("PeReportAllocate")]
    public class PeReportAllocate
    {
        /// <summary>
        /// 体检号
        /// </summary>        
        [SugarColumn(IsNullable = false, IsPrimaryKey = true, Length = 12)]
        public string RegNo { get; set; }

        /// <summary>
        /// 主检医生代码
        /// </summary>        
        [SugarColumn(IsNullable = false, Length = 10)]
        public string CheckDoctorCode { get; set; }

        /// <summary>
        /// 分配时间
        /// </summary>        
        [SugarColumn(IsNullable = false)]
        public DateTime AllocateTime { get; set; }
    }
}