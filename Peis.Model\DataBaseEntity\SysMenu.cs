﻿using SqlSugar;

namespace Peis.Model.DataBaseEntity
{
    ///<summary>
    ///菜单代码表
    ///</summary>
    [SugarTable("SysMenu")]
    public class SysMenu
    {
        /// <summary>
        /// 菜单代码
        /// </summary>        
        [SugarColumn(IsNullable = false, IsPrimaryKey = true, Length = 50)]
        public string MenuCode { get; set; }

        /// <summary>
        /// 标题图标
        /// </summary>        
        [SugarColumn(IsNullable = true, Length = 50)]
        public string Icon { get; set; }

        /// <summary>
        /// 标题名称
        /// </summary>        
        [SugarColumn(IsNullable = false, Length = 50)]
        public string Title { get; set; }

        /// <summary>
        /// 英文名称
        /// </summary>        
        [SugarColumn(IsNullable = false, Length = 50)]
        public string Name { get; set; }

        /// <summary>
        /// 路径
        /// </summary>        
        [SugarColumn(IsNullable = false, Length = 100)]
        public string Path { get; set; }

        /// <summary>
        /// 父级的菜单id,如果是一级菜单则为0
        /// </summary>        
        [SugarColumn(IsNullable = false, Length = 50)]
        public string ParentCode { get; set; }

        /// <summary>
        /// 菜单排列顺序
        /// </summary>        
        [SugarColumn(IsNullable = false)]
        public int SortIndex { get; set; }

        /// <summary>
        /// 菜单状态 1启用 0 禁用
        /// </summary>        
        [SugarColumn(IsNullable = false)]
        public bool IsEnabled { get; set; }

        /// <summary>
        /// 页面标志
        /// </summary>        
        [SugarColumn(IsNullable = false)]
        public bool IsView { get; set; }
    }
}