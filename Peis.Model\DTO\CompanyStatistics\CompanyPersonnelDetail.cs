﻿using Peis.Model.Other.PeEnum;

namespace Peis.Model.DTO.CompanyStatistics
{
    /// <summary>
    /// 单位人员明细报表
    /// </summary>
    public class CompanyPersonnelDetail
    {
        /// <summary>
        /// 体检号
        /// </summary>
        public string RegNo { get; set; }

        /// <summary>
        /// 姓名
        /// </summary>
        public string Name { get; set; }

        /// <summary>
        /// 性别
        /// </summary>
        public Sex? Sex { get; set; }

        /// <summary>
        /// 年龄
        /// </summary>
        public int? Age { get; set; }

        /// <summary>
        /// 证件号码
        /// </summary>
        public string CardNo { get; set; }

        /// <summary>
        /// 体检时间
        /// </summary>
        public string ActiveTime { get; set; }

        /// <summary>
        /// 签收人
        /// </summary>
        public string Signer { get; set; }

        /// <summary>
        /// 签收时间
        /// </summary>
        public string SignTime { get; set; }

        /// <summary>
        /// HIS卡号
        /// </summary>
        public string HisCard { get; set; }
    }
}
