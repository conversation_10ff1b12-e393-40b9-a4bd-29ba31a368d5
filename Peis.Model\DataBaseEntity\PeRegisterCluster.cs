﻿using SqlSugar;

namespace Peis.Model.DataBaseEntity
{
    ///<summary>
    ///登记套餐
    ///</summary>
    [SugarTable("PeRegisterCluster")]
    public class PeRegisterCluster
    {
        /// <summary>
        /// 体检号
        /// </summary>        
        [SugarColumn(IsNullable = false, IsPrimaryKey = true, Length = 12)]
        public string RegNo { get; set; }

        /// <summary>
        /// 套餐号
        /// </summary>        
        [SugarColumn(IsNullable = false, IsPrimaryKey = true, Length = 6)]
        public string ClusCode { get; set; }

        /// <summary>
        /// 套餐名
        /// </summary>        
        [SugarColumn(IsNullable = false, Length = 200)]
        public string ClusName { get; set; }

        /// <summary>
        /// 套餐价格
        /// </summary>        
        [SugarColumn(IsNullable = false)]
        public decimal Price { get; set; }

        /// <summary>
        /// 主套餐标识
        /// </summary>        
        [SugarColumn(IsNullable = false)]
        public bool IsMain { get; set; }

        /// <summary>
        /// 职业病标识
        /// </summary>        
        [SugarColumn(IsNullable = false,DefaultValue ="0")]
        public bool IsOccupation { get; set; }
    }
}