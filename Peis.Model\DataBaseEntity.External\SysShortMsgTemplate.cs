﻿using Peis.Model.DTO.External.ShortMessage;

namespace Peis.Model.DataBaseEntity.External;

/// <summary>
/// 短信模板
/// </summary>
[SugarTable(nameof(SysShortMsgTemplate), TableDescription = "短信模板信息")]
[SugarIndex("index_SysShortMsgTemplate_Name", nameof(Name), OrderByType.Asc)]
public class SysShortMsgTemplate
{
    /// <summary>
    /// 主键Code
    /// </summary>        
    [SugarColumn(IsNullable = false, IsPrimaryKey = true, Length = 64)]
    public string Code { get; set; }

    /// <summary>
    /// 模板名称
    /// </summary>        
    [SugarColumn(IsNullable = false, ColumnDataType = "nvarchar(128)")]
    public string Name { get; set; }

    /// <summary>
    /// 模板类型
    /// </summary>        
    [SugarColumn(IsNullable = false)]
    public EnumShortMsgType Type { get; set; }

    /// <summary>
    /// 模板内容
    /// </summary>        
    [SugarColumn(IsNullable = false, ColumnDataType = "nvarchar(1500)")]
    public string Content { get; set; }

    /// <summary>
    /// 是否启用
    /// </summary>        
    [SugarColumn(IsNullable = false)]
    public bool IsEnable { get; set; }

    /// <summary>
    /// 创建日期
    /// </summary>        
    [SugarColumn(IsNullable = false)]
    public DateTime CreatedTime { get; set; }

    /// <summary>
    /// 修改日期
    /// </summary>        
    [SugarColumn(IsNullable = true)]
    public DateTime? UpdatedTime { get; set; }

    #region Ext

    public void SetCreatedTime(DateTime? dateTime = null)
    {
        CreatedTime = dateTime.HasValue ? dateTime.Value : DateTime.Now;
    }

    public void SetUpdatedTime()
    {
        UpdatedTime = DateTime.Now;
    }

    public void Init([NotNull] string code)
    {
        Code = code;
        IsEnable = true;
    }

    #endregion
}
