﻿using SqlSugar;

namespace Peis.Model.DataBaseEntity
{
    ///<summary>
    ///职业工种信息
    ///</summary>
    [SugarTable("CodeJob")]
    public class CodeJob
    {
        /// <summary>
        /// 职业代码
        /// </summary>        
        [SugarColumn(IsNullable = false, IsPrimaryKey = true, Length = 10)]
        public string JobCode { get; set; }

        /// <summary>
        /// 职业名称
        /// </summary>        
        [SugarColumn(IsNullable = false, Length = 50)]
        public string JobName { get; set; }

        /// <summary>
        /// 拼音码
        /// </summary>        
        [SugarColumn(IsNullable = true, Length = 10)]
        public string PinYinCode { get; set; }

        /// <summary>
        /// 五笔码
        /// </summary>        
        [SugarColumn(IsNullable = true, Length = 10)]
        public string WuBiCode { get; set; }
    }
}