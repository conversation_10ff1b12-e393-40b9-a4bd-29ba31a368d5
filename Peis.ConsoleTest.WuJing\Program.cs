﻿using Microsoft.Extensions.DependencyInjection;
using Peis.Service.WuJing.His.Configuration;
using Peis.Service.WuJing.His.Configuration.FreeSql;
using Peis.Service.WuJing.Pacs.Configuration;
using Peis.Service.WuJing.Pacs.Configuration.FreeSql;
using System.Runtime.InteropServices;

try
{
    //ChineseCharacterTest();// Oracle中文测试
}
catch (Exception e)
{
    Console.WriteLine(e);
}

Console.WriteLine("\n任意键退出...");
Console.ReadKey();

void ChineseCharacterTest()
{
    var services = new ServiceCollection();
    services.AddHisServiceSetup();
    services.AddPacsServiceSetup();

    using var sp = services.BuildServiceProvider();
    Console.WriteLine($"进程架构：{RuntimeInformation.ProcessArchitecture}");

    // His
    Console.WriteLine("连接His数据库");
    var hisSql = sp.GetRequiredService<IFreeSql<HisSqlFlag>>();
    Console.WriteLine("测试Oracle中文乱码");
    var result1 = hisSql.Ado.CommandFluent("select '中文测试（His）' ZH from dual").QuerySingle<string>();
    Console.WriteLine($"执行SQL返回：{result1}");

    // Pacs
    Console.WriteLine("\n连接Pacs数据库");
    var pacsSql = sp.GetRequiredService<IFreeSql<PacsSqlFlag>>();
    Console.WriteLine("测试Oracle中文乱码");
    var result2 = pacsSql.Ado.CommandFluent("select '中文测试（Pacs）' ZH from dual").QuerySingle<string>();
    Console.WriteLine($"执行SQL返回：{result2}");
}