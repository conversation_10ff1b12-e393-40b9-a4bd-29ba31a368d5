﻿namespace Peis.Model.DTO.CompanyData;

/// <summary>
/// 单位次数Dto
/// </summary>
public class CodeCompanyTimesDto
{
    /// <summary>
    /// 单位代码
    /// </summary>        

    public string CompanyCode { get; set; }

    /// <summary>
    /// 次数
    /// </summary>        

    public int CompanyTimes { get; set; }

    /// <summary>
    /// 开始日期
    /// </summary>        

    public DateTime BeginDate { get; set; }

    /// <summary>
    /// 结束日期
    /// </summary>        

    public DateTime EndDate { get; set; }

    /// <summary>
    /// 线上结束日期
    /// </summary>        

    public DateTime OnLineEndDate { get; set; }

    /// <summary>
    /// 发票抬头
    /// </summary>        

    public string InvoiceHeader { get; set; }

    /// <summary>
    /// 税号
    /// </summary>        

    public string TaxID { get; set; }

    /// <summary>
    /// 院内联系人
    /// </summary>        

    public string HospContact { get; set; }

    /// <summary>
    /// 单位联系人
    /// </summary>            

    public string CompanyContact { get; set; }

    /// <summary>
    /// 开户银行
    /// </summary>        

    public string OpenBank { get; set; }

    /// <summary>
    /// 银行账号
    /// </summary>        

    public string BankAccount { get; set; }

    /// <summary>
    /// 电话
    /// </summary>        

    public string Tel { get; set; }

    /// <summary>
    /// 是否外检
    /// </summary>        

    public bool IsOutside { get; set; }

    /// <summary>
    /// 备注
    /// </summary>        

    public string Note { get; set; }
}
