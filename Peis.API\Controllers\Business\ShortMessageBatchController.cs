﻿using Microsoft.AspNetCore.Mvc;
using Peis.Model.DataBaseEntity;
using Peis.Model.DTO.External.ShortMessage;
using Peis.Model.DTO.ShortMessageBatch;
using Peis.Service.IService;
using System.Collections.Generic;

namespace Peis.API.Controllers.Business
{
    /// <summary>
    /// 短信批量发送控制器
    /// </summary>
    [Route("api/[controller]")]
    [ApiController]
    public class ShortMessageBatchController:BaseApiController
    {
        private readonly IShortMessageBatchService _shortMessageBatchService;

        public ShortMessageBatchController(IShortMessageBatchService shortMessageBatchService)
        {
            _shortMessageBatchService = shortMessageBatchService;
        }

        /// <summary>
        /// 短信批量导入发送
        /// </summary>
        /// <param name="list"></param>
        /// <returns></returns>
        [HttpPost("BatchImportShortMsgAndSend")]
        [ProducesResponseType(typeof(List<BatchImportShortMessage>), 200)]
        public IActionResult PeComprehensiveQuery([FromBody] List<BatchImportShortMessage> list)
        {
            result.ReturnData = _shortMessageBatchService.BatchImportShortMsgAndSend(list);
            result.Success = true;
            return Ok(result);
        }

        /// <summary>
        /// 创建并发送短信
        /// </summary>
        /// <param name="shortMessage"></param>
        /// <returns></returns>
        [HttpPost("CreateShortMsgAndSend")]
        [ProducesResponseType(typeof(BatchImportShortMessage), 200)]
        public IActionResult PeComprehensiveQuery([FromBody] BatchImportShortMessage shortMessage)
        {
            result.ReturnData = _shortMessageBatchService.CreateShortMsgAndSend(shortMessage);
            result.Success = true;
            return Ok(result);
        }

        /// <summary>
        /// 查询批量发送短信记录
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        [HttpPost("QueryBatchSendShortMessage")]
        [ProducesResponseType(typeof(List<PeBatchImportShortMessage>), 200)]
        public IActionResult QueryBatchSendShortMessage([FromBody] ShortMessageBatchQuery query)
        {
            result.ReturnData = _shortMessageBatchService.QueryBatchSendShortMessage(query);
            result.Success = true;
            return Ok(result);
        }

        /// <summary>
        /// 查询人员信息
        /// </summary>
        /// <param name="keyWord">档案卡号，姓名，手机号</param>
        /// <returns></returns>
        [HttpPost("QueryPatientInfo")]
        public IActionResult QueryPatientInfo([FromQuery] string keyWord)
        {
            result.ReturnData = _shortMessageBatchService.QueryPatientInfo(keyWord);
            result.Success = true;
            return Ok(result);
        }
    }
}
