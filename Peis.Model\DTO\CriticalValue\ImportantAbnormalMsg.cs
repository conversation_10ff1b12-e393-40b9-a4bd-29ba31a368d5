﻿using Peis.Model.Other.PeEnum;
using System;

namespace Peis.Model.DTO.CriticalValue
{
    /// <summary>
    /// 重大异常消息
    /// </summary>
    public class ImportantAbnormalMsg
    {
        /// <summary>
        /// 重大异常处理状态 未处理 = 1, 已处理 = 2
        /// </summary>
        public CriticalValueState State { get; set; }

        /// <summary>
        /// 危急值Id 
        /// </summary>
        public long Id { get; set; }

        /// <summary>
        /// 体检号
        /// </summary>
        public string RegNo { set; get; }

        /// <summary>
        /// 患者姓名
        /// </summary>
        public string Name { get; set; }

        /// <summary>
        /// 患者性别
        /// </summary>
        public Sex? Sex { get; set; }

        /// <summary>
        /// 患者年龄
        /// </summary>
        public int? Age { get; set; }

        /// <summary>
        /// 重大异常类型 危急值 = 1, 重大阳性 = 2
        /// </summary>
        public CriticalValueType Type { get; set; }

        /// <summary>
        /// 生成人
        /// </summary>
        public string Creator { get; set; }

        /// <summary>
        /// 生成时间
        /// </summary>
        public DateTime CreateTime { get; set; }

        /// <summary>
        /// 确认人
        /// </summary>
        public string Confirmer { get; set; }

        /// <summary>
        /// 确认时间
        /// </summary>
        public DateTime ConfirmTime { get; set; }

        /// <summary>
        /// 明细异常项目列表
        /// </summary>
        public ImportantAbnormalItem[] Items { get; set; }
    }
}
