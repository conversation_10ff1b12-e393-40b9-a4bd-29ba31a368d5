﻿using Peis.Model.DTO.External.HisApply;
using Peis.Model.Other.PeEnum;
using Peis.Repository.IRepository;
using Peis.Service.IService;
using Peis.Service.IService.ExternalSystem;

namespace Peis.External.Platform.Service.Service.ServiceImpl.Apply
{
    /// <summary>
    /// 检验/检查服务
    /// </summary>
    public class ApplyService : IExternalSystemApplyService
    {
        private readonly ISampleRepository _sampleRepository;
        private readonly IRegisterRepository _registerRepository;
        private readonly ISplitTableRepository<ExtHisApplyComb> _extHisApplyCombRepository;
        private readonly IShenShanRegisterRepository _shenShanRegisterRepository;
        private readonly ISystemParameterService _systemParameterService;

        public ApplyService(
            ISampleRepository sampleRepository,
            IRegisterRepository registerRepository,
            ISplitTableRepository<ExtHisApplyComb> extHisApplyCombRepository,
            IShenShanRegisterRepository shenShanRegisterRepository,
            ISystemParameterService systemParameterService)
        {
            _sampleRepository = sampleRepository;
            _registerRepository = registerRepository;
            _extHisApplyCombRepository = extHisApplyCombRepository;
            _shenShanRegisterRepository = shenShanRegisterRepository;
            _systemParameterService = systemParameterService;
        }

        /// <summary>
        /// 同步申请单
        /// </summary>
        /// <param name="register">登记信息</param>
        /// <param name="regCombAdds">添加的组合</param>
        /// <param name="regCombDels">删除的组合</param>
        public void SyncApply(PeRegister register, PeRegisterComb[] regCombAdds, long[] regCombDels)
        {
            var regCombIds = regCombAdds.Select(x => x.Id).ToArray();
            var extHisApplyCombs = _shenShanRegisterRepository.ReadRegCombs(register.RegNo, regCombIds)
                .Where((reg, regComb, comb) => comb.CheckCls == CheckCls.检验检查 || comb.CheckCls == CheckCls.功能检查)
                .Select((reg, regComb, comb, mapCombHisOrderItem) => new ExtHisApplyComb
                {
                    RegCombId      = regComb.Id,
                    RegNo          = reg.RegNo,
                    Name           = reg.Name,
                    ApplyType      = SqlFunc.IIF(regComb.CheckCls == CheckCls.检验检查, "Lis", "Exam"),
                    ApplyStatus    = "0",
                    PayStatus      = regComb.PayStatus,
                    CombCode       = regComb.CombCode,
                    CombName       = comb.CombName,
                    ClsCode        = regComb.ClsCode,
                    Price          = regComb.Price,
                    HisOrderCode   = mapCombHisOrderItem.HisOrderCode,
                    HisOrderCNName = mapCombHisOrderItem.HisOrderCNName,
                    ApplicantCode  = regComb.ApplicantCode,
                    ApplicantName  = regComb.ApplicantName,
                    RegisterTime   = reg.RegisterTime
                })
            .ToArray();

            _extHisApplyCombRepository.SplitTableDelete(register.RegisterTime, x => SqlFunc.ContainsArray(regCombDels, x.RegCombId));
            if(extHisApplyCombs.Length>0)
                _extHisApplyCombRepository.SplitTableInsert(extHisApplyCombs);
        }

        /// <summary>
        /// 体检检验申请查询
        /// </summary>
        /// <param name="xml"></param>
        /// <param name="respData"></param>
        /// <returns></returns>
        public bool QueryLisApplyInfoPE(string xml, ref string respData)
        {
            // 初始化返回模型
            var result = new QueryLisApplyInfoResult();
            
            var lisInfo = XmlHelper.Deserialize<QueryLisApplyInfo>(xml);// 解析入参
            // 体检号和条码号都为空
            if (string.IsNullOrEmpty(lisInfo.PeNo) && string.IsNullOrEmpty(lisInfo.BarCodeNo))
            {
                result.Note = "请输入体检号/条码号其中一个。";
                return ErrorResponse(result, ref respData);
            }

            // 通过体检号或条码号 查询标本条码表
            var peSampleArray = _sampleRepository.ReadSample()
                                .Where(x => x.RegNo == lisInfo.PeNo || x.SampleNo == lisInfo.BarCodeNo)
                                .ToArray();
            if (peSampleArray.Length == 0)
            {
                result.Note = string.IsNullOrEmpty(lisInfo.BarCodeNo) ? "当前体检号在条码表中，检查不到对应的条码。" : "当前条码号在条码表中，检查不到对应的资料。";
                return ErrorResponse(result,ref respData);
            }

            // 体检号、条码号都不为空
            if (!string.IsNullOrEmpty(lisInfo.PeNo) && !string.IsNullOrEmpty(lisInfo.BarCodeNo))
            {
                var regNo = peSampleArray.FirstOrDefault(x => x.SampleNo == lisInfo.BarCodeNo)?.RegNo ?? "";
                if (!regNo.Equals(lisInfo.PeNo))
                {
                    result.Note = $"传入的体检号参数和条码号对应的体检号不一至，请检查。传入的体检号：{lisInfo.PeNo},条码{lisInfo.BarCodeNo}对应的体检号：{regNo}";
                    return ErrorResponse(result, ref respData);
                }
            }
            else if (!string.IsNullOrEmpty(lisInfo.BarCodeNo))
            {
                lisInfo.PeNo = peSampleArray.FirstOrDefault(x => x.SampleNo == lisInfo.BarCodeNo)?.RegNo ?? "";
            }

            if (!QueryRegInfo(lisInfo.PeNo, ref result, ref respData))
                return false;

            //组装数据
            result.ListApplyInfo = GenerateLisApplyData(lisInfo);
            if (result.ListApplyInfo == null || result.ListApplyInfo.Length == 0)
            {
                result.Note = "当前查询条件没有对应的资料。";
                return ErrorResponse(result,ref respData);
            }

            respData = XmlHelper.Serialize(result);
            return true;
        }

        /// <summary>
        /// 体检检验申请状态更新
        /// </summary>
        /// <param name="xml"></param>
        /// <param name="respData"></param>
        /// <returns></returns>
        public bool SynchLisRequisitionStatus(string xml, ref string respData)
        {
            // 初始化返回模型
            var result = new CommonResult();
          
            var lisInfo = XmlHelper.Deserialize<SynchLisRequisitionStatus>(xml);  // 解析入参
            //if (string.IsNullOrEmpty(lisInfo.OperStaffCode))
            //{
            //    result.Note = "操作职工工号 不能为空。";
            //    return ErrorResponse(result,ref respData);
            //}

            if (string.IsNullOrEmpty(lisInfo.BarCodeNo))
            {
                result.Note = "检验条码号 不能为空。";
                return ErrorResponse(result, ref respData);
            }

            //检验申请单状态: 1 条码打印 2 条码采集 3 条码接收  4 条码退回 5 条码作废 6 报告审核 7 取消审核  
            var applyStatusDic = new Dictionary<string, string>
            {
                { "1", "打印" },
                { "2", "采集" },
                { "3", "接收" },
                { "4", "退回" },
                { "5", "作废" },
                { "6", "审核" },
                { "7", "撤审" }
            };
            if (!applyStatusDic.ContainsKey(lisInfo.RequisitionStatus))
            {
                result.Note = "申请单状态 不正确。";
                return ErrorResponse(result,ref respData);
            }

            // 通过条码号查询标本条码表
            var peSampleArray = _sampleRepository.ReadSample()
                                .Where(x => x.SampleNo == lisInfo.BarCodeNo)
                                .ToArray();
            if (peSampleArray.Length == 0)
            {
                result.Note = "当前条码编号查询不到检验项目信息，可能是对应的个人体检单号未缴费 或者 不存在体检系统。";
                return ErrorResponse(result, ref respData);
            }

            // 通过条码号获取条码表数据
            var sampleData = _sampleRepository.ReadSample()
                .Where(samp => samp.SampleNo == lisInfo.BarCodeNo)
                .Select(samp => new
                {
                    samp.RegNo,
                    samp.CombCode,
                    samp.SampleNo,
                    CombName = SqlFunc.Subqueryable<CodeItemComb>()
                               .Where(x => x.CombCode == samp.CombCode)
                               .Select(x => x.CombName) ?? ""
                })
                .ToArray();

            var regInfo = _registerRepository.ReadRegisterNoHosp()
                         .First(x => x.RegNo == sampleData[0].RegNo);//体检号怎么办
            if (regInfo == null)
            {
                result.Note = "当前体检号查询不到体检信息。";
                return ErrorResponse(result, ref respData);
            }

            foreach (var item in sampleData)
            {
                var hisApplyComb = _extHisApplyCombRepository.First(regInfo.RegisterTime, x => x.RegNo == item.RegNo && x.CombCode == item.CombCode);
                hisApplyComb.ApplyStatus = lisInfo.RequisitionStatus;
                _extHisApplyCombRepository.SplitTableUpdate(hisApplyComb);
            }

            // TODO:如果回传状态是7 取消审核，需要删除检验报告信息，删除危急值推送(参考存储过程)
            respData = XmlHelper.Serialize(result);
            return true;
        }

        /// <summary>
        /// 体检检查申请查询
        /// </summary>
        /// <param name="xml"></param>
        /// <param name="respData"></param>
        /// <returns></returns>
        public bool QueryExamApplyInfoPE(string xml, ref string respData)
        {
            // 初始化返回模型
            var result = new QueryExamApplyInfoResult();

            var examInfo = XmlHelper.Deserialize<QueryExamApplyInfo>(xml);// 解析入参
            var regInfo = _registerRepository.ReadRegisterNoHosp()
                         .First(x => x.RegNo == examInfo.PeNo || x.PatCode == examInfo.PeIndexNo);
            if (regInfo == null)
            {
                result.Note = "体检信息为空!";
                return ErrorResponse(result, ref respData);
            }

            // 根据','拆分第三方的项目分类
            var hisClsArray = examInfo.ExamCategCode.Contains(',') ? examInfo.ExamCategCode.Split(','): new string[] { examInfo.ExamCategCode };
            // 体检的项目分类
            var peClsArray = hisClsArray.Select(categCode => HisClsCodeToPeClsCode(categCode)).ToArray();

            // 组装数据
            result.ExamApplyInfo = GenerateExamApplyData(examInfo,peClsArray);
            if (result.ExamApplyInfo == null || result.ExamApplyInfo.Length == 0)
            {
                result.Note = "当前条件没有查询到资料，或者申请单已登记过。";
                return ErrorResponse(result, ref respData);
            }

            respData = XmlHelper.Serialize(result);
            return true;
        }

        /// <summary>
        /// 体检检查申请状态更新
        /// </summary>
        /// <param name="xml"></param>
        /// <param name="respData"></param>
        /// <returns></returns>
        public bool SynchExamRequisitionStatus(string xml, ref string respData)
        {
            // 初始化返回模型
            var result = new CommonResult();
            var ultrasonicCombs = _systemParameterService.UltrasonicCombs;
            var examInfo = XmlHelper.Deserialize<SynchExamRequisitionStatus>(xml);// 解析入参
            if (string.IsNullOrEmpty(examInfo.ElectrRequisitionNo))
            {
                result.Note = "电子申请单编号 不能为空。";
                return ErrorResponse(result,ref respData);
            }

            if (string.IsNullOrEmpty(examInfo.OperStaffCode))
            {
                result.Note = "操作职工工号 不能为空。";
                return ErrorResponse(result, ref respData);
            }

            //检查申请单状态: 1-接收登记 2-取消接收登记 4-确认执行 5-取消执行 7-报告发布 8-取消发布
            var applyStatusDic = new Dictionary<string, string>
            {
                { "1", "接收登记" },
                { "2", "取消接收登记" },
                { "4", "确认执行" },
                { "5", "取消执行" },
                { "7", "报告发布" },
                { "8", "取消发布" }
            };
            if (!applyStatusDic.ContainsKey(examInfo.RequisitionStatus))
            {
                result.Note = "申请单状态 不正确。";
                return ErrorResponse(result, ref respData);
            }

            var regNo = examInfo.ElectrRequisitionNo[..12];// 体检号
            var combCode = examInfo.ElectrRequisitionNo.Substring(12, examInfo.ElectrRequisitionNo.Length-12);// 组合代码
            int examCount;
            if (combCode == "US")
            {
                examCount = _shenShanRegisterRepository.QueryExamApplyInfo(regNo)
                .Where((reg, applyComb) => applyComb.ApplyType == "Exam")
                .Where((reg, applyComb) => reg.RegNo == regNo && applyComb.ClsCode == "15" && !SqlFunc.ContainsArray(ultrasonicCombs,applyComb.CombCode))
                .Count();
            }
            else
            {
                examCount = _shenShanRegisterRepository.QueryExamApplyInfo(regNo)
                .Where((reg, applyComb) => applyComb.ApplyType == "Exam")
                .Where((reg, applyComb) => reg.RegNo == regNo && applyComb.CombCode == combCode)
                .Count();
            }          

            if (examCount == 0)
            {
                result.Note = "当前电子申请单编号查询不到检查项目信息，可能是对应的个人体检单号未缴费 或者 不存在体检系统。";
                return ErrorResponse(result, ref respData);
            }

            // TODO:联表更新
            // 如果回传状态是8 取消发布，需要删除检查报告信息，删除危急值推送(tj_synchPacsReportPE,tj_synchPacsCriticalPE)
            var regInfo = _registerRepository.ReadRegisterNoHosp()
                         .First(x => x.RegNo == regNo);
            if (regInfo == null)
            {
                result.Note = "当前体检号查询不到体检信息。";
                return ErrorResponse(result, ref respData);
            }

            if (combCode == "US")
            {
                var hisApplyCombs = _extHisApplyCombRepository.FindAll(regInfo.RegisterTime, x => x.RegNo == regNo && x.ClsCode=="15" && !SqlFunc.ContainsArray(ultrasonicCombs, x.CombCode)).ToList();
                hisApplyCombs.BatchUpdate(x => x.ApplyStatus = examInfo.RequisitionStatus);
                _extHisApplyCombRepository.SplitTableUpdate(hisApplyCombs);
            }
            else
            {
                var hisApplyComb = _extHisApplyCombRepository.First(regInfo.RegisterTime, x => x.RegNo == regNo && x.CombCode == combCode);
                hisApplyComb.ApplyStatus = examInfo.RequisitionStatus;
                _extHisApplyCombRepository.SplitTableUpdate(hisApplyComb);
            }

            respData = XmlHelper.Serialize(result);
            return true;
        }

        /// <summary>
        /// 获取体检申请单组合列表   
        /// </summary>
        /// <param name="query">查询</param>
        /// <param name="totalNumber">返回总数</param>
        /// <param name="totalPage">返回总页码</param>
        /// <returns>ExtHisApplyCombs</returns>
        public List<ExtHisApplyComb> GetExtHisApplyCombs(ExtHisApplyCombQuery query,ref int totalNumber,ref int totalPage)
        {
           return  _shenShanRegisterRepository.QueryExamApplyInfo(query.RegNo)
                .Where((reg, applyComb) => reg.RegNo == query.RegNo)
                .WhereIF(!query.ApplyType.IsNullOrEmpty(), (reg, applyComb) => applyComb.ApplyType == query.ApplyType)
                .Select((reg, applyComb)=> applyComb)
                .ToMyPageList(query.PageNumber, query.PageSize, ref totalNumber, ref totalPage);
        }

        /// <summary>
        /// 修改体检申请单组合
        /// </summary>
        /// <param name="applyCombs">数据</param>
        /// <returns>bool</returns>
        public bool BatchModifyExtHisApplyComb(List<ExtHisApplyComb> applyCombs)
        {
            CheckHelper.NotNullAndEmpty(applyCombs, nameof(applyCombs));
            applyCombs = applyCombs.FindAll(x => !x.RegCombId.IsNullOrEmpty() && !x.RegNo.IsNullOrEmpty());

            if (applyCombs.IsNullOrEmpty()) return default;
            return _extHisApplyCombRepository.SplitTableUpdate(applyCombs);
        }

        #region 私有方法
        /// <summary>
        /// 通过体检号查询登记数据
        /// </summary>
        /// <param name="regNo"></param>
        /// <param name="result"></param>
        /// <param name="respData"></param>
        /// <returns></returns>
        private bool QueryRegInfo(string regNo, ref QueryLisApplyInfoResult result, ref string respData)
        {
            var register = _registerRepository.ReadRegisterNoHosp(regNo).First();
            if (register == null)
            {
                result.Note = $"体检号：{regNo}数据不存在！";
                return ErrorResponse(result, ref respData);
            }

            if (register.PeStatus > PeStatus.已检完)
            {
                result.Note = "当前体检号的报告已审核。";
                return ErrorResponse(result, ref respData);
            }
            return true;
        }

        /// <summary>
        /// 生成检验数据
        /// </summary>
        /// <param name="data"></param>
        /// <returns></returns>
        private LisApplyInfoResult[] GenerateLisApplyData(QueryLisApplyInfo data)
        {
            int type = 1;  // 体检号、条码号都不为空
            var noGatherLisCombs = _systemParameterService.NoGatherLisCombs;
            if (!string.IsNullOrEmpty(data.PeNo) && !string.IsNullOrEmpty(data.BarCodeNo)) { }
            else if (!string.IsNullOrEmpty(data.PeNo))
                type = 2;  // 体检号不为空,条码号为空
            else if (!string.IsNullOrEmpty(data.BarCodeNo))
                type = 3;  // 体检号为空,条码号不为空
            var visitCard = _shenShanRegisterRepository.GetHisCard(data.PeNo);
            var result = _shenShanRegisterRepository.QueryLisApplyInfo(data.PeNo)
                .WhereIF(type == 1, (reg, applyComb, sample) => reg.RegNo == data.PeNo && sample.SampleNo == data.BarCodeNo)
                .WhereIF(type == 2, reg => reg.RegNo == data.PeNo)
                .WhereIF(type == 3, (reg, applyComb, sample) => sample.SampleNo == data.BarCodeNo)
                .Where((reg, applyComb) => applyComb.ApplyType == "Lis" && (reg.IsCompanyCheck || applyComb.PayStatus == PayStatus.收费))
                .Select((reg, applyComb, sample) => new LisApplyInfoResult
                {
                    PatName               = reg.Name,
                    PhysiSexCode          = (int)reg.Sex,
                    PhysiSexName          = SqlFunc.IIF(reg.Sex == Sex.男, "男", "女"),
                    ElectrRequisitionNo   = reg.RegNo,
                    OrderNo               = reg.RegNo + applyComb.CombCode,
                    OrderGroupNo          = reg.RegNo,
                    PeIndexNo             = reg.RegNo,
                    PeNo                  = reg.RegNo,
                    EmpiId                = reg.PatCode,
                    Age                   = reg.Age,
                    Address               = reg.Address,// 存储过程没返回
                    ChargeType            = reg.IsCompanyCheck ? "团体" : "个人",
                    RequestedDateTime     = reg.ActiveTime.Value.ToString("yyyyMMddHHmmss"),
                    PhoneNumberHome       = reg.Tel,// 存储过程没返回
                    DateOfBirth           = reg.Birthday == null ? "" : reg.Birthday.Value.ToString("yyyyMMdd"),
                    IdNo                  = reg.CardNo,
                    ItemCode              = applyComb.HisOrderCode,
                    ItemName              = applyComb.HisOrderCNName,
                    ItemCosts             = applyComb.Price,
                    BillingIndicator      = SqlFunc.IIF(applyComb.PayStatus == PayStatus.收费, "T", "F"),
                    BillingDate           = DateTime.Now.ToString("yyyyMMddHHmmss"),// 正常是收费时间 待处理
                    SpecimenCode          = sample.SampCode,
                    Specimen              = SqlFunc.Subqueryable<CodeSample>().Where(x => x.SampCode == sample.SampCode).Select(x => x.SampName),
                    BarCodeNo             = sample.SampleNo,
                    ApplyDrCode           = string.IsNullOrEmpty(applyComb.ApplicantCode)|| applyComb.ApplicantCode=="8888" ? "00870": applyComb.ApplicantCode,
                    ApplyDrName           = string.IsNullOrEmpty(applyComb.ApplicantName) || applyComb.ApplicantCode == "8888" ? "甘小玲": applyComb.ApplicantName,
                    ResultStatus          = applyComb.ApplyStatus,
                    Brlb                  = "4",
                    ApplyDeptCode         = "M2000",
                    ApplyDeptName         = "体检中心",
                    BabyFlag              = "0",
                    EmergencyFlag         = "0",
                    VisitCardNo           = visitCard,
                    BillingOper           = string.Empty,
                    DiagCode              = string.Empty,
                    DiagName              = string.Empty,
                    FlagCh                = string.Empty,
                    DrEntrust             = string.Empty,
                    MedicalInstitution    = string.Empty,
                    SuborHospitalDistrict = string.Empty,
                    OutHospNo             = string.Empty,
                    OrgId                 = string.Empty,
                    ExecutDeptCode        = string.Empty,
                    ExecutDeptName        = string.Empty,
                    BarCodePrintTime      = SqlFunc.IIF(noGatherLisCombs.Contains(applyComb.CombCode), DateTime.Now.ToString("yyyyMMddHHmmss"), sample.GatherTime.Value.ToString("yyyyMMddHHmmss")),// 条码打印时间  待处理
                    SampLingTime          = SqlFunc.IIF(noGatherLisCombs.Contains(applyComb.CombCode), DateTime.Now.ToString("yyyyMMddHHmmss"), sample.GatherTime.Value.ToString("yyyyMMddHHmmss"))
                })
                .ToArray();

            return result;
        }

        /// <summary>
        /// 生成检查数据
        /// </summary>
        /// <param name="data"></param>
        /// <param name="peClsArray">体检分类数组</param>
        /// <returns></returns>
        private ExamApplyInfoResult[] GenerateExamApplyData(QueryExamApplyInfo data,string[] peClsArray)
        {
            var visitCard = _shenShanRegisterRepository.GetHisCard(data.PeNo);
            var ultrasonicCombs = _systemParameterService.UltrasonicCombs;
            var result = _shenShanRegisterRepository.QueryExamApplyInfo(data.PeNo)
                .Where((reg, applyComb) => reg.RegNo == data.PeNo || reg.PatCode == data.PeIndexNo)
                .Where((reg, applyComb) => applyComb.ApplyType == "Exam" && (reg.IsCompanyCheck || applyComb.PayStatus == PayStatus.收费))
                .WhereIF(data.ExamCategCode != "%", (reg, applyComb) => applyComb.ApplyStatus == "0" || applyComb.ApplyStatus == "2")// 0 未登记 2 取消登记 查%应该是平台，忽略状态全部给他
                .WhereIF(data.ExamCategCode != "%", (reg, applyComb) => SqlFunc.ContainsArray(peClsArray, applyComb.ClsCode))
                .Select((reg, applyComb) => new ExamApplyInfoResult
                {
                    PeIndexNo             = reg.PatCode,
                    PeNo                  = reg.RegNo,
                    EmpiId                = reg.PatCode,
                    ElectrRequisitionNo   = SqlFunc.IF(applyComb.ClsCode!="15"|| SqlFunc.ContainsArray(ultrasonicCombs, applyComb.CombCode)).Return(reg.RegNo + applyComb.CombCode).End(reg.RegNo +"US"),
                    IdNumber              = reg.CardNo,
                    PatName               = reg.Name,
                    PhoneNumberHome       = reg.Tel,
                    PhysiSexCode          = (int)reg.Sex,
                    MaritalStatusCode     = (int)reg.MarryStatus,
                    PhysiSexName          = SqlFunc.IIF(reg.Sex == Sex.男, "男", "女"),
                    DateOfBirth           = reg.Birthday == null ? "" : reg.Birthday.Value.ToString("yyyyMMdd"),
                    ApplyDate             = reg.ActiveTime.Value.ToString("yyyyMMddHHmmss"),
                    ReserveExamDate       = reg.RegisterTime.ToString("yyyyMMddHHmmss"),
                    ChargeDate            = reg.RegisterTime.ToString("yyyyMMddHHmmss"),// 缴费时间 待处理   （存储过程没返回）
                    Note                  = reg.Note,
                    ItemCode              = applyComb.CombCode,
                    ExamCategCode         = applyComb.ClsCode,
                    TotalMoney            = applyComb.Price,
                    ExamApplyItemCode     = applyComb.HisOrderCode,
                    ExamApplyItemName     = applyComb.HisOrderCNName,
                    ApplyDrCode           = string.IsNullOrEmpty(applyComb.ApplicantCode) || applyComb.ApplicantCode == "8888" ? "00870" : applyComb.ApplicantCode,
                    ApplyDrName           = string.IsNullOrEmpty(applyComb.ApplicantName) || applyComb.ApplicantCode == "8888" ? "甘小玲" : applyComb.ApplicantName,
                    ExamCategName         = SqlFunc.Subqueryable<CodeItemCls>().Where(x => x.ClsCode == applyComb.ClsCode).Select(x => x.ClsName),
                    ChargeFlag            = "Y",
                    InvalidFlag           = "N",
                    ApplyDeptCode         = "M20000",
                    ApplyDeptName         = "体检中心",
                    RequisitionName       = "体检申请",
                    PatResourceCode       = "4",
                    PatResourceName       = "体检",
                    RequisitionStatus     = "0",
                    ClinicSymptom         = string.Empty,
                    ExcuteDrCode          = string.Empty,
                    ExcuteDrName          = string.Empty,
                    ExamAim               = string.Empty,
                    PriorityFlag          = string.Empty,
                    ExecutDeptCode        = string.Empty,
                    ExecutDeptName        = string.Empty,
                    DiagCode              = string.Empty,
                    DiagName              = string.Empty,
                    EthnicCode            = string.Empty,
                    EthnicName            = string.Empty,
                    MaritalStatusName     = reg.MarryStatus.Value.ToString(),
                    MedicalInstitution    = "中山大学孙逸仙纪念医院深汕中心医院",
                    SuborHospitalDistrict = string.Empty,
                    CurrDiseaseHistory    = string.Empty,
                    PastDiseaseHistory    = string.Empty,
                    VisitCardNo           = visitCard,
                    OutHospNo             = string.Empty,
                    OrderNo               = string.Empty,
                    OrderGroupNo          = string.Empty,
                    ExamPartDescr         = string.Empty,
                    RecordDate            = string.Empty,// (存储过程没返回)
                    UpdateDate            = string.Empty,// (存储过程没返回)
                    SampleName            = applyComb.ClsCode == "12" ? "脱落细胞" : string.Empty
                })
                .ToArray();

            foreach (var item in result)
                item.ExamCategCode = PeClsCodeToHisClsCode(item.ExamCategCode);

            return result;
        }

        /// <summary>
        /// 将第三方的项目分类转换成体检项目分类
        /// </summary>
        /// <param name="hisClsCode"></param>
        /// <returns></returns>
        private static string HisClsCodeToPeClsCode(string hisClsCode)
        {
            /* 第三方项目分类: 1-CT, 2-MR,3-X光,4-B超,5-心电图,6-内镜,7-核医学,8-肌脑电,9-眼科,10-耳鼻喉,11-妇科,12-病理
             * 16-经颅超声多普勒（TCD）17-心电图,18-脑电图,19-放射科,20-CT,21-ECT,22-MR,23-胃镜检查,24-肠镜检查,99-其他
             */
            return hisClsCode switch
            {
                "1"  => "20",
                "2"  => "22",
                "3"  => "19",
                "4"  => "15",
                "5"  => "17",
                "6"  => "23",
                "7"  => "",
                "8"  => "18",
                "9"  => "03",
                "10" => "04",
                "11" => "27",
                "12" => "12",
                "16" => "23",
                "18" => "23",
                "17" => "24",
                "99" => "%%",
                "%"  => "%",
                _    => "%"
            };
        }

        /// <summary>
        /// 将体检项目分类转换成第三方分类
        /// </summary>
        /// <param name="clsCode"></param>
        /// <returns></returns>
        private static string PeClsCodeToHisClsCode(string clsCode)
        {
            // 体检分类参考 CodeItemCls表
            return clsCode switch
            {
                "20" => "1",
                "22" => "2",
                "19" => "3",
                "15" => "4",
                "17" => "5",
                "23" => "16",
                "18" => "8",
                "03" => "9",
                "04" => "10",
                "27" => "11",
                "12" => "12",
                "24" => "17",
                _    => "99"
            };
        }

        /// <summary>
        /// 返回错误数据(通用方法)
        /// </summary>
        /// <typeparam name="T">泛型实体</typeparam>
        /// <param name="result">实体</param>
        /// <param name="respData">返回数据</param>
        /// <returns></returns>
        private static bool ErrorResponse<T>(T result,ref string respData)
        {
            var resultCode = result.GetType().GetProperty("ResultCode");
            if (resultCode != null)
                resultCode.SetValue(result, 1);// 0:成功 1:失败

            //var resultNote = result.GetType().GetProperty("Note");
            //if (resultNote != null)
            //    resultNote.SetValue(result, note);

            respData = XmlHelper.Serialize(result);
            return false;
        }
        #endregion
    }
}