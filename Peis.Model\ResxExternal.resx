﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="BHApiGetTokenMsg" xml:space="preserve">
    <value>&lt;message&gt;
    &lt;epKey&gt;{0}&lt;/epKey&gt;
&lt;/message&gt;</value>
    <comment>百慧平台获取token入参格式</comment>
  </data>
  <data name="BHApiKey" xml:space="preserve">
    <value>************************************</value>
    <comment>百慧平台key</comment>
  </data>
  <data name="BHApiMDM_queryChargeItems" xml:space="preserve">
    <value>MDM-queryChargeItems</value>
    <comment>百慧ApiCode：主数据-查询收费项目信息</comment>
  </data>
  <data name="BHApiMDM_queryDrugs" xml:space="preserve">
    <value>MDM-queryDrugs</value>
    <comment>百慧ApiCode：主数据-查询药品信息</comment>
  </data>
  <data name="BHApiMDM_queryOrderItems" xml:space="preserve">
    <value>MDM-queryOrderItems</value>
    <comment>百慧ApiCode：主数据-查询医嘱项目信息</comment>
  </data>
  <data name="BHApiMedicalRecord" xml:space="preserve">
    <value>medicalRecord</value>
    <comment>门诊建档</comment>
  </data>
  <data name="BHApiOpssChannel" xml:space="preserve">
    <value>KangRuang</value>
    <comment>Opss 渠道商名</comment>
  </data>
  <data name="BHApiOpssHospitalId" xml:space="preserve">
    <value>100201</value>
    <comment>Opss 给定的深汕中心医院Id</comment>
  </data>
  <data name="BHApiOpssId_SendMsg" xml:space="preserve">
    <value>sendShortMessage001</value>
    <comment>Opss 发送短信方法Id</comment>
  </data>
  <data name="BHApiOpssKey" xml:space="preserve">
    <value>eaf41e2218f2445886edb44be5cd248a</value>
    <comment>Opss接口给定调用的密钥key</comment>
  </data>
  <data name="BHApiOpssUrl" xml:space="preserve">
    <value>https://gdbyway-hlwyy.sschospital.cn/opss-gateway/doRpc/opss</value>
    <comment>Opss接口地址</comment>
  </data>
  <data name="BHApiQueryCardInfo" xml:space="preserve">
    <value>queryClinicCardInfo01Q006</value>
    <comment>百慧ApiCode：通过门诊卡获取病人信息</comment>
  </data>
  <data name="BHApiQueryTokenInfo" xml:space="preserve">
    <value>queryTokenInfo98Q001J</value>
    <comment>校验临时 Token 并获取登录用户信息</comment>
  </data>
  <data name="BHApiRegistPat" xml:space="preserve">
    <value>registPat</value>
    <comment>主数据建档</comment>
  </data>
  <data name="BHApiSynchPEModifyNotice" xml:space="preserve">
    <value>synchPEModifyNotice</value>
    <comment>申请单同步通知</comment>
  </data>
  <data name="BHApiSynchPEReportInfo" xml:space="preserve">
    <value>synchPEReportInfo</value>
    <comment>体检报告同步</comment>
  </data>
  <data name="BHApiUrlGetToken" xml:space="preserve">
    <value>https://gdbyway-platform-cluster.sschospital.cn/hie/srvmgr/hieSrvMgr/getToken</value>
    <comment>百慧平台获取token地址</comment>
  </data>
  <data name="BHApiUrlMsgTransfer" xml:space="preserve">
    <value>https://gdbyway-platform-cluster.sschospital.cn/hie/services/hieService/msgTransfer</value>
    <comment>百慧平台数据请求统一入口地址</comment>
  </data>
  <data name="FolderExamReport" xml:space="preserve">
    <value>pacsImages</value>
    <comment>非检验图文报告文件夹名称</comment>
  </data>
  <data name="FolderPeisReport" xml:space="preserve">
    <value>PeisReports</value>
    <comment>体检报告文件夹</comment>
  </data>
  <data name="SysShortMsgTemplate_MB01" xml:space="preserve">
    <value>MB01</value>
    <comment>体检报告领取通知模板Code</comment>
  </data>
</root>