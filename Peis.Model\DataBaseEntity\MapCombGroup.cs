﻿using SqlSugar;

namespace Peis.Model.DataBaseEntity
{
    ///<summary>
    ///检验组合对应
    ///</summary>
    [SugarTable("MapCombGroup")]
    public class MapCombGroup
    {
        /// <summary>
        /// 体检组合代码
        /// </summary>        
        [SugarColumn(IsNullable = false, IsPrimaryKey = true, Length = 10)]
        public string CombCode { get; set; }

        /// <summary>
        /// 体检组合名称
        /// </summary>        
        [SugarColumn(IsNullable = false, Length = 200)]
        public string CombName { get; set; }

        /// <summary>
        /// 检验组合代码
        /// </summary>        
        [SugarColumn(IsNullable = false, Length = 20)]
        public string GroupCode { get; set; }

        /// <summary>
        /// 检验组合名称
        /// </summary>        
        [SugarColumn(IsNullable = false, Length = 200)]
        public string GroupName { get; set; }

        /// <summary>
        /// 专业组代码
        /// </summary>        
        [SugarColumn(IsNullable = false, Length = 10)]
        public string RoomCode { get; set; }

        /// <summary>
        /// 专业组名称
        /// </summary>        
        [SugarColumn(IsNullable = false, Length = 20)]
        public string RoomName { get; set; }
    }
}