﻿using Aspose.Words;
using Aspose.Words.Drawing;
using Aspose.Words.Drawing.Charts;
using Aspose.Words.MailMerging;
using System.Drawing;
using System.Linq;

namespace Peis.Model.DTO.CompanyStatistics;

/// <summary>
/// Aspose.Words - 自定义邮件合并回调
/// </summary>
public class FieldMergingCbOfCompYearAnalReport : IFieldMergingCallback
{
    readonly CompanyYearAnalyseReport _reportData;
    readonly static string[] CustomFieldNames = new[] { "SummaryAgePieChart", "SummaryTopDiseAnalyseColumnChart", "SummaryTopDiseDetailColumnChart" };

    public FieldMergingCbOfCompYearAnalReport(CompanyYearAnalyseReport reportData)
    {
        _reportData = reportData;
    }

    /// <summary>
    /// 合并字段回调处理
    /// </summary>
    /// <param name="args"></param>
    public void FieldMerging(FieldMergingArgs args)
    {
        // 饼图、柱状图等特殊合并处理
        if (CustomFieldNames.Any(x => x.Equals(args.FieldName, StringComparison.OrdinalIgnoreCase)))
        {
            // 获取当前字段所在的段落
            Paragraph paragraph = (Paragraph)args.Field.Start.ParentNode;
            // 创建 DocumentBuilder 并移动到该段落
            DocumentBuilder builder = new DocumentBuilder(args.Document);
            builder.MoveTo(paragraph);

            if (CustomFieldNames[0].Equals(args.FieldName, StringComparison.OrdinalIgnoreCase))
            {
                // 插入各年龄段人数及百分比图
                InsertSummaryAgePieChart(args, builder);
            }
            else if (CustomFieldNames[1].Equals(args.FieldName, StringComparison.OrdinalIgnoreCase))
            {
                // 插入全体体检员工异常指标前 几 项分析图
                InsertSummaryTopDiseAnalyseColumnChart(args, builder);
            }
            else if (CustomFieldNames[2].Equals(args.FieldName, StringComparison.OrdinalIgnoreCase))
            {
                // 插入该年龄段异常指标 几 项分析图
                InsertSummaryTopDiseDetailColumnChart(args, builder);
            }

            // 移除域内容
            args.Field.Remove();
        }
    }

    /// <summary>
    /// 图片处理
    /// </summary>
    /// <param name="args"></param>
    public void ImageFieldMerging(ImageFieldMergingArgs args)
    {

    }

    #region 饼图
    /// <summary>
    /// 饼图默认设置
    /// </summary>
    /// <param name="chart"></param>
    void SetPieChartDefault(Chart chart, ChartSeries series)
    {
        chart.Title.Show = true;
        chart.Legend.Position = LegendPosition.Right; // 分类名称显示位置

        // 设置数据标签
        series.DataLabels.NumberFormat.FormatCode = "0.00%";
        series.DataLabels.ShowLeaderLines = true; // 数据标签的引导线
        series.DataLabels.ShowLegendKey = false;   // 图例键
        series.DataLabels.ShowCategoryName = true; // 类别名称
        series.DataLabels.ShowPercentage = true;  // 百分比
        series.DataLabels.ShowSeriesName = false; // 系列名称
        series.DataLabels.ShowValue = true;       // 数据值
        series.DataLabels.Separator = " ";        // 设置数据标签内不同部分的分隔符为空格

        // 设置颜色 (区分颜色后，显示不同分类名称，否则默认不显示)
        Color[] colors = new[]
        {
            Color.FromArgb(113,213,201),
            Color.FromArgb(232,162,160),
            Color.FromArgb(130,159,69),
        };

        for (int i = 0; i < series.DataPoints.Count; i++)
        {
            series.DataPoints[i].Format.Fill.ForeColor = colors[i % colors.Length];
        }
    }

    /// <summary>
    /// 插入各年龄段人数及百分比-饼图
    /// </summary>
    /// <param name="args"></param>
    /// <param name="builder"></param>
    void InsertSummaryAgePieChart(FieldMergingArgs args, DocumentBuilder builder)
    {
        if (args == null || builder == null)
            return;

        // 插入饼图
        Shape chartShape = builder.InsertChart(ChartType.Pie3D, 350, 250);
        Chart chart = chartShape.Chart;
        // 清除默认的示例数据
        chart.Series.Clear();

        // 添加数据
        var chartDatga = _reportData.SummaryAgeList.FindAll(x => x.ItemName != "合计");
        ChartSeries series = chart.Series.Add(
            " ", // 系列名称
            chartDatga.Select(x => x.ItemName).ToArray(), // 类别
            chartDatga.Select(x => (double)x.TotalCount).ToArray() // 值
        );

        // 设置图表标题
        chart.Title.Text = "各年龄段人数及百分比";
        SetPieChartDefault(chart, series);
    }


    #endregion


    #region 柱状图

    /// <summary>
    /// 柱状图默认设置
    /// </summary>
    /// <param name="chart"></param>
    void SetColumnChartDefault(Chart chart, ChartSeries series)
    {
        // 设置Y轴为百分比格式
        ChartAxis yAxis = chart.AxisY;
        yAxis.NumberFormat.FormatCode = "0%";
        yAxis.MajorUnit = 0.1; // 10%间隔
        yAxis.Scaling.Minimum = new AxisBound(0);  // 最小0%
        yAxis.Scaling.Maximum = new AxisBound(1);  // 最大100%

        // 设置X轴（分类轴）显示在左侧
        ChartAxis xAxis = chart.AxisX;
        xAxis.CategoryType = AxisCategoryType.Category;
        xAxis.TickLabels.Position = AxisTickLabelPosition.Low;
        xAxis.TickLabels.Alignment = ParagraphAlignment.Center;

        // 设置图表标题
        chart.Title.Show = true;
        chart.Legend.Position = LegendPosition.Right; // 分类名称显示位置

        // 设置数据标签        
        series.DataLabels.NumberFormat.FormatCode = "0.00%";
        series.DataLabels.ShowLeaderLines = false; // 数据标签的引导线
        series.DataLabels.ShowLegendKey = false;   // 图例键
        series.DataLabels.ShowCategoryName = false; // 类别名称
        series.DataLabels.ShowPercentage = false;  // 百分比
        series.DataLabels.ShowSeriesName = false; // 系列名称
        series.DataLabels.ShowValue = true;       // 数据值
        series.DataLabels.Separator = " ";        // 设置数据标签内不同部分的分隔符为空格

        // 设置颜色 (区分颜色后，显示不同分类名称，否则默认不显示)
        Color[] colors = new[]
        {
            Color.FromArgb(147, 204, 221),
            Color.FromArgb(205, 218, 235),
            Color.FromArgb(178, 162, 199),
            Color.FromArgb(223, 217, 195),
            Color.FromArgb(230, 184, 184)
        };
        for (int i = 0; i < series.DataPoints.Count; i++)
        {
            series.DataPoints[i].Format.Fill.ForeColor = colors[i % colors.Length];
        }
    }

    /// <summary>
    /// 插入全体体检员工异常指标前 几 项分析-柱状图
    /// </summary>
    /// <param name="args"></param>
    /// <param name="builder"></param>
    void InsertSummaryTopDiseAnalyseColumnChart(FieldMergingArgs args, DocumentBuilder builder)
    {
        if (args == null || builder == null || _reportData.TopAnalyseNumber <= 0)
            return;

        // 添加柱状图
        Shape chartShape = builder.InsertChart(ChartType.Column3D, 400, 300);
        Chart chart = chartShape.Chart;
        // 清除默认的示例数据
        chart.Series.Clear();
        string[] categories = new string[_reportData.TopAnalyseNumber];
        double[] values = new double[_reportData.TopAnalyseNumber];
        for (int i = 0; i < _reportData.SummaryTopDiseAnalyseList.Count; i++)
        {
            var item = _reportData.SummaryTopDiseAnalyseList[i];
            categories[i] = item.DiseaseClsName;
            values[i] = item.Percent / 100;
        }
        ChartSeries series = chart.Series.Add(
            " ", // 系列名称
            categories,  // 类别(x)
            values     // 值(y)
        );

        chart.Title.Text = $"全体体检员工异常指标前 {_reportData.TopAnalyseNumber} 项分析";
        SetColumnChartDefault(chart, series);
    }

    /// <summary>
    /// 插入该年龄段-异常指标 几 项分析柱状图-柱状图
    /// </summary>
    /// <param name="args"></param>
    /// <param name="builder"></param>
    void InsertSummaryTopDiseDetailColumnChart(FieldMergingArgs args, DocumentBuilder builder)
    {
        var summaryTopDiseOfAge = _reportData.SummaryTopDiseOfAgeList[args.RecordIndex];
        if (args == null || builder == null || 
            summaryTopDiseOfAge == null || 
            summaryTopDiseOfAge.DiseaseDetailList.IsNullOrEmpty())
            return;

        // 添加柱状图
        Shape chartShape = builder.InsertChart(ChartType.Column3D, 400, 300);
        Chart chart = chartShape.Chart;
        // 清除默认的示例数据
        chart.Series.Clear();

        var diseaseDetailList = summaryTopDiseOfAge.DiseaseDetailList;
        string[] categories = new string[summaryTopDiseOfAge.TopAnalyseNumber];
        double[] values = new double[summaryTopDiseOfAge.TopAnalyseNumber];

        for (int i = 0; i < diseaseDetailList.Count; i++)
        {
            var item = diseaseDetailList[i];
            categories[i] = item.DiseaseClsName;
            values[i] = item.Percent / 100;
        }
        ChartSeries series = chart.Series.Add(
            " ", // 系列名称
            categories,  // 类别(x)
            values     // 值(y)
        );

        chart.Title.Text = $"{summaryTopDiseOfAge.ItemNameOfChinese}体检员工异常指标前 {summaryTopDiseOfAge.TopAnalyseNumber} 项分析";
        SetColumnChartDefault(chart, series);
    }

    #endregion
}
