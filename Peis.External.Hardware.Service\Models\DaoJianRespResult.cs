﻿namespace Peis.External.Hardware.Service.Models;

/// <summary>
/// 导检接口响应报文
/// </summary>
public record DaoJianRespResult
{
    /// <summary>
    /// 响应码
    /// </summary>
    public string Code { get; set; }

    /// <summary>
    /// 响应消息
    /// </summary>
    public string Msg { get; set; }

    /// <summary>
    /// 响应数据
    /// </summary>
    public string Data { get; set; }

    /// <summary>
    /// 是否成功
    /// </summary>
    [JsonIgnore]
    public bool Success => "0".Equals(Code);
}
