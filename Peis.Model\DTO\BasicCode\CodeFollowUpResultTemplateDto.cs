﻿namespace Peis.Model.DTO.BasicCode;

/// <summary>
/// 随访处理情况模板DTO
/// </summary>
public class CodeFollowUpResultTemplateDto
{
    /// <summary>
    /// 模板Id
    /// </summary>        
    public long Id { get; set; }

    /// <summary>
    /// 模板序号
    /// </summary>        
    public int SortIndex { get; set; }

    /// <summary>
    /// 模板内容
    /// </summary>
    [Required(ErrorMessage = "模板内容不能为空！")]
    [MinLength(1, ErrorMessage = "模板内容不能为空！")]
    public string Content { get; set; }
}
