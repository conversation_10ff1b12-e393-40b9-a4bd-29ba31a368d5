﻿using Peis.Model.Constant;
using Peis.Model.DTO.Register;
using Peis.Model.DTO;
using Peis.Model.Other.PeEnum;
using Peis.Repository.IRepository;
using Peis.Service.IService;
using Peis.Service.IService.ExternalSystem;
using Peis.Utility.PeUser;
using Peis.Model.Other.Input;
using Microsoft.Extensions.Logging;
using Peis.Model.DTO.External;
using Peis.Model.DTO.External.HATM;

namespace Peis.External.Platform.Service.Service.ServiceImpl.Machine;

/// <summary>
/// 仪器接口服务
/// </summary>
public class MachineService : IExternalSystemMachineService
{
    private readonly IHttpContextUser _httpContextUser;
    private readonly IShenShanRegisterRepository _shenShanRegisterRepository;
    private readonly ISampleRepository _sampleRepository;
    private readonly IRegisterNewService _registerService;
    private readonly IMapper _mapper;
    private readonly ISampleService _sampleService;
    private readonly ILogger<MachineService> _logger;
    private readonly IRegisterRepository _registerRepository;

    public MachineService(IHttpContextUser httpContextUser,
                        IShenShanRegisterRepository shenShanRegisterRepository,
                        ISampleRepository sampleRepository,
                        IRegisterNewService registerService,
                        IMapper mapper,
                        ISampleService sampleService,
                        ILogger<MachineService> logger,
                        IRegisterRepository registerRepository)
    {
        _httpContextUser = httpContextUser;
        _shenShanRegisterRepository = shenShanRegisterRepository;
        _sampleRepository = sampleRepository;
        _registerService = registerService;
        _mapper = mapper;
        _sampleService = sampleService;
        _logger = logger;
        _registerRepository = registerRepository;
    }

    public SampleInfoToTxt[] GetSampleInfoToTxt(string regNo, out string msg)
    {
        var samples = _sampleRepository.ReadSampleBarcodeDetail()
            .Where((sample, comb, barcode) => sample.RegNo == regNo)
            .Select((sample, comb) => new { sample.SampleNo, comb.CombName }).ToArray();
        var sampleData = _shenShanRegisterRepository.ReadSampleDetailWithSpecimenInfo(regNo)
            .Select((reg, regComb, comb, barcode, samp) => new SampleInfoToTxt
            {
                LabNum = comb.SampleNo,
                HospNum = reg.RegNo,
                PatientType = string.Empty,
                CnName = reg.Name,
                Sex = reg.Sex.ToString(),
                AgeDesc = reg.Age.ToString(),
                Location = "体检科",
                OrderDate = reg.RegisterTime,
                Priority = "高",
                Source = samp.SampName,
                ContainerCode = barcode.Barcode,
                Container = barcode.Note,
                RePrint = "否",
                TestItemDesc = regComb.CombName,
                DeptDesc = "采血科",
                ExamineRequestID = string.Empty,
                PaymentStatus = "已缴费",
                PrintTime = DateTime.Now,
                PrintIP = string.Empty,
                TestType = samp.SampName,
                PrintUser = _httpContextUser.UserName,
                TakeReportDesc = string.Empty,
                TakeReportAddress = "中山大学孙逸仙纪念医院深汕中心医院"
            }).ToArray().DistinctBy(x => x.LabNum).ToArray();
        sampleData.BatchUpdate(x => x.TestItemDesc = string.Join("+", samples.Where(sample => sample.SampleNo == x.LabNum).Select(sample => sample.CombName).ToArray()));
        if (sampleData.Length == 0)
        {
            msg = "当前体检号没有可打印条码数据！";
        }
        else
        {
            msg = string.Empty;
        }
        return sampleData;
    }

    /// <summary>
    /// 自助机 - 订单查询
    /// </summary>
    /// <param name="reqXml">入参xml</param>
    /// <param name="returnXml">出参xml</param>
    public void QueryOrderListOfHATM(string reqXml, ref string returnXml)
    {
        var response = new Response<ReusltListData<HATMOrderListOutputDto>>();
        try
        {
            var request = XmlHelper.Deserialize<HATMOrderListQuery>(reqXml);
            var query = _mapper.Map<RegisterOrderQuery>(request);
            if (query.CardNo.IsNullOrEmpty())
            {
                response.SetFail("请输入身份证号！");
                return;
            }
            query.PeStatuses = new List<PeStatus> { PeStatus.未检查, PeStatus.正在检查 };
            var totalNumber = 0;
            var totalPage = 0;
            var orders = _registerService.GetRegisterOrders(query, ref totalNumber, ref totalPage);

            var records = new HATMOrderListOutputDto();
            foreach (var item in orders)
            {
                var record = _mapper.Map<HATMOrderOutputDto>(item.Patient);
                record.ClusName = string.Join("+", item.Clusters.Select(c => c.ClusName));
                records.Orders.Add(record);
            }
            var data = new ReusltListData<HATMOrderListOutputDto>();
            data.TotalNumber = totalNumber;
            data.TotalPage = totalPage;
            data.Records = records;

            response.Data = data;
        }
        catch (Exception ex)
        {
            _logger.LogException(ex);
            response.SetFail();
        }
        finally
        {
            returnXml = XmlHelper.Serialize(response);
        }
    }

    /// <summary>
    ///自助机 - 获取打印清单列表
    /// </summary>
    /// <param name="reqXml">入参xml</param>
    /// <param name="returnXml">出参xml</param>
    public void QueryPrintFileListOfHATM(string reqXml, ref string returnXml)
    {
        var response = new Response<ReusltListData<HATMPrintListOutputDto>>();
        try
        {
            var query = XmlHelper.Deserialize<HATMPrintListQuery>(reqXml);
            if (!CheckHelper.VerifyRegNo(query.RegNo,out _))
            {
                response.SetFail("请输入正确的体检号！");
                return;
            }
            var reg = _registerRepository.ReadRegister(query.RegNo).First()?? throw new BusinessException($"体检号 {query.RegNo} 不存在！");
            _httpContextUser.SetHospCode(reg.HospCode);
            // 激活
            var acitveMsg = string.Empty;
            var activeData = new ActiveRegister
            {
                RegNoArray = new string[] { query.RegNo },
                ActiveTime = DateTime.Now,
            };
            _registerService.ActivationOrCancel(true, activeData, ref acitveMsg);

            // 查询打印清单
            var printQuery = new PrintQuery
            {
                RegNo = query.RegNo,
                FilterList = new List<PrintFileType>
                {
                    PrintFileType.Guidance,
                    PrintFileType.Gastroscope,
                    PrintFileType.Colonoscopy,
                    PrintFileType.PeLabel,
                    PrintFileType.NonBloodBarcode,
                }
            };
            var prints = _registerService.CheckPrintables(printQuery);
            var records = new HATMPrintListOutputDto();
            foreach (var item in prints)
            {
                var printType = item.ToEnum<PrintFileType>();
                var path = printType.GetApiPath();
                if (path.IsNullOrEmpty()) continue;

                path = $"{ConstantUrl.FileHostUrl}{path.Replace("{regNo}", query.RegNo, StringComparison.OrdinalIgnoreCase)}";
                var record = new HATMPrintOutputDto
                {
                    Type = printType.ToString(),
                    TypeName = printType.GetDescription(),
                    PdfUrl = path,
                };

                records.Prints.Add(record);
            }
            var data = new ReusltListData<HATMPrintListOutputDto>();
            data.TotalNumber = records.Prints.Count;
            data.TotalPage = data.TotalNumber > 0 ? 1 : 0;
            data.Records = records;

            response.Data = data;
        }
        catch (Exception ex)
        {
            _logger.LogException(ex);
            response.SetFail();
        }
        finally
        {
            returnXml = XmlHelper.Serialize(response);
        }
    }

    /// <summary>
    ///自助机 - 更新打印时间
    /// </summary>
    /// <param name="reqXml">入参xml</param>
    /// <param name="returnXml">出参xml</param>
    public void SyncPrintedTimeOfHATM(string reqXml, ref string returnXml)
    {
        var response = new Response<bool>();
        try
        {
            var input = XmlHelper.Deserialize<HATMPrintInputDto>(reqXml);
            if (!CheckHelper.VerifyRegNo(input.RegNo, out _))
            {
                response.SetFail("请输入正确的体检号！");
                return;
            }

            input.PrintedTime = DateTime.Now.Date > input.PrintedTime ? DateTime.Now : input.PrintedTime;
            bool flag = false;
            string msg = string.Empty;
            switch (input.Type)
            {
                case PrintFileType.Guidance:
                    var data = _mapper.Map<RegisterGuidanceDto>(input);
                    data.SetGuidancePrinted(true);
                    flag = _registerService.ModifyRegisterGuidance(data, ref msg);
                    break;
                case PrintFileType.NonBloodBarcode:
                    flag = _sampleService.ModifySamplePrintTime(input.RegNo, input.PrintedTime, ref msg);
                    break;
            }

            response.Data = flag;
        }
        catch (Exception ex)
        {
            _logger.LogException(ex);
            response.SetFail();
        }
        finally
        {
            returnXml = XmlHelper.Serialize(response);
        }
    }

}

