﻿using Peis.Model.DTO;
using SqlSugar;
using System.Collections.Generic;

namespace Peis.Model.DataBaseEntity
{
    ///<summary>
    ///疾病计算表达式表
    ///</summary>
    [SugarTable("CodeDiseaseExpression")]
    public class CodeDiseaseExpression
    {
        /// <summary>
        /// 表达式码
        /// </summary>        
        [SugarColumn(IsNullable = false, IsPrimaryKey = true, Length = 255)]
        public string ExpCode { get; set; }

        /// <summary>
        /// 表达式名称
        /// </summary>        
        [SugarColumn(IsNullable = false, Length = 255)]
        public string ExpName { get; set; }

        /// <summary>
        /// 表达式
        /// </summary>        
        [SugarColumn(IsNullable = false, ColumnDataType = StaticConfig.CodeFirst_BigString)]
        public string ExpText { get; set; }

        /// <summary>
        /// 疾病码
        /// </summary>        
        [SugarColumn(IsNullable = false, Length = 8)]
        public string DiseaseCode { get; set; }

        /// <summary>
        /// 项目列表(用 , 号隔开)
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public string ItemText { get; set; }

        /// <summary>
        /// 项目列表
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public List<ExpItem> Items { get; set; }
    }
}