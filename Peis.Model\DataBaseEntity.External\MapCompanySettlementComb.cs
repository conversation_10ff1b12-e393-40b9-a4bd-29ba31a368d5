﻿namespace Peis.Model.DataBaseEntity.External
{
    /// <summary>
    /// 团体结算记录对应组合关系表
    /// </summary>
    [SugarTable]
    public class MapCompanySettlementComb
    {
        /// <summary>
        /// Id
        /// </summary>
        [SugarColumn(IsPrimaryKey = true)]
        public long Id { set; get; }
        /// <summary>
        /// 当次结算流水号
        /// </summary>
        [SugarColumn(IsNullable = false, Length = 10)]
        public string BillSeqNo { set; get; }
        /// <summary>
        /// 组合代码
        /// </summary>
        [SugarColumn(IsNullable = false, Length = 8)]
        public string CombCode { set; get; }
        /// <summary>
        /// 组合名称
        /// </summary>
        [SugarColumn(IsNullable = false, Length = 200)]
        public string CombName { set; get; }
        /// <summary>
        /// 项目分类
        /// </summary>
        [SugarColumn(IsNullable = false, Length = 6)]
        public string ClsCode { set; get; }
        /// <summary>
        /// 金额
        /// </summary>
        [SugarColumn(IsNullable = false, Length = 12, DecimalDigits = 2)]
        public decimal Price { set; get; }
        /// <summary>
        /// 原始金额
        /// </summary>
        [SugarColumn(IsNullable = false, Length = 12, DecimalDigits = 2)]
        public decimal OriginalPrice { set; get; }
        /// <summary>
        /// 折扣
        /// </summary>
        [SugarColumn(IsNullable = false, Length = 10, DecimalDigits = 2)]
        public decimal Discount { set; get; }
        /// <summary>
        /// 数量
        /// </summary>
        [SugarColumn(IsNullable = false)]
        public int Count { set; get; }
        /// <summary>
        /// 执行科室
        /// </summary>
        [SugarColumn(IsNullable = false)]
        public string ExamDeptCode { set; get; }
    }
}
