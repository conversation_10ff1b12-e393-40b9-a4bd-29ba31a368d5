﻿using System.Collections.Generic;
using Peis.Utility.Cache;

namespace Peis.Repository.Cache;

public class SimpleCache : ISimpleCache
{
    readonly MemoryCacheAdapter _adapter;
    readonly Dictionary<string, ProviderConfig> _configs = new();

    public SimpleCache(MemoryCacheAdapter adapter)
    {
        _adapter = adapter;
    }

    public void AddConfig(ProviderConfig config)
    {
        _configs.Add(config.Key, config);
    }

    public object GetOrCreate(string key)
    {
        if (_configs.TryGetValue(key, out var cfg))
        {
            return _adapter.GetOrCreate(cfg);
        }

        return null;
    }

    public T GetOrCreate<T>(string key)
    {
        object value = GetOrCreate(key);
        return value is T t ? t : default;
    }
}

public interface ISimpleCache
{
    object GetOrCreate(string key);

    T GetOrCreate<T>(string key);
}
