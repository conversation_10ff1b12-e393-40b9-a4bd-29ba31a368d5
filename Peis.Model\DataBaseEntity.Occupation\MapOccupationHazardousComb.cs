﻿using Peis.Model.TableFilter;

namespace Peis.Model.DataBaseEntity.Occupation;

/// <summary>
/// 危害因素推荐组合对应关系
/// </summary>
[SugarTable]
public class MapOccupationHazardousComb: IHospCodeFilter
{
    /// <summary>
    /// 危害因素代码
    /// </summary>
    [SugarColumn(Length = 10, IsPrimaryKey = true)]
    public string HazardousCode { get; set; }
    /// <summary>
    /// 岗位状态代码
    /// </summary>
    [SugarColumn(Length = 10, IsPrimaryKey = true)]
    public string StatusCode { get; set; }
    /// <summary>
    /// 组合代码
    /// </summary>
    [SugarColumn(Length = 10, IsPrimaryKey = true)]
    public string CombCode { get; set; }

    [SugarColumn(Length = 1, IsPrimaryKey = true)]
    public string HospCode { get; set; }

    #region Ext
    /// <summary>
    /// 岗位状态名称
    /// </summary>
    [SugarColumn(IsIgnore = true)]
    public string StatusName { get; set; }

    /// <summary>
    /// 组合名称
    /// </summary>
    [SugarColumn(IsIgnore = true)]
    public string CombName { get; set; }

    /// <summary>
    /// 组合单价
    /// </summary>
    [SugarColumn(IsIgnore = true)]
    public decimal Price { get; set; }
    #endregion
}
