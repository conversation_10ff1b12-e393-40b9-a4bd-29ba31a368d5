﻿namespace Peis.External.Platform.Service.Service.IService.System;

/// <summary>
/// 业务通知公告服务
/// </summary>
public interface INoticeBusinessService
{
    #region 基础数据
    /// <summary>
    /// 发消息通知：收费项目与医嘱项目明细不一致通知
    /// </summary>
    /// <param name="msg"></param>
    /// <returns>bool</returns>
    bool SendNoticeByCombChargeDiff(ref string msg);
    #endregion

    #region 体检

    /// <summary>
    /// 每日需要推送当日退费人员列表
    /// </summary>
    /// <param name="msg">消息</param>
    /// <param name="preDays">查询往日记录，往前推天数,默认0(当天)</param>
    /// <returns>bool</returns>
    bool SendNoticeByPatientRefund(out string msg, int preDays = 0);
    #endregion
}

public class DefaultExternalSystemNoticeBusinessService : INoticeBusinessService
{
    public bool SendNoticeByCombChargeDiff(ref string msg)
    {
        return false;
    }

    public bool SendNoticeByPatientRefund(out string msg, int preDays = 0)
    {
        msg = ResxCommon.Fail;
        return false;
    }
}