﻿using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Peis.Model.DataBaseEntity;
using Peis.Model.DataBaseEntity.External;
using Peis.Model.DTO;
using Peis.Model.DTO.Register;
using Peis.Model.Other.Input;
using Peis.Model.Other.PeEnum;
using Peis.Service.IService;
using Peis.Service.IService.ExternalSystem;
using System;
using System.Collections.Generic;

namespace Peis.API.Controllers.Business
{
    /// <summary>
    /// 个人/单位登记
    /// </summary>
    [Route("api/[controller]")]
    [ApiController]
    public class RegisterNewController : BaseApiController
    {
        private readonly IRegisterNewService _registerService;
        private readonly IExternalSystemOrderService _externalSystemService;

        public RegisterNewController(IRegisterNewService registerService, IExternalSystemOrderService externalSystemService)
        {
            _registerService = registerService;
            _externalSystemService = externalSystemService;
        }

        /// <summary>
        /// 获取候选套餐列表（套餐、绑定的组合、互斥的组合）
        /// </summary>
        /// <returns></returns>
        [ProducesResponseType(typeof(CandidateCluster[]), 200)]
        [HttpPost("ReadCandidateCluster")]
        public IActionResult ReadCandidateCluster()
        {
            result.ReturnData = _registerService.ReadCandidateCluster();
            return Ok(result);
        }

        /// <summary>
        /// 获取单位候选套餐列表（套餐、绑定的组合）
        /// </summary>
        /// <param name="companyCode">单位码</param>
        /// <param name="companyTimes">单位次数</param>
        /// <param name="type"></param>
        /// <returns></returns>
        [ProducesResponseType(typeof(CandidateCluster[]), 200)]
        [HttpPost("ReadCompanyCandidateCluster")]
        public IActionResult ReadCompanyCandidateCluster([FromQuery] string companyCode, [FromQuery] int companyTimes, [FromQuery] PeClsType type)
        {
            result.ReturnData = _registerService.ReadCompanyCandidateCluster(companyCode, companyTimes, type);
            return Ok(result);
        }

        /// <summary>
        /// 获取候选危害因素套餐列表（套餐、绑定的组合、互斥的组合）
        /// </summary>
        /// <returns></returns>
        [ProducesResponseType(typeof(CandidateCluster[]), 200)]
        [HttpPost("ReadOccupationalCandidateCluster")]
        public IActionResult ReadOccupationalCandidateCluster()
        {
            result.ReturnData = _registerService.ReadOccupationalCandidateCluster();
            return Ok(result);
        }

        /// <summary>
        /// 获取候选组合列表（组合、绑定的组合、互斥的组合）
        /// </summary>
        /// <returns></returns>
        [ProducesResponseType(typeof(CandidateComb[]), 200)]
        [HttpPost("ReadCandidateComb")]
        public IActionResult ReadCandidateComb()
        {
            result.ReturnData = _registerService.ReadCandidateComb();
            return Ok(result);
        }

        /// <summary>
        /// 获取候选组合的项目明细
        /// </summary>
        /// <param name="combCode">组合代码</param>
        /// <returns></returns>
        [ProducesResponseType(typeof(CandidateItem[]), 200)]
        [HttpPost("ReadCandidateCombItems")]
        public IActionResult ReadCandidateCombItems([FromQuery] string combCode)
        {
            result.ReturnData = _registerService.ReadCandidateCombItems(combCode);
            return Ok(result);
        }

        /// <summary>
        /// SaveRegisterOrder/Person 保存个人订单
        /// SaveRegisterOrder/Company 保存单位订单
        /// </summary>
        /// <param name="personOrCompany">个人或团体</param>
        /// <param name="order">订单信息</param>
        /// <returns></returns>
        /// <response code="200">返回体检号</response>
        [HttpPost("SaveRegisterOrder/{personOrCompany}")]
        public IActionResult SaveRegisterOrder([FromRoute] string personOrCompany, [FromBody] RegisterNewOrderInfo order)
        {
            var success = false;
            var errMsg = string.Empty;

            order.RegisterOrder.IsCompany = personOrCompany.ToLower() switch
            {
                "person" => false,
                "company" => true,
                _ => throw new BadHttpRequestException("not found"),
            };

            if (string.IsNullOrWhiteSpace(order.RegisterOrder.Patient.RegNo))
                result.ReturnData = _registerService.NewRegisterOrder(order, ref success);
            else
                result.ReturnData = _registerService.AlterRegisterOrder(order, ref success);

            result.Success = success;
            result.ReturnMsg = errMsg;
            return Ok(result);
        }

        /// <summary>
        /// 修改体检人信息
        /// </summary>
        /// <param name="personOrCompany">个人/团体：person/Company</param>
        /// <param name="registerPatient">体检人信息</param>
        /// <returns></returns>
        /// <response code="200">返回体检号</response>
        [HttpPost("AlterRegisterPatient/{personOrCompany}")]
        public IActionResult AlterRegisterPatient([FromRoute] string personOrCompany, [FromBody] AlterRegisterPatient registerPatient)
        {
            var errMsg = string.Empty;
            var isCompanyCheck = false;

            switch (personOrCompany.ToLower())
            {
                case "person":
                    break;
                case "company":
                    isCompanyCheck = true;
                    break;
                default:
                    return BadRequest();
            }

            result.Success = _registerService.AlterRegisterPaitent(isCompanyCheck, registerPatient);

            result.ReturnData = registerPatient.RegisterPatient.RegNo;
            result.ReturnMsg = errMsg;
            return Ok(result);
        }

        /// <summary>
        /// 获取体检人的历史档案资料
        /// </summary>
        /// <param name="queryType">Name(查姓名),CardNo(查证件号),Tel(查手机号)</param>
        /// <param name="queryValue">对应的值</param>
        /// <response code="200">执行成功</response>
        /// <returns></returns>
        [ProducesResponseType(typeof(HistoryArchives[]), 200)]
        [HttpPost("GetHistoryArchives")]
        public IActionResult GetHistoryArchives([FromQuery] string queryType, [FromQuery] string queryValue)
        {
            try
            {
                result.ReturnData = _registerService.ReadHistoryArchives(queryType, queryValue);
            }
            catch (Exception ex)
            {
                result.Success = false;
                result.ReturnMsg = ex.Message;
            }
            return Ok(result);
        }

        /// <summary>
        /// 获取登记资料列表（多筛查条件）
        /// </summary>
        /// <param name="filters"></param>
        /// <response code="200">执行成功</response>
        /// <returns></returns>
        [ProducesResponseType(typeof(RegisterRecord), 200)]
        [HttpPost("GetRegistersByMultipleFilter")]
        public IActionResult GetRegistersByMultipleFilter([FromBody] RegisterMultipleFilters filters)
        {
            int totalNumber = 0;
            int totalPage = 0;
            result.ReturnData = _registerService.ReadRegistersByMultipleFilter(filters, ref totalNumber, ref totalPage);
            result.TotalNumber = totalNumber;
            result.TotalPage = totalPage;
            return Ok(result);
        }

        /// <summary>
        /// 获取登记资料列表
        /// </summary>
        /// <param name="personOrCompany">个人/团体：person/Company</param>
        /// <param name="queryValue">查询的值</param>
        /// <returns></returns>
        [ProducesResponseType(typeof(RegisterByQueryTypeResult), 200)]
        [HttpPost("GetRegisterByQueryType/{personOrCompany}")]
        public IActionResult GetRegisterByQueryType([FromRoute] string personOrCompany, [FromQuery] string queryValue)
        {
            var errMsg = string.Empty;

            var isCompanyCheck = false;

            switch (personOrCompany.ToLower())
            {
                case "person":
                    break;
                case "company":
                    isCompanyCheck = true;
                    break;
                default:
                    return BadRequest();
            }

            result.ReturnData = _registerService.ReadRegisterByQueryType(isCompanyCheck, queryValue, out bool success, ref errMsg);
            result.Success = success;
            result.ReturnMsg = errMsg;

            return Ok(result);
        }

        /// <summary>
        /// 获取订单的资料、套餐、组合
        /// </summary>
        /// <param name="regNo">体检号</param>
        /// <response code="200">执行成功</response>
        /// <returns></returns>
        [ProducesResponseType(typeof(RegisterNewOrderInfo), 200)]
        [HttpPost("GetRegisterOrder")]
        public IActionResult GetRegisterOrder([FromQuery] string regNo)
        {
            result.ReturnData = _registerService.GetRegisterOrder(regNo);
            result.Success = true;
            return Ok(result);
        }

        /// <summary>
        /// 获取订单的套餐、组合(团体登记的查询)
        /// </summary>
        /// <param name="regNo">体检号</param>
        /// <response code="200">执行成功</response>
        /// <returns></returns>
        [ProducesResponseType(typeof(RegisterOrder), 200)]
        [HttpPost("GetRegisterClusterAndComb")]
        public IActionResult GetRegisterClusterAndComb([FromQuery] string regNo)
        {
            try
            {
                result.ReturnData = _registerService.GetRegisterClusterAndComb(regNo);
            }
            catch (Exception ex)
            {
                result.Success = false;
                result.ReturnMsg = ex.Message;
            }
            return Ok(result);
        }

        /// <summary>
        /// 计算组合的试管的材料费
        /// </summary>
        /// <param name="combCodes">组合列表</param>
        /// <returns></returns>
        [ProducesResponseType(typeof(decimal), 200)]
        [HttpPost("CalculateTestTubeMaterialFees")]
        public IActionResult CalculateTestTubeMaterialFees([FromBody] string[] combCodes)
        {
            result.ReturnData = _registerService.ReadTestTubeMaterialFees(combCodes);
            return Ok(result);
        }

        /// <summary>
        /// 同步个人缴费状态
        /// </summary>
        /// <param name="regNo"></param>
        /// <returns></returns>
        [HttpPost("SyncPayStatus")]
        public IActionResult SyncPayStatus([FromQuery] string regNo)
        {
            _externalSystemService.SyncOrderPayStatus(regNo);
            return Ok(result);
        }

        #region 回收记录/恢复记录

        /// <summary>
        /// RecycleRegisterOrder/person  回收订单记录(个检)
        /// RecycleRegisterOrder/company 回收订单记录(团检)
        /// </summary>
        /// <param name="personOrCompany"></param>
        /// <param name="regNoArray">体检号数组</param>
        /// <response code="200">执行成功</response>
        /// <returns></returns>
        [HttpPost("RecycleRegisterOrder/{personOrCompany}")]
        public IActionResult RecycleRegisterOrder([FromRoute] string personOrCompany, [FromBody] string[] regNoArray)
        {
            result.Success = _registerService.RecycleRegisterOrder(regNoArray);
            return Ok(result);
        }

        /// <summary>
        /// GetRecycleRegisterOrder/person  查询回收的订单(个检)
        /// GetRecycleRegisterOrder/company 查询回收的订单(团检)
        /// </summary>
        /// <param name="personOrCompany"></param>
        /// <param name="delQuery"></param>
        /// <response code="200">执行成功</response>
        /// <returns></returns>
        [ProducesResponseType(typeof(DeletedOrder[]), 200)]
        [HttpPost("GetRecycleRegisterOrder/{personOrCompany}")]
        public IActionResult GetRecycleRegisterOrder([FromRoute] string personOrCompany, [FromBody] DelOrderQuery delQuery)
        {
            bool isCompanyCheck = false;

            switch (personOrCompany.ToLower())
            {
                case "person":
                    break;
                case "company":
                    isCompanyCheck = true;
                    break;
                default:
                    return BadRequest();
            }

            result.ReturnData = _registerService.GetRecycleRegisterOrder(isCompanyCheck, delQuery);
            return Ok(result);
        }

        /// <summary>
        /// DeleteRegisterOrder/person  删除订单记录(个检)
        /// DeleteRegisterOrder/company 删除订单记录(团检)
        /// </summary>
        /// <param name="personOrCompany"></param>
        /// <param name="regNoArray">体检号数组</param>
        /// <response code="200">执行成功</response>
        /// <returns></returns>
        [HttpPost("DeleteRegisterOrder/{personOrCompany}")]
        public IActionResult DeleteRegisterOrder([FromRoute] string personOrCompany, [FromBody] string[] regNoArray)
        {
            string msg = string.Empty;
            result.Success = _registerService.DeleteRegisterOrder(regNoArray, ref msg);
            result.ReturnMsg = msg;
            return Ok(result);
        }

        /// <summary>
        /// RestoreRegisterOrder/person  恢复订单记录(个检)
        /// RestoreRegisterOrder/company 恢复订单记录(团体)
        /// </summary>
        /// <param name="personOrCompany"></param>
        /// <param name="regNoArray">体检号数组</param>
        /// <response code="200">执行成功</response>
        /// <returns></returns>
        [HttpPost("RestoreRegisterOrder/{personOrCompany}")]
        public IActionResult RestoreRegisterOrder([FromRoute] string personOrCompany, [FromBody] string[] regNoArray)
        {
            string msg = string.Empty;
            bool flag = _registerService.RestoreRegisterOrder(regNoArray, ref msg);
            if (!flag)
            {
                result.Success = false;
                result.ReturnMsg = msg;
                return Ok(result);
            }

            return Ok(result);
        }

        #endregion

        #region 体检激活

        /// <summary>
        /// 查询可激活/可取消激活记录
        /// </summary>
        /// <param name="activeQuery"></param>
        /// <response code="200">执行成功</response>
        /// <returns></returns>
        [HttpPost("GetActiveRecord")]
        [ProducesResponseType(typeof(ActiveRecord[]), 200)]
        public IActionResult GetActiveRecord(ActiveQuery activeQuery)
        {
            result.ReturnData = _registerService.ReadActiveRecord(activeQuery);
            return Ok(result);
        }

        /// <summary>
        /// {type}:[Active 激活, Deactive 取消激活]：批量操作时进度状态以websocket通知前端
        /// </summary>
        /// <param name="type">Active 激活, Deactive 取消激活</param>
        /// <param name="activeRegister"></param>
        /// <response code="200">执行成功</response>
        /// <returns></returns>
        [HttpPost("ActivationOrCancel/{type}")]
        public IActionResult ActivationOrCancel([FromRoute] string type, [FromBody] ActiveRegister activeRegister)
        {
            string msg = string.Empty;

            switch (type.ToLower())
            {
                case "active":
                    result.Success = _registerService.ActivationOrCancel(true, activeRegister, ref msg);
                    break;
                case "deactive":
                    result.Success = _registerService.ActivationOrCancel(false, activeRegister, ref msg);
                    break;
                default:
                    msg = $"类型不支持：{type}";
                    break;
            }

            result.ReturnMsg = msg;
            return Ok(result);
        }
        #endregion

        #region 批修改

        /// <summary>
        /// 批量修改查询
        /// </summary>
        /// <param name="batchQuery"></param>
        /// <response code="200">执行成功</response>
        /// <returns></returns>
        [HttpPost("GetBatchUpdate")]
        [ProducesResponseType(typeof(BatchUpdateRecord[]), 200)]
        public IActionResult GetBatchUpdate([FromBody] BatchUpdateQuery batchQuery)
        {
            result.ReturnData = _registerService.ReadBatchUpdateRecord(batchQuery);
            return Ok(result);
        }

        /// <summary>
        /// 批量修改
        /// </summary>
        /// <param name="batchUpdateArray"></param>
        /// <response code="200">执行成功</response>
        /// <returns></returns>
        [HttpPost("BatchUpdateByRegNo")]
        public IActionResult BatchUpdateByRegNo([FromBody] BatchUpdateArray batchUpdateArray)
        {
            string msg = string.Empty;
            var flag = _registerService.BatchUpdateRegisterOrder(batchUpdateArray, ref msg);
            if (!flag)
            {
                result.Success = false;
                result.ReturnMsg = msg;
                return Ok(result);
            }
            return Ok(result);
        }

        #endregion

        #region 批增加/批删除

        /// <summary>
        /// GetBatchAddOrDelete    获取批增加/批删除数据
        /// </summary>
        /// <param name="batchQuery"></param>
        /// <response code="200">执行成功</response>
        /// <returns></returns>
        [HttpPost("GetBatchAddOrDelete")]
        [ProducesResponseType(typeof(BatchAddOrDeleteRecord[]), 200)]
        public IActionResult GetBatchAddOrDelete([FromBody] BatchAddOrDeleteQuery batchQuery)
        {
            result.ReturnData = _registerService.ReadBatchAddOrDeleteOrder(batchQuery);
            return Ok(result);
        }

        /// <summary>
        /// BatchAddOrDelete/batchadd    批增加/批删除
        /// BatchAddOrDelete/batchdelete 批增加/批删除
        /// </summary>
        /// <param name="type"></param>
        /// <param name="batchArray"></param>
        /// <response code="200">执行成功</response>
        /// <returns></returns>
        [HttpPost("BatchAddOrDelete/{type}")]
        public IActionResult BatchAddOrDelete([FromRoute] string type, [FromBody] BatchAddOrDeleteArray batchArray)
        {
            string msg = string.Empty;
            bool flag = false;
            switch (type.ToLower())
            {
                case "batchadd":
                    flag = _registerService.BatchAddComb(batchArray, ref msg);
                    break;
                case "batchdelete":
                    flag = _registerService.BatchDeleteComb(batchArray, ref msg);
                    break;
                default:
                    return BadRequest();
            }

            if (!flag)
            {
                result.Success = false;
                result.ReturnMsg = msg;
                return Ok(result);
            }

            return Ok(result);
        }

        #endregion

        /// <summary>
        /// 查询可打印（指引单、报告、体检标签、检验条码、采血检验条码、非采血检验条码）
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        [HttpPost("CheckPrintables")]
        [ProducesResponseType(typeof(List<string>), 200)]
        public IActionResult CheckPrintables([FromBody] PrintQuery query)
        {
            result.ReturnData = _registerService.CheckPrintables(query);
            return Ok(result);
        }

        /// <summary>
        /// 通过体检号获取问卷数据
        /// </summary>
        /// <param name="regNo">体检号</param>
        /// <returns></returns>
        [HttpPost("GetQuestionDataByRegNo")]
        [ProducesResponseType(typeof(WeChatBookQuestion), 200)]
        public IActionResult GetQuestionDataByRegNo([FromQuery] string regNo)
        {
            result.ReturnData = _registerService.GetQuestionDataByRegNo(regNo);
            return Ok(result);
        }

        /// <summary>
        /// 更新微信问卷数据
        /// </summary>
        /// <param name="weChatBookQuestion">问卷数据</param>
        /// <returns></returns>
        [HttpPost("UpdateQuestionData")]
        public IActionResult UpdateQuestionData([FromBody] WeChatBookQuestion weChatBookQuestion)
        {
            result.Success = _registerService.UpdateQuestionData(weChatBookQuestion);
            return Ok(result);
        }

        /// <summary>
        /// 根据体检号获取套餐组合记录
        /// </summary>
        /// <param name="regNo"></param>
        /// <returns></returns>
        [HttpPost("ReadRegisterClusterComb")]
        [ProducesResponseType(typeof(CopyRegisterClusterComb), 200)]
        public IActionResult ReadRegisterClusterComb([FromQuery] string regNo)
        {
            result.ReturnData = _registerService.ReadRegisterClusterComb(regNo);
            result.Success = true;
            return Ok(result);
        }

        /// <summary>
        /// 查询人员列表用于复制组合
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        [HttpPost("ReadCopyPersonList")]
        [ProducesResponseType(typeof(CopyRegisterClusterComb), 200)]
        public IActionResult ReadCopyPersonList([FromBody] CopyPersonQuery query)
        {
            int totalNumber = 0;
            int totalPage = 0;
            result.ReturnData = _registerService.ReadCopyPersonList(query, ref totalNumber, ref totalPage);
            result.TotalNumber = totalNumber;
            result.TotalPage = totalPage;
            result.Success = true;
            return Ok(result);
        }

        /// <summary>
        /// 生成复查订单
        /// </summary>
        /// <param name="recheckComb"></param>
        /// <returns></returns>
        [HttpPost("CreateRecheckOrder")]
        public IActionResult CreateRecheckOrder([FromBody] PeRecheckComb recheckComb)
        {
            result.Success = true;
            result.ReturnData = _registerService.CreateRecheckOrder(recheckComb);
            return Ok(result);
        }

        /// <summary>
        /// 保存复查组合
        /// </summary>
        /// <param name="recheckComb"></param>
        /// <returns></returns>
        [HttpPost("SavePeRecheckComb")]
        public IActionResult SavePeRecheckComb([FromBody] PeRecheckComb recheckComb)
        {
            _registerService.SavePeRecheckComb(recheckComb);
            result.Success = true;
            return Ok(result);
        }

        /// <summary>
        /// 获取复查信息
        /// </summary>
        /// <param name="regNo"></param>
        /// <returns></returns>
        [HttpPost("ReadRecheckInfo")]
        [ProducesResponseType(typeof(RecheckInfo), 200)]
        public IActionResult ReadRecheckInfo([FromQuery] string regNo)
        {
            result.ReturnData = _registerService.ReadRecheckInfo(regNo);
            result.Success = true;
            return Ok(result);
        }

        /// <summary>
        /// 撤销复查
        /// </summary>
        /// <param name="regNo"></param>
        /// <returns></returns>
        [HttpPost("CancelRecheckOrder")]
        public IActionResult CancelRecheckOrder([FromQuery] string regNo)
        {
            result.Success = _registerService.CancelRecheckOrder(regNo);
            return Ok(result);
        }

        #region 登记实时计算
        /// <summary>
        /// 实时计算增删组合
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost("RealtimeCalculateComb")]
        [ProducesResponseType(typeof(CalculateOutput), 200)]
        public IActionResult RealtimeCalculateComb([FromBody] CalculateCombInput input)
        {
            result.ReturnData = _registerService.CalculateComb(input);
            return Ok(result);
        }

        /// <summary>
        /// 实时计算增删套餐
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost("RealtimeCalculateCluster")]
        [ProducesResponseType(typeof(CalculateOutput), 200)]
        public IActionResult RealtimeCalculateCluster([FromBody] CalculateClusterInput input)
        {
            result.ReturnData = _registerService.CalculateCluster(input);
            return Ok(result);
        }

        /// <summary>
        /// 实时计算增删危害因素
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost("RealtimeCalculateHazardFacotr")]
        [ProducesResponseType(typeof(CalculateOutput), 200)]
        public IActionResult RealtimeCalculateHazardFacotr([FromBody] CalculateHazardInput input)
        {
            result.ReturnData = _registerService.CalculateHazardFactors(input);
            return Ok(result);
        }

        /// <summary>
        /// 实时计算材料费
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost("RealtimeCalculateTestTube")]
        [ProducesResponseType(typeof(List<RegisterNewComb>), 200)]
        public IActionResult RealtimeCalculateTestTube([FromBody] CalculateTestTubeInput input)
        {
            result.ReturnData = _registerService.CalculateTestTube(input);
            return Ok(result);
        }
        #endregion
    }
}
