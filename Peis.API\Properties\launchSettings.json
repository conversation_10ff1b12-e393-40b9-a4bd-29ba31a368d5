{"profiles": {"Peis": {"commandName": "Project"}, "Tiga": {"commandName": "Project", "launchUrl": "swagger", "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "Development"}, "applicationUrl": "http://************:9001", "nativeDebugging": true}, "Container (Dockerfile)": {"commandName": "<PERSON>er", "launchUrl": "{Scheme}://{ServiceHost}:{ServicePort}", "environmentVariables": {"ASPNETCORE_URLS": "http://+:80"}, "publishAllPorts": true, "useSSL": false}}}