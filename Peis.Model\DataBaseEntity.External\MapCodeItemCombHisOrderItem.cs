﻿namespace Peis.Model.DataBaseEntity.External;

///<summary>
/// 基础数据：体检组合与His医嘱项目对应关系中间表
///</summary>
[SugarTable(nameof(MapCodeItemCombHisOrderItem), TableDescription = "体检组合与His医嘱项目对应关系中间表")]
public class MapCodeItemCombHisOrderItem
{
    public MapCodeItemCombHisOrderItem() { }

    public MapCodeItemCombHisOrderItem([NotNull] CodeItemComb comb, [NotNull] CodeHisOrderItem codeHisOrder)
    {
        CombCode = comb.CombCode;
        CombName = comb.CombName;
        HisOrderCode = codeHisOrder.OrderItemCode;
        HisOrderCNName = codeHisOrder.OrderItemCNName;
        HisOrderIsAvailable = codeHisOrder.IsAvailable;
        IsDedicatePies = comb.IsDedicatePies;
    }

    /// <summary>
    /// 体检组合代码
    /// </summary>        
    [SugarColumn(IsPrimaryKey = true, Length = 8)]
    public string CombCode { get; init; }

    /// <summary>
    /// 体检组合名称
    /// </summary>        
    [SugarColumn(Length = 200)]
    public string CombName { get; init; }

    /// <summary>
    /// 医嘱项目Code
    /// </summary>        
    [SugarColumn(IsPrimaryKey = true, Length = 64)]
    public string HisOrderCode { get; init; }

    /// <summary>
    /// 医嘱项目中文名称
    /// </summary>        
    [SugarColumn(ColumnDataType = "nvarchar(256)")]
    public string HisOrderCNName { get; init; }

    /// <summary>
    /// 来源医嘱项目是否启用，用于区分，同步数据与现有关系数据是否有禁用与启用差异
    /// </summary>        
    [SugarColumn(Length = 1)]
    public string HisOrderIsAvailable { get; init; }

    /// <summary>
    /// 是否体检专用（用于公告提示过滤）
    /// </summary>        
    [SugarColumn(Length = 1)]
    public bool IsDedicatePies { get; init; }
}