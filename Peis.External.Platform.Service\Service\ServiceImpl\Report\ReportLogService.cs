﻿using Peis.External.Platform.Service.Service.IService.Report;
using Peis.External.Platform.Service.Service.IService.WebService;
using Peis.Model.DTO.External.Report;
using Peis.Service.IService;

namespace Peis.External.Platform.Service.Service.ServiceImpl.Report
{
    public class ReportLogService : IReportLogService
    {
        private readonly IDataRepository<ExtLisReportLog> _lisLogRepository;
        private readonly IDataRepository<ExtExamReportLog> _examLogRepository;
        private readonly IExternalWebService _externalWebService;
        private readonly IExamReportService _examReportService;

        public ReportLogService(
            IDataRepository<ExtLisReportLog> lisLogRepository,
            IDataRepository<ExtExamReportLog> examLogRepository,
            IExternalWebService externalWebService,
            IExamReportService examReportService)
        {
            _lisLogRepository = lisLogRepository;
            _examLogRepository = examLogRepository;
            _externalWebService = externalWebService;
            _examReportService = examReportService;
        }


        public bool GetReportRecieveLog(string regNo, ref List<ExtRecieveLogGroup> logGroups)
        {
            logGroups.Add(new ExtRecieveLogGroup
            {
                RegNo = regNo,
                ReportType = "Lis",
                logs = _lisLogRepository.FindAll(x => x.RegNo == regNo).Select(x => new ExtRecieveLog
                {
                    ReportName = x.CombName,
                    RequestNumber = x.SampleNo,
                    Success = x.Success,
                    CreateTime = x.CreateTime,
                    LastTime = x.LastTime,
                    ReportXML = x.ReportXML,
                    ErrMsg = x.ErrMsg
                }).ToArray()
            });
            logGroups.Add(new ExtRecieveLogGroup
            {
                RegNo = regNo,
                ReportType = "Exam",
                logs = _examLogRepository.FindAll(x => x.RegNo == regNo).Select(x => new ExtRecieveLog
                {
                    ReportName = x.CombName,
                    RequestNumber = x.ApplyNo,
                    Success = x.Success,
                    CreateTime = x.CreateTime,
                    LastTime = x.LastTime,
                    ReportXML = x.ReportXML
                }).ToArray()
            });
            return true;
        }

        public bool RecieveExtReport(string type, string requestNumber)
        {
            switch (type)
            {
                case "Lis":
                    ReacceptLisReport(requestNumber);
                    break;
                case "Exam":
                    _examReportService.ReacceptReport(requestNumber);
                    break;
                default:
                    throw new BusinessException($"未支持的类型：{type}");
            }

            return true;
        }

        void ReacceptLisReport(string requestNumber)
        {
            var reqMsg = _lisLogRepository.First(x => x.SampleNo == requestNumber).ReportXML;
            if (string.IsNullOrEmpty(reqMsg))
                throw new BusinessException("未找到日志报文，无法接收！");

            var result = XmlHelper.Deserialize<ReportResult>(_externalWebService.Operation("synchLisReportPE", reqMsg));
            if (result.ResultCode != "0")
                throw new BusinessException($"重新接收失败，{result.Note}");
        }
    }
}
