﻿namespace Peis.Model.DataBaseEntity.External
{
    /// <summary>
    /// 团体个人套餐外加项收费保存表
    /// </summary>
    [SugarTable]
    public class CompanySettlementEntryPersonExtra
    {
        /// <summary>
        /// 体检号
        /// </summary>
        [SugarColumn(IsNullable = false, IsPrimaryKey = true, Length = 12)]
        public string REG_NO { set; get; }
        /// <summary>
        /// 组合代码
        /// </summary>
        [SugarColumn(IsNullable = false, IsPrimaryKey = true, Length = 10)]
        public string PE_ITEM_CODE { set; get; }
        /// <summary>
        /// 组合名
        /// </summary>
        [SugarColumn(IsNullable = false, Length = 200)]
        public string PE_ITEM_NAME { set; get; }
        /// <summary>
        /// 收费项目代码
        /// </summary>
        [SugarColumn(IsNullable = false, IsPrimaryKey = true, Length = 30)]
        public string HIS_ITEM_CODE { set; get; }
        /// <summary>
        /// 收费项目名称
        /// </summary>
        [SugarColumn(IsNullable = false, Length = 200)]
        public string HIS_ITEM_NAME { set; get; }
        /// <summary>
        /// 单价
        /// </summary>
        [SugarColumn(IsNullable = false, Length = 10, DecimalDigits = 2)]
        public decimal UNIT { set; get; }
        /// <summary>
        /// 应收金额
        /// </summary>
        [SugarColumn(IsNullable = false, Length = 12, DecimalDigits = 2)]
        public decimal THEORY_TOTAL_AMOUNT { set; get; }
        /// <summary>
        /// 实收金额
        /// </summary>
        [SugarColumn(IsNullable = false, Length = 12, DecimalDigits = 2)]
        public decimal ACTUAL_TOTAL_AMOUNT { set; get; }
        /// <summary>
        /// 实收数量
        /// </summary>
        [SugarColumn(IsNullable = false)]
        public int QUANTITY { set; get; }
        /// <summary>
        /// 执行科室
        /// </summary>
        [SugarColumn(IsNullable = false, Length = 1000)]
        public string PERFORMING_DEPT { set; get; }
    }
}
