﻿using Peis.Model.DataBaseEntity;
using Peis.Model.DTO.Register;
using Peis.Model.Other;
using Peis.Model.Other.PeEnum;
using Peis.Model.TableFilter;
using Peis.Repository.IRepository;
using Peis.Utility.PeUser;
using SqlSugar;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Linq.Expressions;

namespace Peis.Repository.Repository;

/// <summary>
/// 枚举/下拉列表数据
/// </summary>
public class EnumDataRepository : IEnumDataRepository
{
    private readonly ISqlSugarClient _db;
    private readonly IHttpContextUser _httpContextUser;


    public EnumDataRepository(ISqlSugarClient db, IHttpContextUser httpContextUser)
    {
        _db = db;
        _httpContextUser = httpContextUser;
    }

    /// <summary>
    /// 获取项目分类
    /// </summary>
    /// <param name="pageNumber"></param>
    /// <param name="pageSize"></param>
    /// <param name="totalNumber"></param>
    /// <param name="totalPage"></param>
    /// <returns></returns>
    public object ItemCls(int pageNumber, int pageSize, ref int totalNumber, ref int totalPage)
    {
        var queryable = _db.Queryable<CodeItemCls>()
                    .OrderBy(x => x.SortIndex)
                    .Select(x => new
                    {
                        x.ClsCode,
                        x.ClsName
                    });

        if (pageNumber == 0 || pageSize == 0)
            return queryable.ToArray();
        else
            return queryable.ToPageList(pageNumber, pageSize, ref totalNumber, ref totalPage);
    }

    /// <summary>
    /// 获取指引单样式
    /// </summary>
    /// <param name="pageNumber"></param>
    /// <param name="pageSize"></param>
    /// <param name="totalNumber"></param>
    /// <param name="totalPage"></param>
    /// <returns></returns>
    public object GuidanceType(int pageNumber, int pageSize, ref int totalNumber, ref int totalPage)
    {
        var queryable = _db.Queryable<CodeReportTemplate>()
                    .Where(x => x.TypeCode == "Guidance")
                    .Select(x => new
                    {
                        x.ReportCode,
                        x.ReportName
                    });

        if (pageNumber == 0 || pageSize == 0)
            return queryable.ToArray();
        else
            return queryable.ToPageList(pageNumber, pageSize, ref totalNumber, ref totalPage);
    }

    /// <summary>
    /// 获取报告样式
    /// </summary>
    /// <param name="pageNumber"></param>
    /// <param name="pageSize"></param>
    /// <param name="totalNumber"></param>
    /// <param name="totalPage"></param>
    /// <returns></returns>
    public object ReportType(int pageNumber, int pageSize, ref int totalNumber, ref int totalPage)
    {
        var queryable = _db.Queryable<CodeReportTemplate>()
                    .Where(x => x.TypeCode == "ExamReport")
                    .Select(x => new
                    {
                        x.ReportCode,
                        x.ReportName
                    });

        if (pageNumber == 0 || pageSize == 0)
            return queryable.ToArray();
        else
            return queryable.ToPageList(pageNumber, pageSize, ref totalNumber, ref totalPage);
    }

    /// <summary>
    /// 获取项目
    /// </summary>
    /// <param name="pageNumber"></param>
    /// <param name="pageSize"></param>
    /// <param name="totalNumber"></param>
    /// <param name="totalPage"></param>
    /// <returns></returns>
    public object Item(int pageNumber, int pageSize, ref int totalNumber, ref int totalPage)
    {
        var queryable = _db.Queryable<CodeItem>()
            .Where(x => x.IsEnabled)
            .OrderBy(x => x.SortIndex)
            .OrderBy(x => x.ClsCode)
            .Select(x => new
            {
                x.ItemCode,
                x.ItemName,
                x.ValueType
            });

        if (pageNumber == 0 || pageSize == 0)
            return queryable.ToArray();
        else
            return queryable.ToPageList(pageNumber, pageSize, ref totalNumber, ref totalPage);
    }

    /// <summary>
    /// 获取项目-项目分类
    /// </summary>
    /// <param name="pageNumber"></param>
    /// <param name="pageSize"></param>
    /// <param name="totalNumber"></param>
    /// <param name="totalPage"></param>
    /// <returns></returns>
    public object Item_ItemCls(int pageNumber, int pageSize, ref int totalNumber, ref int totalPage)
    {
        var queryable = _db
            .Queryable<CodeItem>()
            .InnerJoin<CodeItemCls>((item, itemCls) => item.ClsCode == itemCls.ClsCode)
            .Where((item, itemCls) => item.IsEnabled)
            .OrderBy((item, itemCls) => item.SortIndex)
            .OrderBy((item, itemCls) => itemCls.ClsCode)
            .Select((item, itemCls) => new
            {
                item.ItemCode,
                item.ItemName,
                itemCls.ClsCode,
                itemCls.ClsName
            });

        if (pageNumber == 0 || pageSize == 0)
            return queryable.ToArray();
        else
            return queryable.ToPageList(pageNumber, pageSize, ref totalNumber, ref totalPage);
    }

    /// <summary>
    /// 获取组合
    /// </summary>
    /// <param name="pageNumber"></param>
    /// <param name="pageSize"></param>
    /// <param name="totalNumber"></param>
    /// <param name="totalPage"></param>
    /// <returns></returns>
    public object ItemComb(int pageNumber, int pageSize, ref int totalNumber, ref int totalPage)
    {
        // 获取有关联项目的组合代码
        var combCodesWithItems = _db.Queryable<MapItemComb>()
            .Select(m => m.CombCode)
            .Distinct()
            .ToArray();

        var queryable = _db
                 .Queryable<CodeItemComb>()
                 .Where(comb => comb.IsEnabled == true)
                 .Where(comb => SqlFunc.ContainsArray(combCodesWithItems, comb.CombCode))
                 .OrderBy(comb => comb.SortIndex)
                 .OrderBy(comb => comb.CombCode)
                 .Select(comb => new CandidateComb
                 {
                     CombCode   = comb.CombCode,
                     CombName   = comb.CombName,
                     Price      =comb.Price,
                     Sex        = comb.Sex,
                     PinYinCode = comb.PinYinCode
                 });

        List<CandidateComb> candidateCombs;
        if (pageNumber == 0 || pageSize == 0)
            candidateCombs = queryable.ToList();
        else
            candidateCombs = queryable.ToPageList(pageNumber, pageSize, ref totalNumber, ref totalPage);

        // 添加互斥组合信息
        AddMutexCombs(candidateCombs);

        return candidateCombs;
    }

    /// <summary>
    /// 获取组合-项目分类
    /// </summary>
    /// <param name="pageNumber"></param>
    /// <param name="pageSize"></param>
    /// <param name="totalNumber"></param>
    /// <param name="totalPage"></param>
    /// <returns></returns>
    public object ItemComb_ItemCls(int pageNumber, int pageSize, ref int totalNumber, ref int totalPage)
    {
        var queryable = _db
               .Queryable<CodeItemComb>()
               .InnerJoin<CodeItemCls>((comb, itemCls) => comb.ClsCode == itemCls.ClsCode)
               .Where((comb, itemCls) => comb.IsEnabled == true)
               .Where(comb => SqlFunc.Subqueryable<MapItemComb>().Where(m => m.CombCode == comb.CombCode).Any())
               .OrderBy((comb, itemCls) => itemCls.SortIndex)
               .OrderBy((comb, itemCls) => itemCls.ClsCode)
               .OrderBy((comb, itemCls) => comb.SortIndex)
               .OrderBy((comb, itemCls) => comb.CombCode)
               .Select((comb, itemCls) => new CandidateComb
               {
                   CombCode   = comb.CombCode,
                   CombName   = comb.CombName,
                   ClsCode    = itemCls.ClsCode,
                   ClsName    = SqlFunc.IsNull(itemCls.ClsName, "(无项目分类)"),
                   Price      = comb.Price,
                   HisCode    = comb.HisCode,
                   ReportShow = comb.ReportShow,
                   Sex        = comb.Sex,
                   PinYinCode = comb.PinYinCode
               });

        List<CandidateComb> candidateCombs;
        if (pageNumber == 0 || pageSize == 0)
            candidateCombs = queryable.ToList();
        else
            candidateCombs = queryable.ToPageList(pageNumber, pageSize, ref totalNumber, ref totalPage);

        // 添加互斥组合信息
        AddMutexCombs(candidateCombs);

        return candidateCombs;
    }

    /// <summary>
    /// 获取体检套餐
    /// </summary>
    /// <param name="pageNumber"></param>
    /// <param name="pageSize"></param>
    /// <param name="totalNumber"></param>
    /// <param name="totalPage"></param>
    /// <returns></returns>
    public object Cluster(int pageNumber, int pageSize, ref int totalNumber, ref int totalPage)
    {
        var queryable = _db.Queryable<CodeCluster>()
                .Select(x => new
                {
                    x.ClusCode,
                    x.ClusName,
                    x.Price
                });

        if (pageNumber == 0 || pageSize == 0)
            return queryable.ToArray();
        else
            return queryable.ToPageList(pageNumber, pageSize, ref totalNumber, ref totalPage);
    }

    /// <summary>
    /// 获取医生列表
    /// </summary>
    /// <param name="pageNumber"></param>
    /// <param name="pageSize"></param>
    /// <param name="totalNumber"></param>
    /// <param name="totalPage"></param>
    /// <returns></returns>
    public object Operator(int pageNumber, int pageSize, ref int totalNumber, ref int totalPage)
    {
        var queryable = _db.Queryable<SysOperator>()
                .InnerJoin<CodeDepartment>((oper, dept) => oper.DeptCode == dept.DeptCode).ClearFilter<IHospCodeFilter>()
                .Where(oper => SqlFunc.JsonLike(oper.HospCode, _httpContextUser.HospCode))
                .Select((oper, dept) => new
                {
                    oper.OperatorCode,
                    oper.Name,
                    dept.DeptName
                });

        if (pageNumber == 0 || pageSize == 0)
            return queryable.ToArray();
        else
            return queryable.ToPageList(pageNumber, pageSize, ref totalNumber, ref totalPage);
    }

    /// <summary>
    /// 获取科室列表
    /// </summary>
    /// <param name="pageNumber"></param>
    /// <param name="pageSize"></param>
    /// <param name="totalNumber"></param>
    /// <param name="totalPage"></param>
    /// <returns></returns>
    public object Department(int pageNumber, int pageSize, ref int totalNumber, ref int totalPage)
    {
        var queryable = _db.Queryable<CodeDepartment>()
               .Select(x => new
               {
                   x.DeptCode,
                   x.DeptName
               });

        if (pageNumber == 0 || pageSize == 0)
            return queryable.ToArray();
        else
            return queryable.ToPageList(pageNumber, pageSize, ref totalNumber, ref totalPage);
    }

    /// <summary>
    /// 获取档案项目
    /// </summary>
    /// <param name="pageNumber"></param>
    /// <param name="pageSize"></param>
    /// <param name="totalNumber"></param>
    /// <param name="totalPage"></param>
    /// <returns></returns>
    public object ArchiveItem(int pageNumber, int pageSize, ref int totalNumber, ref int totalPage)
    {
        var queryable = _db.Queryable<CodeArchiveItem>()
               .Select(x => new
               {
                   x.ArchiveCode,
                   x.ArchiveName
               });

        if (pageNumber == 0 || pageSize == 0)
            return queryable.ToArray();
        else
            return queryable.ToPageList(pageNumber, pageSize, ref totalNumber, ref totalPage);
    }

    /// <summary>
    /// 获取疾病列表
    /// </summary>
    /// <param name="pageNumber"></param>
    /// <param name="pageSize"></param>
    /// <param name="totalNumber"></param>
    /// <param name="totalPage"></param>
    /// <returns></returns>
    public object Disease(int pageNumber, int pageSize, string keyword, ref int totalNumber, ref int totalPage)
    {
        var queryable = _db.Queryable<CodeDisease>()
                .WhereIF(!string.IsNullOrEmpty(keyword), x => x.DiseaseName.Contains(keyword))
                .Select(x => new
                {
                    x.DiseaseCode,
                    x.DiseaseName
                });

        if (pageNumber == 0 || pageSize == 0)
            return queryable.ToArray();
        else
            return queryable.ToPageList(pageNumber, pageSize, ref totalNumber, ref totalPage);
    }

    /// <summary>
    /// 单位列表
    /// </summary>
    /// <param name="pageNumber"></param>
    /// <param name="pageSize"></param>
    /// <param name="totalNumber"></param>
    /// <param name="totalPage"></param>
    /// <returns></returns>
    public object Company(int pageNumber, int pageSize, ref int totalNumber, ref int totalPage)
    {
        var queryable = _db.Queryable<CodeCompany>()
            .Where(x => SqlFunc.Subqueryable<CodeCompanyTimes>().Where(y => y.CompanyCode == x.CompanyCode).Any())
            .Select(x => new
            {
                x.CompanyCode,
                x.CompanyName
            });

        if (pageNumber == 0 || pageSize == 0)
            return queryable.ToArray();
        else
            return queryable.ToPageList(pageNumber, pageSize, ref totalNumber, ref totalPage);
    }

    /// <summary>
    /// 获取所有单位及体检次数
    /// </summary>
    /// <param name="pageNumber"></param>
    /// <param name="pageSize"></param>
    /// <param name="totalNumber"></param>
    /// <param name="totalPage"></param>
    /// <returns></returns>
    public object CompanyAndTimes(int pageNumber, int pageSize, ref int totalNumber, ref int totalPage)
    {
        var queryable = _db.Queryable<CodeCompany>()
            .InnerJoin<CodeCompanyTimes>((company, times) => times.CompanyCode == company.CompanyCode)
            .Select((company, times) => new
            {
                company.CompanyCode,
                company.CompanyName,
                times.CompanyTimes,
                times.BeginDate,
                times.EndDate
            });

        if (pageNumber == 0 || pageSize == 0)
        {
            return queryable
                .ToList()
                .GroupBy(x => new { x.CompanyCode, x.CompanyName })
                .Select(x => new
                {
                    x.Key.CompanyCode,
                    x.Key.CompanyName,
                    CompanyTimes = x.Select(y => new
                    {
                        y.CompanyTimes,
                        y.BeginDate,
                        y.EndDate,
                    }).ToArray()
                }).ToArray();
        }
        else
        {
            return queryable.ToPageList(pageNumber, pageSize, ref totalNumber, ref totalPage)
                 .GroupBy(x => new { x.CompanyCode, x.CompanyName })
                 .Select(x => new
                 {
                     x.Key.CompanyCode,
                     x.Key.CompanyName,
                     CompanyTimes = x.Select(y => new
                     {
                         y.CompanyTimes,
                         y.BeginDate,
                         y.EndDate,
                     }).ToArray()
                 }).ToArray();
        }
    }

    /// <summary>
    /// 籍贯列表
    /// </summary>
    /// <param name="pageNumber"></param>
    /// <param name="pageSize"></param>
    /// <param name="totalNumber"></param>
    /// <param name="totalPage"></param>
    /// <returns></returns>
    public object NativePlace(int pageNumber, int pageSize, ref int totalNumber, ref int totalPage)
    {
        var queryable = _db.Queryable<CodeNativePlace>()
            .Select(x => new
            {
                x.NatCode,
                x.NatName
            });

        if (pageNumber == 0 || pageSize == 0)
            return queryable.ToArray();
        else
            return queryable.ToPageList(pageNumber, pageSize, ref totalNumber, ref totalPage);
    }

    /// <summary>
    /// 证件类型
    /// </summary>
    /// <param name="pageNumber"></param>
    /// <param name="pageSize"></param>
    /// <param name="totalNumber"></param>
    /// <param name="totalPage"></param>
    /// <returns></returns>
    public object CardType(int pageNumber, int pageSize, ref int totalNumber, ref int totalPage)
    {
        //var queryable = _db.Queryable<DictCardType>()
        //    .Select(x => new
        //    {
        //        x.CardTypeCode,
        //        x.CardTypeName
        //    });

        //if (pageNumber == 0 || pageSize == 0)
        //    return queryable.ToArray();
        //else
        //    return queryable.ToPageList(pageNumber, pageSize, ref totalNumber, ref totalPage);

        var enumList = Enum.GetValues<CardType>()
            .Select(x => new
            {
                CardTypeCode = Convert.ToInt32(x).ToString(),
                CardTypeNamex = Enum.GetName(x)
            });

        return enumList;
    }

    /// <summary>
    /// 工种
    /// </summary>
    /// <param name="pageNumber"></param>
    /// <param name="pageSize"></param>
    /// <param name="totalNumber"></param>
    /// <param name="totalPage"></param>
    /// <returns></returns>
    public object Job(int pageNumber, int pageSize, ref int totalNumber, ref int totalPage)
    {
        var queryable = _db.Queryable<CodeJob>()
            .Select(x => new
            {
                x.JobCode,
                x.JobName
            });

        if (pageNumber == 0 || pageSize == 0)
            return queryable.ToArray();
        else
            return queryable.ToPageList(pageNumber, pageSize, ref totalNumber, ref totalPage);
    }

    /// <summary>
    /// 条码分类
    /// </summary>
    /// <param name="pageNumber"></param>
    /// <param name="pageSize"></param>
    /// <param name="totalNumber"></param>
    /// <param name="totalPage"></param>
    /// <returns></returns>
    public object BarcodeType(int pageNumber, int pageSize, ref int totalNumber, ref int totalPage)
    {
        var queryable = _db.Queryable<CodeBarcodeType>()
            .Select(x => new
            {
                x.Barcode,
                x.BarcodeName,
                x.Color,
                x.Note,
                x.FeeCombCode
            });

        if (pageNumber == 0 || pageSize == 0)
            return queryable.ToArray();
        else
            return queryable.ToPageList(pageNumber, pageSize, ref totalNumber, ref totalPage);
    }

    /// <summary>
    /// 按角色获取操作员列表
    /// </summary>
    /// <returns></returns>
    public List<SysOperator> GetOperatorListByRoleCode(string roleCode)
    {
        return _db.Queryable<SysOperatorRole>()
            .InnerJoin<SysOperator>((role, oper) => role.OperatorCode == oper.OperatorCode && role.RoleCode == roleCode)
            .Where((role, oper) => oper.IsEnabled == true)
            .Where((role, oper) => SqlFunc.JsonLike(oper.HospCode, _httpContextUser.HospCode))
            .Select((role, oper) => oper)
            .ToList();
    }

    /// <summary>
    /// 获取数据表枚举列表
    /// </summary>
    /// <typeparam name="TTable"></typeparam>
    /// <param name="selectExp"></param>
    /// <param name="whereExp"></param>
    /// <returns></returns>
    public List<EnumCodeName> GetDataBaseEnums<TTable>(Expression<Func<TTable, EnumCodeName>> selectExp,
        Expression<Func<TTable, bool>> whereExp = null)
    {
        return _db.Queryable<TTable>()
            .WhereIF(whereExp != null, whereExp)
            .Select(selectExp).
            ToList();
    }


    #region private methods
    /// <summary>
    /// 添加互斥组合信息
    /// </summary>
    /// <param name="candidateCombs"></param>
    void AddMutexCombs(List<CandidateComb> candidateCombs)
    {
        if (candidateCombs.IsNullOrEmpty()) return;

        var dictCombs = candidateCombs.ToDictionary(x => x.CombCode);
        var combMutexCombs = _db.Queryable<CodeMutexComb>()
                    .Select(x => new
                    {
                        x.MutexCode,
                        x.CombCode
                    }).ToArray();

        var mutexGroups = combMutexCombs.GroupBy(x => x.MutexCode, x => x.CombCode);
        foreach (var mutexCombs in mutexGroups)
        {
            var combs = mutexCombs.ToArray();
            for (int a = 0; a < combs.Length; a++)
            {
                for (int b = 0; b < combs.Length; b++)
                {
                    if (b == a)
                        continue;

                    if (dictCombs.ContainsKey(combs[a]))
                    {
                        if (!dictCombs[combs[a]].MutexCombs.Contains(combs[b]))
                            dictCombs[combs[a]].MutexCombs.Add(combs[b]);
                    }
                }
            }
        }
    }

    #endregion
}
