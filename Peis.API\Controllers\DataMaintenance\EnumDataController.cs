﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Peis.Service.IService;

namespace Peis.API.Controllers.DataMaintenance
{
    /// <summary>
    /// 枚举/下拉列表数据
    /// </summary>
    [Route("api/[controller]")]
    [ApiController]
    public class EnumDataController : BaseApiController
    {
        private readonly IEnumDataService _enumDataService;

        public EnumDataController(IEnumDataService enumDataService)
        {
            _enumDataService = enumDataService;
        }

        /// <summary>
        /// 获取项目分类
        /// </summary>
        /// <param name="pageNumber"></param>
        /// <param name="pageSize"></param>
        /// <returns></returns>
        [HttpPost("ItemCls")]
        public IActionResult ItemCls([FromQuery] int pageNumber, [FromQuery] int pageSize)
        {
            int totalNumber = 0;
            int totalPage = 0;

            result.ReturnData = _enumDataService.ItemCls(pageNumber, pageSize, ref totalNumber, ref totalPage);

            result.TotalNumber = totalNumber;
            result.TotalPage = totalPage;

            return Ok(result);
        }

        /// <summary>
        /// 获取指引单样式
        /// </summary>
        /// <param name="pageNumber"></param>
        /// <param name="pageSize"></param>
        /// <returns></returns>
        [HttpPost("GuidanceType")]
        public IActionResult GuidanceType([FromQuery] int pageNumber, [FromQuery] int pageSize)
        {
            int totalNumber = 0;
            int totalPage = 0;

            result.ReturnData = _enumDataService.GuidanceType(pageNumber, pageSize, ref totalNumber, ref totalPage);

            result.TotalNumber = totalNumber;
            result.TotalPage = totalPage;

            return Ok(result);
        }

        /// <summary>
        /// 获取报告样式
        /// </summary>
        /// <param name="pageNumber"></param>
        /// <param name="pageSize"></param>
        /// <returns></returns>
        [HttpPost("ReportType")]
        public IActionResult ReportType([FromQuery] int pageNumber, [FromQuery] int pageSize)
        {
            int totalNumber = 0;
            int totalPage = 0;

            result.ReturnData = _enumDataService.ReportType(pageNumber, pageSize, ref totalNumber, ref totalPage);

            result.TotalNumber = totalNumber;
            result.TotalPage = totalPage;

            return Ok(result);
        }

        /// <summary>
        /// 获取项目
        /// </summary>
        /// <param name="pageNumber"></param>
        /// <param name="pageSize"></param>
        /// <returns></returns>
        [HttpPost("Item")]
        public IActionResult Item([FromQuery] int pageNumber, [FromQuery] int pageSize)
        {
            int totalNumber = 0;
            int totalPage = 0;

            result.ReturnData = _enumDataService.Item(pageNumber, pageSize, ref totalNumber, ref totalPage);

            result.TotalNumber = totalNumber;
            result.TotalPage = totalPage;

            return Ok(result);
        }

        /// <summary>
        /// 获取项目-项目分类
        /// </summary>
        /// <param name="pageNumber"></param>
        /// <param name="pageSize"></param>
        /// <returns></returns>
        [HttpPost("Item_ItemCls")]
        public IActionResult Item_ItemCls([FromQuery] int pageNumber, [FromQuery] int pageSize)
        {

            int totalNumber = 0;
            int totalPage = 0;

            result.ReturnData = _enumDataService.Item_ItemCls(pageNumber, pageSize, ref totalNumber, ref totalPage);

            result.TotalNumber = totalNumber;
            result.TotalPage = totalPage;

            return Ok(result);
        }

        /// <summary>
        /// 获取组合
        /// </summary>
        /// <param name="pageNumber"></param>
        /// <param name="pageSize"></param>
        /// <returns></returns>
        [HttpPost("ItemComb")]
        public IActionResult ItemComb([FromQuery] int pageNumber, [FromQuery] int pageSize)
        {
            int totalNumber = 0;
            int totalPage = 0;

            result.ReturnData = _enumDataService.ItemComb(pageNumber, pageSize, ref totalNumber, ref totalPage);

            result.TotalNumber = totalNumber;
            result.TotalPage = totalPage;

            return Ok(result);
        }

        /// <summary>
        /// 获取组合-项目分类
        /// </summary>
        /// <param name="pageNumber"></param>
        /// <param name="pageSize"></param>
        /// <returns></returns>
        [HttpPost("ItemComb_ItemCls")]
        public IActionResult ItemComb_ItemCls([FromQuery] int pageNumber, [FromQuery] int pageSize)
        {
            int totalNumber = 0;
            int totalPage = 0;

            result.ReturnData = _enumDataService.ItemComb_ItemCls(pageNumber, pageSize, ref totalNumber, ref totalPage);

            result.TotalNumber = totalNumber;
            result.TotalPage = totalPage;

            return Ok(result);
        }

        /// <summary>
        /// 获取体检套餐
        /// </summary>
        /// <param name="pageNumber"></param>
        /// <param name="pageSize"></param>
        /// <returns></returns>
        [HttpPost("Cluster")]
        public IActionResult Cluster([FromQuery] int pageNumber, [FromQuery] int pageSize)
        {

            int totalNumber = 0;
            int totalPage = 0;

            result.ReturnData = _enumDataService.Cluster(pageNumber, pageSize, ref totalNumber, ref totalPage);

            result.TotalNumber = totalNumber;
            result.TotalPage = totalPage;

            return Ok(result);
        }

        /// <summary>
        /// 获取操作员列表
        /// </summary>
        /// <param name="pageNumber"></param>
        /// <param name="pageSize"></param>
        /// <returns></returns>
        [HttpPost("Operator")]
        public IActionResult Operator([FromQuery] int pageNumber, [FromQuery] int pageSize)
        {
            int totalNumber = 0;
            int totalPage = 0;

            result.ReturnData = _enumDataService.Operator(pageNumber, pageSize, ref totalNumber, ref totalPage);

            result.TotalNumber = totalNumber;
            result.TotalPage = totalPage;

            return Ok(result);
        }

        /// <summary>
        /// 获取科室列表
        /// </summary>
        /// <param name="pageNumber"></param>
        /// <param name="pageSize"></param>
        /// <returns></returns>
        [HttpPost("Department")]
        public IActionResult Department([FromQuery] int pageNumber, [FromQuery] int pageSize)
        {

            int totalNumber = 0;
            int totalPage = 0;

            result.ReturnData = _enumDataService.Department(pageNumber, pageSize, ref totalNumber, ref totalPage);

            result.TotalNumber = totalNumber;
            result.TotalPage = totalPage;

            return Ok(result);
        }

        /// <summary>
        /// 获取档案项目
        /// </summary>
        /// <param name="pageNumber"></param>
        /// <param name="pageSize"></param>
        /// <returns></returns>
        [HttpPost("ArchiveItem")]
        public IActionResult ArchiveItem([FromQuery] int pageNumber, [FromQuery] int pageSize)
        {

            int totalNumber = 0;
            int totalPage = 0;

            result.ReturnData = _enumDataService.ArchiveItem(pageNumber, pageSize, ref totalNumber, ref totalPage);

            result.TotalNumber = totalNumber;
            result.TotalPage = totalPage;

            return Ok(result);
        }

        /// <summary>
        /// 获取疾病列表
        /// </summary>
        /// <param name="pageNumber"></param>
        /// <param name="pageSize"></param>
        /// <param name="keyword"></param>
        /// <returns></returns>
        [HttpPost("Disease")]
        public IActionResult Disease([FromQuery] int pageNumber, [FromQuery] int pageSize, [FromQuery] string keyword)
        {

            int totalNumber = 0;
            int totalPage = 0;

            result.ReturnData = _enumDataService.Disease(pageNumber, pageSize, keyword, ref totalNumber, ref totalPage);

            result.TotalNumber = totalNumber;
            result.TotalPage = totalPage;

            return Ok(result);
        }

        /// <summary>
        /// 单位列表
        /// </summary>
        /// <param name="pageNumber"></param>
        /// <param name="pageSize"></param>
        /// <returns></returns>
        [HttpPost("Company")]
        public IActionResult Company([FromQuery] int pageNumber, [FromQuery] int pageSize)
        {
            int totalNumber = 0;
            int totalPage = 0;
            result.ReturnData = _enumDataService.Company(pageNumber, pageSize, ref totalNumber, ref totalPage);

            result.TotalNumber = totalNumber;
            result.TotalPage = totalPage;

            return Ok(result);
        }

        /// <summary>
        /// 获取所有单位及体检次数
        /// </summary>
        /// <param name="pageNumber"></param>
        /// <param name="pageSize"></param>
        /// <returns></returns>
        [HttpPost("CompanyAndTimes")]
        public IActionResult CompanyAndTimes([FromQuery] int pageNumber, [FromQuery] int pageSize)
        {
            int totalNumber = 0;
            int totalPage = 0;

            result.ReturnData = _enumDataService.CompanyAndTimes(pageNumber, pageSize, ref totalNumber, ref totalPage);

            result.TotalNumber = totalNumber;
            result.TotalPage = totalPage;

            return Ok(result);
        }

        /// <summary>
        /// 籍贯列表
        /// </summary>
        /// <param name="pageNumber"></param>
        /// <param name="pageSize"></param>
        /// <returns></returns>
        [HttpPost("NativePlace")]
        public IActionResult NativePlace([FromQuery] int pageNumber, [FromQuery] int pageSize)
        {
            int totalNumber = 0;
            int totalPage = 0;

            result.ReturnData = _enumDataService.NativePlace(pageNumber, pageSize, ref totalNumber, ref totalPage);

            result.TotalNumber = totalNumber;
            result.TotalPage = totalPage;

            return Ok(result);
        }

        /// <summary>
        /// 证件类型
        /// </summary>
        /// <param name="pageNumber"></param>
        /// <param name="pageSize"></param>
        /// <returns></returns>
        [HttpPost("CardType")]
        public IActionResult CardType([FromQuery] int pageNumber, [FromQuery] int pageSize)
        {
            int totalNumber = 0;
            int totalPage = 0;

            result.ReturnData = _enumDataService.CardType(pageNumber, pageSize, ref totalNumber, ref totalPage);

            result.TotalNumber = totalNumber;
            result.TotalPage = totalPage;

            return Ok(result);
        }

        /// <summary>
        /// 工种
        /// </summary>
        /// <param name="pageNumber"></param>
        /// <param name="pageSize"></param>
        /// <returns></returns>
        [HttpPost("Job")]
        public IActionResult Job([FromQuery] int pageNumber, [FromQuery] int pageSize)
        {
            int totalNumber = 0;
            int totalPage = 0;

            result.ReturnData = _enumDataService.Job(pageNumber, pageSize, ref totalNumber, ref totalPage);

            result.TotalNumber = totalNumber;
            result.TotalPage = totalPage;

            return Ok(result);
        }

        /// <summary>
        /// 条码分类
        /// </summary>
        /// <param name="pageNumber"></param>
        /// <param name="pageSize"></param>
        /// <returns></returns>
        [HttpPost("BarcodeType")]
        public IActionResult BarcodeType([FromQuery] int pageNumber, [FromQuery] int pageSize)
        {
            int totalNumber = 0;
            int totalPage = 0;

            result.ReturnData = _enumDataService.BarcodeType(pageNumber, pageSize, ref totalNumber, ref totalPage);

            result.TotalNumber = totalNumber;
            result.TotalPage = totalPage;

            return Ok(result);
        }

        /// <summary>
        /// 获取常用枚举列表
        /// </summary>
        /// <returns></returns>
        [HttpPost("GetEnumList")]
        public IActionResult GetEnumList()
        {
            result.ReturnData = _enumDataService.GetEnumList();

            return Ok(result);
        }

        /// <summary>
        /// 按角色获取操作员列表 Doctor医生  Nurse护士  Audit审核医生
        /// </summary>
        /// <returns></returns>
        [HttpPost("GetOperatorListByRoleCode")]
        public IActionResult GetOperatorListByRoleCode([FromQuery] string roleCode)
        {
            result.ReturnData = _enumDataService.GetOperatorListByRoleCode(roleCode);

            return Ok(result);
        }
    }
}
