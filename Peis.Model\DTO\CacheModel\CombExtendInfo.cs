﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Peis.Model.DTO.CacheModel
{
    public class CombExtendInfo
    {
        /// <summary>
        /// 组合信息
        /// </summary>
        public CodeItemComb ItemComb { get; set; }
        /// <summary>
        /// 绑定的组合
        /// </summary>
        public string[] BindCombs { get; set; }
        /// <summary>
        ///互斥的组合
        /// </summary>
        public string[] MutexCombs { get; set; }
    }
}
