﻿using Peis.Model.Other.PeEnum.Occupation;

namespace Peis.Model.DataBaseEntity.Occupation
{
    /// <summary>
    /// 职业病体检项目字典
    /// </summary>
    [SugarTable]
    public class CodeOccupationalItem
    {
        /// <summary>
        /// 项目代码
        /// </summary>
        [SugarColumn(Length =5,IsPrimaryKey = true)]
        public string ItemCode
        {
            get; set;
        }
        /// <summary>
        /// 项目名称
        /// </summary>
        [SugarColumn(Length =50,IsNullable =false)]
        public string ItemName
        {
            get; set;
        }
        /// <summary>
        /// 分类代码
        /// </summary>
        [SugarColumn(IsNullable =false)]
        public OccupationalItemType ItemType { get; set; }

        /// <summary>
        /// 分类名
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public string ItemTypeName { get => this.ItemType.ToString(); }

        /// <summary>
        /// 计量单位代码
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public string UnitCode { set; get; }

        /// <summary>
        /// 计量单位
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public string UnitName { set; get; }
    }
}
