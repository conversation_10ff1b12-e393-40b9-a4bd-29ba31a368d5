﻿using Peis.Model.DataBaseEntity;
using Peis.Model.Other.PeEnum;
using System;

namespace Peis.Model.DTO.DataQuery
{
    /// <summary>
    /// 重大阳性记录
    /// </summary>
    public class MajorPositiveRecord
    {
        /// <summary>
        /// 体检号
        /// </summary>
        public string RegNo { get; set; }

        /// <summary>
        /// 姓名
        /// </summary>
        public string Name { get; set; }

        /// <summary>
        /// 性别（0通用1男2女）
        /// </summary>
        public Sex Sex { get; set; }

        /// <summary>
        /// 年龄
        /// </summary>
        public int Age { get; set; }

        /// <summary>
        /// 电话号码
        /// </summary>
        public string Tel { get; set; }

        /// <summary>
        /// 创建人
        /// </summary>
        public string Creator { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreateTime { get; set; }

        /// <summary>
        /// 详情
        /// </summary>
        public MajorPositivePersonDetail[] Details { get; set; }
    }
}
