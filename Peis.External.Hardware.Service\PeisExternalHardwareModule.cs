﻿using Microsoft.OpenApi.Models;

namespace Peis.External.Hardware.Service;

/// <summary>
/// 硬件对接模块
/// </summary>
[DependsOn(typeof(PeisRepositoryModule))]
[DependsOn(typeof(PeisServiceModule))]
public class PeisExternalHardwareModule : AbpModule
{
    public override void ConfigureServices(ServiceConfigurationContext context)
    {
        var execAssembly = Assembly.GetExecutingAssembly();
        #region Swagger
        if (Appsettings.GetSectionBooleanValue("AppSettings:SwaggerIsEnabled"))
        {
            context.Services.AddSwaggerGen(c =>
            {
                c.SwaggerDoc("Hardware", new OpenApiInfo { Title = "Hardware API", Version = "v1" });
                var xml = Path.Combine(AppContext.BaseDirectory, $"{execAssembly.GetName().Name}.xml");
                if (File.Exists(xml))
                    c.IncludeXmlComments(xml, true);
            });
        }
        #endregion
    }

    public override void OnApplicationInitialization(ApplicationInitializationContext context)
    {

    }
}
