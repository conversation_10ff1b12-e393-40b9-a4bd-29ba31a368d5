﻿using Peis.Model.Other.PeEnum;

namespace Peis.Model.DataBaseEntity
{
    ///<summary>
    ///系统参数
    ///</summary>
    [SugarTable("CodeSystemParameter")]
    public class CodeSystemParameter
    {
        /// <summary>
        /// 参数代码
        /// </summary>        
        [SugarColumn(IsPrimaryKey = true, IsNullable = false, Length = 50)]
        public string ParameterCode { get; set; }

        /// <summary>
        /// 参数名称
        /// </summary>        
        [SugarColumn(IsNullable = false, Length = 50)]
        public string ParameterName { get; set; }

        /// <summary>
        /// 值
        /// </summary>        
        [SugarColumn(IsNullable = false, Length = 100)]
        public string Value { get; set; }

        /// <summary>
        /// 参数类型
        /// </summary>        
        [SugarColumn(IsNullable = false)]
        public ParamType ParamType { get; set; }

        /// <summary>
        /// 院区代码
        /// </summary>
        [SugarColumn(IsPrimaryKey = true, Length = 1)]
        public string HospCode { get; set; }
    }
}