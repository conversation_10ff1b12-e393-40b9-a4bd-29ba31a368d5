﻿using MediatR;
using Peis.External.Platform.Service.Service.IService.Job;

namespace Peis.External.Platform.Service.Service.ServiceImpl.MediatR;

public class RemoveSendShortMsgToPatForApprovedJobHandle : IRequestHandler<RemoveSendShortMsgToPatForApprovedJobHandle.Data>
{
    private readonly ISendShortMsgToPatForApprovedJob _quartzTaskService;

    public RemoveSendShortMsgToPatForApprovedJobHandle(ISendShortMsgToPatForApprovedJob quartzTaskService)
    {

        _quartzTaskService = quartzTaskService;
    }

    public Task Handle(Data notification, CancellationToken cancellationToken)
    {
        _quartzTaskService.RemoveJob(notification.RegNo);
        return Task.CompletedTask;
    }

    public record Data(string RegNo) : IRequest;
}
