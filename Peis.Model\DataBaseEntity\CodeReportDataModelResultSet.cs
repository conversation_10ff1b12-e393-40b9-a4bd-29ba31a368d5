﻿using Peis.Model.DTO.ReportDataModel;

namespace Peis.Model.DataBaseEntity
{
    /// <summary>
    /// 报表数据模型结果集
    /// </summary>
    [SugarTable("CodeReportDataModelResultSet")]
    public class CodeReportDataModelResultSet
    {
        /// <summary>
        /// 数据模型代码
        /// </summary>
        [SugarColumn(IsPrimaryKey = true, Length = 4)]
        public string ModelCode { get; set; }

        /// <summary>
        /// 结果集名称
        /// </summary>
        [SugarColumn(IsPrimaryKey = true, Length = 255)]
        public string ResultSet { get; set; }

        /// <summary>
        /// 结果设置列表
        /// </summary>
        [SugarColumn(IsNullable = false, IsJson = true, ColumnDataType = StaticConfig.CodeFirst_BigString)]
        public ResultOptions[] Results { get; set; }
    }
}
