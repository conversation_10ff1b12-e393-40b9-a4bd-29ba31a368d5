﻿using Peis.Model.Other.PeEnum;
using Peis.Model.TableFilter;
using SqlSugar;

namespace Peis.Model.DataBaseEntity
{
    ///<summary>
    ///科室信息表
    ///</summary>
    [SugarTable("CodeDepartment")]
    public class CodeDepartment: IHospCodeFilter
    {
        /// <summary>
        /// 科室代码
        /// </summary>        
        [SugarColumn(IsNullable = false, IsPrimaryKey = true, Length = 6)]
        public string DeptCode { get; set; }

        /// <summary>
        /// 科室名称
        /// </summary>        
        [SugarColumn(IsNullable = false, Length = 30)]
        public string DeptName { get; set; }

        /// <summary>
        /// 科室分类
        /// </summary>        
        [SugarColumn(IsNullable = false, Length = 10)]
        public string DeptCls { get; set; }

        /// <summary>
        /// 医生工作站模板( 1:文本 2:标签 3:多选)
        /// </summary>        
        [SugarColumn(IsNullable = false)]
        public DoctorStationTemplate Template { get; set; }

        /// <summary>
        /// His代码
        /// </summary>
        [SugarColumn(IsNullable = false, Length = 10)]
        public string HisCode { get; set; }

        /// <summary>
        /// 科室描述
        /// </summary>        
        [SugarColumn(IsNullable = true, Length = 1000)]
        public string DeptInfo { get; set; }

        /// <summary>
        /// 显示顺序
        /// </summary>        
        [SugarColumn(IsNullable = false, Length = 10)]
        public string SortIndex { get; set; }

        /// <summary>
        /// 拼音码
        /// </summary>        
        [SugarColumn(IsNullable = true, Length = 10)]
        public string PinYinCode { get; set; }

        /// <summary>
        /// 五笔码
        /// </summary>        
        [SugarColumn(IsNullable = true, Length = 10)]
        public string WuBiCode { get; set; }

        /// <summary>
        /// 自定义码
        /// </summary>        
        [SugarColumn(IsNullable = true, Length = 10)]
        public string CustomCode { get; set; }

        /// <summary>
        /// 备注
        /// </summary>        
        [SugarColumn(IsNullable = true, Length = 1000)]
        public string DeptNote { get; set; }

        /// <summary>
        /// 是否显示单位
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public bool IsShowUnits { get; set; }

        /// <summary>
        /// 是否显示参考范围 
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public bool IsShowReferenceRange { get; set; }

        /// <summary>
        /// 是否显示上次的结果 
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public bool IsShowLastResult { get; set; }

        /// <summary>
        /// 院区代码
        /// </summary>
        [SugarColumn(IsNullable = false, Length = 1)]
        public string HospCode { get; set; }
    }
}