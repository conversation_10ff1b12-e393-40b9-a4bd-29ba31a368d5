﻿using Peis.Model.Other.PeEnum;
using System.Collections.Generic;

namespace Peis.Model.DTO.DataQuery
{
    /// <summary>
    /// 体检漏项数据
    /// </summary>
    public class MissingItemData
    {
        /// <summary>
        /// 表头
        /// </summary>
        public Dictionary<string, string> Header { get; set; }

        /// <summary>
        /// 表数据
        /// </summary>
        public List<Dictionary<string, string>> BodyData { get; set; }

    }

    /// <summary>
    /// 项目
    /// </summary>
    public class MissingItem
    {
        /// <summary>
        /// 体检号
        /// </summary>
        public string RegNo { get; set; }

        /// <summary>
        /// 姓名
        /// </summary>
        public string Name { get; set; }

        /// <summary>
        /// 性别
        /// </summary>
        public Sex? Sex { get; set; }

        /// <summary>
        /// 年龄
        /// </summary>
        public int? Age { get; set; }

        /// <summary>
        /// 组合代码
        /// </summary>
        public string CombCode { get; set; }

        /// <summary>
        /// 组合名称
        /// </summary>
        public string CombName { get; set; }
    }
}
