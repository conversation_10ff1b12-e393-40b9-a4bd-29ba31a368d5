﻿using Peis.Model.DataBaseEntity;
using Peis.Model.Other.PeEnum;
using Peis.Utility.Helper;
using SqlSugar;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Text.Json;

namespace Peis.ConsoleTest.Test_Yx.Other
{
    public class GetEnumsTest
    {
        private static SqlSugarClient _db = new SqlSugarClient(new ConnectionConfig()
        {
            ConnectionString = Appsettings.GetSectionValue("ConnectionString:Entities"),
            DbType = DbType.SqlServer,
            IsAutoCloseConnection = true,//开启自动释放模式
            InitKeyType = InitKeyType.Attribute,//从特性读取主键和自增列信息
        });

        private class EnumListDict
        {
            public EnumListDict()
            {
                Describe = new Dictionary<string, string>();
                Data = new Dictionary<string, Dictionary<string, string>>();
            }

            public IDictionary<string, string> Describe { get; set; }
            public IDictionary<string, Dictionary<string, string>> Data { get; set; }
        }

        private class DictKeyValue
        {
            public string Code { get; set; }
            public string Name { get; set; }
        }

        private class DataBaseEnumList
        {
            public EnumListDict enumListDict = new();

            /// <summary>
            /// 枚举在这里往下添加
            /// </summary>
            private void Init()
            {
                AddEnum<CodeItemCls>("项目分类", x => new() { Code = x.ClsCode, Name = x.ClsName });
                AddEnum<CodeDepartment>("科室信息", x => new() { Code = x.DeptCode, Name = x.DeptName });
            }

            private void AddEnum<TTable>(string enumName, Expression<Func<TTable, DictKeyValue>> selectExp)
            {
                var enumList = _db.Queryable<TTable>().Select(selectExp).ToArray()
                    .ToDictionary(x => x.Code, x => x.Name);

                enumListDict.Describe[typeof(TTable).Name] = enumName;
                enumListDict.Data[typeof(TTable).Name] = enumList;
            }

            public static EnumListDict GetEnumsList()
            {
                DataBaseEnumList enumList = new();
                enumList.Init();

                return enumList.enumListDict;
            }
        }

        private class SystemEnumList
        {
            private static EnumListDict enumListDict = new();

            static SystemEnumList() => Init();

            /// <summary>
            /// 枚举在这里往下添加
            /// </summary>
            private static void Init()
            {
                AddEnum<Sex>("性别");
                AddEnum<MarryStatus>("婚姻状况");
                AddEnum<PeCls>("体检分类");
                AddEnum<DiseaseGrade>("疾病等级");
                AddEnum<FeeClsGroupType>("收费项目分类");
                AddEnum<PayStatus>("支付状态");
            }

            private static void AddEnum<T>(string enumName) where T : struct, Enum
            {
                var enumList = Enum.GetValues<T>().ToDictionary(x => Convert.ToInt32(x).ToString(), x => Enum.GetName(x));

                enumListDict.Describe[typeof(T).Name] = enumName;
                enumListDict.Data[typeof(T).Name] = enumList;
            }

            public static EnumListDict GetEnumsList()
            {
                return enumListDict;
            }
        }

        public object GetEnumList()
        {
            var sysEnums = SystemEnumList.GetEnumsList();
            var dbEnums = DataBaseEnumList.GetEnumsList();

            return new
            {
                describe = new Dictionary<string, string>(sysEnums.Describe.Union(dbEnums.Describe)),
                data = new Dictionary<string, Dictionary<string, string>>(sysEnums.Data.Union(dbEnums.Data))
            };
        }

        public static void Invoke()
        {
            var obj = new GetEnumsTest().GetEnumList();
            var json = JsonSerializer.Serialize(obj, new JsonSerializerOptions() { WriteIndented = true });

            var obj2 = new GetEnumsTest().GetEnumList();
            var json2 = JsonSerializer.Serialize(obj, new JsonSerializerOptions() { WriteIndented = true });

            Console.WriteLine(json);
        }
    }
}
