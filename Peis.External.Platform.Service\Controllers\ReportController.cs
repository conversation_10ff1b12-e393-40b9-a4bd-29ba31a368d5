﻿using Microsoft.AspNetCore.Mvc;
using Peis.Model.DataBaseEntity.External;
using Peis.Model.DTO.External.HisApply;
using Peis.Service.IService;
using Peis.Service.IService.ExternalSystem;

namespace Peis.External.Platform.Service.Controllers
{
    [Route("api/<PERSON><PERSON><PERSON>/[controller]")]
    [ApiController]
    public class ReportController : BaseApiController
    {

        private readonly IReportFileService _fileReportService;
        private readonly IExternalSystemApplyService _applyService;
        private readonly IMapper _mapper;

        public ReportController(IReportFileService fileReportService, IExternalSystemApplyService applyService, IMapper mapper)
        {
            _fileReportService = fileReportService;
            _applyService = applyService;
            _mapper = mapper;
        }

        /// <summary>
        /// 生成总检报告并同步到平台
        /// </summary>
        /// <param name="regNo">体检号</param>
        /// <param name="isCover">是否覆盖已生成的</param>
        /// <returns></returns>
        [HttpPost("SavePeReportFileByRegNo")]
        public IActionResult SavePeReportFileByRegNo([FromQuery] string regNo, [FromQuery] bool isCover = false)
        {
            result.ReturnData = _fileReportService.SavePeReportFilesByRegNo(regNo, out var msg, isCover);
            result.ReturnMsg = msg;
            result.Success = !result.ReturnData.IsNullOrEmpty();

            return Ok(result);
        }

        /// <summary>
        /// 删除总检报告并同步到平台
        /// </summary>
        /// <param name="regNo">体检号</param>
        /// <returns></returns>
        [HttpPost("RemovePeReportFilesByRegNo")]
        public IActionResult RemovePeReportFileByRegNo([FromQuery] string regNo)
        {
            result.Success = _fileReportService.RemovePeReportFilesByRegNo(regNo, out var msg);
            result.ReturnMsg = msg;

            return Ok(result);
        }

        /// <summary>
        /// 同步报告到平台
        /// </summary>
        /// <param name="regNo">体检号，为空时，全部同步</param>
        /// <returns></returns>
        [HttpPost("SyncPeReport")]
        public IActionResult SyncPeReport([FromQuery] string regNo)
        {
            result.Success = _fileReportService.SyncPeReport(regNo, out var msg);
            result.ReturnMsg = msg;

            return Ok(result);
        }

        /// <summary>
        /// 获取体检申请单组合列表
        /// </summary>
        /// <param name="query">查询条件</param>
        /// <returns></returns>
        [HttpPost("GetExtHisApplyCombs")]
        [ProducesResponseType(typeof(List<ExtHisApplyCombDto>), 200)]
        public IActionResult GetExtHisApplyCombs([FromBody] ExtHisApplyCombQuery query)
        {
            int totalNumber = 0;
            int totalPage = 0;
            var data = _applyService.GetExtHisApplyCombs(query, ref totalNumber, ref totalPage);
            result.ReturnData = _mapper.Map<List<ExtHisApplyCombDto>>(data);
            result.TotalNumber = totalNumber;
            result.TotalPage = totalPage;

            return Ok(result);
        }

        /// <summary>
        /// 修改体检申请单组合
        /// </summary>
        /// <param name="data">体检申请单组合数据</param>
        /// <returns></returns>
        [HttpPost("BatchModifyExtHisApplyComb")]
        [ProducesResponseType(typeof(bool), 200)]
        public IActionResult BatchModifyExtHisApplyComb([FromBody] List<ExtHisApplyCombDto> data)
        {
            result.Success = _applyService.BatchModifyExtHisApplyComb(_mapper.Map<List<ExtHisApplyComb>>(data));
            result.ReturnMsg = result.Success ? ResxCommon.Success : ResxCommon.Fail;

            return Ok(result);
        }
    }
}
