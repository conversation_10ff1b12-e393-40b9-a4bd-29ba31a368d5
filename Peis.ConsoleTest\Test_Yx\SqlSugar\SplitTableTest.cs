﻿using SqlSugar;
using System;
using System.Collections.Generic;
using System.Linq;

namespace Peis.ConsoleTest.Test_Yx.SqlSugar
{
    public class SplitTableTest
    {
        public static void Invoke()
        {
            DropTable();

            var insetData = new List<SplitTestTable>()
            {
                new(){ Id = 1, Name = "A", CreateTime = new(2010, 2, 10, 15, 30, 0) },
                new(){ Id = 2, Name = "B", CreateTime = new(2010, 5, 30, 15, 30, 0) },

                new(){ Id = 3, Name = "C", CreateTime = new(2020, 6, 27, 15, 30, 0) },
                new(){ Id = 4, Name = "D", CreateTime = new(2020, 7, 05, 15, 30, 0) },

                new(){ Id = 5, Name = "E", CreateTime = new(2021, 3, 11, 15, 30, 0) },

                new(){ Id = 6, Name = "F", CreateTime = new(2022, 8, 18, 15, 30, 0) },
            };

            SqlClientTest.DB().Insertable(insetData).SplitTable().ExecuteCommand();

            var deleteIds = new long[] { 3, 5 };

            var deleteData = insetData.Where(x => deleteIds.Contains(x.Id)).ToArray();

            DateTime begin = deleteData.Min(x => x.CreateTime);
            DateTime end = deleteData.Max(x => x.CreateTime);

            var tables = SqlClientTest.DB()
                .SplitHelper<SplitTestTable>()
                .GetTables()
                .Where(tab => tab.Date.Year >= begin.Year && tab.Date.Year <= end.Year)
                .Select(tab => tab.TableName)
                .ToArray();

            var tableName = SqlClientTest.DB()
                .SplitHelper<SplitTestTable>()
                .GetTableName(DateTime.Now);

            IEnumerable<SplitTableInfo> BeTween(List<SplitTableInfo> splitTables, int startYear, int endYear)
            {
                return splitTables.Where(tab => tab.Date.Year >= startYear && tab.Date.Year <= endYear);
            }

            SqlClientTest.DB()
                .Deleteable<SplitTestTable>()
                .Where(x => SqlFunc.ContainsArray(deleteIds, x.Id))
                .SplitTable(tas => BeTween(tas, begin.Year, end.Year))
                .ExecuteCommand();

            DropTable();
        }

        static void DropTable()
        {
            var tableNames = SqlClientTest.DB()
                .SplitHelper<SplitTestTable>()
                .GetTables()
                .Select(x => "Drop Table " + x.TableName)
                .ToArray();

            var sql = string.Join("\r\n", tableNames);

            if (!string.IsNullOrEmpty(sql))
                SqlClientTest.DB().Ado.ExecuteCommand(sql);
        }
    }

    [SplitTable(SplitType.Year)]//按年分表 （自带分表支持 年、季、月、周、日）
    [SugarTable("SplitTestTable_{year}{month}{day}")]//3个变量必须要有，这么设计为了兼容开始按年，后面改成按月、按日
    public class SplitTestTable
    {
        [SugarColumn(IsPrimaryKey = true)]
        public long Id { get; set; }

        public string Name { get; set; }

        [SplitField] //分表字段 在插入的时候会根据这个字段插入哪个表，在更新删除的时候用这个字段找出相关表
        public DateTime CreateTime { get; set; }
    }
}
