﻿using Peis.Model.DataBaseEntity;
using Peis.Model.DataBaseEntity.External;
using Peis.Model.DataBaseViewModel.External;
using SqlSugar;

namespace Peis.External.Platform.Service.Repository.IRepository
{
    /// <summary>
    /// 深汕基础数据仓储接口
    /// </summary>
    public interface IShenShanMainDataRepository
    {
        /// <summary>
        /// 项目视图查询
        /// </summary>
        /// <returns></returns>
        ISugarQueryable<v_lis_item> QueryViewCodeLisItems();

        /// <summary>
        /// 关联项目查询
        /// </summary>
        /// <returns>ISugarQueryable</returns>
        ISugarQueryable<MapCodeLisItem> QueryMapCodeLisItems();

        /// <summary>
        /// 收费项目基础信息查询
        /// </summary>
        /// <returns>ISugarQueryable</returns>
        ISugarQueryable<CodeHisChargeItem> QueryCodeHisChargeItems();

        /// <summary>
        /// 查询体检组合收费项目信息
        /// </summary>
        /// <param name="combCode">组合code</param>
        /// <returns>ISugarQueryable</returns>
        ISugarQueryable<MapCodeItemCombHisChargeItem, CodeHisChargeItem, CodeHisDrugItem> QueryCodeMapChargeItems(string combCode);

        /// <summary>
        /// 查询体检组合收费项目信息
        /// </summary>
        /// <param name="combCodes">组合code</param>
        /// <returns>ISugarQueryable</returns>
        ISugarQueryable<CodeItemComb, MapCodeItemCombHisChargeItem> QueryCodeCombMapChargeItems(List<string> combCodes = null);

        /// <summary>
        /// 查询医嘱项目基础信息
        /// </summary>
        /// <returns>ISugarQueryable</returns>
        ISugarQueryable<CodeHisOrderItem> QueryCodeHisOrderItems();

        /// <summary>
        /// 查询医嘱收费明细
        /// </summary>
        /// <param name="mainPId"></param>
        /// <returns>ISugarQueryable</returns>
        ISugarQueryable<CodeHisOrderItemCharge> QueryCodeHisOrderItemCharges(string mainPId = null);

        /// <summary>
        /// 查询体检组合与医嘱信息
        /// </summary>
        /// <param name="combCodes">组合code</param>
        /// <returns>ISugarQueryable</returns>
        ISugarQueryable<MapCodeItemCombHisOrderItem, CodeHisOrderItem, CodeHisOrderItemCharge> QueryCodeItemCombMapOrders(List<string> combCodes);

        /// <summary>
        /// 查询体检组合
        /// </summary>
        /// <param name="combCodes">组合code</param>
        /// <returns>ISugarQueryable</returns>
        ISugarQueryable<CodeItemComb> QueryCodeItemCombs(List<string> combCodes = null);

        /// <summary>
        /// 查询体检分类
        /// </summary>
        /// <param name="clsCodes">分类code</param>
        /// <returns>ISugarQueryable</returns>
        ISugarQueryable<CodeItemCls> QueryCodeItemClss(List<string> clsCodes);

        #region 药品
        /// <summary>
        /// 查询药品基础信息
        /// </summary>
        /// <returns>ISugarQueryable</returns>
        ISugarQueryable<CodeHisDrugItem> QueryCodeHisDrugItems();
        #endregion

        #region TRUNCATE
        /// <summary>
        /// 清空His收费项目表操作，慎用！！！
        /// </summary>
        void TruncateCodeHisChargeItemTable();

        /// <summary>
        /// 清空His医嘱项目表操作，慎用！！！
        /// </summary>
        void TruncateCodeHisOrderItemTable();

        /// <summary>
        /// 清空His医嘱项目明细表操作，慎用！！！
        /// </summary>
        void TruncateCodeHisOrderItemChargeTable();

        /// <summary>
        /// 清空His药品信息表操作，慎用！！！
        /// </summary>
        void TruncateCodeHisDrugItemTable();
        #endregion
    }
}
