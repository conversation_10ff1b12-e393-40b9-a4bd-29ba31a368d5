﻿using Peis.Model.DataBaseEntity;
using Peis.Utility.Helper;
using SqlSugar;
using System;
using System.Collections.Generic;
using System.Linq;

namespace Peis.ConsoleTest.Test_Cq.SqlSugar
{
    internal class BatchUpdate
    {
        public static void Invoke()
        {
            Update();
        }

        private static SqlSugarClient DB()
        {
            var _db = new SqlSugarClient(new ConnectionConfig()
            {
                ConnectionString = Appsettings.GetSectionValue("ConnectionString:Entities"),
                DbType = DbType.SqlServer,
                IsAutoCloseConnection = true,//开启自动释放模式
                InitKeyType = InitKeyType.Attribute,//从特性读取主键和自增列信息
            });

            //Print sql
            _db.Aop.OnLogExecuting = (sql, pars) =>
            {
                Console.WriteLine(sql + "\r\n" + _db.Utilities.SerializeObject(pars.ToDictionary(it => it.ParameterName, it => it.Value)));
                Console.WriteLine();
            };

            return _db;
        }

        private static bool Update()
        {
            string[] str = new string[] { "01", "02" };
            return DB().Updateable<CodeNation>().SetColumns(x => new CodeNation() { PinYinCode = "HZ", WuBiCode = "WUBI" }
              ).Where(set => str.Contains(set.NatCode)).ExecuteCommand() > 1;
        }
    }
}
