﻿using Peis.Model.Other.PeEnum;

namespace Peis.Model.DTO.CompanyStatistics
{
    public class CompanyPatientClustersQuery : StatisticalQueryBase
    {
        /// <summary>
        /// 年龄上限
        /// </summary>        
        public int UpperAgeLimit { get; set; }

        /// <summary>
        /// 年龄下限
        /// </summary>        
        public int LowerAgeLimit { get; set; }

        /// <summary>
        /// 部门代码
        /// </summary>
        public string CompanyDeptCode { get; set; }

        /// <summary>
        /// 套餐代码
        /// </summary>
        public string ClusterCode { get; set; }

        /// <summary>
        /// 体检状态
        /// </summary>
        public PeStatus PeStatus { get; set; }
    }
}
