﻿using Peis.Model.Other.PeEnum;
using System;

namespace Peis.Model.DTO.CompanyStatistics
{
    /// <summary>
    /// 单位体检人员套餐
    /// </summary>
    public class CompanyPatientCluster
    {
        /// <summary>
        /// 体检号
        /// </summary>
        public string RegNo { get; set; }
        /// <summary>
        /// 姓名
        /// </summary>
        public string Name { get; set; }
        /// <summary>
        /// 性别
        /// </summary>
        public Sex? Sex { get; set; }
        /// <summary>
        /// 年龄
        /// </summary>
        public int? Age { get; set; }
        /// <summary>
        /// 年龄单位
        /// </summary>
        public AgeUnit? AgeUnit { get; set; }
        /// <summary>
        /// 单位代码
        /// </summary>
        public string CompanyCode { get; set; }
        /// <summary>
        /// 部门
        /// </summary>
        public string CompanyDeptName { get; set; }
        /// <summary>
        /// 体检时间（报到时间）
        /// </summary>
        public DateTime? ActiveTime { get; set; }
        /// <summary>
        /// 电话
        /// </summary>
        public string Tel { get; set; }
        /// <summary>
        /// 证件类型
        /// </summary>
        public string CardType { get; set; }
        /// <summary>
        /// 证件号码
        /// </summary>
        public string CardNo { get; set; }
        /// <summary>
        /// 套餐名称
        /// </summary>
        public string ClusterName { get; set; }
        /// <summary>
        /// 体检状态
        /// </summary>
        public PeStatus PeStatus { get; set; }
    }
}
