﻿using Microsoft.Extensions.Logging;
using Peis.Model.Constant;
using Peis.Model.DTO.External.Report;
using Peis.Model.Other.PeEnum;
using Peis.Repository.IRepository;
using Peis.Service.IService;
using Peis.Service.IService.ExternalSystem;
using Peis.Utility.PeUser;
using System.Net.Http;
using System.Net.Mime;
using System.Text;

namespace Peis.Service.Service;

/// <summary>
/// 报告文件接口服务
/// </summary>
public class ReportFileService : IReportFileService
{
    private readonly string FOLDER_BASE = Path.Combine(AppContext.BaseDirectory, ResxCommon.FolderWebHost); // 根目录

    readonly ISplitTableRepository<PeReportFile> _peReportFileRepository;
    readonly IReportFileRepository _reportFileRepository;
    readonly IRegisterRepository _registerRepository;
    readonly IHttpClientHelper _httpClientHelper;
    readonly ILogger<ReportFileService> _logger;
    readonly ISystemParameterService _systemParameterService;
    readonly IReportConclusionRepository _reportConclusionRepository;
    readonly IExternalSystemReportFileService _externalSystemReportFileService;
    readonly IHttpContextUser _httpContextUser;

    public ReportFileService(
        ISplitTableRepository<PeReportFile> peReportFileRepository,
        IReportFileRepository reportRepository,
        IRegisterRepository registerRepository,
        IHttpClientHelper httpClientHelper,
        ILogger<ReportFileService> logger,
        ISystemParameterService systemParameterService,
        IReportConclusionRepository reportConclusionRepository,
        IExternalSystemReportFileService externalSystemReportFileService,
        IHttpContextUser httpContextUser)
    {
        _peReportFileRepository = peReportFileRepository;
        _reportFileRepository = reportRepository;
        _registerRepository = registerRepository;
        _httpClientHelper = httpClientHelper;
        _logger = logger;
        _systemParameterService = systemParameterService;
        _reportConclusionRepository = reportConclusionRepository;
        _externalSystemReportFileService = externalSystemReportFileService;
        _httpContextUser = httpContextUser;
    }

    /// <summary>
    /// 根据体检号保存默认体检报告文件，推送平台
    /// </summary>
    /// <param name="regNo">体检号</param>
    /// <param name="msg">消息</param>
    /// <param name="isCover">是否覆盖重新生成，默认否</param>
    /// <returns>PeReportFiles</returns>
    public List<PeReportFile> SavePeReportFilesByRegNo(string regNo, out string msg, bool isCover = false)
    {
        var msgs = new List<string>();
        var res = new List<PeReportFile>();
        var defReport = SavePeReportFileWxByRegNo(regNo, out msg, isCover);
        if (!defReport.IsNullOrEmpty())
            res.Add(defReport);
        else
            msgs.Add($"{regNo}，有公章报告生成失败：{msg}");

        var internalReport = SavePeReportFileByRegNo(regNo, out msg, isCover);
        if (!internalReport.IsNullOrEmpty())
            res.Add(internalReport);
        else
            msgs.Add($"{regNo}，无公章报告生成失败：{msg}");

        msg = msgs.IsNullOrEmpty() ? ResxCommon.Success : string.Join("\r\n", msgs);
        return res;
    }

    /// <summary>
    /// 获取体检报告文件信息
    /// </summary>
    /// <param name="regNo">体检号</param>
    /// <param name="msg">消息</param>
    /// <returns>PeReportFile-list</returns>
    public List<PeReportFile> GetPeReportFilesByRegNo(string regNo, out string msg)
    {
        regNo.NotNullAndEmpty(nameof(regNo));
        msg = ResxCommon.Success;
        return _reportFileRepository.QueryPeReportFiles(regNo).ToList();
    }

    /// <summary>
    /// 获取体检报告文件信息
    /// </summary>
    /// <param name="regNo">体检号</param>
    /// <param name="reportType">报告类型</param>
    /// <param name="msg">消息</param>
    /// <param name="isWithStamp">是否带公章，默认带</param>
    /// <returns>PeReportFile</returns>
    public PeReportFile GetPeReportFileByRegNo(string regNo, string reportType, out string msg, bool isWithStamp = true)
    {
        regNo.NotNullAndEmpty(nameof(regNo));
        reportType.NotNullAndEmpty(nameof(reportType));

        msg = ResxCommon.Success;
        return _reportFileRepository
            .QueryPeReportFiles(regNo)
            .First(x => x.ReportType.Equals(reportType) && x.IsWithStamp == isWithStamp);
    }

    /// <summary>
    /// 获取默认微信体检报告文件信息
    /// </summary>
    /// <param name="regNo">体检号</param>
    /// <param name="msg">消息</param>
    /// <returns>PeReportFile</returns>
    public PeReportFile GetDefaultPeReportFileWxByRegNo(string regNo, out string msg)
    {
        var defaultExamReportWx = _systemParameterService.DefaultExamReportWx;
        return GetPeReportFileByRegNo(regNo, defaultExamReportWx, out msg);
    }

    /// <summary>
    /// 删除体检报告文件
    /// </summary>
    /// <param name="regNo">体检号</param>
    /// <param name="msg">消息</param>
    /// <returns>bool</returns>
    public bool RemovePeReportFilesByRegNo(string regNo, out string msg)
    {
        regNo.NotNullAndEmpty(nameof(regNo));
        var reports = _reportFileRepository.QueryPeReportFiles(regNo).ToList();
        msg = ResxCommon.NotExistRecord;
        if (reports.IsNullOrEmpty()) return false;

        FileInfo oldFile = null;
        foreach (var report in reports)
        {
            var oldFullPath = Path.Combine(FOLDER_BASE, report.FilePath);
            oldFile = new FileInfo(oldFullPath);
            if (oldFile.Exists)
                oldFile.Delete();
        }

        var flag = _peReportFileRepository.SplitTableDelete(reports);
        if (flag)
        {
            var reg = _registerRepository.ReadRegister(regNo).First();
            if (_httpContextUser.HospCode.IsNullOrEmpty())
                _httpContextUser.SetHospCode(reg.HospCode);

            var syncReport = new PeReport(reg, reports.First());
            syncReport.SetReportStatus(EnumReportStatus.Revocation);
            _externalSystemReportFileService.SyncPeReport(reg.RegNo, out _);
        }

        msg = ResxCommon.Success;
        return true;
    }

    /// <summary>
    /// 批量生成体检报告
    /// </summary>
    /// <param name="startTime">审核时间-开始</param>
    /// <param name="endTime">审核时间-结束</param>
    /// <param name="msg">消息</param>
    /// <param name="isCover">是否重新生成</param>
    /// <returns>bool</returns>
    public bool BatchSavePeReportFiles(DateTime startTime, DateTime endTime, out string msg, bool isCover = false)
    {
        var defaultExamReportWx = _systemParameterService.DefaultExamReportWx;
        var regs = _reportConclusionRepository
            .ReadReportConclusion(startTime, endTime)
          .Where((reg, report) => reg.PeStatus == PeStatus.已审核)
          .Where((reg, report) => SqlFunc.Between(report.AuditTime, startTime, endTime))
          .Select((reg, report) => new { reg.RegNo, reg.ReportType })
          .ToArray();

        int toGenerCount = isCover ? regs.Length : 0;
        int successCount = 0;
        var msgWarn = new List<string>();
        foreach (var item in regs)
        {
            try
            {
                if (!isCover)
                {
                    var reportFiles = _reportFileRepository.QueryPeReportFiles(item.RegNo).ToArray();
                    if (reportFiles.All(x => new[] { item.ReportType, defaultExamReportWx }.Contains(x.ReportType)))
                    {
                        ++successCount;
                        continue;
                    }                      
                    else
                        ++toGenerCount;
                }

                SavePeReportFilesByRegNo(item.RegNo, out string _msg, isCover);
                if (_msg.Equals(ResxCommon.Success))
                    ++successCount;
                else
                    msgWarn.Add(_msg);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"批量生成体检报告失败({item.RegNo})");
            }
        }

        msg = $"共需要生成体检报告{regs.Length}人，成功{successCount}人，存在失败{msgWarn.Count}人";
        if (!msgWarn.IsNullOrEmpty())
            msg += $"\r\n存在失败内容{string.Join("\r\n", msgWarn)}";

        return true;
    }

    /// <summary>
    /// 同步总检报告至外部系统
    /// </summary>
    /// <param name="regNo">体检号，为空时，全部同步</param>
    /// <param name="msg">消息</param>
    /// <returns>bool</returns>
    public bool SyncPeReport(string regNo, out string msg)
    {
        return _externalSystemReportFileService.SyncPeReport(regNo, out msg);
    }

    #region 本地私有方法

    /// <summary>
    /// 下载体检报告文件至本地，文件会覆盖
    /// </summary>
    /// <param name="queryString"></param>
    /// <param name="reportCode"></param>
    /// <param name="fullPath"></param>
    bool DownloadPeReportPDF(string queryString, string reportCode, string fullPath)
    {
        var paramObj = new
        {
            queryString,      
            reportCode
        };
        var httpContent = new StringContent(paramObj.ToJson(), Encoding.UTF8, MediaTypeNames.Application.Json);
        return _httpClientHelper.DownLocalAsync(ConstantUrl.ExportToPdfUrl, httpContent, fullPath).Result;
    }

    /// <summary>
    /// 根据体检号保存默认微信体检报告文件，推送外部系统
    /// </summary>
    /// <param name="regNo">体检号</param>
    /// <param name="msg">消息</param>
    /// <param name="isCover">是否覆盖重新生成，默认否</param>
    /// <returns>PeReportFile</returns>
    PeReportFile SavePeReportFileWxByRegNo(string regNo, out string msg, bool isCover = false)
    {
        regNo.NotNullAndEmpty(nameof(regNo));
        var reg = _registerRepository.ReadRegister(regNo).First();
        if (reg.IsNullOrEmpty())
        {
            msg = ResxCommon.NotExitPatient;
            return default;
        }
        if(_httpContextUser.HospCode.IsNullOrEmpty())
            _httpContextUser.SetHospCode(reg.HospCode);

        var defaultExamReportWx = _systemParameterService.DefaultExamReportWx;
        if (reg.PeStatus != PeStatus.已审核)
        {
            msg = "保存失败，未完成体检！";
            return default;
        }

        var report = _reportFileRepository.QueryPeReportFiles(reg.RegNo)
            .Where(a => a.ReportType.Equals(defaultExamReportWx))
            .Where(a => a.IsWithStamp)
            .First();
        FileInfo oldFile = null;
        if (!report.IsNullOrEmpty() && !report.FilePath.IsNullOrEmpty())
        {
            // 不覆盖&存在，返回
            var oldFullPath = Path.Combine(FOLDER_BASE, report.FilePath);
            oldFile = new FileInfo(oldFullPath);
            if (!isCover && oldFile.Exists)
            {
                msg = ResxCommon.Success;
                return report;
            }
        }
        // 下载文件
        var fileName = $"{reg.RegNo.Trim()}_stamp.pdf";
        var filePath = Path.Combine(ResxExternal.FolderPeisReport, DateTime.Now.ToString("yyyy.MM.dd"), fileName);
        var fullPath = Path.Combine(FOLDER_BASE, filePath);
        // IsShowStamp：下载带有公章的报告
        DownloadPeReportPDF($"regNo={reg.RegNo}&IsShowStamp=true", defaultExamReportWx, fullPath);
        // 保存文件信息
        var file = new FileInfo(fullPath);
        if (!file.Exists)
        {
            msg = "保存失败，未能下载到文件，请稍后重试！";
            return default;
        }

        bool flag;
        if (report.IsNullOrEmpty())
        {
            // 新增
            report = new PeReportFile(SnowFlakeSingle.Instance.NextId(), reg, filePath, file);
            report.SetReportType(defaultExamReportWx);
            report.SetIsWithStamp(true);
            flag = _peReportFileRepository.SplitTableInsert(report) > 0;
        }
        else
        {
            // 更新
            report.Modify(filePath, file);
            flag = _peReportFileRepository.SplitTableUpdate(report);
            if (flag &&
                !oldFile.IsNullOrEmpty() &&
                oldFile.Exists &&
                oldFile.FullName != file.FullName)
                oldFile.Delete(); // 路径不同，删除旧文件
        }

        if (flag)
            _externalSystemReportFileService.SyncPeReport(reg.RegNo, out _);

        msg = ResxCommon.Success;
        return report;
    }

    /// <summary>
    /// 根据体检号保存体检报告文件，不推送外部系统
    /// </summary>
    /// <param name="regNo">体检号</param>
    /// <param name="msg">消息</param>
    /// <param name="isCover">是否覆盖重新生成，默认否</param>
    /// <returns>PeReportFile</returns>
    PeReportFile SavePeReportFileByRegNo(string regNo, out string msg, bool isCover = false)
    {
        regNo.NotNullAndEmpty(nameof(regNo));

        var reg = _registerRepository.ReadRegister(regNo).First();
        if (reg.IsNullOrEmpty())
        {
            msg = ResxCommon.NotExitPatient;
            return null;
        }
        if (reg.PeStatus != PeStatus.已审核)
        {
            msg = "保存失败，未完成体检！";
            return null;
        }

        var report = _reportFileRepository.QueryPeReportFiles(reg.RegNo)
            .Where(a => a.ReportType.Equals(reg.ReportType))
            .Where(a => !a.IsWithStamp)
            .First();
        FileInfo oldFile = null;
        if (!report.IsNullOrEmpty() && !report.FilePath.IsNullOrEmpty())
        {
            // 不覆盖&存在，返回
            var oldFullPath = Path.Combine(FOLDER_BASE, report.FilePath);
            oldFile = new FileInfo(oldFullPath);
            if (!isCover && oldFile.Exists)
            {
                msg = ResxCommon.Success;
                return report;
            }
        }

        // 下载文件
        var fileName = $"{reg.RegNo.Trim()}.pdf";
        var filePath = Path.Combine(ResxExternal.FolderPeisReport, DateTime.Now.ToString("yyyy.MM.dd"), fileName);
        var fullPath = Path.Combine(FOLDER_BASE, filePath);
        DownloadPeReportPDF($"regNo={reg.RegNo}", reg.ReportType, fullPath);
        // 保存文件信息
        var file = new FileInfo(fullPath);
        if (!file.Exists)
        {
            msg = "保存失败，未能下载到文件，请稍后重试！";
            return null;
        }

        bool flag;
        if (report.IsNullOrEmpty())
        {
            // 新增
            report = new PeReportFile(SnowFlakeSingle.Instance.NextId(), reg, filePath, file);
            flag = _peReportFileRepository.SplitTableInsert(report) > 0;
        }
        else
        {
            // 更新
            report.Modify(filePath, file);
            flag = _peReportFileRepository.SplitTableUpdate(report);
            if (flag &&
                !oldFile.IsNullOrEmpty() &&
                oldFile.Exists &&
                oldFile.FullName != file.FullName)
                oldFile.Delete(); // 路径不同，删除旧文件
        }

        msg = ResxCommon.Success;
        return report;
    }
    #endregion
}
