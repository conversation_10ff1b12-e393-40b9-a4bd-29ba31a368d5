﻿using System;

namespace Peis.Model.DTO.DataQuery
{
    /// <summary>
    /// 体检标本数据
    /// </summary>
    public class PeSampleData
    {
        /// <summary>
        /// 条码号
        /// </summary>
        public string SampleNo { get; set; }

        /// <summary>
        /// 系统生成条码模式：状态(0:全部 1:未打印 2:已打印 3:已运送)
        /// 预制条码模式    ：状态(0:全部 1:未配管 2:已配管 3:已运送)
        /// </summary>
        public int Status { get; set; }

        /// <summary>
        /// 条码类型
        /// </summary>
        public string BarcodeName { get; set; }

        /// <summary>
        /// 组合名称
        /// </summary>
        public string CombName { get; set; }

        /// <summary>
        /// 采集时间
        /// </summary>
        public DateTime GatherTime { get; set; }

        /// <summary>
        /// 采集人
        /// </summary>
        public string GatherOperator { get; set; }

        /// <summary>
        /// 包运送时间
        /// </summary>
        public DateTime? TransportTime { get; set; }
    }
}
