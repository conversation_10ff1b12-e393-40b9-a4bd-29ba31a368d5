﻿using SqlSugar;

namespace Peis.Model.DataBaseEntity
{
    ///<summary>
    ///Mac配置
    ///</summary>
    [SugarTable("CodeMacConfig")]
    public class CodeMacConfig
    {
        /// <summary>
        /// 物理地址
        /// </summary>        
        [SugarColumn(IsNullable = false, IsPrimaryKey = true, Length = 17)]
        public string MacCode { get; set; }

        /// <summary>
        /// 版本号
        /// </summary>        
        [SugarColumn(IsNullable = false)]
        public long VersionNo { get; set; }

        /// <summary>
        /// 配置
        /// </summary>        
        [SugarColumn(IsNullable = false, ColumnDataType = StaticConfig.CodeFirst_BigString)]
        public string Config { get; set; }
    }
}