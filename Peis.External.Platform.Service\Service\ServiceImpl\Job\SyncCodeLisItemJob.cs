﻿using Peis.Quartz.UI.BaseService;
using Peis.Service.IService.ExternalSystem;

namespace Peis.External.Platform.Service.Service.ServiceImpl.Job;

/// <summary>
/// 作业：同步Lis体检项目信息
/// </summary>
public class SyncCodeLisItemJob : IJobService
{
    readonly IExternalSystemDataSyncService _extSystemDataSyncService;

    public SyncCodeLisItemJob(IExternalSystemDataSyncService extSystemDataService)
    {
        _extSystemDataSyncService = extSystemDataService;
    }

    public string ExecuteService(string parameter)
    {
        int.TryParse(parameter, out var pageSize);
        pageSize = pageSize <= 0 ? 200 : pageSize;
        _extSystemDataSyncService.SyncCodeLisItems(pageSize, out var msg);
        return msg;
    }
}
