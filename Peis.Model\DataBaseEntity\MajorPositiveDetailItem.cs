﻿namespace Peis.Model.DataBaseEntity;

///<summary>
/// 重大阳性项目内容表
///</summary>
[SugarTable(TableName = nameof(MajorPositiveDetailItem), TableDescription = "重大阳性项目内容表")]
[SugarIndex($"idx_{nameof(MajorPositiveDetailItem)}", nameof(ItemCode), OrderByType.Asc, nameof(DetailId), OrderByType.Asc, nameof(ItemName), OrderByType.Asc)]
public class MajorPositiveDetailItem
{
    /// <summary>
    /// 项目代码
    /// </summary>
    [SugarColumn(IsPrimaryKey = true, Length = 20)]
    public string ItemCode { get; set; }

    /// <summary>
    /// 主记录Id
    /// </summary>
    [SugarColumn(IsPrimaryKey = true)]
    public long DetailId { get; set; }

    /// <summary>
    /// 项目名称
    /// </summary>
    [SugarColumn(Length = 512)]
    public string ItemName { get; set; }

    /// <summary>
    /// 项目结果
    /// </summary>
    [SugarColumn(ColumnDataType = StaticConfig.CodeFirst_BigString)]
    public string ItemResult { get; set; }

    /// <summary>
    /// 结果下限
    /// </summary>
    [SugarColumn(IsNullable = true, Length = 20)]
    public string LowerLimit { get; set; }

    /// <summary>
    /// 结果上限
    /// </summary>
    [SugarColumn(IsNullable = true, Length = 20)]
    public string UpperLimit { get; set; }
}
