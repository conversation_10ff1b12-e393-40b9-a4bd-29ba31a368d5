﻿using Peis.Model.Other.PeEnum;
using Peis.Model.TableFilter;
using SqlSugar;
using System;

namespace Peis.Model.DataBaseEntity
{
    ///<summary>
    ///发票分配
    ///</summary>
    [SugarTable("FeeInvoiceAllocate")]
    public class FeeInvoiceAllocate: IHospCodeFilter
    {
        /// <summary>
        /// 分配Id
        /// </summary>        
        [SugarColumn(IsNullable = false, IsPrimaryKey = true, IsIdentity = true)]
        public int Id { get; set; }

        /// <summary>
        /// 发票入库的Id
        /// </summary>        
        [SugarColumn(IsNullable = true)]
        public int? WareHouseId { get; set; }

        /// <summary>
        /// 发票前缀
        /// </summary>        
        [SugarColumn(IsNullable = false, Length = 2)]
        public string Prefix { get; set; }

        /// <summary>
        /// 发票起始号
        /// </summary>        
        [SugarColumn(IsNullable = false)]
        public int StartNo { get; set; }

        /// <summary>
        /// 发票结束号
        /// </summary>        
        [SugarColumn(IsNullable = false)]
        public int EndNo { get; set; }

        /// <summary>
        /// 当前发票号
        /// </summary>        
        [SugarColumn(IsNullable = true)]
        public int? CurrentNo { get; set; }

        /// <summary>
        /// 最后使用时间
        /// </summary>        
        [SugarColumn(IsNullable = true)]
        public DateTime? LastUseDate { get; set; }

        /// <summary>
        /// 发票状态(1 未用、2 在用、3 用完)
        /// </summary>        
        [SugarColumn(IsNullable = false)]
        public InvoiceStatus Status { get; set; }

        /// <summary>
        /// 领取人
        /// </summary>        
        [SugarColumn(IsNullable = false, Length = 10)]
        public string Receiver { get; set; }

        /// <summary>
        /// 院区编码
        /// </summary>        
        [SugarColumn(IsNullable = false, Length = 1)]
        public string HospCode { get; set; }
    }
}