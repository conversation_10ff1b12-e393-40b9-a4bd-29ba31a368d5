{
    "Logging": {
        "LogLevel": {
            "Default": "Information",
            "Microsoft.AspNetCore": "Warning"
        }
    },
    "AllowedHosts": "*",
    // 应用配置
    "AppSettings": {
        "RunUrls": [
            "http://*:5003"
        ],
        "CorsIPs": "https://localhost:5001,http://***********:8080,http://***********:8081",
        "SwaggerIsEnabled": true
    },
    //数据库配置
    "DataBase": {
        "Peis": {
            "ConnectionString": "Data Source=*************\\MSSQLSERVER2014,11433;Initial Catalog=PeisHub_ShenShan; User ID=sa;Password=****;",
            "PrintSQL": true
        }
    },
    // 业务配置
    "WebserviceHub": {
        "ServiceCode": "0" // 服务器编码（院内为"0"|院外）生成体检号用
    },
    // 中间件
    "Middleware": {
        "UseRequestDecrypt": true,
        "UseReqResLogger": false,
        "UseExceptionHandling": true
    }
}
