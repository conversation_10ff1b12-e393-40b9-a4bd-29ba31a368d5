﻿namespace Peis.Model.DataBaseEntity.External
{
    /// <summary>
    /// 微信问卷调查
    /// </summary>
    public class WeChatBookQuestion
    {
        /// <summary>
        /// 体检号
        /// </summary>        
        [SugarColumn(IsNullable = false, IsPrimaryKey = true, Length = 12)]
        public string RegNo { get; set; }

        /// <summary>
        /// 家族史
        /// </summary>        
        [SugarColumn(IsNullable = true, Length = 150)]
        public string FamilyMedicalHistory { get; set; }

        /// <summary>
        /// 既往病史
        /// </summary>        
        [SugarColumn(IsNullable = true, Length = 150)]
        public string PastMedicalHistory { get; set; }

        /// <summary>
        /// 手术状况
        /// </summary>        
        [SugarColumn(IsNullable = true, Length = 150)]
        public string OperationStatus { get; set; }

        /// <summary>
        /// 吸烟习惯
        /// </summary>        
        [SugarColumn(IsNullable = true, Length = 150)]
        public string SmokingHabit { get; set; }

        /// <summary>
        /// 喝酒习惯
        /// </summary>        
        [SugarColumn(IsNullable = true, Length = 150)]
        public string DrinkingHabit { get; set; }

        /// <summary>
        /// 生活习惯
        /// </summary>        
        [SugarColumn(IsNullable = true, Length = 150)]
        public string LivingHabit { get; set; }

        /// <summary>
        /// 现在病况
        /// </summary>        
        [SugarColumn(IsNullable = true, Length = 150)]
        public string CurrentCondition { get; set; }

        /// <summary>
        /// 问卷答案
        /// </summary>        
        [SugarColumn(IsNullable = true, Length = 300)]
        public string QuestionnaireAnswer { get; set; }
    }
}
