﻿namespace Peis.Model.DataBaseEntity.Occupation
{
    /// <summary>
    /// 职业病报告特殊模块项目对应
    /// </summary>
    [SugarTable]
    public class MapOccupationalReportModelItem
    {
        /// <summary>
        /// 模块代码
        /// </summary>
        [SugarColumn(Length =20,IsPrimaryKey =true)]
        public string ModelCode { get; set; }
        /// <summary>
        /// 模块名
        /// </summary>
        [SugarColumn(Length = 20)]
        public string ModelName { get; set; }
        /// <summary>
        /// 项目代码
        /// </summary>
        [SugarColumn(Length = 10, IsPrimaryKey = true)]
        public string ItemCode { get; set; }
    }
}
