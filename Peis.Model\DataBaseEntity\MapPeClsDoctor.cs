﻿using Peis.Model.Other.PeEnum;
using SqlSugar;

namespace Peis.Model.DataBaseEntity
{
    ///<summary>
    ///体检分类-主检/审核医生表
    ///</summary>
    [SugarTable("MapPeClsDoctor")]
    public class MapPeClsDoctor
    {
        /// <summary>
        /// 自增id
        /// </summary>        
        [SugarColumn(IsNullable = false, IsPrimaryKey = true, IsIdentity = true)]
        public long Id { get; set; }

        /// <summary>
        /// 主检/审核 0主检 1审核
        /// </summary>        
        [SugarColumn(IsNullable = false)]
        public CheckAudit CheckAudit { get; set; }

        /// <summary>
        /// 体检分类
        /// </summary>        
        [SugarColumn(IsNullable = false)]
        public PeCls PeCls { get; set; }

        /// <summary>
        /// 医生代码
        /// </summary>        
        [SugarColumn(IsNullable = false, Length = 10)]
        public string OperatorCode { get; set; }
    }
}