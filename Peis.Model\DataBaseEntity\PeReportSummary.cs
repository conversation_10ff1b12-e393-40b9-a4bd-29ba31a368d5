namespace Peis.Model.DataBaseEntity;

///<summary>
///体检综述表
///</summary>
[SplitTable(SplitType.Year)]
[SugarTable("PeReportSummary_{yyyy}", "体检综述表")]
[SugarIndex("index_RegNo_", nameof(RegNo), OrderByType.Asc)]
public class PeReportSummary
{
    /// <summary>
    /// 小结标签Id
    /// </summary>        
    [SugarColumn(IsNullable = false, IsPrimaryKey = true)]
    public long Id { get; set; }

    /// <summary>
    /// 体检号
    /// </summary>        
    [SugarColumn(IsNullable = false, Length = 12)]
    public string RegNo { get; set; }

    /// <summary>
    /// 小结标签
    /// </summary>        
    [SugarColumn(IsNullable = false, ColumnDataType = StaticConfig.CodeFirst_BigString)]
    public string SummTag { get; set; }

    /// <summary>
    /// 组合代码
    /// </summary>        
    [SugarColumn(IsNullable = false, Length = 8)]
    public string CombCode { get; set; }

    /// <summary>
    /// 组合名称
    /// </summary>        
    [SugarColumn(IsNullable = false, Length = 200)]
    public string CombName { get; set; }

    /// <summary>
    /// 排序
    /// </summary>        
    [SugarColumn(IsNullable = false)]
    public int SortIndex { get; set; }

    /// <summary>
    /// 体检登记时间（分表依据）
    /// </summary>        
    [SplitField]
    [SugarColumn(IsNullable = false)]
    public DateTime RegisterTime { get; set; }

    /// <summary>
    /// 是否职业病
    /// </summary>
    [SugarColumn(IsNullable = false)]
    public bool IsOccupaiton { get; set; }
}