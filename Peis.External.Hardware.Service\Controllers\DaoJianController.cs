﻿using Microsoft.AspNetCore.Authorization;
using Peis.Model.Other.PeEnum;
using System.Net.Http.Headers;
using System.Net.Mime;

namespace Peis.External.Hardware.Service.Controllers;

/// <summary>
/// 导检-内部使用，不加密验证
/// </summary>
[ApiController]
[Route("api/[controller]/[action]")]
[AllowAnonymous]
public class DaoJianController : BaseApiController
{
    /// <summary>
    /// 导检主机地址
    /// </summary>
    protected readonly string DaoJianHostUrl;
    private readonly IRegisterNewService _registerService;
    private readonly IHttpClientHelper _httpClient;
    private readonly ISystemParameterService _systemParameterService;

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="registerService"></param>
    /// <param name="httpClient"></param>
    /// <param name="systemParameterService"></param>
    public DaoJianController(IRegisterNewService registerService, IHttpClientHelper httpClient, ISystemParameterService systemParameterService)
    {
        _registerService = registerService;
        _httpClient = httpClient;
        _systemParameterService = systemParameterService;
        DaoJianHostUrl = _systemParameterService.GuideInternalUrl;
    }

    /// <summary>
    /// 根据体检号获取体检人、组合信息
    /// </summary>
    /// <param name="regNo">体检号 （必填）</param>
    /// <returns></returns>
    [HttpGet("{regNo}")]
    [ProducesResponseType(typeof(RegisterWithComb), 200)]
    public IActionResult GetRegisterWithComb([Regex(VerifyRegNo = true, ErrorMessage = "[regNo]-体检号格式错误！")] string regNo)
    {
        Tuple<PeRegister, PeRegisterComb[]> data = _registerService.GetRegisterWithComb(regNo);
        if (data.Item1.IsNullOrEmpty())
        {
            result.Success = false;
            result.ReturnMsg = ResxCommon.NotExitPatient;
        }
        else if (!data.Item1.IsCompanyCheck && data.Item2.Any(x => x.PayStatus == PayStatus.未收费))
        {
            result.Success = false;
            result.ReturnMsg = "存在未缴费组合！";
        }
        else
        {
            result.ReturnData = new RegisterWithComb(data.Item1, data.Item2);
        }

        return Ok(result);
    }

    /// <summary>
    /// 叫号（重呼）
    /// </summary>
    /// <param name="macCode">mac编码（必填）根据使用电脑获得</param>
    /// <returns></returns>
    [HttpGet("{macCode}")]
    public IActionResult Call([Regex(ErrorMessage = "[macCode]-内容不允许为空！")] string macCode)
    {
        var url = $"{DaoJianHostUrl}/tts/Call";
        var reqContent = new
        {
            macCode,
        }.ToJson();

        var mediaType = new MediaTypeHeaderValue(MediaTypeNames.Application.Json);
        var resContent = _httpClient.PostStrContentAsync(url, reqContent, mediaType).Result;
        var resData = resContent.ToObject<DaoJianRespResult>();
        result.Success = resData?.Success ?? false;
        result.ReturnData = resData?.Data ?? "";
        result.ReturnMsg = resData?.Msg ?? ResxCommon.Fail;

        return Ok(result);
    }

    /// <summary>
    /// 完成检查
    /// </summary>
    /// <param name="data">请求内容</param>
    /// <returns></returns>
    [HttpPost]
    public IActionResult Done(DaoJianDoneData data)
    {
        var url = $"{DaoJianHostUrl}/tts/DoneQueueByRegNo";
        var mediaType = new MediaTypeHeaderValue(MediaTypeNames.Application.Json);
        var resContent = _httpClient.PostStrContentAsync(url, data.ToJson(), mediaType).Result;
        var resData = resContent.ToObject<DaoJianRespResult>();
        result.Success = resData?.Success ?? false;
        result.ReturnData = resData?.Data ?? "";
        result.ReturnMsg = resData?.Msg ?? ResxCommon.Fail;

        return Ok(result);
    }

    /// <summary>
    /// 置后
    /// </summary>
    /// <param name="macCode">mac编码（必填）根据使用电脑获得</param>
    /// <returns></returns>
    [HttpGet("{macCode}")]
    public IActionResult Back([Regex(ErrorMessage = "[macCode]-内容不允许为空！")] string macCode)
    {
        var url = $"{DaoJianHostUrl}/tts/Back";
        var reqContent = new
        {
            macCode,
        }.ToJson();

        var mediaType = new MediaTypeHeaderValue(MediaTypeNames.Application.Json);
        var resContent = _httpClient.PostStrContentAsync(url, reqContent, mediaType).Result;
        var resData = resContent.ToObject<DaoJianRespResult>();
        result.Success = resData?.Success ?? false;
        result.ReturnData = resData?.Data ?? "";
        result.ReturnMsg = resData?.Msg ?? ResxCommon.Fail;

        return Ok(result);
    }

}
