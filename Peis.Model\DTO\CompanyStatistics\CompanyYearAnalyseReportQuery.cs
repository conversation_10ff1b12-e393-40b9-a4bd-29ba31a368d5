﻿using Aspose.Words;
using Peis.Model.Constant;
using Peis.Utility.CustomException;
using Peis.Utility.Enums;
using Peis.Utility.Helper;
using System.IO;
using System.Text.Json.Serialization;

namespace Peis.Model.DTO.CompanyStatistics;

/// <summary>
/// 单位年度报告分析-查询
/// </summary>
public class CompanyYearAnalyseReportQuery
{
    /// <summary>
    /// 查询时间-开始
    /// </summary>
    public DateTime StartTime { get; set; }

    /// <summary>
    /// 查询时间-结束
    /// </summary>
    public DateTime EndTime { get; set; }

    /// <summary>
    /// 单位名称
    /// </summary>
    [Required]
    public string CompanyName { get; set; }

    /// <summary>
    /// 单位编码
    /// </summary>
    [Required]
    public string CompanyCode { get; set; }

    /// <summary>
    /// 单位次数
    /// </summary>
    public int CompanyTimes { get; set; }

    /// <summary>
    /// 输出文件类型：限定word、pdf
    /// </summary>
    public OfficeFormatType RepFileType { get; set; }

    /// <summary>
    /// 模板文件地址
    /// </summary>
    [JsonIgnore]
    public string TemplatePath { get; set; }

    /// <summary>
    /// 验证
    /// </summary>
    /// <exception cref="BusinessException"></exception>
    public void Verify()
    {
        switch (this.RepFileType)
        {
            case OfficeFormatType.Doc:
            case OfficeFormatType.Docx:
            case OfficeFormatType.Pdf:
                break;
            default:
                throw new BusinessException("暂不支持此格式！");
        }

        TemplatePath = Path.Combine(ConstantUrl.AppDomainBaseDirectory, ResxCommon.FolderWebHost, ConstantSystem.CompanyYearAnalyseReportTemplate);
        if (!File.Exists(TemplatePath))
            throw new BusinessException("未找到单位年度体检分析报告模板！");
    }
}
