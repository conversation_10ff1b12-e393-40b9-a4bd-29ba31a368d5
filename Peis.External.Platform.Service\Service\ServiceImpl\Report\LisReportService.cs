﻿using Peis.Quartz.UI.Tools;
using Microsoft.AspNetCore.Cors.Infrastructure;
using Peis.External.Platform.Service.Service.IService.Report;
using Peis.Model.DataBaseEntity.External;
using Peis.Model.DTO.CriticalValue;
using Peis.Model.DTO.Disease;
using Peis.Model.DTO.DoctorStation;
using Peis.Model.DTO.External.Report;
using Peis.Model.DTO.MajorPositive;
using Peis.Model.Other.PeEnum;
using Peis.Repository.IRepository;
using Peis.Service.IService;
using Peis.Utility.PeUser;
using System.Data;
using System.Globalization;
using System.Net;
using System.Text.RegularExpressions;

namespace Peis.External.Platform.Service.Service.ServiceImpl.Report
{
    public class LisReportService : ILisReportService
    {
        private readonly ISqlSugarClient _db;
        private readonly ISplitTable _splitTable;
        private readonly IDataTranRepository _dataTranRepository;
        private readonly IRegisterRepository _registerRepository;
        private readonly IDiseaseService _diseaseService;
        private readonly IRecordNewService _recordService;
        private readonly IDataRepository<PeRegister> _peRegisterRepository;
        private readonly IDataRepository<CodeDisease> _codeDiseaseRepository;
        private readonly IDataRepository<CodeItemBound> _codeItemBoundRepository;
        private readonly IDataRepository<CodeBoundType> _codeBoundTypeRepository;
        private readonly IRecordRepository _recordRepository;
        private readonly IDataRepository<ExtLisReportLog> _reportLogRepository;
        private readonly ICriticalValueService _criticalValueService;
        private readonly IHttpContextUser _httpContextUser;
        private readonly ILogBusinessNewService _logBusinessNewService;

        public LisReportService(
            ISqlSugarClient db,
            ISplitTable splitTable,
            IDataTranRepository dataTranRepository,
            IRegisterRepository registerRepository,
            IDiseaseService diseaseService,
            IRecordNewService recordService,
            IDataRepository<PeRegister> peRegisterRepository,
            IDataRepository<CodeDisease> codeDiseaseRepository,
            IDataRepository<CodeItemBound> codeItemBoundRepository,
            IDataRepository<CodeBoundType> codeBoundTypeRepository,
            IRecordRepository recordRepository,
            IDataRepository<ExtLisReportLog> extLisReportLog,
            ICriticalValueService criticalValueService,
            IHttpContextUser httpContextUser,
            ILogBusinessNewService logBusinessNewService)
        {
            _db = db;
            _splitTable = splitTable;
            _dataTranRepository = dataTranRepository;
            _registerRepository = registerRepository;
            _diseaseService = diseaseService;
            _recordService = recordService;
            _peRegisterRepository = peRegisterRepository;
            _codeDiseaseRepository = codeDiseaseRepository;
            _codeItemBoundRepository = codeItemBoundRepository;
            _codeBoundTypeRepository = codeBoundTypeRepository;
            _recordRepository = recordRepository;
            _reportLogRepository = extLisReportLog;
            _criticalValueService = criticalValueService;
            _httpContextUser = httpContextUser;
            _logBusinessNewService = logBusinessNewService;
        }

        public void AcceptReport(string xml, ref string respData)
        {
            var lisReport = XmlHelper.Deserialize<LisReport>(xml);
            var register  = _registerRepository.ReadRegisterNoHosp(lisReport.PE_NO).First();
            if (register == null)
            {
                respData = XmlHelper.Serialize(ReportResult.Bad("体检系统不存在该患者"));
                return;
            }
            // 患者已主检或已审核
            if (register.PeStatus > PeStatus.已检完)
            {
                respData = XmlHelper.Serialize(ReportResult.Bad("当前体检已经审核或者打印报告，不能修改项目结果"));
                return;
            }
            _httpContextUser.SetHospCode(register.HospCode);
            var log = LogAcceptBeginning(lisReport, xml);
            var failedItems = new List<string>();

            // 通过条码号获取组合
            var sampleCombs = _splitTable.GetTableOrDefault<PeRegisterComb>(register.RegisterTime)
                .InnerJoin<PeSample>((comb, samp) => comb.Id == samp.RegCombId)
                .Where((comb, samp) => samp.SampleNo == lisReport.BAR_CODE_NO)
                .Select(comb => comb)
                .ToArray();
            var lisItemReports = lisReport.DETAIL.ToDictionary(x => x.TEST_ITEM_CODE);
            foreach (var regComb in sampleCombs)
            {
                var failedItemsComb = new List<string>(); //单个组合的错误信息
                PeRecordComb          recComb;            // 组合小结
                PeRecordCombTag[]     recCombTags;        // 组合小结标签
                List<PeRecordItem>    recItems = new();   // 项目结果
                List<PeRecordItemTag> recItemTags = new();// 项目结果标签

                var codeItemCount = _db.Queryable<CodeItem>()
                    .InnerJoin<MapItemComb>((item, mapComb) => item.ItemCode == mapComb.ItemCode)
                    .Where((item, mapComb) => mapComb.CombCode == regComb.CombCode)
                    .Count();
                var codeItems = _db.Queryable<CodeItem>()
                    .InnerJoin<MapItemComb>((item, mapComb) => item.ItemCode == mapComb.ItemCode)
                    .LeftJoin<MapCodeLisItem>((item, mapComb, mapLis) => item.ItemCode == mapLis.ItemCode)
                    .Where((item, mapComb, mapLis) => mapComb.CombCode == regComb.CombCode)
                    .Distinct()
                    .Select((item, mapComb, mapLis) => new CodeItemWithLis
                    {
                        ItemCode    = item.ItemCode.SelectAll(),
                        LisItemCode = mapLis.LisItemCode
                    }).ToArray();

                if (codeItems.Length == 0)
                {
                    failedItemsComb.Add($"{regComb.CombName}，缺少检验项目对应");
                    continue;
                }

                recComb = NewRecordComb(regComb, lisReport);
                foreach (var codeItem in codeItems)
                {
                    if (codeItem.LisItemCode.IsNullOrEmpty())
                    {
                        failedItemsComb.Add($"{regComb.CombName} - {codeItem.ItemCode}:{codeItem.ItemName} - 未对应检验代码 ");
                        continue;
                    }
                    if (!lisItemReports.TryGetValue(codeItem.LisItemCode, out var lisItemReport))
                    {
                        if (!regComb.CombName.Contains("药敏"))
                            failedItemsComb.Add($"{regComb.CombName} - {codeItem.ItemCode}:{codeItem.ItemName} - LisItemCode:{codeItem.LisItemCode} ");
                        continue;
                    }

                    var recItem    = NewRecordItem(recComb, codeItem, lisItemReport);
                    var recItemTag = NewRecordItemTag(recItem);

                    recItems.Add(recItem);
                    recItemTags.Add(recItemTag);
                }

                if (regComb.CombName.Contains("药敏"))
                {
                    // 药敏且项目无结果，则添加异常错误
                    if (recItems.Count == 0)
                    {
                        failedItems.Add($"{regComb.CombName}");
                        continue;
                    }
                }
                else if (recItems.Count != codeItemCount)
                {
                    failedItems.AddRange(failedItemsComb);//如果未接收完全则保存错误信息，接收完全视为成功
                    // 非药敏，需要全部项目都有结果
                    continue;
                }

                recCombTags = NewRecordCombTag(register, recComb, recItems);
                SaveRecord(recComb, recCombTags, recItems, recItemTags);
            }

            // //接收项目为空，或者对应有误 体检内部错误，暂不抛出给检验
            if (failedItems.Count == 0)
            {
                UpdatePeStatus(lisReport.PE_NO);
                LogAcceptSucceed(log);
            }
            else
            {
                LogAcceptFailed(log, failedItems);
            }

            respData = XmlHelper.Serialize(ReportResult.Ok());
            return;
        }

        public void AcceptCritical(string xml, ref string respData)
        {
            var criticalReport = XmlHelper.Deserialize<LisReportCritical>(xml);
            var register = _registerRepository.ReadRegisterNoHosp(criticalReport.PE_NO).First();
            if (register == null)
            {
                respData = XmlHelper.Serialize(ReportResult.Bad("体检系统不存在该患者！"));
                return;
            }
            _httpContextUser.SetHospCode(register.HospCode);
            var peItemCode = _db.Queryable<MapCodeLisItem>()
                .InnerJoin<MapItemComb>((mapLis,mapComb)=>mapLis.ItemCode==mapComb.ItemCode)
                .InnerJoin<PeSample>((mapLis, mapComb,sample)=>mapComb.CombCode==sample.CombCode)
                .Where((mapLis, mapComb, sample)=>sample.SampleNo==criticalReport.BAR_CODE_NO)
                .Where((mapLis, mapComb, sample) => mapLis.LisItemCode == criticalReport.TEST_ITEM_CODE)
                .Select(mapLis => mapLis.ItemCode).First();
            //var id = _db.Queryable<CriticalValueMsg>()
            //    .InnerJoin<CriticalValueMsgItem>((m, i) => m.Id == i.CriticalId)
            //    .Where((m, i) => m.RegNo == criticalReport.PE_NO && i.ItemCode == peItemCode).Select(m => m.Id).First();
            //if (id >0)
            //{
            //    respData = XmlHelper.Serialize(ReportResult.Ok());
            //    return;
            //}
            var isValidDate = DateTime.TryParseExact(criticalReport.REPORT_DATE, "yyyyMMddHHmmss", CultureInfo.InvariantCulture, DateTimeStyles.None, out var sendDate);
            ConfirmCriticalValue criticalValue = new ConfirmCriticalValue
            {
                Confirmer = criticalReport.PROMPT_DR_NAME,
                RegNo = criticalReport.PE_NO,
                Type = CriticalValueType.危急值,
                ConfirmTime = isValidDate? sendDate : DateTime.Now, //Convert.ToDateTime(criticalReport.REPORT_DATE),
                Items = new ConfirmCriticalValueItem[]
                {
                    new ConfirmCriticalValueItem
                    {
                        ItemCode = peItemCode,
                        ItemName = criticalReport.TEST_ITEM_NAME,
                        ItemTags = new List<ItemTag>(){ new ItemTag{
                            Tag = criticalReport.TEST_PROMPT_RESULT_VALUE
                        } },
                        LowerLimit = criticalReport.REFERENCE_RANGES,
                        UpperLimit = string.Empty,
                    }
                }
            };
            _criticalValueService.ConfirmCriticalValue(criticalValue);
            respData = XmlHelper.Serialize(ReportResult.Ok());
            return;
        }

        #region 本地方法
        private static PeRecordComb NewRecordComb(PeRegisterComb regComb, LisReport report)
        {
            return new PeRecordComb
            {
                Id           = regComb.Id,
                RegNo        = regComb.RegNo,
                RegCombId    = regComb.Id,
                CombCode     = regComb.CombCode,
                //CombResult   = null,
                ClsCode      = regComb.ClsCode,
                ExamDeptCode = regComb.ExamDeptCode,
                OperName     = string.Empty,
                DoctorName   = report.REPORT_DR_NAME,
                IsError      = false,
                OperTime     = DateTime.Now,
                ExamTime     = DateTime.ParseExact(report.REPORT_DATE, "yyyyMMddHHmmss", CultureInfo.InvariantCulture),
                RegisterTime = regComb.RegisterTime
            };
        }
        private PeRecordCombTag[] NewRecordCombTag(PeRegister register, PeRecordComb recComb, List<PeRecordItem> recItems)
        {
            var combTags = new List<CombTag>();
            var items = recItems
                .Select(x =>
                {
                    var itemRangeLimits = new ItemRangeLimit
                    {
                        ItemCode         = x.ItemCode,
                        ItemName         = x.ItemName,
                        ResultType       = x.ResultType,
                        Unit             = x.Unit
                    };

                    var itemBound = GetCodeItemBound(register.Sex, register.Age, register.AgeUnit ?? AgeUnit.岁, x.ItemCode);
                    if (itemBound != null)
                    {
                        itemRangeLimits.LowerLimit       = itemBound.LowerLimit;
                        itemRangeLimits.UpperLimit       = itemBound.UpperLimit;
                        itemRangeLimits.DangerLowerLimit = itemBound.DangerLowerLimit;
                        itemRangeLimits.DangerUpperLimit = itemBound.DangerUpperLimit;
                    }

                    return itemRangeLimits;
                }).ToArray();

            #region 从结果计算小结
            var itemTags = recItems
                .Select(x => new ItemTag
                {
                    Id             = x.Id,
                    ItemCode       = x.ItemCode,
                    Tag            = x.ItemResult,
                    AbnormalType   = x.AbnormalType,
                    ResultId       = -1,
                    IsCalcResul    = false,
                    CalcItemTagIds = new(),
                    LowerLimit     = x.LowerLimit,
                    UpperLimit     = x.UpperLimit,
                }).ToList();

            combTags.AddRange(_diseaseService.GetDiseaseForExtLisReport(itemTags, items, itemTags));
            #endregion

            #region 从提示符号获取小结
            foreach (var recItem in recItems)
            {
                string combTagValue = recItem.Hint.Trim() switch
                {
                    "↑" => combTagValue = $"{recItem.ItemName}高{recItem.ItemResult} {recItem.Unit}",
                    "↓" => combTagValue = $"{recItem.ItemName}低{recItem.ItemResult} {recItem.Unit}",
                    "+"  => combTagValue = $"{recItem.ItemName}{recItem.ItemResult}",
                    "+-" => combTagValue = $"{recItem.ItemName}{recItem.ItemResult}",
                    "★" => combTagValue = GetCombTagByRange(recItem) ?? $"{recItem.ItemName}{recItem.ItemResult} {recItem.Unit}",
                    _ => string.Empty
                };

                if (!string.IsNullOrWhiteSpace(combTagValue))
                {
                    var combTag = new CombTag
                    {
                        Id           = SnowFlakeSingle.Instance.NextId(),
                        Tag          = combTagValue,
                        IsCustom     = false,
                        BindItemTags = new() { recItem.Id }
                    };

                    // 匹配疾病
                    var diseaseMatchs = _codeDiseaseRepository.FindAll(x => combTag.Tag.Contains(x.DiseaseName)).ToArray();
                    if (diseaseMatchs.Length > 0)
                    {
                        var diseaseMatch = diseaseMatchs.OrderByDescending(x => x.DiseaseName.Length).First();

                        combTag.DiseaseCode = diseaseMatch.DiseaseCode;
                        combTag.DiseaseName = diseaseMatch.DiseaseName;
                    }

                    combTags.Add(combTag);
                    recItem.AbnormalType = AbnormalType.异常;
                    recItem.IsError      = true;
                }
            }
            #endregion

            // 合并疾病或设置默认小结
            if (combTags.Any())
                combTags = _diseaseService.GetDistinctDisease(combTags, items, itemTags);
            else
                combTags.Add(new CombTag
                {
                    Id           = SnowFlakeSingle.Instance.NextId(),
                    Tag          = "未见明显异常",
                    IsCustom     = true,
                    BindItemTags = new()
                });

            // 把小结标签汇集为组合小结
            recComb.CombResult = string.Join(';', combTags.Select(x => x.Tag));

            // 返回小结标签
            return combTags
                .Select(x => new PeRecordCombTag
                {
                    Id           = x.Id,
                    RecCombId    = recComb.Id,
                    CombCode     = recComb.CombCode,
                    CombTag      = x.Tag,
                    IsCustom     = false,
                    BindItemTags = string.Join(';', x.BindItemTags ?? new()),
                    RegisterTime = recComb.RegisterTime,
                    AbnormalType = x.AbnormalType,
                    DiseaseCode = x.DiseaseCode,
                    DiseaseName = x.DiseaseName,
                })
                .ToArray();
        }
        private PeRecordItem NewRecordItem(PeRecordComb recComb, CodeItemWithLis item, LisItemResult result)
        {
            return new PeRecordItem
            {
                Id             = SnowFlakeSingle.Instance.NextId(),
                RecCombId      = recComb.Id,
                CombCode       = recComb.CombCode,
                ItemCode       = item.ItemCode,
                ItemResult     = WebUtility.HtmlDecode(result.TEST_RESULT_VALUE),
                ItemName       = item.ItemName,
                SortIndex      = item.SortIndex,
                ResultType     = item.ValueType,
                Unit           = result.TEST_RESULT_VALUE_UNIT,
                LowerLimit     = WebUtility.HtmlDecode(result.REFERENCE_RANGES),
                //UpperLimit   = result.RefMax,
                Hint           = result.NORMAL_FLAG,
                //AbnormalType = abnormalType,                      // 延迟到计算小结再赋值
                //IsError      = abnormalType != AbnormalType.正常, // 延迟到计算小结再赋值
                RegisterTime   = recComb.RegisterTime,
                ReferenceRange = WebUtility.HtmlDecode(result.REFERENCE_RANGES),
            };
        }
        private static PeRecordItemTag NewRecordItemTag(PeRecordItem recItem)
        {
            return new PeRecordItemTag
            {
                Id             = SnowFlakeSingle.Instance.NextId(),
                RecItemId      = recItem.Id,
                BindResultId   = -1,
                ItemCode       = recItem.ItemCode,
                ItemTag        = recItem.ItemResult,
                AbnormalType   = recItem.AbnormalType,
                IsCalcResul    = false,
                CalcItemTagIds = string.Empty,
                RegisterTime   = recItem.RegisterTime
            };
        }

        private CodeItemBound GetCodeItemBound(Sex sex, int age, AgeUnit ageUnit, string itemCode)
        {
            var itemBounds = CacheHelper.GetOrCreate(key: "LisReportService.GetCodeItemBound", absoluteExpiration: TimeSpan.FromMinutes(1),
                valueFactory: () =>
                {
                    var boundTypes = _codeBoundTypeRepository.FindAll()
                        .Where(x => int.TryParse(x.LowerAgeLimit, out var _) && int.TryParse(x.UpperAgeLimit, out var _))
                        .ToDictionary(x => x.BoundTypeCode);

                    return _codeItemBoundRepository.FindAll()
                        .GroupBy(x => x.ItemCode)
                        .Select(g => new
                        {
                            ItemCode = g.Key,
                            Bounds   = g
                                        .Where(x => boundTypes.ContainsKey(x.BoundCodeType))
                                        .Select(x => new
                                        {
                                            BoundTypeCode    = boundTypes[x.BoundCodeType].BoundTypeCode,
                                            Sex              = boundTypes[x.BoundCodeType].Sex,
                                            LowerAgeLimit    = int.Parse(boundTypes[x.BoundCodeType].LowerAgeLimit),
                                            UpperAgeLimit    = int.Parse(boundTypes[x.BoundCodeType].UpperAgeLimit),
                                            LowerLimit       = x.LowerLimit,
                                            UpperLimit       = x.UpperLimit,
                                            DangerLowerLimit = x.DangerLowerLimit,
                                            DangerUpperLimit = x.DangerUpperLimit
                                        })
                                        .ToArray()
                        })
                        .ToDictionary(x => x.ItemCode);
                });

            if (!itemBounds.TryGetValue(itemCode, out var itemBound))
                return null;
            var year = ageUnit == AgeUnit.岁 ? age : age / 12;
            return itemBound.Bounds
                .Where(x => x.Sex == sex || x.Sex == Sex.通用)
                .Where(x => year >= x.LowerAgeLimit && year <= x.UpperAgeLimit)
                .Select(x => new CodeItemBound
                {
                    ItemCode         = itemCode,
                    BoundCodeType    = x.BoundTypeCode,
                    LowerLimit       = x.LowerLimit,
                    UpperLimit       = x.UpperLimit,
                    DangerLowerLimit = x.DangerLowerLimit,
                    DangerUpperLimit = x.DangerUpperLimit
                })
                .FirstOrDefault();
        }
        private static string GetCombTagByRange(PeRecordItem recItem)
        {
            var itemNmae       = recItem.ItemName;
            var itemResultStr  = recItem.ItemResult;
            var itemUnit       = recItem.Unit;
            var referenceRange = recItem.LowerLimit;// 目前是使用下限来接收第三方的参考范围

            if (!itemResultStr.TryParseDouble(out var itemValue))
                return null;

            Match matchRange = Regex.Match(referenceRange, @"^([\d\.]+)--([\d\.]+)$");
            if (!matchRange.Success)
                return null;

            if (matchRange.Groups[1].Value.TryParseDouble(out var lowerLimit) && matchRange.Groups[2].Value.TryParseDouble(out var upperLimit))
            {
                if (itemValue < lowerLimit)
                    return $"{itemNmae}低{itemResultStr} {itemUnit}";

                if (itemValue > upperLimit)
                    return $"{itemNmae}高{itemResultStr} {itemUnit}";
            }
            
            return null;
        }

        private bool UpdatePeStatus(string regNo)
        {
            var register = _registerRepository.ReadRegister(regNo).First();
            if (register == null)
                return false;

            //查出所有体检的组合
            var peRecordComb = _recordRepository.ReadCombsInfo(regNo)
                                .Select((regComb, recComb) => new
                                {
                                    regComb.CombName,
                                    recComb.DoctorName
                                }).ToList();
            //查已录入结果的组合总数
            var hasResult = peRecordComb.Where(x => !string.IsNullOrEmpty(x.DoctorName)).Count();

            //没有医生有值  未检查
            if (hasResult == 0)
                register.PeStatus = PeStatus.未检查;

            //有一个有医生值 正在检查
            else if (hasResult < peRecordComb.Count())
                register.PeStatus = PeStatus.正在检查;

            //所有医生都有值 已检完
            else
                register.PeStatus = PeStatus.已检完;

            _peRegisterRepository.Update(register);
            return true;
        }
        private void SaveRecord(PeRecordComb recComb, PeRecordCombTag[] recCombTags, List<PeRecordItem> recItems, List<PeRecordItemTag> recItemTags)
        {
            _dataTranRepository.ExecTran(() =>
            {
                // 删除旧记录
                _recordService.DeleteRecordCombAndImage(new() { RegNo = recComb.RegNo, RegCombId = recComb.RegCombId }, out var recordComb, out var recordItems, false);
                if (recordComb == null)
                {
                    _logBusinessNewService.RecordNewLog(recComb.RegNo, recComb.CombCode, logOper: string.IsNullOrEmpty(_httpContextUser.UserName) ? "自动接收" : _httpContextUser.UserName);
                }
                else
                {
                    _logBusinessNewService.RecordModifyLog(recordComb, recComb, recordItems, recItems.ToArray(), logOper: string.IsNullOrEmpty(_httpContextUser.UserName) ? "自动接收" : _httpContextUser.UserName);
                }
                // 保存新记录
                _db.Insertable(recComb).SplitTable().ExecuteCommand();
                _db.Insertable(recCombTags).SplitTable().ExecuteCommand();
                _db.Insertable(recItems).SplitTable().ExecuteCommand();
                _db.Insertable(recItemTags).SplitTable().ExecuteCommand();
                _logBusinessNewService.SaveLogs();
            });

            try
            {
                var regComb = _registerRepository.ReadRegisterCombs(recComb.RegNo, recComb.RegCombId).First();
                var recCombs = recItems.Select(x => new RecComb
                {
                    RegNo = recComb.RegNo,
                    RecCombId = recComb.Id,
                    CombCode = recComb.CombCode,
                    CombName = regComb?.CombName,
                    ItemCode = x.ItemCode,
                    ItemName = x.ItemName,
                    ItemResult = x.ItemResult,
                    ResultType = x.ResultType,
                    LowerLimit = x.LowerLimit,
                    UpperLimit = x.UpperLimit,
                    OperCode = _httpContextUser.UserId,
                }).ToList();
                QuartzTaskHelper.AddJobAsync<IMajorPositiveService>(nameof(IMajorPositiveService.SaveMajorPositiveWhileSaveRecord), recCombs).ConfigureAwait(false);
            }
            catch { }
        }

        private ExtLisReportLog LogAcceptBeginning(LisReport lisReport,string xml)
        {
            ExtLisReportLog log = _reportLogRepository.FindInSingleKey(lisReport.BAR_CODE_NO);
            if (log == null)
            {
                log = new ExtLisReportLog()
                {
                    RegNo      = lisReport.PE_NO,
                    SampleNo   = lisReport.BAR_CODE_NO,
                    CombName   = lisReport.REPORT_NAME,
                    ReportXML  = xml,
                    CreateTime = DateTime.Now,
                    LastTime   = DateTime.Now,
                    Success    = false
                };
                _reportLogRepository.Insert(log);
            }
            else
            {
                log.ReportXML = xml;
                log.LastTime  = DateTime.Now;
                log.Success   = false;
                _reportLogRepository.Update(log);
            }
            return log;
        }
        private void LogAcceptSucceed(ExtLisReportLog log)
        {
            log.Success = true;
            log.ErrMsg  = string.Empty;
            _reportLogRepository.Update(log);
        }
        private void LogAcceptFailed(ExtLisReportLog log, List<string> failedItems)
        {
            log.Success = false;
            log.ErrMsg  = "Lis报告明细缺少结果或未对应：" + Environment.NewLine + string.Join($"{Environment.NewLine}  ", failedItems);
            _reportLogRepository.Update(log);
        }
    }
    #endregion
}

