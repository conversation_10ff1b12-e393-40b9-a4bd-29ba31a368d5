﻿using Peis.Quartz.UI.BaseService;
using Peis.Service.IService.ExternalSystem;

namespace Peis.External.Platform.Service.Service.ServiceImpl.Job;

/// <summary>
/// 作业：同步His医嘱项目信息
/// </summary>
public class SyncCodeHisOrderItemJob : IJobService
{
    readonly IExternalSystemDataSyncService _extSystemDataSyncService;

    public SyncCodeHisOrderItemJob(IExternalSystemDataSyncService extSystemDataService)
    {
        _extSystemDataSyncService = extSystemDataService;
    }

    public string ExecuteService(string parameter)
    {
        int.TryParse(parameter, out var pageSize);
        pageSize = pageSize <= 0 ? 50 : pageSize;
        _extSystemDataSyncService.SyncCodeHisOrderItems(pageSize, out var msg);
        return msg;
    }
}
