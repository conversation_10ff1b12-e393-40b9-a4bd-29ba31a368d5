﻿namespace Peis.Model.DataBaseEntity;

///<summary>
/// 疾病-重大阳性对应表
///</summary>
[SugarTable(TableName = nameof(MapDiseaseMajorPositive), TableDescription = "疾病-重大阳性对应表")]
[SugarIndex($"idx_{nameof(MapDiseaseMajorPositive)}", nameof(DiseaseCode), OrderByType.Asc, nameof(PositiveCode), OrderByType.Asc)]
public class MapDiseaseMajorPositive
{
    /// <summary>
    /// 疾病代码
    /// </summary>        
    [SugarColumn(IsNullable = false, IsPrimaryKey = true, Length = 8)]
    [Required(ErrorMessage = "疾病代码不能为空")]
    public string DiseaseCode { get; set; }

    /// <summary>
    /// 重大阳性代码
    /// </summary>        
    [SugarColumn(IsNullable = false, IsPrimaryKey = true, Length = 8)]
    [Required(ErrorMessage = "重大阳性代码不能为空")]
    public string PositiveCode { get; set; }

    /// <summary>
    /// 显示顺序
    /// </summary>        
    public int SortIndex { get; set; }
}