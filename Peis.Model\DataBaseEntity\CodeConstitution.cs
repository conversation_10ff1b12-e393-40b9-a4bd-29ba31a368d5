﻿using SqlSugar;

namespace Peis.Model.DataBaseEntity
{
    ///<summary>
    ///体质信息（中医管理）
    ///</summary>
    [SugarTable("CodeConstitution")]
    public class CodeConstitution
    {
        /// <summary>
        /// 体质代码
        /// </summary>        
        [SugarColumn(IsNullable = false, IsPrimaryKey = true, Length = 2)]
        public string ConstitutionCode { get; set; }

        /// <summary>
        /// 体质名称
        /// </summary>        
        [SugarColumn(IsNullable = false, Length = 50)]
        public string ConstitutionName { get; set; }

        /// <summary>
        /// 体质特征
        /// </summary>        
        [SugarColumn(IsNullable = true, ColumnDataType = StaticConfig.CodeFirst_BigString)]
        public string PhysiqueFeature { get; set; }

        /// <summary>
        /// 体质定义
        /// </summary>        
        [SugarColumn(IsNullable = true, ColumnDataType = StaticConfig.CodeFirst_BigString)]
        public string PhysiqueDefine { get; set; }

        /// <summary>
        /// 体质成因
        /// </summary>        
        [SugarColumn(IsNullable = true, ColumnDataType = StaticConfig.CodeFirst_BigString)]
        public string PhysiqueOrigin { get; set; }

        /// <summary>
        /// 体质介绍
        /// </summary>        
        [SugarColumn(IsNullable = true, ColumnDataType = StaticConfig.CodeFirst_BigString)]
        public string PhysiqueIntroduce { get; set; }

        /// <summary>
        /// 形体特征
        /// </summary>        
        [SugarColumn(IsNullable = true, ColumnDataType = StaticConfig.CodeFirst_BigString)]
        public string FormFeature { get; set; }

        /// <summary>
        /// 常见表现
        /// </summary>        
        [SugarColumn(IsNullable = true, ColumnDataType = StaticConfig.CodeFirst_BigString)]
        public string CommonManifestations { get; set; }

        /// <summary>
        /// 心理特征
        /// </summary>        
        [SugarColumn(IsNullable = true, ColumnDataType = StaticConfig.CodeFirst_BigString)]
        public string MentalProfile { get; set; }

        /// <summary>
        /// 发病倾向
        /// </summary>        
        [SugarColumn(IsNullable = true, ColumnDataType = StaticConfig.CodeFirst_BigString)]
        public string IncidenceTendency { get; set; }

        /// <summary>
        /// 人群分布
        /// </summary>        
        [SugarColumn(IsNullable = true, ColumnDataType = StaticConfig.CodeFirst_BigString)]
        public string PopulationDistribution { get; set; }

        /// <summary>
        /// 适应能力
        /// </summary>        
        [SugarColumn(IsNullable = true, ColumnDataType = StaticConfig.CodeFirst_BigString)]
        public string Adaptability { get; set; }

        /// <summary>
        /// 生活起居
        /// </summary>        
        [SugarColumn(IsNullable = true, ColumnDataType = StaticConfig.CodeFirst_BigString)]
        public string DailyLife { get; set; }

        /// <summary>
        /// 精神调摄
        /// </summary>        
        [SugarColumn(IsNullable = true, ColumnDataType = StaticConfig.CodeFirst_BigString)]
        public string MentalAdjustment { get; set; }

        /// <summary>
        /// 形体运动
        /// </summary>        
        [SugarColumn(IsNullable = true, ColumnDataType = StaticConfig.CodeFirst_BigString)]
        public string BodyMovement { get; set; }

        /// <summary>
        /// 饮食调养
        /// </summary>        
        [SugarColumn(IsNullable = true, ColumnDataType = StaticConfig.CodeFirst_BigString)]
        public string DietRecuperation { get; set; }

        /// <summary>
        /// 推荐药膳
        /// </summary>        
        [SugarColumn(IsNullable = true, ColumnDataType = StaticConfig.CodeFirst_BigString)]
        public string MedicinalDiet { get; set; }

        /// <summary>
        /// 音乐疗法
        /// </summary>        
        [SugarColumn(IsNullable = true, ColumnDataType = StaticConfig.CodeFirst_BigString)]
        public string MusicTherapy { get; set; }

        /// <summary>
        /// 常用药物
        /// </summary>        
        [SugarColumn(IsNullable = true, ColumnDataType = StaticConfig.CodeFirst_BigString)]
        public string CommonlyUsedDrugs { get; set; }

        /// <summary>
        /// 经典名方
        /// </summary>        
        [SugarColumn(IsNullable = true, ColumnDataType = StaticConfig.CodeFirst_BigString)]
        public string ClassicFormula { get; set; }

        /// <summary>
        /// 中成药
        /// </summary>        
        [SugarColumn(IsNullable = true, ColumnDataType = StaticConfig.CodeFirst_BigString)]
        public string ChinesePatentDrug { get; set; }

        /// <summary>
        /// 经络保健
        /// </summary>        
        [SugarColumn(IsNullable = true, ColumnDataType = StaticConfig.CodeFirst_BigString)]
        public string MeridianHealthCare { get; set; }

        /// <summary>
        /// 个性化建议模板
        /// </summary>        
        [SugarColumn(IsNullable = true, ColumnDataType = StaticConfig.CodeFirst_BigString)]
        public string SuggestedTemplate { get; set; }
    }
}