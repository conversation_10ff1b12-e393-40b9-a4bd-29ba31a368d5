﻿namespace Peis.External.Hardware.Service.Models;

/// <summary>
/// RegisterWithComb
/// </summary>
/// <param name="RegNo">体检号</param>
/// <param name="Name">名称</param>
/// <param name="PatTypeCode">客户类型：0-普通，1-vip</param>
/// <param name="PeTypeCode">体检者类型：1-团检，2-个检</param>
/// <param name="SexCode">性别：1-男，2-女</param>
/// <param name="Age">年龄</param>
/// <param name="TJAreaCode">院区代码</param>
/// <param name="IsSpecial">填默认T</param>
/// <param name="BloodTube">抽血管(默认1)</param>
/// <param name="GroupInfo">组合信息集合</param>
public record RegisterWithComb(string RegNo,
                               string Name,
                               string PatTypeCode,
                               string PeTypeCode,
                               string SexCode,
                               string Age,
                               string TJAreaCode,
                               string IsSpecial,
                               string BloodTube,
                               GroupItem[] GroupInfo)
{
    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="peRegister"></param>
    /// <param name="registerCombs"></param>
    public RegisterWithComb([NotNull] PeRegister peRegister,
                            [NotNull] PeRegisterComb[] registerCombs) : this(
        RegNo      : peRegister.RegNo,
        Name       : peRegister.Name,
        PatTypeCode: "0",// 暂时固定为“0”
        PeTypeCode : peRegister.IsCompanyCheck ? "1" : "2",
        SexCode    : ((int)peRegister.Sex).ToString(),
        Age        : peRegister.Age.ToString(),
        TJAreaCode : peRegister.HospCode,
        IsSpecial  : (peRegister.IsVIP ?? false) || (peRegister.IsLeader ?? false) ? "T" : "F",
        BloodTube  : "1",
        GroupInfo  : registerCombs
        .Select(x => new GroupItem(x.CombCode))
        .Distinct()
        .OrderBy(x => x.GroupCode)
        .ToArray()
        )
    {

    }
}


/// <summary>
/// 组合信息
/// </summary>
/// <param name="GroupCode">组合Code</param>
public record GroupItem(string GroupCode);

