﻿using Microsoft.AspNetCore.Mvc;
using Peis.Model.DTO;
using Peis.Model.DTO.CompanyStatistics;
using Peis.Model.DTO.DataQuery;
using Peis.Model.Other.Input.CompanyStatistics;
using Peis.Service.IService;
using System.Collections.Generic;

namespace Peis.API.Controllers.Statistics
{
    /// <summary>
    /// 单位统计
    /// </summary>
    [Route("api/[controller]")]
    [ApiController]
    public class CompanyStatisticsController : BaseApiController
    {
        private readonly ICompanyStatisticsService _companyStatisticsService;

        public CompanyStatisticsController(ICompanyStatisticsService companyStatisticsService)
        {
            _companyStatisticsService = companyStatisticsService;
        }

        ///// <summary>
        ///// 获取单位套餐统计
        ///// </summary>
        ///// <param name="companyClusterQuery">查询条件</param>
        ///// <returns></returns>
        //[HttpPost("GetCompanyCluster")]
        //[ProducesResponseType(typeof(CompanyClusterStatistics), 200)]
        //public IActionResult GetCompanyCluster(CompanyClusterQuery companyClusterQuery)
        //{
        //    CompanyClusterStatistics companyClusterStatistics = new();

        //    var msg = string.Empty;

        //    result.Success = _companyStatisticsService.GetCompanyCluster(companyClusterQuery, ref companyClusterStatistics, ref msg);

        //    result.ReturnMsg = msg;

        //    result.ReturnData = companyClusterStatistics;

        //    return Ok(result);
        //}

        /// <summary>
        /// 获取单位体检人员项目明细统计(单位体检人员套餐)
        /// </summary>
        /// <param name="query">查询条件</param>
        /// <returns></returns>
        [HttpPost("GetCompanyPatientClusters")]
        [ProducesResponseType(typeof(List<CompanyPatientCluster>), 200)]
        public IActionResult GetCompanyPatientClusters([FromBody] CompanyPatientClustersQuery query)
        {
            List<CompanyPatientCluster> companyPatientClusters = new();
            var msg = string.Empty;
            result.Success = _companyStatisticsService.GetCompanyPatientClusters(query, ref companyPatientClusters, ref msg);
            result.ReturnMsg = msg;
            result.ReturnData = companyPatientClusters;
            return Ok(result);
        }

        /// <summary>
        /// 获取单位体检人员项目汇总(单位体检人员组合)
        /// </summary>
        /// <param name="statisticalQueryBase">查询条件</param>
        /// <returns></returns>
        [HttpPost("GetCompanyPatientCombs")]
        [ProducesResponseType(typeof(CompanyPatientComb), 200)]
        public IActionResult GetCompanyPatientCombs(StatisticalQueryBase statisticalQueryBase)
        {
            CompanyPatientComb companyPatientComb = new();

            var msg = string.Empty;

            result.Success = _companyStatisticsService.GetCompanyPatientCombs(statisticalQueryBase, ref companyPatientComb, ref msg);

            result.ReturnMsg = msg;

            result.ReturnData = companyPatientComb;

            return Ok(result);
        }

        /// <summary>
        /// 获取单位项目工作量统计（单位组合费用/次数）
        /// </summary>
        /// <param name="statisticalQueryBase">查询条件</param>
        /// <returns></returns>
        [HttpPost("GetCompanyCombAmount")]
        [ProducesResponseType(typeof(CompanyCombStatistics), 200)]
        public IActionResult GetCompanyCombAmount(StatisticalQueryBase statisticalQueryBase)
        {
            CompanyCombStatistics company = new();
            var msg = string.Empty;
            result.Success = _companyStatisticsService.GetCompanyCombAmount(statisticalQueryBase, ref company, ref msg);
            result.ReturnMsg = msg;
            result.ReturnData = company;
            return Ok(result);
        }

        /// <summary>
        /// 单位人员汇总报表
        /// </summary>
        /// <param name="query">查询条件</param>
        /// <returns></returns>
        [HttpPost("CompanyPersonnelSummaryReport")]
        [ProducesResponseType(typeof(CompanyPersonnelSummary), 200)]
        public IActionResult CompanyPersonnelSummaryReport([FromBody] PersonnelSummaryQuery query)
        {
            CompanyPersonnelSummary data = new();
            var msg = string.Empty;
            result.Success = _companyStatisticsService.CompanyPersonnelSummaryReport(query, ref data, ref msg);
            result.ReturnMsg = msg;
            result.ReturnData = data;
            return Ok(result);
        }

        /// <summary>
        /// 单位人员明细报表
        /// </summary>
        /// <param name="query">查询条件</param>
        /// <returns></returns>
        [HttpPost("CompanyPersonnelDetailReport")]
        [ProducesResponseType(typeof(CompanyPersonnelDetail[]), 200)]
        public IActionResult CompanyPersonnelDetailReport([FromBody] CompanyPersonnelDetailQuery query)
        {
            var data = new CompanyPersonnelDetail[] { };
            var msg = string.Empty;
            result.Success = _companyStatisticsService.CompanyPersonnelDetailReport(query, ref data, ref msg);
            result.ReturnMsg = msg;
            result.ReturnData = data;
            return Ok(result);
        }

        /// <summary>
        /// 单位体检报告
        /// </summary>
        /// <param name="query">查询条件</param>
        /// <returns></returns>
        [HttpPost("CompanyPeReport")]
        [ProducesResponseType(typeof(CompanyPeReportData[]), 200)]
        public IActionResult CompanyPeReport([FromBody] CompanyPeReportQuery query)
        {
            var data = new CompanyPeReportData[] { };
            var msg = string.Empty;
            result.Success = _companyStatisticsService.CompanyPeReport(query, ref data, ref msg);
            result.ReturnMsg = msg;
            result.ReturnData = data;
            return Ok(result);
        }

        /// <summary>
        /// 获取单位人员组合费用明细报表（交叉报表）
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        [HttpPost("QueryCompanyDetialCombPrice")]
        [ProducesResponseType(typeof(MissingItemData), 200)]
        public IActionResult QueryCompanyDetialCombPrice([FromBody] QueryCompanyDetialCombPrice query)
        {
            var data = new MissingItemData();
            result.Success = _companyStatisticsService.QueryCompanyDetialCombPrice(query, ref data);
            result.ReturnData = data;
            return Ok(result);
        }

        /// <summary>
        /// 单位体检人员项目明细报表
        /// </summary>
        /// <param name="peItemResultListQuery"></param>
        /// <returns></returns>
        [HttpPost("PeItemResultDetailListReportQuery")]
        public IActionResult PeItemResultDetailListReportQuery([FromBody] PeItemResultDetailListReportQuery peItemResultListQuery)
        {
            string msg = string.Empty;
            PeItemResultDetailListReport report = new();
            result.Success = _companyStatisticsService.PeItemResultDetailList(peItemResultListQuery, ref report, ref msg);
            result.ReturnData = report;
            result.ReturnMsg = msg;
            return Ok(result);
        }

        /// <summary>
        /// 下载单位年度报告分析文件
        /// </summary>
        /// <param name="filter">过滤条件</param>
        /// <returns></returns>
        [HttpPost("DownloadCompanyYearAnalyseFile")]
        public IActionResult DownloadCompanyYearAnalyseFile([FromBody] CompanyYearAnalyseReportQuery filter)
        {
            var (stream, contentType) = _companyStatisticsService.GetCompanyYearAnalyseReportFile(filter);
            if (stream == null)
                return NoContent();

            return File(stream, contentType, 
                $"{filter.CompanyName}年度体检分析报告.{filter.RepFileType.ToString().ToLower()}");
        }
    }
}