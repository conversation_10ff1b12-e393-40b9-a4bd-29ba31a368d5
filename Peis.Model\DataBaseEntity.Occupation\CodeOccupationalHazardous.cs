﻿using Peis.Model.Other.PeEnum.Occupation;

namespace Peis.Model.DataBaseEntity.Occupation
{
    /// <summary>
    /// 危害因素表
    /// </summary>
    [SugarTable]
    public class CodeOccupationalHazardous
    {
        /// <summary>
        /// 危害因素代码
        /// </summary>
        [SugarColumn(Length = 10, IsPrimaryKey = true)]
        public string HazardousCode { get; set; }
        /// <summary>
        /// 危害因素名称
        /// </summary>
        [SugarColumn(Length = 100, IsNullable = false)]
        public string HazardousName { get; set; }
        /// <summary>
        /// 危害因素种类
        /// </summary>
        [SugarColumn(IsNullable = false)]
        public HazardousType HazardousType { get; set; }
        /// <summary>
        /// 是否主要粉尘
        /// </summary>
        [SugarColumn]
        public bool IsMainDust { get; set; }
        /// <summary>
        /// 是否其他危害因素
        /// </summary>
        [SugarColumn(IsNullable = false)]
        public bool IsOtherHazardous { get; set; }


        #region Ext
        /// <summary>
        /// 危害因素种类名称（显示用）
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public string HazardousTypeName { get => this.HazardousType.ToString(); }

        /// <summary>
        /// 是否选中（前端用）
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public bool IsSelected { set; get; }
        /// <summary>
        /// 推荐组合（根据在岗状态分类）
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public Dictionary<string, string[]> CombsByStatus { get; set; }

        /// <summary>
        /// 组合对应
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public List<MapOccupationHazardousComb> MapCombs { get; set; }
        #endregion
    }
}
