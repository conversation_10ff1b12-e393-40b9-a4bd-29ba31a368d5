﻿using Peis.External.Platform.Service.Service.IService.His;
using Peis.Model.DataBaseEntity.External;
using Peis.Model.Other.PeEnum;
using Peis.Model.ReportDataSource.GraphicReport;
using Peis.Repository.IRepository;
using Peis.Service.IService.ExternalSystem;

namespace Peis.External.Platform.Service.Service.ServiceImpl;

public class ShenShanOrderService : IExternalSystemOrderService
{
    private readonly IHisBillService _hisBillService;
    private readonly IExternalSystemApplyService _applyService;
    private readonly IRegisterRepository _registerRepository;
    private readonly ISplitTable _splitTable;
    private readonly IShenShanRegisterRepository _shenShanRegisterRepository;
    private readonly IReportFileRepository _shenShanReportRepository;

    public ShenShanOrderService(
        IHisBillService hisBillService,
        IExternalSystemApplyService applyService,
        IRegisterRepository registerRepository,
        ISplitTable splitTable,
        IShenShanRegisterRepository shenShanRegisterRepository,
        IReportFileRepository shenShanReportRepository)
    {
        _hisBillService = hisBillService;
        _applyService = applyService;
        _registerRepository = registerRepository;
        _splitTable = splitTable;
        _shenShanRegisterRepository = shenShanRegisterRepository;
        _shenShanReportRepository = shenShanReportRepository;
    }

    public void SyncOrder(PeRegister register)
    {
        // 平台：患者建档接口 registPat
        // 平台：接收体检记录信息 medicalRecord

        // 由于体检发起的预退费，退费还需要另外做接口和前端走流程，下面是未收费的账单、申请单流程

        // 收费相关（表：计价与取消计价） jp
        // 申请单相关（表：添加申请单和删除申请单） cq

        // 平台：体检医嘱信息变更 synchPEModifyNotice

        var curCombs = _registerRepository.ReadRegisterCombs(register.RegNo).ToArray();
        long[] oldCombs;
        if (!register.IsCompanyCheck)
        {
            oldCombs = _splitTable.GetTableOrDefault<ExtHisBillComb>(register.RegisterTime)
            .Where(x => x.RegNo == register.RegNo)
            .Select(x => x.RegCombId)
            .ToArray();
        }
        else
        {
            var oldApply = _splitTable.GetTableOrDefault<ExtHisApplyComb>(register.RegisterTime)
            .Where(x => x.RegNo == register.RegNo)
            .Select(x => x.RegCombId)
            .ToArray();
            var oldBill = _splitTable.GetTableOrDefault<ExtHisBillComb>(register.RegisterTime)
            .Where(x => x.RegNo == register.RegNo)
            .Select(x => x.RegCombId)
            .ToArray();
            oldCombs = new HashSet<long>(oldApply).Union(new HashSet<long>(oldBill)).ToArray();
        }
        // 对比差异（增加的项目、删除的项目）
        var peCombAdds = curCombs.Where(x => !oldCombs.Any(sq => sq == x.Id) && x.PayStatus == PayStatus.未收费).ToArray();
        var peCombDels = oldCombs.Where(x => !curCombs.Any(sq => sq.Id == x)).Select(x => x).ToArray();

        // 添加门诊推送队列任务
        _hisBillService.SyncBillInfo(register, peCombAdds, peCombDels);
        _applyService.SyncApply(register, peCombAdds, peCombDels);
    }

    public void SyncOrder(string regNo)
    {
        var register = _registerRepository.ReadRegisterNoHosp(regNo).First();
        SyncOrder(register);
    }

    public void ClearOrder(string regNo)
    {
        // 不允许删除存在已收费订单
        // 不允许删除已检查的申请单的订单
        // 清空账单
        // 清空申请单

        // 待沟通：后续怎么删除His、平台和第三方的记录，团体应该是需要这个功能的
    }

    public void SyncOrderPayStatus(string regNo)
    {
        // 这边是等待第三方调用储存过程，所以需要另外实现
        return;
    }

    /// <summary>
    /// 获取门诊卡号
    /// </summary>
    /// <param name="regNo"></param>
    /// <returns></returns>
    public string GetHisCard(string regNo)
    {
        return _shenShanRegisterRepository.GetHisCard(regNo);
    }

    public decimal GetHisPrice(string regNo)
    {
        return _shenShanRegisterRepository.GetHisPrice(regNo);
    }

    public GraphicReportPath[] GetImageReport(string regNo)
    {
        var images = _shenShanReportRepository.QueryPeRecordPartFiles(regNo).Select(x => new { CombCode = x.CombCode, RegCombId = x.RegCombId, ReportPath = x.FilePath }).ToList();
        var combs = _registerRepository.ReadRegisterCombs(regNo).Select(x => new { Id = x.Id, ClsSortIndex = x.ClsSortIndex, CombSortIndex = x.CombSortIndex }).ToList();
        var list = from image in images
                   join comb in combs on image.RegCombId equals comb.Id
                   orderby comb.ClsSortIndex, comb.CombSortIndex
                   select new GraphicReportPath
                   {
                       CombCode = image.CombCode,
                       ReportPath = image.ReportPath
                   };
        return list.ToArray();
    }

    public bool OrderRefund(string regNo, long[] deleteIds, out string msg)
    {
        return _hisBillService.OrderRefund(regNo, deleteIds, out msg);
    }

    public bool CancelOrderRefund(string regNo, out string msg)
    {
        return _hisBillService.CancelOrderRefund(regNo, out msg);
    }
}

