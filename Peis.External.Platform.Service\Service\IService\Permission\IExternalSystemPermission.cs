﻿using Peis.Model.DTO.External.Permission;

namespace Peis.External.Platform.Service.Service.IService.Permission;

/// <summary>
/// 第三方扩展系统权限接口
/// </summary>
public interface IExternalSystemPermission
{
    /// <summary>
    /// 校验统一门户Token 并获取登录用户信息。
    /// </summary>
    /// <param name="token">统一门户token</param>
    /// <param name="msg">消息</param>
    /// <returns>UserInfoExternalDto</returns>
    UserInfoExternalDto VerifyToken(string token, out string msg);

    /// <summary>
    /// 根据统一门户Token登录
    /// </summary>
    /// <param name="token">统一门户token</param>
    /// <param name="msg">消息</param>
    /// <returns>object</returns>
    object Login(string token, out string msg);
}

public class DefaultExternalSystemPermissionService : IExternalSystemPermission
{
    public object Login(string token, out string msg)
    {
        msg = ResxCommon.Fail;
        return null;
    }

    public UserInfoExternalDto VerifyToken(string token, out string msg)
    {
        msg = ResxCommon.Fail;
        return null;
    }
}
