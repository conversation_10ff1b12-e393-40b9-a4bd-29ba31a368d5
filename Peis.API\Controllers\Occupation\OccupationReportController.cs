﻿using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Peis.Model.DTO.DataQuery;
using Peis.Model.DTO.Occupation.ReportTemplate;
using Peis.Service.IService.Occupation;
using Peis.Utility.Enums;
using Peis.Utility.Helper;
using System.IO;

namespace Peis.API.Controllers.Occupation
{
    /// <summary>
    /// 职业病导出报表相关接口
    /// </summary>
    [Route("api/Occupation/[controller]")]
    [ApiController]
    public class OccupationReportController : BaseApiController
    {
        private readonly IOccupationReportService _occupationReportService;

        public OccupationReportController(IOccupationReportService occupationReportService)
        {
            _occupationReportService = occupationReportService;
        }

        /// <summary>
        /// 获取疑似职业病/职业禁忌证/复查导出人员列表
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        [HttpPost("GetExportPersonList")]
        [ProducesDefaultResponseType(typeof(ExportPersonData[]))]
        public IActionResult GetExportPersonList([FromBody] ExportPersonQuery query)
        {
            result.ReturnData = _occupationReportService.GetExportPersonList(query);
            result.Success = true;
            return Ok(result);
        }

        /// <summary>
        /// 疑似职业病告知书
        /// </summary>
        /// <param name="regNos"></param>
        /// <param name="fileFormat">文件格式</param>
        /// <returns></returns>
        [HttpPost("GetOccupationalDiseaseNoticeTemplate/{fileFormat?}")]
        public IActionResult GetOccupationalDiseaseNoticeTemplate([FromBody] string[] regNos, OfficeFormatType fileFormat = OfficeFormatType.Docx)
        {
            var stream = _occupationReportService.OccupationalDiseaseNoticeTemplate(regNos);
            return FileConvert(fileFormat, stream, "疑似职业病告知书");
        }

        /// <summary>
        /// 疑似职业病报告卡
        /// </summary>
        /// <param name="regNos"></param>
        /// <param name="fileFormat">文件格式</param>
        /// <returns></returns>
        [HttpPost("GetOccupationalDiseaseCardTemplate/{fileFormat?}")]
        public IActionResult GetOccupationalDiseaseCardTemplate([FromBody] string[] regNos, OfficeFormatType fileFormat = OfficeFormatType.Docx)
        {
            var stream = _occupationReportService.OccupationalDiseaseCardTemplate(regNos);
            return FileConvert(fileFormat, stream, "疑似职业病报告卡");
        }

        /// <summary>
        /// 职业禁忌证告知书
        /// </summary>
        /// <param name="regNos"></param>
        /// <param name="fileFormat">文件格式</param>
        /// <returns></returns>
        [HttpPost("OccupationalContraindicationNoticeTemplate/{fileFormat?}")]
        public IActionResult GetOccupationalContraindicationNoticeTemplate([FromBody] string[] regNos, OfficeFormatType fileFormat = OfficeFormatType.Docx)
        {
            var stream = _occupationReportService.OccupationalContraindicationNoticeTemplate(regNos);
            return FileConvert(fileFormat, stream, "职业禁忌证告知书");
        }

        /// <summary>
        /// 职业禁忌证报告卡
        /// </summary>
        /// <param name="regNos"></param>
        /// <param name="fileFormat">文件格式</param>
        /// <returns></returns>
        [HttpPost("GetOccupationalContraindicationCardTemplate/{fileFormat?}")]
        public IActionResult GetOccupationalContraindicationCardTemplate([FromBody] string[] regNos, OfficeFormatType fileFormat = OfficeFormatType.Docx)
        {
            var stream = _occupationReportService.OccupationalContraindicationCardTemplate(regNos);
            return FileConvert(fileFormat, stream, "职业禁忌证报告卡");
        }

        /// <summary>
        /// 复查通知书
        /// </summary>
        /// <param name="regNos"></param>
        /// <param name="fileFormat">文件格式</param>
        /// <returns></returns>
        [HttpPost("GetOccupationalRecheckNoticeTemplate/{fileFormat?}")]
        public IActionResult GetOccupationalRecheckNoticeTemplate([FromBody] string[] regNos, OfficeFormatType fileFormat = OfficeFormatType.Docx)
        {
            var stream = _occupationReportService.OccupationalRecheckNoticeTemplate(regNos);
            return FileConvert(fileFormat, stream, "复查通知书");
        }

        /// <summary>
        /// 重点职业病监测报表
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        [HttpPost("GetImportantMonitorItemReport")]
        [ProducesResponseType(typeof(MissingItemData), StatusCodes.Status200OK)]
        public IActionResult GetImportantMonitorItemReport([FromBody] ImportantMonitorItemQuery query)
        {
            int totalNumber = 0;
            int totalPage = 0;
            result.ReturnData = _occupationReportService.ImportantMonitorItemReport(query, ref totalNumber, ref totalPage);
            result.TotalNumber = totalNumber;
            result.TotalPage = totalPage;
            return Ok(result);
        }


        /// <summary>
        /// 职业病单位总结报告
        /// </summary>
        /// <param name="query"></param>
        /// <param name="fileFormat">文件格式</param>
        /// <returns></returns>
        [HttpPost("GetOccupationalFinalReport/{fileFormat?}")]
        public IActionResult OccupationalFinalReport([FromBody] CompanyReportQuery query, OfficeFormatType fileFormat = OfficeFormatType.Docx)
        {
            var stream = _occupationReportService.OccupationalFinalReport(query.CompanyCode, query.CompanyTimes);
            return FileConvert(fileFormat, stream, "年度总结报告");
        }


        #region Private Methods
        /// <summary>
        ///  文件转换，默认文件流为docx格式  <br/>
        ///  1. docx格式直接返回文件流   <br/>
        ///  2. docx格式转换为pdf格式  <br/>
        /// </summary>
        /// <param name="fileFormat"></param>
        /// <param name="stream"></param>
        /// <param name="fileName"></param>
        /// <returns></returns>
        IActionResult FileConvert(OfficeFormatType fileFormat, Stream stream, string fileName)
        {
            if (stream == null || stream.Length == 0) return NotFound();

            return fileFormat switch
            {
                OfficeFormatType.Docx => File(stream, FileHelper.ReponseContentType(fileFormat), $"{fileName}.docx"),
                OfficeFormatType.Pdf => File(WordHelper.ConvertPdf(stream), FileHelper.ReponseContentType(fileFormat), $"{fileName}.pdf"),
                _ => NotFound(),
            };
        }
        #endregion
    }
}
