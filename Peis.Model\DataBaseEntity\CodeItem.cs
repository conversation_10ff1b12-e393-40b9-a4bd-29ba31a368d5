﻿namespace Peis.Model.DataBaseEntity
{
    ///<summary>
    ///项目代码
    ///</summary>
    [SugarTable("CodeItem")]
    public class CodeItem
    {
        /// <summary>
        /// 项目代码
        /// </summary>        
        [SugarColumn(IsNullable = false, IsPrimaryKey = true, Length = 6)]
        public string ItemCode { get; set; }

        /// <summary>
        /// 项目名称
        /// </summary>        
        [SugarColumn(IsNullable = false, Length = 200)]
        public string ItemName { get; set; }

        /// <summary>
        /// 项目分类
        /// </summary>        
        [SugarColumn(IsNullable = false, Length = 6)]
        public string ClsCode { get; set; }

        /// <summary>
        /// 结果单位
        /// </summary>        
        [SugarColumn(IsNullable = true, Length = 20)]
        public string Unit { get; set; }

        /// <summary>
        /// 参考值
        /// </summary>        
        [SugarColumn(IsNullable = true, Length = 30)]
        public string ReferenceValue { get; set; }

        /// <summary>
        /// 结果类型（0字符1数值2复合型）
        /// </summary>        
        [SugarColumn(IsNullable = false)]
        public Other.PeEnum.ValueType ValueType { get; set; }

        /// <summary>
        /// 启用标识
        /// </summary>        
        [SugarColumn(IsNullable = false)]
        public bool IsEnabled { get; set; }

        /// <summary>
        /// 报告中显示
        /// </summary>        
        [SugarColumn(IsNullable = false)]
        public bool ReportShow { get; set; }

        /// <summary>
        /// 显示顺序
        /// </summary>        
        [SugarColumn(IsNullable = false)]
        public int SortIndex { get; set; }

        /// <summary>
        /// 拼音码
        /// </summary>        
        [SugarColumn(IsNullable = true, Length = 200)]
        public string PinYinCode { get; set; }

        /// <summary>
        /// 五笔码
        /// </summary>        
        [SugarColumn(IsNullable = true, Length = 200)]
        public string WuBiCode { get; set; }

        /// <summary>
        /// His码
        /// </summary>        
        [SugarColumn(IsNullable = true, Length = 50)]
        public string HisCode { get; set; }
        /// <summary>
        /// 职业病项目代码
        /// </summary>
        [SugarColumn(IsIgnore =true)]
        public string OccupationalItemCode { get; set; }

        /// <summary>
        /// 职业病项目名称
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public string OccupationalItemName { get; set; }
    }
}