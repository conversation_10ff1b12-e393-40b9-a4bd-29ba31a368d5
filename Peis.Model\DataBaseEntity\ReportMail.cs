﻿using SqlSugar;
using System;

namespace Peis.Model.DataBaseEntity
{
    ///<summary>
    ///报告邮寄表
    ///</summary>
    [SugarTable("ReportMail")]
    public class ReportMail
    {
        /// <summary>
        /// 体检号
        /// </summary>        
        [SugarColumn(IsNullable = false, IsPrimaryKey = true, Length = 12)]
        public string RegNo { get; set; }

        /// <summary>
        /// 邮寄地址
        /// </summary>        
        [SugarColumn(IsNullable = false, Length = 150)]
        public string MailAddress { get; set; }

        /// <summary>
        /// 快递公司
        /// </summary>        
        [SugarColumn(IsNullable = false, Length = 20)]
        public string ExpressCompany { get; set; }

        /// <summary>
        /// 快递单号
        /// </summary>        
        [SugarColumn(IsNullable = false, Length = 15)]
        public string ExpressNo { get; set; }

        /// <summary>
        /// 联系电话
        /// </summary>        
        [SugarColumn(IsNullable = true, Length = 15)]
        public string ContactTel { get; set; }

        /// <summary>
        /// 是否邮寄标识
        /// </summary>        
        [SugarColumn(IsNullable = false)]
        public bool IsSend { get; set; }

        /// <summary>
        /// 寄出时间
        /// </summary>        
        [SugarColumn(IsNullable = false)]
        public DateTime SendTime { get; set; }
    }
}