﻿namespace Peis.Model.DTO.DataQuery
{
    /// <summary>
    /// 
    /// </summary>
    public class PeReportProcessAnalyzeData
    {
        /// <summary>
        /// 间隔小于3天数量
        /// </summary>
        public int CountLessThanThree { set; get; }

        /// <summary>
        /// 间隔3-6天数量
        /// </summary>
        public int CountThreeToSix { set; get; }

        /// <summary>
        /// 间隔6-9天数量
        /// </summary>
        public int CountSixToNine { set; get; }

        /// <summary>
        /// 间隔9-12天数量
        /// </summary>
        public int CountNineToTwelve { set; get; }

        /// <summary>
        /// 间隔大于12天数量
        /// </summary>
        public int CountMoreThanTwelve { set; get; }

        /// <summary>
        /// 总数量
        /// </summary>
        public int CountAllReport { set; get; }

        /// <summary>
        /// 中位天数
        /// </summary>
        public int MedianDays { set; get; }

        /// <summary>
        /// 90分位天数
        /// </summary>
        public int NinetyPercentDays { set; get; }

        /// <summary>
        /// 最大天数
        /// </summary>
        public int MaxDays { set; get; }

        /// <summary>
        /// 最小天数
        /// </summary>
        public int MinDays { set; get; }

        /// <summary>
        /// 平均天数
        /// </summary>
        public double AvgDays { set; get; }
    }
}
