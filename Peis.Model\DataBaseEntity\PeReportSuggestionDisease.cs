using Newtonsoft.Json;
using Peis.Model.DTO.ReportConclusionNew;

namespace Peis.Model.DataBaseEntity
{
    ///<summary>
    ///体检建议疾病标签表
    ///</summary>
    [SplitTable(SplitType.Year)]
    [SugarTable("PeReportSuggestionDisease_{yyyy}", "体检建议疾病标签表")]
    [SugarIndex("index_SuggId_", nameof(SuggId), OrderByType.Asc)]
    [SugarIndex("index_RegNo_", nameof(RegNo), OrderByType.Asc)]
    public class PeReportSuggestionDisease
    {
        /// <summary>
        /// 疾病标签Id
        /// </summary>        
        [SugarColumn(IsNullable = false, IsPrimaryKey = true)]
        public long Id { get; set; }

        /// <summary>
        /// 建议id
        /// </summary>        
        [SugarColumn(IsNullable = false)]
        public long SuggId { get; set; }

        /// <summary>
        /// 体检号
        /// </summary>        
        [SugarColumn(IsNullable = false, Length = 12)]
        public string RegNo { get; set; }

        /// <summary>
        /// 疾病代码
        /// </summary>        
        [SugarColumn(IsNullable = false, Length = 255)]
        public string DiseaseCode { get; set; }

        /// <summary>
        /// 疾病标签
        /// </summary>        
        [SugarColumn(IsNullable = false, ColumnDataType = StaticConfig.CodeFirst_BigString)]
        public string DiseaseTag { get; set; }

        /// <summary>
        /// 疾病标签-备注
        /// </summary>        
        [SugarColumn(IsNullable = true, Length = 256)]
        public string DiseaseTagRemark { get; set; }

        /// <summary>
        /// 绑定综述标签列表
        /// </summary>        
        [SugarColumn(IsNullable = false, ColumnDataType = StaticConfig.CodeFirst_BigString)]// 原长度250，但因为“未见明显异常”标签绑定的id太多，这属于流程设计上的问题
        public string BindSummTagIds { get; set; }

        /// <summary>
        /// 体检登记时间（分表依据）
        /// </summary>        
        [SplitField]
        [SugarColumn(IsNullable = false)]
        public DateTime RegisterTime { get; set; }

        /// <summary>
        /// 是否职业病
        /// </summary>
        [SugarColumn(IsNullable = false)]
        public bool IsOccupaiton { get; set; }

        /// <summary>
        /// 排序
        /// </summary>
        [SugarColumn(IsNullable = false, DefaultValue = "0")]
        public int SortIndex { get; set; }

        public List<SummTagMach> combindTagList()
        {
            try
            {
                return JsonConvert.DeserializeObject<List<SummTagMach>>(BindSummTagIds);
            }
            catch
            {
                return new();
            }
        }


    }
}