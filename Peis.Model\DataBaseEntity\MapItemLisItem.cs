﻿using SqlSugar;

namespace Peis.Model.DataBaseEntity
{
    ///<summary>
    ///检验项目对应
    ///</summary>
    [SugarTable("MapItemLisItem")]
    public class MapItemLisItem
    {
        /// <summary>
        /// 体检项目代码
        /// </summary>        
        [SugarColumn(IsNullable = false, IsPrimaryKey = true, Length = 6)]
        public string ItemCode { get; set; }

        /// <summary>
        /// 体检项目名称
        /// </summary>        
        [SugarColumn(IsNullable = false, Length = 200)]
        public string ItemName { get; set; }

        /// <summary>
        /// 检验项目代码
        /// </summary>        
        [SugarColumn(IsNullable = false, IsPrimaryKey = true, Length = 10)]
        public string LisItemCode { get; set; }

        /// <summary>
        /// 检验项目名称
        /// </summary>        
        [SugarColumn(IsNullable = false, Length = 200)]
        public string LisItemName { get; set; }

        /// <summary>
        /// 专业组代码
        /// </summary>        
        [SugarColumn(IsNullable = false, IsPrimaryKey = true, Length = 10)]
        public string LisRoomCode { get; set; }

        /// <summary>
        /// 专业组名称
        /// </summary>        
        [SugarColumn(IsNullable = false, Length = 50)]
        public string LisRoomName { get; set; }
    }
}