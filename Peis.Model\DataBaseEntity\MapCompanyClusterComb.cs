﻿using Peis.Model.Other.PeEnum;

namespace Peis.Model.DataBaseEntity;

///<summary>
///单位套餐组合对应
///</summary>
[SugarTable("MapCompanyClusterComb")]
public class MapCompanyClusterComb
{
    /// <summary>
    /// 套餐代码
    /// </summary>        
    [SugarColumn(IsNullable = false, IsPrimaryKey = true, Length = 15)]
    public string ClusterCode { get; set; }

    /// <summary>
    /// 组合代码
    /// </summary>        
    [SugarColumn(IsNullable = false, IsPrimaryKey = true, Length = 8)]
    public string CombCode { get; set; }

    /// <summary>
    /// 原始单价
    /// </summary>        
    [SugarColumn(IsNullable = false, Length = 12, DecimalDigits = 2)]
    public decimal OriginalPrice { get; set; }
    /// <summary>
    /// 单价
    /// </summary>        
    [SugarColumn(IsNullable = false, Length = 12, DecimalDigits = 2)]
    public decimal Price { get; set; }

    /// <summary>
    /// 折扣率
    /// </summary>        
    [SugarColumn(IsNullable = false, Length = 12, DecimalDigits = 2)]
    public decimal Discount { get; set; }

    /// <summary>
    /// 是否自费
    /// </summary>        
    [SugarColumn(IsNullable = false)]
    public bool IsPayBySelf { get; set; }

    #region Ext
    /// <summary>
    /// 组合名称
    /// </summary>        
    [SugarColumn(IsIgnore = true)]
    public string CombName { get; set; }

    /// <summary>
    /// 性别
    /// </summary>
    [SugarColumn(IsIgnore = true)]
    public Sex Sex { get; set; }

    /// <summary>
    /// 数量
    /// </summary>
    [SugarColumn(IsIgnore = true)]
    public int Count { get; set; }

    /// <summary>
    /// 是否检查组合
    /// </summary>
    [SugarColumn(IsIgnore = true)]
    public bool IsCheckComb { get; set; }

    /// <summary>
    /// 来源组合
    /// </summary>
    [SugarColumn(IsIgnore = true)]
    public string BeFrom { get; set; }
    #endregion
}