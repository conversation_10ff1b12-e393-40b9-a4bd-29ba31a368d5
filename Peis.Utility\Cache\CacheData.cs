﻿using System;

namespace Peis.Utility.Cache;

/*
public class CacheData : IDisposable
{
    public object Value { get; set; }
    public IProvider Refresher { get; set; }
    public int Time { get; set; }

    public CacheData(object value, IProvider refresher, int time)
    {
        Value = value;
        Refresher = refresher;
        Time = time;
    }

    public object Refresh()
    {
        Value = Refresher.Refresh();
        return Value;
    }

    public void Dispose()
    {
        GC.SuppressFinalize(this);
    }
}
*/