﻿using Microsoft.Extensions.Logging;
using Peis.External.Platform.Service.Service.IService.BaiHui;
using Peis.Model.DTO.External.BaiHui;
using System.Net.Http.Headers;
using System.Net.Mime;

namespace Peis.External.Platform.Service.Service.ServiceImpl.BaiHui;

/// <summary>
/// 百慧平台：基础服务实现
/// </summary>
public class BHBasicService : IBHBasicService
{
    /// <summary>
    /// 尝试次数
    /// </summary>
    int tryNum = 0;

    /// <summary>
    /// token-key（token失效期为43200秒(12小时)
    /// </summary>
    const string BHTOKENKEY = "BH:token-key";

    readonly IHttpClientHelper _httpClientHelper;
    readonly ILogger<BHBasicService> _logger;

    public BHBasicService(IHttpClientHelper httpClientHelper, ILogger<BHBasicService> logger)
    {
        _httpClientHelper = httpClientHelper;
        _logger = logger;
        tryNum = 2;
    }

    #region token
    /// <summary>
    /// 获取Token
    /// </summary>
    /// <returns>string</returns>
    /// <exception cref="BusinessException"></exception>
    public string GetToken()
    {
        // 读取内存
        if (CacheHelper.TryGet(BHTOKENKEY, out string token))
        {
            // 缓存中存在该键
            return token;
        }

        // 读取远程
        string content = string.Format(ResxExternal.BHApiGetTokenMsg, ResxExternal.BHApiKey);
        string postContent = _httpClientHelper.PostStrContentAsync(ResxExternal.BHApiUrlGetToken, content, new MediaTypeHeaderValue(MediaTypeNames.Application.Xml)).Result;
        if (string.IsNullOrWhiteSpace(postContent))
            throw new BusinessException("http response message is null or empty！");

        // <result getToken="yes">c6cec975-b18d-4ad5-bb34-b755f66ed4cf</result>
        _logger.LogInformation("BH token:{BHToken}", postContent);
        XmlElement resultXml = XmlHelper.Deserialize<XmlElement>(postContent);
        token = resultXml.InnerText;

        if (string.IsNullOrWhiteSpace(token))
            throw new BusinessException("token is null or empty！");

        // 保存token至内存
        CacheHelper.Set(BHTOKENKEY, token, TimeSpan.FromHours(11));

        return token;
    }

    /// <summary>
    /// 清除缓存中的token
    /// </summary>
    public void ClearToken()
    {
        CacheHelper.Remove(BHTOKENKEY);
    }
    #endregion

    #region MsgTransfer
    /// <summary>
    /// PostMsgTransfer请求
    /// </summary>
    /// <param name="requestMsg">BHRequestMsg-入参内容</param>
    /// <returns>BHApiReturn</returns>
    public BHApiReturn<T> PostMsgTransfer<T>(BHRequestMsg requestMsg)
    {
        BHApiReturn<T> resInfo;
        void postFunc()
        {
            // 设置token
            string token = GetToken();
            requestMsg.SetToken(token);
            if (requestMsg.IsPostXml)
                resInfo = PostXmlMsgTransfer<T>(requestMsg);
            else
                resInfo = PostJsonMsgTransfer<T>(requestMsg);
        }

        postFunc();
        if (resInfo.IsTokenErr)
        {
            // token 错误，尝试再次请求
            _logger.LogInformation("token error，try get token and again post msgTransfer");
            while (tryNum > 0)
            {
                tryNum--;
                ClearToken();
                postFunc();
                if (resInfo.IsSuccess) return resInfo;
            }
        }

        if (!resInfo.IsSuccess)
            throw new BusinessException(resInfo.ErrorInfo ?? ResxCommon.Fail);

        return resInfo;
    }

    /// <summary>
    /// PostMsgTransfer 分页数据请求
    /// </summary>
    /// <param name="requestMsg">BHRequestMsg-入参内容</param>
    /// <returns>BHApiPageResult</returns>
    /// <exception cref="BusinessException"></exception>
    public BHApiPageResult<BHApiPageData<T>> PostMsgTransferPage<T>(BHRequestMsg requestMsg)
    {
        requestMsg.IsEnableLog = false; // 数据量多，屏蔽记录日志
        var resInfo = PostMsgTransfer<string>(requestMsg);
        var resDataInfo = XmlHelper.Deserialize<BHApiPageResult<BHApiPageData<T>>>(resInfo.SyncResult);
        if (!resDataInfo.IsSuccess)
        {
            _logger.LogError("BH return error:{BusinessError}", XmlHelper.Serialize(resInfo));
            throw new BusinessException(resDataInfo.ResultContent ?? ResxCommon.Fail);
        }

        return resDataInfo;
    }

    /// <summary>
    /// post Xml内容
    /// </summary>
    /// <param name="requestMsg">BHRequestMsg</param>
    /// <returns>BHApiReturn</returns>
    /// <exception cref="BusinessException"></exception>
    private BHApiReturn<T> PostXmlMsgTransfer<T>(BHRequestMsg requestMsg)
    {
        string data = XmlHelper.Serialize(requestMsg);
        data = XmlHelper.Decode(data);
        if (requestMsg.IsEnableLog)
            _logger.LogInformation("POST sending {RequestUrl} {RequestBody}", ResxExternal.BHApiUrlMsgTransfer, data);

        // 发起post请求
        string postContent = _httpClientHelper.PostStrContentAsync(ResxExternal.BHApiUrlMsgTransfer, data,
            new MediaTypeHeaderValue(MediaTypeNames.Application.Xml)).Result;
        if (string.IsNullOrWhiteSpace(postContent))
            throw new BusinessException("http response message is null or empty！");

        if (requestMsg.IsEnableLog)
            _logger.LogInformation("POST response {ResponseBody}", postContent);

        var resInfo = XmlHelper.Deserialize<BHApiReturn<T>>(postContent);
        return resInfo;
    }

    /// <summary>
    /// post Json内容
    /// </summary>
    /// <param name="requestMsg">BHRequestMsg</param>
    /// <returns>BHApiReturn</returns>
    /// <exception cref="BusinessException"></exception>
    private BHApiReturn<T> PostJsonMsgTransfer<T>(BHRequestMsg requestMsg)
    {
        var data = requestMsg.ToJson();
        if (requestMsg.IsEnableLog)
            _logger.LogInformation("POST sending {RequestUrl} {RequestBody}", ResxExternal.BHApiUrlMsgTransfer, data);

        // 发起post请求
        string postContent = _httpClientHelper.PostStrContentAsync(ResxExternal.BHApiUrlMsgTransfer, data,
            new MediaTypeHeaderValue(MediaTypeNames.Application.Json)).Result;
        if (string.IsNullOrWhiteSpace(postContent))
            throw new BusinessException("http response message is null or empty！");

        if (requestMsg.IsEnableLog)
            _logger.LogInformation("POST response {ResponseBody}", postContent);

        var resInfo = JsonHelper.ToObject<BHApiReturn<T>>(postContent);
        return resInfo;
    }
    #endregion

    #region Opss
    /// <summary>
    /// 发起Opss接口Post请求
    /// </summary>
    /// <typeparam name="T">请求参数</typeparam>
    /// <param name="reqMsg">请求内容</param>
    /// <returns>业务级BHApiOpssContent</returns>
    /// <exception cref="BusinessException"></exception>
    public BHApiOpssContent PostOpss<T>(BHApiOpssReqMsg<T> reqMsg)
    {
        reqMsg.NotNullAndEmpty(nameof(reqMsg));
        // ============== 请求头内容SM3加密 ================
        var opssHeader = new BHApiOpssHeader(reqMsg.MethodCode);
        var headers = opssHeader.ToReqHeaders();

        // ============== 请求报文SM4加密 ================
        reqMsg.GenerSM4Content();

        // ============== 发起Post请求 ================
        var mediaType = new MediaTypeHeaderValue(MediaTypeNames.Text.Plain);
        string postContent = _httpClientHelper.PostStrContentAsync(ResxExternal.BHApiOpssUrl, reqMsg.SM4Content, mediaType, headers).Result;
        if (string.IsNullOrWhiteSpace(postContent))
            throw new BusinessException("http response message is null or empty！");

        var apiResInfo = postContent.ToObject<BHApiOpssReturn>();
        if (!apiResInfo.IsSuccess)
            throw new BusinessException(apiResInfo.ErrorMsg);

        // SM4内容解密
        var decContent = BHSecurityHelper.SM4DecryptECB(apiResInfo.Content, ResxExternal.BHApiOpssKey);
        var busResInfo = XmlHelper.Deserialize<BHApiOpssContent>(decContent);

        return busResInfo;
    }
    #endregion
}

