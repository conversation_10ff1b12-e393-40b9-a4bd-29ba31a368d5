using Peis.Model.DataBaseEntity;
using Peis.Repository.IRepository;
using SqlSugar;
using System;
using System.Collections.Generic;
using System.Linq;

namespace Peis.Repository.Repository
{
    /// <summary>
    /// 疾病数据访问实现
    /// </summary>
    public class DiseaseRepository : IDiseaseRepository
    {
        private readonly ISqlSugarClient _db;

        public DiseaseRepository(ISqlSugarClient db)
        {
            _db = db;
        }

        /// <summary>
        /// 获取项目结果绑定的疾病信息
        /// </summary>
        /// <param name="itemCodes">项目代码数组</param>
        /// <returns>项目结果疾病信息</returns>
        public List<ItemResultDiseaseInfo> GetItemResultDiseases(string[] itemCodes)
        {
            try
            {
                if (itemCodes == null || itemCodes.Length == 0)
                    return new List<ItemResultDiseaseInfo>();

                return _db.Queryable<CodeItemResult>()
                    .LeftJoin<CodeDisease>((result, disease) => result.DiseaseCode == disease.DiseaseCode)
                    .Where((result, disease) => SqlFunc.ContainsArray(itemCodes, result.ItemCode))
                    .Select((result, disease) => new ItemResultDiseaseInfo
                    {
                        ItemCode = result.ItemCode,
                        ResultDesc = result.ResultDesc,
                        DiseaseCode = result.DiseaseCode,
                        DiseaseName = disease.DiseaseName,
                        AbnormalType = result.AbnormalType
                    })
                    .ToList();
            }
            catch (Exception)
            {
                return new List<ItemResultDiseaseInfo>();
            }
        }

        /// <summary>
        /// 获取疾病审核条件项目信息
        /// </summary>
        /// <param name="itemCodes">项目代码数组</param>
        /// <returns>疾病审核条件项目信息</returns>
        public List<DiseaseCriteriaItemDto> GetDiseaseCriteriaItems(string[] itemCodes)
        {
            try
            {
                if (itemCodes == null || itemCodes.Length == 0)
                    return new List<DiseaseCriteriaItemDto>();

                return _db.Queryable<CodeDiseaseCriteriaItem>()
                    .LeftJoin<CodeDisease>((criteria, disease) => criteria.DiseaseCode == disease.DiseaseCode)
                    .Where((criteria, disease) => SqlFunc.ContainsArray(itemCodes, criteria.ItemCode))
                    .Select((criteria, disease) => new DiseaseCriteriaItemDto
                    {
                        DiseaseCode = criteria.DiseaseCode,
                        DiseaseName = disease.DiseaseName,
                        ItemCode = criteria.ItemCode,
                        Operator = criteria.Operator,
                        Value = criteria.Value,
                        ValueType = criteria.ValueType
                    })
                    .ToList();
            }
            catch (Exception)
            {
                throw;
            }
        }

        /// <summary>
        /// 获取疾病审核条件逻辑信息
        /// </summary>
        /// <param name="diseaseCodes">疾病代码数组</param>
        /// <returns>疾病代码和逻辑关系的字典</returns>
        public Dictionary<string, int> GetDiseaseCriteriaLogics(string[] diseaseCodes)
        {
            try
            {
                if (diseaseCodes == null || diseaseCodes.Length == 0)
                    return new Dictionary<string, int>();

                var criteriaList = _db.Queryable<CodeDiseaseCriteria>()
                    .Where(x => SqlFunc.ContainsArray(diseaseCodes, x.DiseaseCode))
                    .ToList();

                return criteriaList.ToDictionary(x => x.DiseaseCode, x => x.Logic);
            }
            catch (Exception)
            {
                throw;
            }
        }

        /// <summary>
        /// 获取疾病表达式代码
        /// </summary>
        /// <param name="itemCodes">项目代码数组</param>
        /// <returns>表达式代码数组</returns>
        public string[] GetDiseaseExpressionCodes(string[] itemCodes)
        {
            try
            {
                if (itemCodes == null || itemCodes.Length == 0)
                    return Array.Empty<string>();

                return _db.Queryable<CodeDiseaseExpressionItem>()
                    .Where(x => SqlFunc.ContainsArray(itemCodes, x.ItemCode))
                    .Select(x => x.ExpCode)
                    .ToArray();
            }
            catch (Exception)
            {
                throw;
            }
        }

        /// <summary>
        /// 获取疾病表达式详细信息
        /// </summary>
        /// <param name="expCodes">表达式代码数组</param>
        /// <returns>疾病表达式信息</returns>
        public List<DiseaseExpressionInfo> GetDiseaseExpressions(string[] expCodes)
        {
            try
            {
                if (expCodes == null || expCodes.Length == 0)
                    return new List<DiseaseExpressionInfo>();

                return _db.Queryable<CodeDiseaseExpression>()
                    .InnerJoin<CodeDiseaseExpressionItem>((deaExp, deaExpItem) => deaExpItem.ExpCode == deaExp.ExpCode)
                    .Where(deaExp => SqlFunc.ContainsArray(expCodes, deaExp.ExpCode))
                    .Select((deaExp, deaExpItem) => new DiseaseExpressionInfo
                    {
                        ExpCode = deaExp.ExpCode,
                        ExpText = deaExp.ExpText,
                        DiseaseCode = deaExp.DiseaseCode,
                        ItemCode = deaExpItem.ItemCode,
                        ValueType = deaExpItem.ValueType
                    })
                    .ToList();
            }
            catch (Exception)
            {
                throw;
            }
        }

        /// <summary>
        /// 根据疾病代码获取疾病基本信息
        /// </summary>
        /// <param name="diseaseCodes">疾病代码数组</param>
        /// <returns>疾病基本信息</returns>
        public List<DiseaseBasicInfo> GetDiseaseBasicInfo(string[] diseaseCodes)
        {
            try
            {
                if (diseaseCodes == null || diseaseCodes.Length == 0)
                    return new List<DiseaseBasicInfo>();

                return _db.Queryable<CodeDisease>()
                    .Where(x => SqlFunc.ContainsArray(diseaseCodes, x.DiseaseCode))
                    .Select(x => new DiseaseBasicInfo
                    {
                        DiseaseCode = x.DiseaseCode,
                        DiseaseName = x.DiseaseName
                    })
                    .ToList();
            }
            catch (Exception)
            {
                throw;
            }
        }

        /// <summary>
        /// 获取疾病包含关系
        /// </summary>
        /// <param name="parentDiseaseCodes">父疾病代码数组</param>
        /// <returns>疾病包含关系</returns>
        public List<MapDiseaseDisease> GetDiseaseRelations(string[] parentDiseaseCodes)
        {
            try
            {
                if (parentDiseaseCodes == null || parentDiseaseCodes.Length == 0)
                    return new List<MapDiseaseDisease>();

                return _db.Queryable<MapDiseaseDisease>()
                    .Where(x => SqlFunc.ContainsArray(parentDiseaseCodes, x.ParentDiseaseCode))
                    .ToList();
            }
            catch (Exception)
            {
                // 记录错误日志
                // _logger.LogError(ex, "获取疾病包含关系时发生错误");
                return new List<MapDiseaseDisease>();
            }
        }

        /// <summary>
        /// 获取所有疾病词条
        /// </summary>
        /// <returns>疾病词条列表</returns>
        public List<CodeDiseaseEntry> GetAllDiseaseEntries()
        {
            try
            {
                return _db.Queryable<CodeDiseaseEntry>().ToList();
            }
            catch (Exception)
            {
                throw;

            }
        }
    }
}
