﻿namespace Peis.Model.DTO.CompanyStatistics
{
    /// <summary>
    /// 单位人员汇总
    /// </summary>
    public class CompanyPersonnelSummary
    {
        /// <summary>
        /// 体检人数
        /// </summary>
        public int TotalPeCount { get; set; }

        /// <summary>
        /// 预约人数
        /// </summary>
        public int TotalBookCount { get; set; }

        /// <summary>
        /// 预约到检人数
        /// </summary>
        public int TotalBookActiveCount { get; set; }

        /// <summary>
        /// 单位人员汇总
        /// </summary>
        public PersonnelSummary[] Report { get; set; }
    }

    /// <summary>
    /// 单位人员汇总
    /// </summary>
    public class PersonnelSummary
    {
        /// <summary>
        /// 单位编码
        /// </summary>
        public string CompanyCode { get; set; }

        /// <summary>
        /// 单位
        /// </summary>
        public string CompanyName { get; set; }

        /// <summary>
        /// 体检人数
        /// </summary>
        public int PeCount { get; set; }

        /// <summary>
        /// 预约人数
        /// </summary>
        public int BookCount { get; set; }

        /// <summary>
        /// 预约到检人数
        /// </summary>
        public int BookActiveCount { get; set; }
    }
}
