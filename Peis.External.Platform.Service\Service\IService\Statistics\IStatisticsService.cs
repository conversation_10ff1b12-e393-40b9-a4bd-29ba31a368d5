﻿using Peis.Model.DTO.External.His;
using Peis.Model.DTO.FinancialStatistics;
using Peis.Model.DTO.PackageStatisticsReport;

namespace Peis.External.Platform.Service.Service.IService.Statistics;

public interface IStatisticsService
{
    /// <summary>
    /// 获取收费信息列表
    /// </summary>
    /// <param name="query"></param>
    /// <param name="data"></param>
    /// <param name="msg"></param>
    /// <returns></returns>
    bool HisBillListQuery(HisBillListQuery query, ref PersonnelFeeList data, ref string msg);

    /// <summary>
    /// 获取个人收费日结组合统计报表
    /// </summary>
    /// <param name="query">查询条件</param>
    /// <param name="flag">验证</param>
    /// <param name="msg">消息</param>
    /// <returns>PersonalSettlementCombReportDto</returns>
    PersonalSettlementCombReportDto GetPersonalSettlementCombReport(PersonalSettlementCombReportQuery query, out bool flag, out string msg);
}

//public class DefaultExternalSystemStatisticsService : IExternalSystemStatisticsService
//{
//    public PersonalSettlementCombReportDto GetPersonalSettlementCombReport(PersonalSettlementCombReportQuery query, out bool flag, out string msg)
//    {
//        msg = ResxCommon.Fail;
//        flag = false;
//        return null;
//    }

//    public bool HisBillListQuery(HisBillListQuery query, ref PersonnelFeeList data, ref string msg)
//    {
//        msg = ResxCommon.Fail;
//        return false;
//    }

//}