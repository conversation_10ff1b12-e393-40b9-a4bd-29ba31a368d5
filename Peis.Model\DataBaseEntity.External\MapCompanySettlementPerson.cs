﻿namespace Peis.Model.DataBaseEntity.External
{
    [SugarTable]
    public class MapCompanySettlementPerson
    {
        /// <summary>
        /// 体检结算流水号
        /// </summary>
        [SugarColumn(IsPrimaryKey = true, Length = 10)]
        public string BillSeqNo { get; set; }
        /// <summary>
        /// 体检号
        /// </summary>
        [SugarColumn(IsPrimaryKey = true, Length = 12)]
        public string RegNo { get; set; }
        /// <summary>
        /// 组合ID
        /// </summary>
        [SugarColumn(IsPrimaryKey = true)]
        public long RegCombId { get; set; }
    }
}
