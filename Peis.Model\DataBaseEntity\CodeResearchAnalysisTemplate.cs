﻿using Peis.Model.TableFilter;
using SqlSugar;
using System;
using System.Collections.Generic;

namespace Peis.Model.DataBaseEntity
{
    ///<summary>
    ///科研分析模板
    ///</summary>
    [SugarTable("CodeResearchAnalysisTemplate")]
    public class CodeResearchAnalysisTemplate: IHospCodeFilter
    {
        /// <summary>
        /// 模板编码
        /// </summary>        
        [SugarColumn(IsNullable = false, IsPrimaryKey = true, Length = 5)]
        public string TemplateCode { get; set; }

        /// <summary>
        /// 模板名称
        /// </summary>        
        [SugarColumn(IsNullable = false, Length = 20)]
        public string TemplateName { get; set; }

        /// <summary>
        /// 操作符  And  Or
        /// </summary>        
        [SugarColumn(IsNullable = false, Length = 5)]
        public string Operator { get; set; }

        /// <summary>
        /// 创建人
        /// </summary>        
        [SugarColumn(IsNullable = false, Length = 10)]
        public string Creator { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>        
        [SugarColumn(IsNullable = false)]
        public DateTime CreateTime { get; set; }

        /// <summary>
        /// 查询类型 1:组合 2:项目
        /// </summary>        
        [SugarColumn(IsNullable = false)]
        public int QueryType { get; set; }

        /// <summary>
        /// 模板详情
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public List<CodeResearchAnalysisTemplateDetail> TemplateDetail { get; set; }

        /// <summary>
        /// 院区代码
        /// </summary>
        [SugarColumn(IsNullable = false, Length = 1)]
        public string HospCode { get; set; }
    }
}