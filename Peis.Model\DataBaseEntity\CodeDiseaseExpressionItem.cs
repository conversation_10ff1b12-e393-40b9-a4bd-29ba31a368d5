﻿namespace Peis.Model.DataBaseEntity
{
    ///<summary>
    ///疾病计算表达式项目表
    ///</summary>
    [SugarTable("CodeDiseaseExpressionItem")]
    public class CodeDiseaseExpressionItem
    {
        /// <summary>
        /// 表达式码
        /// </summary>        
        [SugarColumn(IsNullable = false, IsPrimaryKey = true, Length = 255)]
        public string ExpCode { get; set; }

        /// <summary>
        /// 项目码
        /// </summary>        
        [SugarColumn(IsNullable = false, IsPrimaryKey = true, Length = 6)]
        public string ItemCode { get; set; }

        /// <summary>
        /// 结果类型（0字符1数值2复合型）
        /// </summary>        
        [SugarColumn(IsNullable = false)]
        public Other.PeEnum.ValueType ValueType { get; set; }
    }
}