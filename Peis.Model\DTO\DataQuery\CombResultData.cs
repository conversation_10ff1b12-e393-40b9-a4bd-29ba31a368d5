﻿namespace Peis.Model.DTO.DataQuery
{
    /// <summary>
    /// 体检综合查询--获取项目情况
    /// </summary>
    public class CombResultData
    {
        /// <summary>
        /// 分类代码
        /// </summary>
        public string ClsCode { get; set; }

        /// <summary>
        /// 分类名称
        /// </summary>
        public string ClsName { get; set; }

        /// <summary>
        /// 组合代码
        /// </summary>
        public string CombCode { get; set; }

        /// <summary>
        /// 组合名称
        /// </summary>
        public string CombName { get; set; }

        /// <summary>
        /// 医生名称
        /// </summary>
        public string DoctorName { get; set; }

        /// <summary>
        /// 自费标识
        /// </summary>
        public bool IsPayBySelf { get; set; }
    }
}
