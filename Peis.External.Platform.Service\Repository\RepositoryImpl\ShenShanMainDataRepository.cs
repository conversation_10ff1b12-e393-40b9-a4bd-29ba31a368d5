﻿using Peis.Model.DataBaseEntity.External;
using Peis.Model.DataBaseViewModel.External;
using Peis.Model.DTO.External.DataMaintenances;
using Peis.Model.Other.PeEnum;

namespace Peis.External.Platform.Service.Repository.RepositoryImpl
{
    /// <summary>
    /// 深汕基础数据仓储实现
    /// </summary>
    public class ShenShanMainDataRepository : IShenShanMainDataRepository
    {
        private readonly ISqlSugarClient _db;
        public ShenShanMainDataRepository(
            ISqlSugarClient db)
        {
            _db = db;
        }

        /// <summary>
        /// 项目视图查询
        /// </summary>
        /// <returns></returns>
        public ISugarQueryable<v_lis_item> QueryViewCodeLisItems()
        {
            return _db.Queryable<v_lis_item>().Distinct();
        }

        /// <summary>
        /// 关联项目查询
        /// </summary>
        /// <returns>ISugarQueryable</returns>
        public ISugarQueryable<MapCodeLisItem> QueryMapCodeLisItems()
        {
            return _db.Queryable<MapCodeLisItem>();
        }

        /// <summary>
        /// 收费项目基础信息查询
        /// </summary>
        /// <returns>ISugarQueryable</returns>
        public ISugarQueryable<CodeHisChargeItem> QueryCodeHisChargeItems()
        {
            return _db.Queryable<CodeHisChargeItem>()
                  .Where(x => x.IsAvailable.Equals(EnumYesOrNoStr.Y.ToString())
                  && x.IsDeleted.Equals(EnumYesOrNoStr.N.ToString()));
        }

        /// <summary>
        /// 查询体检组合收费项目信息
        /// </summary>
        /// <param name="combCode">组合code</param>
        /// <returns>ISugarQueryable</returns>
        public ISugarQueryable<MapCodeItemCombHisChargeItem, CodeHisChargeItem, CodeHisDrugItem> QueryCodeMapChargeItems(string combCode)
        {
            return _db.Queryable<MapCodeItemCombHisChargeItem>()
                 .LeftJoin<CodeHisChargeItem>((a, b) => b.ChargeItemCode.Equals(a.HisChargeItemCode) && a.ItemType.Equals(EnumCombHisItemType.Exam))
                 .LeftJoin<CodeHisDrugItem>((a, b, c) => c.DrugCode.Equals(a.HisChargeItemCode) && a.ItemType.Equals(EnumCombHisItemType.Drug))
                 .Where( a => combCode.Equals(a.CombCode));
        }

        /// <summary>
        /// 查询体检组合收费项目信息
        /// </summary>
        /// <param name="combCodes">组合code</param>
        /// <returns>ISugarQueryable</returns>
        public ISugarQueryable<CodeItemComb, MapCodeItemCombHisChargeItem> QueryCodeCombMapChargeItems(List<string> combCodes = null)
        {
            return _db.Queryable<CodeItemComb>()
                  .InnerJoin<MapCodeItemCombHisChargeItem>((a, b) => b.CombCode.Equals(a.CombCode))
                 .WhereIF(!combCodes.IsNullOrEmpty(), (a, b) => combCodes.Contains(a.CombCode));
        }

        /// <summary>
        /// 查询医嘱项目基础信息
        /// </summary>
        /// <returns>ISugarQueryable</returns>
        public ISugarQueryable<CodeHisOrderItem> QueryCodeHisOrderItems()
        {
            return _db.Queryable<CodeHisOrderItem>()
                   .Where(x => x.IsAvailable.Equals(EnumYesOrNoStr.Y.ToString())
                  && x.IsDeleted.Equals(EnumYesOrNoStr.N.ToString()));
        }

        /// <summary>
        /// 查询医嘱收费明细
        /// </summary>
        /// <param name="mainPId"></param>
        /// <returns>ISugarQueryable</returns>
        public ISugarQueryable<CodeHisOrderItemCharge> QueryCodeHisOrderItemCharges(string mainPId = null)
        {
            return _db.Queryable<CodeHisOrderItemCharge>()
                  .WhereIF(!string.IsNullOrWhiteSpace(mainPId), x => x.MainPId.Equals(mainPId));
        }

        /// <summary>
        /// 查询体检组合与医嘱信息
        /// </summary>
        /// <param name="combCodes">组合code</param>
        /// <returns>ISugarQueryable</returns>
        public ISugarQueryable<MapCodeItemCombHisOrderItem, CodeHisOrderItem, CodeHisOrderItemCharge> QueryCodeItemCombMapOrders(List<string> combCodes)
        {
            return _db.Queryable<MapCodeItemCombHisOrderItem>()
                 .InnerJoin<CodeHisOrderItem>((a, b) => b.OrderItemCode.Equals(a.HisOrderCode))
                 .InnerJoin<CodeHisOrderItemCharge>((a, b, c) => c.MainPId.Equals(b.PId))
                 .WhereIF(!combCodes.IsNullOrEmpty(), (a, b) => combCodes.Contains(a.CombCode));
        }

        /// <summary>
        /// 查询体检组合
        /// </summary>
        /// <param name="combCodes">组合code</param>
        /// <returns>ISugarQueryable</returns>
        public ISugarQueryable<CodeItemComb> QueryCodeItemCombs(List<string> combCodes = null)
        {
            return _db.Queryable<CodeItemComb>()
                 .WhereIF(!combCodes.IsNullOrEmpty(), (a) => combCodes.Contains(a.CombCode));
        }

        /// <summary>
        /// 查询体检分类
        /// </summary>
        /// <param name="clsCodes">分类code</param>
        /// <returns>ISugarQueryable</returns>
        public ISugarQueryable<CodeItemCls> QueryCodeItemClss(List<string> clsCodes)
        {
            return _db.Queryable<CodeItemCls>()
                 .WhereIF(!clsCodes.IsNullOrEmpty(), (a) => clsCodes.Contains(a.ClsCode));
        }


        #region 药品
        /// <summary>
        /// 查询药品基础信息
        /// </summary>
        /// <returns>ISugarQueryable</returns>
        public ISugarQueryable<CodeHisDrugItem> QueryCodeHisDrugItems()
        {
            return _db.Queryable<CodeHisDrugItem>()
                  .Where(x => x.IsAvailable.Equals(EnumYesOrNoStr.Y.ToString())
                  && x.IsDeleted.Equals(EnumYesOrNoStr.N.ToString()));
        }

        #endregion

        #region TRUNCATE
        /// <summary>
        /// 清空His收费项目表操作，慎用！！！
        /// </summary>
        public void TruncateCodeHisChargeItemTable()
        {
            _db.DbMaintenance.TruncateTable<CodeHisChargeItem>();
        }

        /// <summary>
        /// 清空His医嘱项目表操作，慎用！！！
        /// </summary>
        public void TruncateCodeHisOrderItemTable()
        {
            _db.DbMaintenance.TruncateTable<CodeHisOrderItem>();
        }

        /// <summary>
        /// 清空His医嘱项目明细表操作，慎用！！！
        /// </summary>
        public void TruncateCodeHisOrderItemChargeTable()
        {
            _db.DbMaintenance.TruncateTable<CodeHisOrderItemCharge>();
        }

        /// <summary>
        /// 清空His药品信息表操作，慎用！！！
        /// </summary>
        public void TruncateCodeHisDrugItemTable()
        {
            _db.DbMaintenance.TruncateTable<CodeHisDrugItem>();
        }
        #endregion
    }
}
