﻿using Peis.Quartz.UI.Model;
using Peis.Quartz.UI.Tools;
using MediatR;
using Microsoft.AspNetCore.Mvc;
using Peis.External.Platform.Service.Service.ServiceImpl.MediatR;
using Peis.Model;
using Peis.Model.DataBaseEntity;
using Peis.Model.DataBaseEntity.External;
using Peis.Model.DTO.ReportConclusion;
using Peis.Model.DTO.ReportConclusionNew;
using Peis.Service.IService;
using Peis.Service.Service.MediatR;
using Peis.Utility.CustomAttribute;
using Peis.Utility.Helper;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace Peis.API.Controllers.Business
{
    /// <summary>
    /// 体检报告主检审核
    /// </summary>
    [Route("api/[controller]")]
    [ApiController]
    public class ReportConclusionV2Controller : BaseApiController
    {
        private readonly IReportConclusionV2Service _reportConclusionNewService;
        private readonly IMediator _mediator;

        public ReportConclusionV2Controller(IReportConclusionV2Service reportAuditService, IMediator mediator)
        {
            _reportConclusionNewService = reportAuditService;
            _mediator = mediator;
        }

        #region 主检审核查询列表（不分配、分配、优先分配）
        /// <summary>
        /// 获取主检的人员列表（主检不分配）/审核的人员列表
        /// </summary>
        /// <param name="queryPatientList"></param>
        /// <returns></returns>
        [HttpPost("GetPatientList4NotAllocate")]
        [ProducesResponseType(typeof(List<PatientInfo>), 200)]
        public IActionResult GetPatientList4NotAllocate(QueryPatientList queryPatientList)
        {
            result.ReturnData = _reportConclusionNewService.GetPatientList4NotAllocate(queryPatientList);
            return Ok(result);
        }

        /// <summary>
        /// 登记、患者信息查询：主检、审核
        /// </summary>
        /// <param name="query">查询条件</param>
        /// <returns>ReportPeRegisterDtos</returns>
        [HttpPost("GetReportPeRegisters")]
        [ProducesResponseType(typeof(List<ReportPeRegisterDto>), 200)]
        public ActionResult GetReportPeRegisters([FromBody] ReportPeRegisterQuery query)
        {
            int totalNumber = 0;
            int totalPage = 0;
            result.ReturnData = _reportConclusionNewService.GetReportPeRegisters(query, ref totalNumber,ref totalPage);
            result.TotalNumber = totalNumber;
            result.TotalPage = totalPage;
            result.ReturnMsg = ResxCommon.Success;

            return Ok(result);
        }
        #endregion

        #region 综述建议（读取、生成、编辑）

        /// <summary>
        /// 获取报告结论及综述建议
        /// </summary>
        /// <param name="regNo">体检号</param>
        /// <param name="oper">0 主检 1 审核</param>
        /// <param name="isOccupation">是否职检</param>
        /// <returns></returns>
        [HttpPost("GetReportConclusion")]
        [ProducesResponseType(typeof(ReportConclusionNew), 200)]
        public ActionResult GetReportConclusion([FromQuery] string regNo, [FromQuery] CheckAuditOper oper, [FromQuery] bool isOccupation)
        {
            result.ReturnData = _reportConclusionNewService.GetReportConclusion(regNo, oper, isOccupation);
            return Ok(result);
        }

        /// <summary>
        /// 根据结果记录生成综述建议
        /// </summary>
        /// <param name="regNo">体检号</param>
        /// <param name="combCodes">组合编号集合</param>
        /// <param name="isOccupation">是否职检</param>
        /// <returns></returns>
        [HttpPost("GenerateSummarySuggestionFromRecord")]
        [ProducesResponseType(typeof(SummarySuggestion), 200)]
        public ActionResult GenerateSummarySuggestionFromRecord(
            [Required(ErrorMessage = "体检号不能为空！")][Regex(VerifyRegNo = true, ErrorMessage = "体检号格式错误！")][FromQuery] string regNo, [FromQuery] bool isOccupation,
            [FromBody] string[] combCodes)
        {
            result.ReturnData = _reportConclusionNewService.GenerateSummarySuggestion(regNo, isOccupation,combCodes);
            return Ok(result);
        }

        /// <summary>
        /// 编辑综述标签
        /// </summary>
        /// <param name="editRequest">编辑的综述标签，及其关联的疾病标签</param>
        /// <returns></returns>
        [HttpPost("EditSummaryTag")]
        [ProducesResponseType(typeof(EditSummaryTagBack), 200)]
        public ActionResult EditSummaryTag([FromBody] EditSummaryTagRequest editRequest)
        {
            result.ReturnData = _reportConclusionNewService.EditSummaryTag(editRequest);
            return Ok(result);
        }

        #endregion

        #region 主检审核（暂存、确认主检审核、取消主检审核）

        /// <summary>
        /// 暂存主检审核
        /// </summary>
        /// <param name="repCon">报告结论及主检审核数据</param>
        /// <returns></returns>
        [HttpPost("TempSaveReportConclusion")]
        public ActionResult TempSaveReportConclusion([FromBody] ReportConclusionNew repCon)
        {
            _reportConclusionNewService.TempSaveReportConclusion(repCon);
            return Ok(result);
        }

        /// <summary>
        /// 确认主检审核
        /// </summary>
        /// <param name="repCon">报告结论及主检审核数据</param>
        /// <param name="oper">0 主检 1 审核</param>
        /// <returns></returns>
        [HttpPost("ConfirmReportConclusion")]
        public ActionResult ConfirmReportConclusion([FromBody] ReportConclusionNew repCon, [FromQuery] CheckAuditOper oper)
        {
            _reportConclusionNewService.ConfirmReportConclusion(repCon, oper);

            // 发布报告生成、短信通知体检人任务
            if (oper == CheckAuditOper.审核)
                AuditReportOtherBussHandle(repCon.Patient.RegNo);

            return Ok(result);
        }

        /// <summary>
        /// 取消主检审核
        /// </summary>
        /// <param name="regNo">体检号</param>
        /// <param name="doctorCode">操作医生代码</param>
        /// <param name="oper">0 主检 1 审核</param>
        /// <param name="isOccupation">是否职检</param>
        /// <returns></returns>
        [HttpPost("CancelReportConclusion")]
        public ActionResult CancelReportConclusion([FromQuery] string regNo, [FromQuery] string doctorCode, [FromQuery] CheckAuditOper oper, [FromQuery] bool isOccupation)
        {
            _reportConclusionNewService.CancelReportConclusion(regNo, doctorCode, oper,isOccupation);
            CancelReportOtherBussHandle(regNo);

            return Ok(result);
        }

        #region 审核/取消
        /// <summary>
        /// 审核报告结论
        /// </summary>
        /// <param name="auditInfo">审核信息</param>
        /// <returns></returns>
        [HttpPost("AuditReportConclusion")]
        public IActionResult AuditReportConclusion(AuditInfo auditInfo)
        {
            var msg = string.Empty;
            result.Success = _reportConclusionNewService.AuditReportConclusion(auditInfo, ref msg);
            result.ReturnMsg = msg;

            if (result.Success)
                AuditReportOtherBussHandle(auditInfo.RegNo);

            return Ok(result);
        }

        /// <summary>
        /// 取消审核报告结论
        /// </summary>
        /// <param name="auditInfo">审核信息</param>
        /// <returns></returns>
        [HttpPost("CancelAuditReportConclusion")]
        public IActionResult CancelAuditReportConclusion(AuditInfo auditInfo)
        {
            var msg = string.Empty;
            result.Success = _reportConclusionNewService.CancelAuditReportConclusion(auditInfo, ref msg);
            result.ReturnMsg = msg;

            if (result.Success)
            {
                _mediator.Send(new RemovePeReportFileByRegNoHandle.Data(auditInfo.RegNo));
                _mediator.Send(new RemoveSendShortMsgToPatForApprovedJobHandle.Data(auditInfo.RegNo));
            }

            return Ok(result);
        }
        #endregion

        #region 撤回主检
        /// <summary>
        /// 撤回主检
        /// </summary>
        /// <param name="returnChecked">撤回主检</param>
        /// <returns></returns>
        [HttpPost("ReturnChecked")]
        public IActionResult ReturnChecked(ReturnChecked returnChecked)
        {
            result.ReturnData = _reportConclusionNewService.ReturnChecked(returnChecked);
            result.Success = true;
            return Ok(result);
        }

        /// <summary>
        /// 获取撤回主检记录（主检回复）
        /// </summary>
        /// <param name="operatorCode">审核医生</param>
        /// <param name="isOccupation"></param>
        /// <returns></returns>
        [HttpPost("GetReturnChecked")]
        [ProducesResponseType(typeof(List<PeReportExtReturnChecked>), 200)]
        public IActionResult GetReturnChecked([FromQuery] string operatorCode, [FromQuery] bool isOccupation)
        {
            result.ReturnData = _reportConclusionNewService.GetReturnChecked(operatorCode, isOccupation);
            result.Success = true;
            return Ok(result);
        }

        /// <summary>
        /// 获取撤回主检记录（主检未答复）
        /// </summary>
        /// <param name="replyerCode">答复医生代码（主检医生）</param>
        /// <param name="isOccupation"></param>
        /// <returns></returns>
        [HttpPost("GetNotReplyReturn")]
        [ProducesResponseType(typeof(List<PeReportExtReturnChecked>), 200)]
        public IActionResult GetNotReplyReturn([FromQuery] string replyerCode, [FromQuery]bool isOccupation)
        {
            result.Success = true;
            result.ReturnData = _reportConclusionNewService.GetNotReplyReturn(replyerCode, isOccupation);
            return Ok(result);
        }

        /// <summary>
        /// 答复撤回主检
        /// </summary>
        /// <param name="peReportExtReturnCheckeds">主检问题单</param>
        /// <returns></returns>
        [HttpPost("ReplyReturn")]
        public IActionResult ReplyReturn(List<PeReportExtReturnChecked> peReportExtReturnCheckeds)
        {
            result.Success = _reportConclusionNewService.ReplyReturn(peReportExtReturnCheckeds);
            return Ok(result);
        }

        /// <summary>
        /// 取消撤回主检
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpPost("CancelReturnChecked")]
        public IActionResult CancelReturnChecked([FromQuery] int id)
        {
            result.Success = _reportConclusionNewService.CancelReturnChecked(id);
            return Ok(result);
        }

        /// <summary>
        /// 获取撤回主检列表
        /// </summary>
        /// <param name="replyerCode"></param>
        /// <param name="isOccupation"></param>
        /// <returns></returns>
        [HttpPost("GettReturnCheckedList")]
        [ProducesResponseType(typeof(List<PeReportExtReturnChecked>), 200)]
        public IActionResult GettReturnCheckedList([FromQuery] string replyerCode, [FromQuery] bool isOccupation)
        {
            result.ReturnData = _reportConclusionNewService.GettReturnCheckedList(replyerCode, isOccupation);
            result.Success = true;
            return Ok(result);
        }
        #endregion

        /// <summary>
        /// 读取历史综述
        /// </summary>
        /// <param name="regNo"></param>
        /// <returns></returns>
        [HttpPost("ReadHistorySummary")]
        public ActionResult ReadHistorySummary([FromQuery] string regNo)
        {
            result.ReturnData = _reportConclusionNewService.ReadHistorySummary(regNo);
            result.Success = true;
            return Ok(result);
        }

        /// <summary>
        /// 读取历史组合结果
        /// </summary>
        /// <param name="regNo"></param>
        /// <returns></returns>
        [HttpPost("ReadHistoryCombResult")]
        public ActionResult ReadHistoryCombResult([FromQuery] string regNo)
        {
            result.ReturnData = _reportConclusionNewService.ReadHistoryCombResult(regNo);
            result.Success = true;
            return Ok(result);
        }

        #region private
        /// <summary>
        /// 审核报告后其他业务处理
        /// </summary>
        /// <param name="regNo"></param>
        void AuditReportOtherBussHandle(string regNo)
        {
            if (regNo.IsNullOrEmpty()) return;

            _mediator.Send(new AddSendShortMsgToPatForApprovedJobHandle.Data(regNo));

            QuartzTaskHelper.AddJobAsync<IReportFileService>(nameof(IReportFileService.SavePeReportFilesByRegNo),
               new ParameterInfoDto(regNo) , new ParameterInfoDto(typeof(string), true), new ParameterInfoDto(true)).ConfigureAwait(false);
        }

        /// <summary>
        /// 取消审核报告后其他业务处理
        /// </summary>
        /// <param name="regNo"></param>
        void CancelReportOtherBussHandle(string regNo)
        {
            if (regNo.IsNullOrEmpty()) return;

            _mediator.Send(new RemoveSendShortMsgToPatForApprovedJobHandle.Data(regNo));

            QuartzTaskHelper.AddJobAsync<IReportFileService>(nameof(IReportFileService.RemovePeReportFilesByRegNo),
                new ParameterInfoDto(regNo), new ParameterInfoDto(typeof(string), true)).ConfigureAwait(false);
        }
        #endregion

        #endregion

        #region 次要功能（医疗指导）
        /// <summary>
        /// 获取医疗指导
        /// </summary>
        /// <param name="regNo"></param>
        /// <returns></returns>
        [HttpPost("ReadMedicalAdvice")]
        public ActionResult ReadMedicalAdvice([FromQuery] string regNo)
        {
            result.ReturnData = _reportConclusionNewService.ReadMedicalAdvice(regNo);
            return Ok(result);
        }

        /// <summary>
        /// 保存医疗指导
        /// </summary>
        /// <param name="advice"></param>
        [HttpPost("SaveMedicalAdvice")]
        public ActionResult SaveMedicalAdvice([FromBody] MedicalAdvice advice)
        {
            _reportConclusionNewService.SaveMedicalAdvice(advice);
            return Ok(result);
        }
        #endregion
    }
}
