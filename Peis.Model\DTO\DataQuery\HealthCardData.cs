﻿namespace Peis.Model.DTO.DataQuery
{
    [XmlRoot(ElementName = "DataInfo")]
    public class HealthCardData
    {
        [XmlElement(ElementName = "Data-fields")]
        public Datafields Datafields { get; set; }
    }

    [XmlRoot(ElementName = "Data-fields")]
    public class Datafields
    {
        [XmlElement(ElementName = "ZJLB")]
        public string ZJLB { get; set; }
        [XmlElement(ElementName = "ZJHM")]
        public string ZJHM { get; set; }
        [XmlElement(ElementName = "CZRXM")]
        public string CZRXM { get; set; }
        [XmlElement(ElementName = "XB")]
        public string XB { get; set; }
        [XmlElement(ElementName = "TJRQ")]
        public string TJRQ { get; set; }
        [XmlElement(ElementName = "JKZZH")]
        public string JKZZH { get; set; }
        [XmlElement(ElementName = "DYZT")]
        public string DYZT { get; set; }
        [XmlElement(ElementName = "LZZT")]
        public string LZZT { get; set; }
        [XmlElement(ElementName = "ZSZT")]
        public string ZSZT { get; set; }
        [XmlElement(ElementName = "SQRQ")]
        public string SQRQ { get; set; }
        [XmlElement(ElementName = "NL")]
        public string NL { get; set; }
        [XmlElement(ElementName = "GZDW")]
        public string GZDW { get; set; }
        [XmlElement(ElementName = "LXDH")]
        public string LXDH { get; set; }
        [XmlElement(ElementName = "JKZLB")]
        public string JKZLB { get; set; }
        [XmlElement(ElementName = "JKZJCDW")]
        public string JKZJCDW { get; set; }
        [XmlElement(ElementName = "JCDWSSDQ")]
        public string JCDWSSDQ { get; set; }
        [XmlElement(ElementName = "ZP")]
        public string ZP { get; set; }
        [XmlAttribute(AttributeName = "ID")]
        public string ID { get; set; }
    }
}
