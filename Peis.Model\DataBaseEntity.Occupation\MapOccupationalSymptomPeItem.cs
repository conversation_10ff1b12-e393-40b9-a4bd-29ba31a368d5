﻿namespace Peis.Model.DataBaseEntity.Occupation
{
    /// <summary>
    /// 症状询问-体检项目对应表
    /// </summary>
    [SugarTable]
    public class MapOccupationalSymptomPeItem
    {
        /// <summary>
        /// 症状代码
        /// </summary>
        [SugarColumn(Length =10,IsNullable = false)]
        public string SymptomCode { get; set; }

        /// <summary>
        /// 体检项目代码
        /// </summary>
        [SugarColumn(Length = 10, IsPrimaryKey = true)]
        public string ItemCode { get; set; }
    }
}
