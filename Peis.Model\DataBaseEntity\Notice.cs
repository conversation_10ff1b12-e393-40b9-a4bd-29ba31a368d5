﻿using Peis.Model.Other.PeEnum;
using Peis.Model.TableFilter;
using SqlSugar;
using System;

namespace Peis.Model.DataBaseEntity
{
    ///<summary>
    ///公告表
    ///</summary>
    [SugarTable("Notice")]
    public class Notice: IHospCodeFilter
    {
        /// <summary>
        /// 公告Id
        /// </summary>        
        [SugarColumn(IsNullable = false, IsPrimaryKey = true)]
        public long NoticeId { get; set; }

        /// <summary>
        /// 公告标题
        /// </summary>        
        [SugarColumn(IsNullable = false, Length = 255)]
        public string Title { get; set; }

        /// <summary>
        /// 公告内容
        /// </summary>        
        [SugarColumn(IsNullable = false, ColumnDataType = StaticConfig.CodeFirst_BigString)]
        public string Content { get; set; }

        /// <summary>
        /// 创建人
        /// </summary>        
        [SugarColumn(IsNullable = false, Length = 10)]
        public string OperCode { get; set; }

        /// <summary>
        /// 公告推送目标类型（0 所有科室 1 所选科室）
        /// </summary>        
        [SugarColumn(IsNullable = false)]
        public NoticeTarget TargetType { get; set; }

        /// <summary>
        /// 推送部分科室的列表
        /// </summary>        
        [SugarColumn(IsNullable = false, ColumnDataType = StaticConfig.CodeFirst_BigString)]
        public string TargetDepts { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>        
        [SugarColumn(IsNullable = false)]
        public DateTime CreateTime { get; set; }

        /// <summary>
        /// 院区编码
        /// </summary>        
        [SugarColumn(IsNullable = false, Length = 5)]
        public string HospCode { get; set; }
    }
}