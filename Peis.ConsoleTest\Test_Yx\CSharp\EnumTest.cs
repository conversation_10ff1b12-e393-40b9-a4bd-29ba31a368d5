﻿using System;

namespace Peis.ConsoleTest.Test_Yx.CSharp
{
    internal class EnumTest
    {
        public static void Invoke()
        {
            //Console.WriteLine(GameState.CurrentSeason);

            Season seasons = Season.春季 | Season.夏季;

            if ((seasons & Season.春季) > 0)
                Console.WriteLine(Season.春季);

            if ((seasons & Season.秋季) > 0)
                Console.WriteLine(Season.秋季);
        }
    }

    static class GameState
    {
        public static Season CurrentSeason = GetCurrentSeason();

        static Season GetCurrentSeason()
        {
            var queryValue = "8"; //冬季
            //var queryValue = ""; //没有该定义
            //var queryValue = "99"; //没有该定义
            //var queryValue = "一些字符串"; //没有该定义

            if (int.TryParse(queryValue, out int enumValue))
            {
                if (Enum.IsDefined(typeof(Season), enumValue))
                    return (Season)enumValue;
            }

            //默认
            Console.WriteLine("枚举没有该定义值,返回默认值");
            return Season.春季;
        }
    }

    enum Season
    {
        春季 = 1,
        夏季 = 2,
        秋季 = 4,
        冬季 = 8
    }
}
