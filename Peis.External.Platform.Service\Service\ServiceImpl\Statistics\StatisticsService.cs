﻿using Peis.External.Platform.Service.Service.IService.Statistics;
using Peis.Model.DTO;
using Peis.Model.DTO.External.His;
using Peis.Model.DTO.FinancialStatistics;
using Peis.Model.DTO.PackageStatisticsReport;
using Peis.Model.Other.PeEnum;
using Peis.Service.IService.ExternalSystem;

namespace Peis.External.Platform.Service.Service.ServiceImpl.Statistics
{
    public class StatisticsService: IStatisticsService
    {
        private readonly IShenShanRegisterRepository _shenShanRegisterRepository;
        private readonly IDataRepository<CodeItemCls> _codeItemClsRepository;

        public StatisticsService(
            IShenShanRegisterRepository shenShanRegisterRepository,
            IDataRepository<CodeItemCls> codeItemClsRepository)
        {
            _shenShanRegisterRepository = shenShanRegisterRepository;
            _codeItemClsRepository = codeItemClsRepository;
        }  

        public bool HisBillListQuery(HisBillListQuery query, ref PersonnelFeeList data, ref string msg)
        {
            var beginDate = query.BeginDate;
            var endDate = query.EndDate.Add(new TimeSpan(23, 59, 59));

            data.FeeList = _shenShanRegisterRepository.ReadHisBillList()
                .LeftJoin<PeRegisterCluster>((order, bill, clus) => order.RegNo == clus.RegNo)
                .Where((order, bill) => SqlFunc.Between(bill.CreateTime, beginDate, endDate))
                .Where(order => !order.Name.Contains("测试"))
                .WhereIF(!query.ClusCode.IsNullOrEmpty(), (order, bill, clus) => clus.ClusCode == query.ClusCode)
                .WhereIF(!query.ClusCode.IsNullOrEmpty(), (order, bill, clus) => clus.ClusCode == query.ClusCode)
                .WhereIF(Enum.IsDefined(typeof(PeCls), query.PeCls), order => order.PeCls == query.PeCls)
                .WhereIF(query.VIPNormal == VIPNormal.VIP, order => order.IsVIP == true)
                .WhereIF(query.VIPNormal == VIPNormal.普通, order => order.IsVIP == false)
                .WhereIF(Enum.IsDefined(typeof(BookType), query.BookType), order => order.BookType == query.BookType)
                .Select((order, bill) => new PersonnelData
                {
                    RegNo = order.RegNo,
                    CompanyName = SqlFunc.IIF(string.IsNullOrEmpty(order.CompanyCode), "",
                                    SqlFunc.Subqueryable<CodeCompany>().Where(x => x.CompanyCode == order.CompanyCode)
                                    .Select(x => x.CompanyName)),
                    Name       = order.Name,
                    Sex        = order.Sex,
                    Age        = order.Age,
                    Tel        = order.Tel,
                    ActiveTime = order.RegisterTime,
                    Price      = bill.Price,
                    ChargeTime = bill.ChargeDate
                }).ToArray();

            //人数判断 退费冲销的不计入人数，多次缴费的按1次算，与综合查询一致
            var feeNumbers  = data.FeeList.GroupBy(x => x.RegNo, x => x.Price > 0 ? 1 : -1).Where(x => x.Sum() > 0).Count();

            data.TotalCount = feeNumbers;
            data.TotalPrice = data.FeeList.Sum(x => x.Price);
            return true;
        }

        /// <summary>
        /// 获取个人收费日结组合统计报表
        /// </summary>
        /// <param name="query">查询条件</param>
        /// <param name="flag">验证</param>
        /// <param name="msg">消息</param>
        /// <returns>PersonalSettlementCombReportDto</returns>
        public PersonalSettlementCombReportDto GetPersonalSettlementCombReport(PersonalSettlementCombReportQuery query, out bool flag, out string msg)
        {
            if (query.IsNullOrEmpty(nameof(query), out msg))
            {
                flag = false;
                return null;
            }

            query.ChargeTimeStart = query.ChargeTimeStart.Date;
            query.ChargeTimeEnd = query.ChargeTimeEnd.Date.Add(new TimeSpan(23, 59, 59));

            //通过收费时间查询 最小、最大登记时间 用于下方查询分表
            var register = _shenShanRegisterRepository.ReadHisBillList()
            .Where((order, bill) => SqlFunc.Between(bill.ChargeDate, query.ChargeTimeStart, query.ChargeTimeEnd))
                .Select(order => new RegisterDateRange
                {
                    MinRegTime = (DateTime)SqlFunc.AggregateMin(order.RegisterTime),
                    MaxRegTime = (DateTime)SqlFunc.AggregateMax(order.RegisterTime),
                }).First();
            //实际人数
            var queryable = _shenShanRegisterRepository.GetPersonalSettlementComb(register.MinRegTime, register.MaxRegTime)
                .Where(a => !a.Name.Contains("测试"))
                .Where((a, b) => SqlFunc.Between(b.ChargeDate, query.ChargeTimeStart, query.ChargeTimeEnd))
                .WhereIF(!query.ClsCode.IsNullOrEmpty(), (a, b, c) => c.ClsCode.Equals(query.ClsCode))
                .WhereIF(query.PeCls.HasValue, (a, b) => a.PeCls.Equals(query.PeCls))
                .WhereIF(query.BookType.HasValue, (a, b) => a.BookType.Equals(query.BookType))
                .WhereIF(!query.ClusterCode.IsNullOrEmpty(), (a, b, c, d) => d.ClusCode == query.ClusterCode);

            var totalCount = queryable
                .Select((a, b, c, d)=>b)
                .Distinct()
                .ToList()
                .GroupBy(x => x.RegNo, x => x.Price > 0 ? 1 : -1).Where(x => x.Sum() > 0).Count();
           
            var hisOrderCombs = queryable
                .GroupBy((a, b, c) => new { c.ClsCode,c.CombCode })
                .Select((a, b, c) => new
                {
                    c.ClsCode,
                    comb = new PersonalSettlementCombReportDetailItemDto
                    {
                        CombCode = c.CombCode,
                        CombName = SqlFunc.AggregateMax(c.CombName),
                        Price = SqlFunc.AggregateMax(c.Price),
                        Number = SqlFunc.AggregateSum(SqlFunc.IF(b.PayStatus == PayStatus.已退费).Return(-1).End(1)),
                    }
                }).ToList().Where(x=>x.comb.Number>0).ToList();
            var clsDictionary = _codeItemClsRepository.FindAll().ToDictionary(obj=>obj.ClsCode,obj=>obj.ClsName);

            // 组装数据
            var resData = new PersonalSettlementCombReportDto();
            resData.ClsSummaryList = hisOrderCombs.GroupBy(x => x.ClsCode)
                .Select(a => new PersonalSettlementCombReportDetailDto
                {
                    ClsCode = a.Key,
                    ClsName = clsDictionary[a.Key],
                    CombItemList = a.Select(x=>x.comb).ToList()
                })
                .ToList();

            resData.TotalCount = totalCount;
            resData.TotalAmount = resData.ClsSummaryList.Sum(x => x.CombItemList.Sum(y => y.TotalPrice));

            flag = true;
            msg = ResxCommon.Success;
            return resData;
        }
    }
}
