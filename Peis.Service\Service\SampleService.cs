using Peis.Model.Constant;
using Peis.Model.DTO;
using Peis.Model.DTO.Register;
using Peis.Model.DTO.Sample;
using Peis.Model.Other.Input;
using Peis.Model.Other.PeEnum;
using Peis.Repository.IRepository;
using Peis.Service.IService;
using Peis.Service.IService.Helper;
using Peis.Utility.PeUser;

namespace Peis.Service.Service
{
    public class SampleService : ISampleService
    {
        private readonly ISampleRepository _sampleRepository;
        private readonly IRegisterRepository _registerRepository;
        private readonly IClusterCombRepository _clusterCombRepository;
        private readonly IDataRepository<PeSample> _peSampleRepository;
        private readonly INoGeneration _noGeneration;
        private readonly IDataRepository<PeSamplePackage> _peSamplePackageRepository;
        private readonly ISystemParameterService _systemParameterService;
        private readonly IDataTranRepository _tranRepository;
        private readonly IHttpContextUser _httpContextUser;
        private readonly ICacheRepository _cacheRepository;

        public SampleService(
            ISampleRepository sampleRepository,
            IRegisterRepository registerRepository,
            IClusterCombRepository clusterCombRepository,
            IDataRepository<PeSample> peSampleRepository,
            INoGeneration noGeneration,
            IDataRepository<PeSamplePackage> peSamplePackage,
            IDataTranRepository tranRepository,
            ISystemParameterService systemParameterService,
            IHttpContextUser httpContextUser,
            ICacheRepository cacheRepository)
        {
            _sampleRepository = sampleRepository;
            _registerRepository = registerRepository;
            _clusterCombRepository = clusterCombRepository;
            _peSampleRepository = peSampleRepository;
            _noGeneration = noGeneration;
            _peSamplePackageRepository = peSamplePackage;
            _tranRepository = tranRepository;
            _systemParameterService = systemParameterService;
            _httpContextUser = httpContextUser;
            _cacheRepository = cacheRepository;
        }

        public TestTubeCategory[] TestTubeCategoryList()
        {
            return _sampleRepository.ReadBarcodeType()
                .Select(x => new TestTubeCategory
                {
                    PreNum = x.PreNum,
                    Note = x.Note,
                    Color = x.Color,
                    Attention = x.Attention,
                })
                .Distinct()
                .ToArray();
        }

        public List<SampleData> ReadSampleData(SampleMultipleFilters filters)
        {
            List<SampleData> sampDatas = new();
            DateTime? startDate = new DateTime();
            DateTime? endDate = new DateTime();

            if (filters.StartDate != null && filters.EndDate != null)
            {
                startDate = filters.StartDate?.Date;
                endDate = filters.EndDate?.AddDays(1).Date;
            }

            //查询登记信息
            var regNos = _registerRepository.ReadRegister()
                .Where(reg => reg.IsActive)
                .WhereIF(!string.IsNullOrEmpty(filters.RegNo), reg => reg.RegNo == filters.RegNo)
                .WhereIF(!string.IsNullOrEmpty(filters.Name), reg => reg.Name.Contains(filters.Name))
                .WhereIF(!string.IsNullOrEmpty(filters.CardNo), reg => reg.CardNo.Contains(filters.CardNo))
                .WhereIF(filters.Type == 1, reg => reg.IsCompanyCheck == false)//个人
                .WhereIF(filters.Type == 2 && string.IsNullOrEmpty(filters.CompanyCode), reg => reg.IsCompanyCheck)//团体(单位为空)
                .WhereIF(filters.Type == 2 && !string.IsNullOrEmpty(filters.CompanyCode), reg => reg.IsCompanyCheck && reg.CompanyCode == filters.CompanyCode)//团体(单位不为空)
                .WhereIF(string.IsNullOrEmpty(filters.RegNo) && string.IsNullOrEmpty(filters.SampleNo) && filters.StartDate != null, reg => SqlFunc.Between(reg.RegisterTime, startDate, endDate))
                .Select(reg => reg.RegNo).ToArray();

            if (regNos.Length == 0)
                return sampDatas;

            //过滤数据
            var firstData = _sampleRepository.ReadSampleData(regNos)
                .WhereIF(!string.IsNullOrEmpty(filters.SampleNo), (reg, regComb, sample) => sample.SampleNo == filters.SampleNo)
                .WhereIF(!string.IsNullOrEmpty(filters.Barcode), (reg, regComb, sample) => sample.BarcodeType == filters.Barcode)
                .WhereIF(filters.SampCode.Length > 0, (reg, regComb, sample, comb, barcode) => SqlFunc.ContainsArray(filters.SampCode, barcode.SampCode))
                .Select((reg, regComb, sample, comb, barcode) => new
                {
                    reg.RegNo,
                    reg.Name,
                    reg.Sex,
                    reg.Age,
                    sample.SampleNo,
                    sample.BarcodeSN,
                    sample.PrintTime,
                    sample.GatherTime,
                    comb.BarCodeType,
                    DeptName = SqlFunc.Subqueryable<CodeDepartment>().Where(x => x.DeptCode == comb.ExamDeptCode).Select(x => x.DeptName),
                    comb.CombCode,
                    comb.CombName,
                    HasTransport = SqlFunc.Subqueryable<PeSamplePackage>().Where(x => x.PackageNo == sample.PackageNo && x.HasTransport).Any(),
                    MergeCode = SqlFunc.IIF<string>(comb.IsMergeTube, comb.BarCodeType, comb.CombCode)
                })
                .Distinct()
                .ToList();

            //根据体检号,条码分组
            var thirdData = firstData.GroupBy(x => new { x.RegNo, x.BarCodeType, x.MergeCode,x.SampleNo });

            //组装数据
            foreach (var item in thirdData)
            {
                var combs = item.ToList();
                var person = combs[0];

                sampDatas.Add(new SampleData()
                {
                    BarcodeSN   = person.BarcodeSN,
                    SampleNo    = person.SampleNo,
                    RegNo       = person.RegNo,
                    Name        = person.Name,
                    Sex         = person.Sex,
                    Age         = person.Age,
                    BarCodeType = person.BarCodeType,
                    DeptName    = combs.FirstOrDefault()?.DeptName,
                    Combs       = combs.Select(x => x.CombCode).ToArray(),
                    CombName    = string.Join("|", combs.Select(x => x.CombName).ToArray()),
                    Status      = person.HasTransport ? 4 : person.GatherTime != null ? 3 : person.PrintTime != null ? 2 : 1,
                    GatherTime  = person.GatherTime,
                    PdfUrl      = GetPrintUrl(person.RegNo, person.SampleNo)
                });
            }

            //筛选标本状态
            if (filters.Status != 0)
                sampDatas = sampDatas.Where(x => x.Status == filters.Status).ToList();

            return sampDatas.OrderByDescending(x => x.Status).ToList();
        }

        public List<SampleDetails> ReadSampleDetail(List<SampDataQuery> sampDataQuery)
        {
            if (sampDataQuery.Count <= 0)
            {
                return null;
            }

            var firstData = _sampleRepository.ReadSampleDetail(sampDataQuery.Select(x => x.RegNo).ToArray())
                           .Where(reg => reg.IsActive)
                           .Select((reg, regComb, comb, barcode) => new
                           {
                               reg.Name,
                               reg.RegNo,
                               barcode.Note,
                               barcode.Color,
                               barcode.Attention,
                               barcode.PreNum,
                               regComb.CombCode,
                               comb.CombName,
                               comb.BarCodeType
                           }).ToList();

            var secondData = firstData.GroupBy(x => new { x.RegNo, x.BarCodeType });

            List<SampleDetails> details = new List<SampleDetails>();

            foreach (var item in secondData)
            {
                //当前体检号只查询符合条件的配管列表
                var currentRegNo = sampDataQuery.Where(x => x.RegNo == item.Key.RegNo).FirstOrDefault();

                CombItems[] combItems = new CombItems[] { };
                if (currentRegNo.BarcodeTypes.Length <= 0)
                    combItems = item.ToList().Select(x => new CombItems { CombCode = x.CombCode, CombName = x.CombName }).ToArray();
                else
                    combItems = item.ToList().Where(x => Array.IndexOf(currentRegNo.BarcodeTypes, x.BarCodeType) != -1).Select(x => new CombItems { CombCode = x.CombCode, CombName = x.CombName }).ToArray();

                if (combItems.Length <= 0)
                    continue;

                var person = item.ToList()[0];

                details.Add(new SampleDetails
                {
                    Name = person.Name,
                    RegNo = person.RegNo,
                    Attention = person.Attention,
                    PreNum = person.PreNum,
                    Color = person.Color,
                    Note = person.Note,
                    Combs = combItems
                });
            }

            return details;
        }

        public List<SampleBarcodeDetails> ReadSampleBarcodeDetail(string[] sampleNos)
        {
            if (sampleNos.Length <= 0)
            {
                return null;
            }

            var firstData = _sampleRepository.ReadSampleBarcodeDetail()
                .Where(sample => SqlFunc.ContainsArray(sampleNos, sample.SampleNo))
                           .Select((sample, comb, barcode) => new
                           {
                               sample.RegNo,
                               sample.SampleNo,
                               comb.CombName,
                               barcode.BarcodeName,
                               barcode.Note,
                               barcode.Color,
                               barcode.Attention,
                               barcode.Barcode
                           }).ToList();

            var secondData = firstData.GroupBy(x => new { x.SampleNo, x.Barcode });

            List<SampleBarcodeDetails> details = new List<SampleBarcodeDetails>();

            foreach (var item in secondData)
            {
                var barData = item.ToList();
                var barcodeInfo = barData.Select(x => new BarcodeDetails
                {
                    CombName = x.CombName,
                    Note = x.Note,
                    Color = x.Color,
                    Attention = x.Attention,
                }).ToArray();

                var model = firstData.Where(x => x.SampleNo == item.Key.SampleNo).FirstOrDefault();

                details.Add(new SampleBarcodeDetails
                {
                    RegNo = model.RegNo,
                    SampleNo = model.SampleNo,
                    BarcodeName = model.BarcodeName,
                    BarcodeDetails = barcodeInfo
                });
            }

            return details;
        }

        public string CreateSampleNo()
        {
            return _noGeneration.NextSampleNo()[0];
        }

        public bool SampleBindBarCode(SampleBarCode sampleBarCode, ref string msg)
        {
            if (sampleBarCode.SampleNo?.Length != 10)
            {
                msg = "条码号位数必须为10位!";
                return false;
            }

            if (!sampleBarCode.SampleNo.StartsWith(sampleBarCode.PreNum))
            {
                msg = $"条码的前缀必须是\"{sampleBarCode.PreNum}\"开头";
                return false;
            }

            //查询条码是否存在
            if (_peSampleRepository.Any(x => x.SampleNo == sampleBarCode.SampleNo))
            {
                msg = "条码已被使用,请重新输入!";
                return false;
            }

            //未激活不允许配管
            if (_registerRepository.ReadRegister(sampleBarCode.RegNo).Any(x => x.IsActive == false))
            {
                msg = "记录不存在或未激活!";
                return false;
            }

            //获取登记组合的雪花Id
            var combs = _registerRepository.ReadRegisterCombs(sampleBarCode.RegNo)
                         .Where(x => SqlFunc.ContainsArray(sampleBarCode.Combs, x.CombCode))
                         .Select(x => new
                         {
                             x.Id,
                             x.CombCode
                         }).ToArray();

            //获取组合的条码类型
            var barCodeList = _clusterCombRepository.ReadComb(sampleBarCode.Combs)
                            .Select(x => new
                            {
                                x.CombCode,
                                x.BarCodeType
                            }).ToArray();

            List<PeSample> sampleList = new List<PeSample>();
            //条码序号是一致的
            var barCodeType = barCodeList.Where(x => x.CombCode == sampleBarCode.Combs[0]).Select(x => x.BarCodeType).FirstOrDefault();
            var squenceNo = _noGeneration.NextBarcodeNo(barCodeType)[0];

            //插入数据
            foreach (var item in sampleBarCode.Combs)
            {
                sampleList.Add(new PeSample()
                {
                    RegNo = sampleBarCode.RegNo,
                    RegCombId = combs.Where(x => x.CombCode == item).Select(x => x.Id).FirstOrDefault(),
                    CombCode = item,
                    SampleNo = sampleBarCode.SampleNo,
                    BarcodeType = barCodeType,
                    BarcodeSN = squenceNo,
                    GatherTime = DateTime.Now,
                    GatherOperator = sampleBarCode.GatherOperator
                });
            }

            _peSampleRepository.Insert(sampleList);
            return true;
        }

        public bool SampleBindBarCode(GeneratBarcode sampleBarCode, ref string sampleNo, ref string msg)
        {
            if (sampleBarCode == null)
            {
                msg = "数据为空";
                return false;
            }

            //未激活不允许配管
            if (_registerRepository.ReadRegister(sampleBarCode.RegNo).Any(x => x.IsActive == false))
            {
                msg = "记录不存在或未激活!";
                return false;
            }

            //获取登记组合的雪花Id
            var combs = _registerRepository.ReadRegisterCombs(sampleBarCode.RegNo)
                         .Where(x => SqlFunc.ContainsArray(sampleBarCode.Combs, x.CombCode))
                         .Select(x => new
                         {
                             x.Id,
                             x.CombCode
                         }).ToArray();

            //获取组合的条码类型
            var barCodeList = _clusterCombRepository.ReadComb(sampleBarCode.Combs)
                            .Select(x => new
                            {
                                x.CombCode,
                                x.BarCodeType
                            }).ToArray();

            List<PeSample> sampleList = new List<PeSample>();
            //条码序号是一致的
            var barCodeType = barCodeList.Where(x => x.CombCode == sampleBarCode.Combs[0]).Select(x => x.BarCodeType).FirstOrDefault();
            var squenceNo = _noGeneration.NextBarcodeNo(barCodeType)[0];
            var nextSampleNo = _noGeneration.NextSampleNo()[0];

            //插入数据
            foreach (var item in sampleBarCode.Combs)
            {
                sampleList.Add(new PeSample()
                {
                    RegNo = sampleBarCode.RegNo,
                    RegCombId = combs.Where(x => x.CombCode == item).Select(x => x.Id).FirstOrDefault(),
                    CombCode = item,
                    SampleNo = nextSampleNo,
                    BarcodeType = barCodeType,
                    BarcodeSN = squenceNo,
                    GatherTime = DateTime.Now,
                    GatherOperator = sampleBarCode.GatherOperator
                });
            }

            _peSampleRepository.Insert(sampleList);

            sampleNo = nextSampleNo;//返回条码号给前端打印
            return true;
        }

        public bool CancelSampleBarCode(string[] sampleNos, ref string msg)
        {
            if (sampleNos.Length <= 0)
            {
                msg = "条码号为空";
                return false;
            }

            if (_peSampleRepository.Any(x => SqlFunc.ContainsArray(sampleNos, x.SampleNo) && !string.IsNullOrEmpty(x.PackageNo)))
            {
                msg = "标本已打包运送,无法取消配管";
                return false;
            }

            return _peSampleRepository.Delete(x => SqlFunc.ContainsArray(sampleNos, x.SampleNo));
        }

        public UnPackedSample[] ReadUnPackedSample(SampleUnpackedQuery sampleUnpackedQuery)
        {
            DateTime? startDate = new DateTime();
            DateTime? endDate = new DateTime();

            if (sampleUnpackedQuery.BeginGatherTime != null && sampleUnpackedQuery.EndGatherTime != null)
            {
                startDate = sampleUnpackedQuery.BeginGatherTime?.Date;
                endDate = sampleUnpackedQuery.EndGatherTime?.AddDays(1).Date;
            }

            return _sampleRepository.ReadSamplePackageData()
                .Where((samp, reg, bar) => SqlFunc.IsNullOrEmpty(samp.PackageNo))
                .Where((samp, reg, bar) => SqlFunc.Between(samp.GatherTime, startDate, endDate))
                .Where((samp, reg, bar) => samp.BarcodeSN >= sampleUnpackedQuery.BeginBarcodeSN && samp.BarcodeSN <= sampleUnpackedQuery.EndBarcodeSN)
                .WhereIF(!sampleUnpackedQuery.IsAll, (samp, reg, bar) => reg.IsCompanyCheck == sampleUnpackedQuery.IsCompanyCheck)
                .WhereIF(!string.IsNullOrEmpty(sampleUnpackedQuery.CompanyCode), (samp, reg, bar) => reg.CompanyCode == sampleUnpackedQuery.CompanyCode)
                .WhereIF(!string.IsNullOrEmpty(sampleUnpackedQuery.BarcodeType), (samp, reg, bar) => samp.BarcodeType == sampleUnpackedQuery.BarcodeType)
                .WhereIF(!string.IsNullOrEmpty(sampleUnpackedQuery.SampleCode), (samp, reg, bar) => bar.SampCode == sampleUnpackedQuery.SampleCode)
                .Select((samp, reg) => new UnPackedSample
                {
                    ActiveTime = reg.ActiveTime,
                    BarcodeSN = samp.BarcodeSN,
                    Name = reg.Name,
                    Sex = reg.Sex,
                    Age = reg.Age,
                    RegNo = samp.RegNo,
                    SampleNo = samp.SampleNo
                })
                .Distinct()
                .ToArray();
        }

        public UnPackedSample ReadUnPackedSample(string sampleNo)
        {
            if (string.IsNullOrEmpty(sampleNo))
            {
                return null;
            }

            return _sampleRepository.ReadSamplePackageData()
                .Where(samp => samp.SampleNo == sampleNo && SqlFunc.IsNullOrEmpty(samp.PackageNo))
                .Select((samp, reg, bar) => new UnPackedSample
                {
                    ActiveTime = reg.ActiveTime,
                    BarcodeSN = samp.BarcodeSN,
                    Name = reg.Name,
                    Sex = reg.Sex,
                    Age = reg.Age,
                    RegNo = reg.RegNo,
                    SampleNo = samp.SampleNo
                })
                .First();
        }

        public bool PackTransportSample(SamplePackTransport samplePackTransport, ref string msg)
        {
            var sampleNos = samplePackTransport.SampleNos;//条码号列表

            var len = sampleNos.Length;//提供的条码数

            if (len == 0)
            {
                msg = "没有提供条码号";
                return false;
            }
            else
            {
                //用于判断提供的条码是否都存在
                var count = _sampleRepository
                    .ReadSample()
                    .Where(x => SqlFunc.ContainsArray(sampleNos, x.SampleNo) && SqlFunc.IsNullOrEmpty(x.PackageNo))
                    .Select(x => x.SampleNo)
                    .Distinct()
                    .Count();

                if (len != count)
                {
                    msg = "条码可能不存在或已打包";
                    return false;
                }
            }

            var packageNo = _noGeneration.NextSamplePkgNo()[0];//包号

            var dateTime = DateTime.Now;

            var peSamplePackageNew = new PeSamplePackage
            {
                PackageNo = packageNo,
                PackageTime = dateTime,
                Packer = samplePackTransport.OperatorCode,
                HasTransport = true,
                TransportTime = dateTime
            };

            _tranRepository.ExecTran(() =>
            {
                _peSamplePackageRepository.Insert(peSamplePackageNew);

                #region 更新标本条码的包信息
                _peSampleRepository.Update(
                    x => new PeSample()
                    {
                        PackageNo = packageNo,
                        PackageTime = DateTime.Now
                    }, x => SqlFunc.ContainsArray(sampleNos, x.SampleNo));
                #endregion
            });

            msg = "成功";
            return true;
        }

        public SamplePackage ReadPackageBySampleNo(string sampleNo)
        {
            if (string.IsNullOrEmpty(sampleNo))
            {
                return null;
            }

            return _peSampleRepository
                .FindAll(a => a.SampleNo == sampleNo)
                .Select(a => new SamplePackage
                {
                    PackageNo = a.PackageNo,
                    PackageTime = a.PackageTime
                })
                .FirstOrDefault();
        }

        public SamplePackage[] ReadSamplePackage(DateTime? beginPackageTime, DateTime? endPackageTime, string sampleCode)
        {
            DateTime? startDate = new DateTime();
            DateTime? endDate = new DateTime();

            if (beginPackageTime != null && endPackageTime != null)
            {
                startDate = beginPackageTime?.Date;
                endDate = endPackageTime?.AddDays(1).Date;
            }

            return _sampleRepository.ReadSamplePackage()
                .Where((a, b) => !SqlFunc.IsNullOrEmpty(a.PackageNo))
                .Where((a, b) => SqlFunc.Between(a.PackageTime, startDate, endDate))
                .Where((a, b) => b.SampCode == sampleCode)
                .Select((a, b) => new SamplePackage
                {
                    PackageNo = a.PackageNo,
                    PackageTime = a.PackageTime
                })
                .Distinct()
                .ToArray();
        }

        public SamplePackageTime ReadSamplePackageTime(string packageNo)
        {
            return _sampleRepository.ReadSamplePackage()
                .Where((a, b) => !SqlFunc.IsNullOrEmpty(a.PackageNo))
                .Where((a, b) => a.PackageNo == packageNo)
                .Select((a, b) => new SamplePackageTime
                {
                    SampleCode = b.SampCode,
                    PackageTime = a.PackageTime
                })
                .First();
        }

        public bool DeletePackage(string packageNo, ref string msg)
        {
            if (string.IsNullOrEmpty(packageNo))
            {
                msg = "包号为空";
                return false;
            }

            #region 删除标本打包表
            var packages = _peSamplePackageRepository
                .FindAll(x => x.PackageNo == packageNo)
                .ToList();

            if (packages.Any())
            {
                var isExist = packages.Any(a => a.IsArrive);//是否送达

                if (isExist)
                {
                    msg = "已送达，不能删除";
                    return false;
                }
            }
            else
            {
                msg = "不存在包记录";
                return false;
            }
            #endregion

            #region 修改标本条码表
            var samples = _peSampleRepository
                .FindAll(x => x.PackageNo == packageNo)
                .ToList();

            if (samples.Any())
            {
                foreach (var item in samples)
                {
                    item.PackageNo = null;
                }
            }
            else
            {
                msg = "存在包记录，不存在相关的标本";
                return false;
            }
            #endregion

            _tranRepository.ExecTran(() =>
            {
                _peSamplePackageRepository.Delete(packages);

                _peSampleRepository.Update(samples);
            });

            msg = "成功";
            return true;
        }

        public PackageData[] ReadDataByPackageNo(string packageNo)
        {
            if (string.IsNullOrEmpty(packageNo))
            {
                return null;
            }

            return _sampleRepository.ReadSamplePackageData()
                .Where(samp => samp.PackageNo == packageNo)
                .Select((samp, reg) => new PackageData
                {
                    BarcodeSN = samp.BarcodeSN,
                    SampleNo = samp.SampleNo,
                    RegNo = samp.RegNo,
                    Name = reg.Name,
                    Sex = reg.Sex,
                    Age = reg.Age
                })
                .Distinct()
                .ToArray();
        }

        public bool DeletePackageSample(string sampleNo, ref string msg)
        {
            if (string.IsNullOrEmpty(sampleNo))
            {
                msg = "条码号为空";
                return false;
            }

            #region 获取标本信息
            var samples = _peSampleRepository.FindAll(a => a.SampleNo == sampleNo).ToList();

            var packageNo = "";

            if (samples.Any())
            {
                var peSamples = samples.Where(a => !string.IsNullOrEmpty(a.PackageNo));//是否打包

                if (!peSamples.Any())
                {
                    msg = "标本没有打包";
                    return false;
                }

                packageNo = peSamples.First().PackageNo;

                //从包中剔除
                foreach (var item in samples)
                {
                    item.PackageNo = null;
                }
            }
            else
            {
                msg = "不存在标本记录";
                return false;
            }
            #endregion

            #region 删除包
            var packages = _peSamplePackageRepository
                .FindAll(a => a.PackageNo == packageNo)
                .ToList();

            if (packages.Any())
            {
                var isExist = packages.Any(a => a.IsArrive);//已送达

                if (isExist)
                {
                    msg = "相关包已送达";
                    return false;
                }
            }
            else
            {
                msg = "没有相关的包记录";
                return false;
            }

            //是否存在其它标本,不存在则删除包
            var isExistOtherSample = _sampleRepository.ReadSample()
                .Any(a => a.PackageNo == packageNo && a.SampleNo != sampleNo);
            #endregion

            _tranRepository.ExecTran(() =>
            {
                _peSampleRepository.Update(samples);

                if (!isExistOtherSample)//不存在其它标本，删除包
                {
                    _peSamplePackageRepository.Delete(packages);
                }
            });

            msg = "成功";
            return true;
        }

        /// <summary>
        /// 生成所有标本条码
        /// </summary>
        /// <param name="regNo"></param>
        /// <returns></returns>
        public bool SyncSampleBindBarCodeAll(string regNo,string clusterCode = null)
        {
            if (_systemParameterService.TestTubeMode != "系统生成")
                return false;
            if (regNo == null)
                return false;
            //未激活不允许配管
            if (_registerRepository.ReadRegister(regNo).Any(x => x.IsActive == false && !x.IsCompanyCheck))
                return false;
            //获取未收费检验组合的id及分管条件
            var sampList = _sampleRepository.ReadAllCombSampleToGenerate(regNo)
                        .Select((comb, regComb, sample, bar) => new {
                            regComb.CombCode,
                            regComb.Id,
                            comb.BarCodeType,
                            sample.SampleNo,
                            MergeCode = comb.IsMergeTube ? comb.BarCodeType : regComb.CombCode,
                            bar.SampCode
                        }).ToList();

            if (sampList.Count() > 0)
            {
                var combsdd = CalculateTestTubeBefrom(sampList.Select(x => x.CombCode).ToArray(), clusterCode);
                //生成全部条码
                foreach (var tube in combsdd.Where(x=>x.TubeCombCode!=_systemParameterService.VenousBloodComb))
                {
                    List<PeSample> sampleList = new List<PeSample>();
                    var hh = sampList.Where(x => tube.BeFrom.Contains(x.CombCode)).ToArray();
                    var squenceNo = _noGeneration.NextBarcodeNo(tube.BarCode)[0];
                    //获取已生成条码号并管或生成新条码
                    var nextSampleNo = hh.Any(x => !string.IsNullOrEmpty(x.SampleNo)) ? hh.Where(x => !string.IsNullOrEmpty(x.SampleNo)).First().SampleNo : _noGeneration.NextSampleNo()[0];
                    //插入数据
                    foreach (var item in hh)
                    {
                        if (!string.IsNullOrEmpty(item.SampleNo))
                        {
                            continue;
                        }
                        sampleList.Add(new PeSample()
                        {
                            RegNo = regNo,
                            RegCombId = item.Id,
                            CombCode = item.CombCode,
                            SampleNo = nextSampleNo,
                            BarcodeType = tube.BarCode,
                            BarcodeSN = squenceNo,
                            SampCode = item.SampCode
                        });
                    }
                    if (sampleList.Count() > 0)
                        _peSampleRepository.Insert(sampleList);
                }
            }

            //删除多余条码
            var existLisCombs = _registerRepository.ReadRegisterCombs(regNo).Where(x => x.CheckCls == CheckCls.检验检查).ToList();
            var deletes = _sampleRepository.ReadSample()
                .Where(sample => sample.RegNo == regNo && !existLisCombs.Select(x => x.Id).ToArray().Contains(sample.RegCombId)).ToList();
            if (deletes.Count > 0)
            {
                _peSampleRepository.Delete(deletes);
            }
            return true;
        }

        public bool GatherSample(string SampleNo, ref object info, ref string msg)
        {
            var samples = _peSampleRepository.FindAll(x => x.SampleNo == SampleNo).ToList();
            if (samples == null || samples.Count == 0)
            {
                msg = "条码不存在";
                return false;
            }
            else
            {
                var peStatus = _registerRepository.ReadRegister().Where(x => x.RegNo == samples[0].RegNo).Select(x => x.PeStatus).First();
                if(peStatus==PeStatus.已总检|| peStatus == PeStatus.已审核)
                {
                    msg = "已总检/审核，无法采集！";
                    return false;
                }
                if (samples[0].GatherTime != null)
                {
                    msg = "条码已采集！";
                    return false;
                }
                var count = _registerRepository.ReadRegisterCombs(samples[0].RegNo)
                    .Where(x => SqlFunc.ContainsArray(samples.Select(sample=>sample.RegCombId).ToArray(),x.Id)&&x.PayStatus==PayStatus.未收费 &&x.IsPayBySelf).Count();
                if(count > 0)
                {
                    msg = "该条码所包含组合未收费！";
                    return false;
                }
                samples.BatchUpdate(x => x.GatherOperator = _httpContextUser.UserId);
                samples.BatchUpdate(x => x.GatherTime = DateTime.Now);
                info = new
                {
                    RegNo = samples[0].RegNo,
                    SampleNo = SampleNo,
                    GatherTime = samples[0].GatherTime,
                };
                _peSampleRepository.Update(samples);
                return true;
            }
        }

        public bool CancelGatherSample(string SampleNo, ref string msg)
        {
            var samples = _peSampleRepository.FindAll(x => x.SampleNo == SampleNo).ToList();
            if (samples == null || samples.Count == 0)
            {
                msg = "条码不存在";
                return false;
            }
            else
            {
                if (samples[0].GatherTime == null)
                {
                    msg = "条码未采集！";
                    return false;
                }
                samples.BatchUpdate(x => x.GatherOperator = null);
                samples.BatchUpdate(x => x.GatherTime = null);
                _peSampleRepository.Update(samples);
                return true;
            }
        }

        public bool PrintSample(string SampleNo, ref string msg)
        {
            var samples = _peSampleRepository.FindAll(x => x.SampleNo == SampleNo).ToList();
            if (samples.IsNullOrEmpty())
            {
                msg = "条码不存在";
                return false;
            }
            else
            {
                samples.BatchUpdate(x => x.PrintTime = DateTime.Now);
                _peSampleRepository.Update(samples);
                return true;
            }           
        }

        /// <summary>
        /// 更新打印时间
        /// </summary>
        /// <param name="regNo">体检号</param>
        /// <param name="dateTime">打印时间</param>
        /// <param name="msg">消息</param>
        /// <returns>bool</returns>
        public bool ModifySamplePrintTime(string regNo, DateTime? dateTime, ref string msg)
        {
            var samples = _peSampleRepository.FindAll(x => x.RegNo == regNo).ToList();
            if (samples.IsNullOrEmpty())
            {
                msg = "条码不存在";
                return false;
            }
            else
            {
                samples.BatchUpdate(x => x.PrintTime = dateTime);
                _peSampleRepository.Update(samples);
                return true;
            }
        }

        public List<SampleData> ReadGatherSampleData(SampleMultipleFilters filters)
        {
            List<SampleData> sampDatas = new();
            DateTime? startDate = new DateTime();
            DateTime? endDate = new DateTime();

            if (filters.StartDate != null && filters.EndDate != null)
            {
                startDate = filters.StartDate?.Date;
                endDate = filters.EndDate?.AddDays(1).Date;
            }
            string[] regNos = new string[0];
            if (!string.IsNullOrEmpty(filters.SampleNo))
            {
                var regNo = _peSampleRepository.First(x => x.SampleNo == filters.SampleNo)?.RegNo;
                if (regNo != null)
                {
                    regNos = new string[] { regNo };
                }
            }
            else
            {
                regNos = _registerRepository.ReadRegister()
                .Where(reg => reg.IsActive)
                .WhereIF(!string.IsNullOrEmpty(filters.RegNo), reg => reg.RegNo == filters.RegNo)
                .WhereIF(!string.IsNullOrEmpty(filters.Name), reg => reg.Name.Contains(filters.Name))
                .WhereIF(!string.IsNullOrEmpty(filters.CardNo), reg => reg.CardNo.Contains(filters.CardNo))
                .WhereIF(filters.Type == 1, reg => reg.IsCompanyCheck == false)//个人
                .WhereIF(filters.Type == 2 && string.IsNullOrEmpty(filters.CompanyCode), reg => reg.IsCompanyCheck)//团体(单位为空)
                .WhereIF(filters.Type == 2 && !string.IsNullOrEmpty(filters.CompanyCode), reg => reg.IsCompanyCheck && reg.CompanyCode == filters.CompanyCode)//团体(单位不为空)
                .WhereIF(string.IsNullOrEmpty(filters.RegNo) && string.IsNullOrEmpty(filters.SampleNo) && filters.StartDate != null, reg => SqlFunc.Between(reg.RegisterTime, startDate, endDate))
                .Select(reg => reg.RegNo).ToArray();
            }
            //查询登记信息

            if (regNos.Length == 0)
                return sampDatas;

            //过滤数据
            var sampleData = _sampleRepository.ReadSampleData(regNos)
                .WhereIF(!string.IsNullOrEmpty(filters.Barcode), (reg, regComb, sample) => sample.BarcodeType == filters.Barcode)
                .WhereIF(filters.SampCode.Length > 0, (reg, regComb, sample, comb, barcode) => SqlFunc.ContainsArray(filters.SampCode, barcode.SampCode))
                .ToArray();

            // 获取缓存数据
            var deptDict = _cacheRepository.DictDepartment();
            var sampleDict = _cacheRepository.DictSample();

            var sampleData = _sampleRepository.ReadSampleData(regNos)
                .WhereIF(!string.IsNullOrEmpty(filters.Barcode), (reg, regComb, sample) => sample.BarcodeType == filters.Barcode)
                .WhereIF(filters.SampCode.Length > 0, (reg, regComb, sample, comb, barcode) => SqlFunc.ContainsArray(filters.SampCode, barcode.SampCode))
                .Select((reg, regComb, sample, comb, barcode) => new
                {
                    RegNo = reg.RegNo,
                    Name = reg.Name,
                    Sex = reg.Sex,
                    Age = reg.Age,
                    SampleNo = sample.SampleNo,
                    BarcodeSN = sample.BarcodeSN,
                    PrintTime = sample.PrintTime,
                    GatherTime = sample.GatherTime,
                    BarCodeType = comb.BarCodeType,
                    ExamDeptCode = comb.ExamDeptCode,
                    CombCode = comb.CombCode,
                    CombName = comb.CombName,
                    SampCode = sample.SampCode
                })
                .Distinct()
                .ToArray();

            // 获取缓存数据
            var deptDict = _cacheRepository.DictDepartment();
            var sampleDict = _cacheRepository.DictSample();

            var firstData = sampleData.Select(x => new
            {
                x.RegNo,
                x.Name,
                x.Sex,
                x.Age,
                x.SampleNo,
                x.BarcodeSN,
                x.PrintTime,
                x.GatherTime,
                x.BarCodeType,
                DeptName = deptDict.TryGetValue(x.ExamDeptCode ?? "", out var dept) ? dept.DeptName : "",
                x.CombCode,
                x.CombName,
                SampName = sampleDict.TryGetValue(x.SampCode ?? "", out var sampleInfo) ? sampleInfo.SampName : ""
            }).ToList();

            //根据体检号,条码分组
            var thirdData = firstData.GroupBy(x => new { x.RegNo, x.SampleNo });

            //组装数据
            foreach (var item in thirdData)
            {
                var combs = item.ToList();
                var person = combs[0];

                sampDatas.Add(new SampleData()
                {
                    BarcodeSN   = person.BarcodeSN,
                    SampleNo    = person.SampleNo,
                    RegNo       = person.RegNo,
                    Name        = person.Name,
                    Sex         = person.Sex,
                    Age         = person.Age,
                    BarCodeType = person.BarCodeType,
                    DeptName    = combs.FirstOrDefault()?.DeptName,
                    Combs       = combs.Select(x => x.CombCode).ToArray(),
                    CombName    = string.Join("|", combs.Select(x => x.CombName).ToArray()),
                    Status      = person.GatherTime != null ? 3 : 2,
                    GatherTime  = person.GatherTime,
                    SampName    = person.SampName
                });
            }

            //筛选标本状态
            if (filters.Status != 0)
                sampDatas = sampDatas.Where(x => x.Status == filters.Status).ToList();

            return sampDatas.OrderByDescending(x => x.Status).ToList();
        }

        /// <summary>
        /// 计算试管关系（旧）
        /// </summary>
        /// <param name="combs"></param>
        /// <param name="clustCode"></param>
        /// <returns></returns>
        public List<SampleCombs> CalculateTestTubeBefrom(string[] combs, string clustCode = null)
        {
            var calculateTubeBefromInputs = combs.Select(x => new CalculateTubeBefromInput
            {
                CombCode = x,
                IsPayBySelf = true
            }).ToArray();
            return CalculateTestTubeBefrom(calculateTubeBefromInputs, clustCode);
        }
        /// <summary>
        /// 计算试管关系（团体套餐用）
        /// </summary>
        /// <param name="combs"></param>
        /// <param name="clustCode"></param>
        /// <returns></returns>
        public List<SampleCombs> CalculateTestTubeBefrom(CompanyComb[] combs, string clustCode = null)
        {
            var calculateTubeBefromInputs = combs.Select(x => new CalculateTubeBefromInput
            {
                CombCode    = x.CombCode,
                IsPayBySelf = x.IsPayBySelf
            }).ToArray();
            return CalculateTestTubeBefrom(calculateTubeBefromInputs, clustCode);
        }

        /// <summary>
        /// 生成所有条码-新团体导入用 不存在旧条码，取消数据库判断
        /// </summary>
        /// <param name="regNo"></param>
        /// <param name="comb"></param>
        public List<PeSample> GenerateAllSampleNewCompany(string regNo, RegisterNewComb[] comb,string clusterCode = null)
        {
            if (_systemParameterService.TestTubeMode != "系统生成")
                return new();
            if (regNo.IsNullOrEmpty())
                return new();
            if (comb.Length == 0)
                return new();
            var combDictionary = _cacheRepository.DictComb();
            var barcodeDictionary = _cacheRepository.DictBarcode();
            var newSamples = new List<PeSample>();
            var calculateCombs = comb.ToArray();
            var mergeRelationship = CalculateTestTubeBefromNew(calculateCombs, clusterCode).Where(x => x.TubeCombCode != _systemParameterService.VenousBloodComb).ToList();
            if (mergeRelationship.Any())
            {
                var nextSampleNos = _noGeneration.NextSampleNo(mergeRelationship.Count);
                for(int i = 0; i < mergeRelationship.Count; i++)
                {
                    var item = mergeRelationship[i];
                    var lisComb = comb.Where(x => item.BeFrom.Any(l => l == x.Id)).ToList();
                    if (lisComb.Count > 0)
                    {
                        var barcode = combDictionary[lisComb[0].CombCode].ItemComb.BarCodeType;
                        var newSampno = nextSampleNos[i];
                        //次数跳过标本序号的生成，减少更新数据库次数，提高效率
                        for (int j = 0; j < lisComb.Count; j++)
                        {
                            newSamples.Add(new PeSample()
                            {
                                RegNo = regNo,
                                RegCombId = lisComb[j].Id,
                                CombCode = lisComb[j].CombCode,
                                SampleNo = newSampno,
                                BarcodeType = barcode,
                                SampCode = barcodeDictionary[barcode].SampCode
                            });
                        }
                    } 
                }
            }
            return newSamples;
        }

        /// <summary>
        /// 计算试管关系-新
        /// </summary>
        /// <param name="combs"></param>
        /// <param name="clustCode"></param>
        /// <returns></returns>
        public List<SampleCombsNew> CalculateTestTubeBefromNew(RegisterNewComb[] combs,string clustCode = null)
        {
            if (combs.Length == 0)
            {
                return new();
            }
            var combDictionary = _cacheRepository.DictComb();
            var barcodeDictionary = _cacheRepository.DictBarcode();
            var sampleCombs = new List<SampleCombsNew>();

            if (!clustCode.IsNullOrEmpty())
            {
                //获取套餐管关系 加入 加项组合关系继续并管算
                sampleCombs.AddRange(GetCompanyTubeRelation(combs, clustCode));
                combs = combs.Where(x => !sampleCombs.Any(l => l.BeFrom.Contains(x.Id))).ToArray();
            }

            var needCalculateComb = combs.Where(x => (x.IsPayBySelf == true && x.PayStatus == PayStatus.未收费) || (x.IsPayBySelf == false && x.IsGathered == false)).ToArray();
            var combBindTestTubes = combDictionary.Join(barcodeDictionary,
                     comb => comb.Value.ItemComb.BarCodeType, barType => barType.Key, (comb, barType) => new
                     {
                         RegCombCode  = comb.Value.ItemComb.CombCode,
                         TubeCombCode = barType.Value.FeeCombCode,
                         BarCodeType  = barType.Key,
                         MegeType     = comb.Value.ItemComb.IsMergeTube
                     }
                ).ToArray();
            //组合与检验试管的新对应关系
            var newCombMapTestTubes = (
                from combTube in combBindTestTubes
                join regComb in needCalculateComb
                on combTube.RegCombCode equals regComb.CombCode
                select new
                {
                    regComb.Id,
                    combTube.RegCombCode,
                    combTube.TubeCombCode,
                    combTube.BarCodeType,
                    MergeCode = combTube.MegeType ? combTube.TubeCombCode : combTube.RegCombCode,
                    regComb.IsPayBySelf
                }).ToArray();
              sampleCombs.AddRange( newCombMapTestTubes.GroupBy(g => new { g.TubeCombCode, g.MergeCode, g.BarCodeType, g.IsPayBySelf })
                .Select(tube => new SampleCombsNew
                {
                    TubeCombCode = tube.Key.TubeCombCode,
                    BeFrom       = tube.Select(x => x.Id).ToArray(),
                    BarCode      = tube.Key.BarCodeType,
                    Discount     = 1,
                    Price        = tube.Key.TubeCombCode.IsNullOrEmpty() ? 0 : combDictionary[tube.Key.TubeCombCode].ItemComb.Price,
                    IsPayBySelf  = tube.Key.IsPayBySelf
                }));
            return sampleCombs;
        }

        /// <summary>
        /// 生成所有条码-登记用
        /// </summary>
        /// <param name="regNo"></param>
        /// <param name="comb"></param>
        /// <param name="clustCode"></param>
        public void GenerateAllSampleNew(string regNo, RegisterNewComb[] comb, string clustCode = null)
        {
            if (_systemParameterService.TestTubeMode != "系统生成")
                return;
            if (regNo.IsNullOrEmpty())
                return;            
            var oldSamples = _sampleRepository.ReadSample().Where(x => x.RegNo == regNo).ToList();        
            var combDictionary = _cacheRepository.DictComb();
            var barcodeDictionary = _cacheRepository.DictBarcode();
            var deleteSamples = new List<PeSample>();
            var newSamples = new List<PeSample>();

            if (comb.Length > 0)
            {
                var calculateCombs = comb.Where(x => !oldSamples.Any(l => l.RegCombId == x.Id && l.GatherTime != null)).ToArray();
                var mergeRelationship = CalculateTestTubeBefromNew(calculateCombs, clustCode);//用试管忽略了不收试管费的项目
                if (mergeRelationship.Any())
                {
                    foreach (var item in mergeRelationship.Where(x=>x.TubeCombCode!=_systemParameterService.VenousBloodComb))
                    {
                        var lisComb = comb.Where(x => item.BeFrom.Any(l => l == x.Id)).ToList();
                        if (lisComb.IsNullOrEmpty()) continue;

                        var sampleNo = oldSamples.Where(x => item.BeFrom.Any(l => l == x.RegCombId) && x.GatherTime == null).Select(x => x.SampleNo).FirstOrDefault();
                        if (sampleNo == null)
                        {
                            // 新增条码
                            var barcode = combDictionary[lisComb[0].CombCode].ItemComb.BarCodeType;
                            var newSampno = _noGeneration.NextSampleNo()[0];
                            newSamples.AddRange(lisComb.Select(x => new PeSample()
                            {
                                RegNo = regNo,
                                RegCombId = x.Id,
                                CombCode = x.CombCode,
                                SampleNo = newSampno,
                                BarcodeType = barcode,
                                BarcodeSN = _noGeneration.NextBarcodeNo(barcode)[0],
                                SampCode = barcodeDictionary[barcode].SampCode
                            }).ToArray());
                        }
                        else
                        {
                            // 合并条码
                            var newComb = lisComb.Where(x => !oldSamples.Any(l => l.RegCombId == x.Id)).ToArray();
                            var delComb = oldSamples.Where(x => !lisComb.Any(l => l.Id == x.RegCombId) && x.SampleNo == sampleNo);
                            if (newComb.Length > 0)
                            {
                                var barcode = combDictionary[newComb[0].CombCode].ItemComb.BarCodeType;
                                newSamples.AddRange(newComb.Select(x => new PeSample()
                                {
                                    RegNo = regNo,
                                    RegCombId = x.Id,
                                    CombCode = x.CombCode,
                                    SampleNo = sampleNo,
                                    BarcodeType = barcode,
                                    BarcodeSN = _noGeneration.NextBarcodeNo(barcode)[0],
                                    SampCode = barcodeDictionary[barcode].SampCode
                                }).ToArray());
                            }
                            deleteSamples.AddRange(delComb);
                        }
                    }
                }
            }

            //判断完全不存在的
            deleteSamples.AddRange(oldSamples.Where(x => !comb.Any(l => l.Id == x.RegCombId)).Except(deleteSamples));
            if (newSamples.Count > 0)
            {
                _peSampleRepository.Insert(newSamples);
            }
            if (deleteSamples.Count > 0)
            {
                _peSampleRepository.Delete(deleteSamples);
            }
        }

        #region 私有方法 
        /// <summary>
        /// 获取团体试管关系 新版
        /// </summary>
        /// <param name="comb"></param>
        /// <param name="clustCode"></param>
        /// <returns></returns>
        private List<SampleCombsNew> GetCompanyTubeRelation(RegisterNewComb[] comb, string clustCode)
        {
            _cacheRepository.DictCompanyClusterTestTube().TryGetValue(clustCode, out var tubeList);
            if (tubeList == null)
                return new();
            var combDictionary = _cacheRepository.DictComb();
            var barcodeDictionary = _cacheRepository.DictBarcode();
            var samples = new List<SampleCombsNew>();
            foreach (var item in tubeList)
            {
                foreach (var item2 in item.BeFromList)
                {
                    var barcode = combDictionary[item2[0]].ItemComb.BarCodeType;
                    samples.Add(new SampleCombsNew
                    {
                        TubeCombCode = item.CombCode,
                        BeFrom       = comb.Where(x=>item2.Contains(x.CombCode)).Select(x=>x.Id).ToArray(),
                        BarCode      = barcode,
                        Discount     = item.Discount,
                        Price        = item.Price,
                        IsPayBySelf  = item.IsPayBySelf
                    });
                }
            }
            return samples;
        }

        /// <summary>
        /// 获取团体试管关系 兼容旧版
        /// </summary>
        /// <param name="combs"></param>
        /// <param name="clustCode"></param>
        /// <returns></returns>
        private List<SampleCombs> GetCompanyTubeRelation(string[] combs, string clustCode)
        {
            _cacheRepository.DictCompanyClusterTestTube().TryGetValue(clustCode, out var tubeList);
            if (tubeList == null)
                return new();
            var combDictionary = _cacheRepository.DictComb();
            var barcodeDictionary = _cacheRepository.DictBarcode();
            var samples = new List<SampleCombs>();
            foreach (var item in tubeList)
            {
                foreach (var item2 in item.BeFromList)
                {
                    var barcode = combDictionary[item2[0]].ItemComb.BarCodeType;
                    samples.Add(new SampleCombs
                    {
                        TubeCombCode = item.CombCode,
                        BeFrom = combs.Where(x => item2.Contains(x)).ToArray(),
                        BarCode = barcode,
                        TubeCombName = combDictionary[item.CombCode].ItemComb.CombName,
                        Price = item.Price,
                        Discount = item.Discount,
                    });
                }
            }
            return samples;
        }

        /// <summary>
        /// 获取条码打印地址
        /// </summary>
        /// <param name="regNo"></param>
        /// <param name="sampleNo"></param>
        /// <returns></returns>
        private string GetPrintUrl(string regNo, string sampleNo)
        {
            var path = PrintFileType.Barcode.GetApiPath();
            if (path.IsNullOrEmpty())
                return string.Empty;

            path = $"{ConstantUrl.FileHostUrl}{path.Replace("{regNo}", regNo, StringComparison.OrdinalIgnoreCase)}?sampleNo={sampleNo}&updatePrintStatus=true";
            return path;
        }
        
        /// <summary>
        /// 计算试管关系（通过组合代码）
        /// </summary>
        /// <param name="combs"></param>
        /// <param name="clustCode"></param>
        /// <returns></returns>
        private List<SampleCombs> CalculateTestTubeBefrom(CalculateTubeBefromInput[] combs, string clustCode = null)
        {
            if (combs.Length == 0)
            {
                return new();
            }
            var combDictionary = _cacheRepository.DictComb();
            var barcodeDictionary = _cacheRepository.DictBarcode();
            var sampleCombs = new List<SampleCombs>();
            if (!clustCode.IsNullOrEmpty())
            {
                sampleCombs.AddRange(GetCompanyTubeRelation(combs.Select(x => x.CombCode).ToArray(), clustCode));
                combs = combs.Where(x => !sampleCombs.Any(l => l.BeFrom.Contains(x.CombCode))).ToArray();
            }
            var combBindTestTubes = combDictionary.Join(barcodeDictionary,
                     comb => comb.Value.ItemComb.BarCodeType, barType => barType.Key, (comb, barType) => new
                     {
                         RegCombCode = comb.Value.ItemComb.CombCode,
                         TubeCombCode = barType.Value.FeeCombCode,
                         BarCodeType = barType.Key,
                         MegeType = comb.Value.ItemComb.IsMergeTube
                     }
                ).ToArray();
            //组合与检验试管的新对应关系
            var newCombMapTestTubes = (
                from combTube in combBindTestTubes
                join regComb in combs
                on combTube.RegCombCode equals regComb.CombCode
                select new
                {
                    CombCode = regComb.CombCode,
                    combTube.RegCombCode,
                    combTube.TubeCombCode,
                    combTube.BarCodeType,
                    MergeCode = combTube.MegeType ? combTube.TubeCombCode : combTube.RegCombCode,
                    regComb.IsPayBySelf
                }).ToArray();
            sampleCombs.AddRange(newCombMapTestTubes.GroupBy(g => new { g.TubeCombCode, g.MergeCode, g.BarCodeType,g.IsPayBySelf })
                .Select(tube => new SampleCombs
                {
                    TubeCombCode = tube.Key.TubeCombCode,
                    BeFrom = tube.Select(x => x.CombCode.ToString()).ToArray(),
                    BarCode = tube.Key.BarCodeType,
                    TubeCombName = tube.Key.TubeCombCode.IsNullOrEmpty() ? string.Empty : combDictionary[tube.Key.TubeCombCode].ItemComb.CombName,
                    Price = tube.Key.TubeCombCode.IsNullOrEmpty() ? 0 : combDictionary[tube.Key.TubeCombCode].ItemComb.Price,
                    Discount = 1,
                    IsPayBySelf = tube.Key.IsPayBySelf,
                }));
            return sampleCombs;
        }
    }
}
#endregion