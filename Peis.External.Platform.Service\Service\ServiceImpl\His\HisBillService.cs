﻿using MediatR;
using Peis.External.Platform.Service.Service.IService.BaiHui;
using Peis.External.Platform.Service.Service.IService.His;
using Peis.Model.DTO;
using Peis.Model.DTO.External.His;
using Peis.Model.Other.PeEnum;
using Peis.Repository.IRepository;
using Peis.Service.Service.MediatR;
using System.Collections.Concurrent;
using System.Text.RegularExpressions;

namespace Peis.External.Platform.Service.Service.ServiceImpl.His;

public class HisBillService : IHisBillService
{
    #region 仓储
    private readonly IBHBasicService _bhBasicService;
    private readonly IDataTranRepository _dataTranRepository;
    private readonly IDataRepository<ExtHisOrderIndex> _hisOrderIndexRepository;
    private readonly IDataRepository<ExtHisBill> _hisBillRepository;
    private readonly IDataRepository<PeRegister> _peRegisterRepository;
    private readonly ISplitTableRepository<ExtHisBillComb> _hisBillCombRepository;
    private readonly ISplitTableRepository<PeRegisterComb> _peRegisterCombRepository;
    private readonly IShenShanRegisterRepository _shenRegisterRepository;
    private readonly IDataRepository<CodeItemComb> _codeItemCombRepository;
    private readonly IRegisterRepository _registerRepository;
    #endregion
    private readonly IMediator _mediator;
    private readonly static ConcurrentDictionary<string, string> _hisOrderIndexCache = new();

    public HisBillService(
        IBHBasicService bhBasicService,
        IDataTranRepository dataTranRepository,
        IDataRepository<ExtHisOrderIndex> hisOrderIndexRepository,
        IDataRepository<ExtHisBill> hisBillRepository,
        IDataRepository<PeRegister> peRegisterRepository,
        ISplitTableRepository<PeRegisterComb> peRegisterCombRepository,
        ISplitTableRepository<ExtHisBillComb> hisBillCombRepository,
        IShenShanRegisterRepository shenRegisterRepository,
        IDataRepository<CodeItemComb> codeItemCombRepository,
        IRegisterRepository registerRepository,
        IMediator mediator)
    {
        _bhBasicService = bhBasicService;
        _dataTranRepository = dataTranRepository;
        _hisOrderIndexRepository = hisOrderIndexRepository;
        _hisBillRepository = hisBillRepository;
        _peRegisterRepository = peRegisterRepository;
        _hisBillCombRepository = hisBillCombRepository;
        _peRegisterCombRepository = peRegisterCombRepository;
        _shenRegisterRepository = shenRegisterRepository;
        _codeItemCombRepository = codeItemCombRepository;
        _registerRepository = registerRepository;
        _mediator = mediator;
    }

    /// <summary>
    /// 收费信息表创建
    /// </summary>
    public void SyncBillInfo(PeRegister register, PeRegisterComb[] regCombAdds, long[] regCombDels)
    {
        if(!_hisOrderIndexCache.TryAdd(register.RegNo, string.Empty))
            return;
        try
        {
            var hisOrderIndex = SaveOrderIndex(register);
            SaveOrderCombs(hisOrderIndex, regCombAdds, regCombDels);
            CreatePatiendInfo(hisOrderIndex);
        }
        finally
        {
            _hisOrderIndexCache.Remove(register.RegNo, out _);
        }
    }
    /// <summary>
    /// 推送订单通知（His、主索引、EMR）
    /// </summary>
    /// <param name="regNo"></param>
    public void SendNotice(string regNo)
    {
        var hisOrderIndex = _hisOrderIndexRepository.FindInSingleKey(regNo);
        ExecuteMedicalRecord(hisOrderIndex);
        ExecuteRegistPat(hisOrderIndex);
        ExecuteSynchPEModifyNotice(hisOrderIndex);
        hisOrderIndex.IsEmrNoticed = true;
        _hisOrderIndexRepository.Update(hisOrderIndex);
    }
    /// <summary>
    /// HIS退费预操作 支持全退半退
    /// </summary>
    /// <param name="regNo"></param>
    /// <param name="deleteIds"></param>
    /// <param name="msg"></param>
    /// <returns></returns>
    public bool OrderRefund(string regNo, long[] deleteIds, out string msg)
    {
        //deleteIds
        //设置已收费的记录为待退费，并且插入其余不退费的到视图，如果全退需要全部插入

        #region 查询相关操作数据
        var register = _peRegisterRepository.FindInSingleKey(regNo);
        //获取本次选择的组合
        var deleteCombs = _peRegisterCombRepository.FindAll(register.RegisterTime, x => deleteIds.Contains(x.Id)).ToList();
        //已操作过退费时 已插入的数据
        var newFeeCombsHasInsert = _hisBillCombRepository.FindAll(register.RegisterTime, x => x.RegNo == register.RegNo && x.PayStatus == PayStatus.未收费).ToList();
        //多次执行部分退费时，已经设置为冲销，此次不在deleteIds中
        var newFeeCombsHasReFee = _peRegisterCombRepository.FindAll(register.RegisterTime, x => x.RegNo == register.RegNo && x.PayStatus == PayStatus.冲销).ToList();
        //本次若是二次退费，删除上次插入需重收列表的记录
        var newFeeCombsToDelete = newFeeCombsHasInsert.Where(x => deleteIds.Contains(x.RegCombId)).ToList();
        //前端隐藏了材料费，需要计算是否退材料费
        var tubes = _peRegisterCombRepository.FindAll(register.RegisterTime, x => x.RegNo == regNo && x.BeFrom != null).ToArray();
        //befrom集合，是本次选择+已冲销
        List<long> befroms = new();
        befroms.AddRange(deleteIds);
        befroms.AddRange(newFeeCombsHasReFee.Select(x=>x.Id));
        var tubeIds = tubes.Where(x => x.BeFrom.All(item => befroms.Contains(long.Parse(item)))).Select(x => x.Id).ToArray();
        //删除上次插入的需重收，此次计算出来需要退的试管
        if(tubeIds.Count() > 0)
            newFeeCombsToDelete.AddRange(newFeeCombsHasInsert.Where(x => tubeIds.Contains(x.RegCombId)));
        #region 0元项目处理
        var combs = _codeItemCombRepository.FindAll(x => SqlFunc.Subqueryable<MapCodeItemCombHisOrderItem>().Where(order => order.CombCode == x.CombCode).NotAny()
                                                    && x.Price == 0).Select(x => x.CombCode).ToArray();
        var delZero = deleteCombs.Join(combs, del => del.CombCode, comb => comb, (del, comb) => del).ToArray();
        #endregion
        var deleteBillCombs = _hisBillCombRepository.FindAll(register.RegisterTime, x => x.RegNo == register.RegNo
                                                                                    && deleteIds.Contains(x.RegCombId)
                                                                                    && x.PayStatus == PayStatus.收费).ToList();
        if (deleteBillCombs.Count() == 0 && delZero.Length == 0)
        {
            msg = "当前选择组合不存在收费记录1";
            return false;
        }
        if(tubeIds.Count() > 0)
        {
            deleteCombs.AddRange(_peRegisterCombRepository.FindAll(register.RegisterTime, x => tubeIds.Contains(x.Id)).ToArray());
        }
        #endregion

        #region 判断是否已出结果,或已采集
        var recordCombIds = _shenRegisterRepository.GetPeRecordComb(regNo).Select(x => x.Id).ToArray();
        var sampleIds = _shenRegisterRepository.GetSampleInfo(regNo).Where(x=>x.GatherTime!=null).Select(x => x.RegCombId).ToArray();
        var recordCombs = deleteCombs.Where(x => recordCombIds.Contains(x.Id)).ToArray();
        var gatherCombs = deleteCombs.Where(x => sampleIds.Contains(x.Id)).ToArray();
        if (recordCombs.Length > 0)
        {
            msg = string.Concat("当前选择组合中，", string.Join(",",recordCombs.Select(x=>x.CombName).ToArray()),"已出结果，无法退费");
            return false;
        }
        if (gatherCombs.Length > 0)
        {
            msg = string.Concat("当前选择组合中，", string.Join(",", gatherCombs.Select(x => x.CombName).ToArray()), "已出结果，无法退费");
            return false;
        }
        #endregion

        #region 获取收费的发票ID
        var invoinceIds = deleteBillCombs.Select(x => x.InvoiceId).Distinct().ToArray();
        var extHisBills = _hisBillRepository.FindAll(x => x.RegNo == register.RegNo && invoinceIds.Contains(x.InvoiceId)).ToList();
        if (extHisBills.Count == 0 && delZero.Length == 0)
        {
            msg = "当前选择组合不存在收费记录";
            return false;
        }
        #endregion

        extHisBills.BatchUpdate(x => x.FeeType = FeeType.待退费);
        deleteCombs.BatchUpdate(x => x.PayStatus = PayStatus.冲销);

        #region 计算需要重收的组合
        //部分退 获取需要重收的组合,如果删除组合
        var reFeeCombs = _hisBillCombRepository
            .FindAll(register.RegisterTime, x => x.RegNo == register.RegNo && invoinceIds.Contains(x.InvoiceId))
            .ToList();
        
        //存在删除组合以外的为部分退，过滤删除以外的插入
        if (reFeeCombs.Any(x=>!deleteCombs.Select(x => x.Id).ToArray().Contains(x.RegCombId)))
        {
            reFeeCombs = reFeeCombs.Where(x => !deleteCombs.Select(x => x.Id).ToArray().Contains(x.RegCombId)).ToList();
            if (newFeeCombsHasInsert.Count > 0)
            {
                reFeeCombs = reFeeCombs.Where(x => !newFeeCombsHasInsert.Select(x => x.RegCombId).ToArray().Contains(x.RegCombId)).ToList();
            }
            if (newFeeCombsHasReFee.Count > 0)
            {
                reFeeCombs = reFeeCombs.Where(x => !newFeeCombsHasReFee.Select(x => x.Id).ToArray().Contains(x.RegCombId)).ToList();
            }
            if (tubeIds.Length > 0)
            {
                reFeeCombs = reFeeCombs.Where(x => !tubeIds.Contains(x.RegCombId)).ToList();
            }
        }
        else
        {
            reFeeCombs.Clear();
        }
        if (reFeeCombs.Count > 0)
        {
            foreach (var billcomb in reFeeCombs)
            {
                billcomb.CreateTime = DateTime.Now;
                billcomb.PayStatus = PayStatus.未收费;
                billcomb.InvoiceId = string.Empty;
                billcomb.InvoiceNo = string.Empty;
            }
        }
        #endregion

        _dataTranRepository.ExecTran(() =>
        {
            //更新收费记录状态
            _hisBillRepository.Update(extHisBills);
            _peRegisterCombRepository.SplitTableUpdate(deleteCombs);
            //部分退
            if (reFeeCombs.Count > 0)
                _hisBillCombRepository.SplitTableInsert(reFeeCombs);
            if (newFeeCombsToDelete.Count > 0)
                _hisBillCombRepository.SplitTableDelete(newFeeCombsToDelete);
            if (delZero.Length > 0 && deleteCombs.Count == delZero.Length && newFeeCombsHasReFee.Count == 0)
            {
                _peRegisterCombRepository.SplitTableDelete(delZero);
            }
        });
        _mediator.Publish(new SyncPeRegisterOrderHandle.Data(new string[] { regNo }));
        msg = "退费申请成功";
        return true;
    }
    /// <summary>
    /// 撤销HIS预退费
    /// </summary>
    /// <param name="regNo"></param>
    /// <param name="msg"></param>
    /// <returns></returns>
    public bool CancelOrderRefund(string regNo, out string msg)
    {
        var hisBill = _hisBillRepository.FindAll(x => x.RegNo == regNo && x.FeeType == FeeType.待退费).ToList();
        if (hisBill.Count == 0)
        {
            msg = "当前人员没有已发送的退费申请";
            return true;
        }
        var regCombs = _registerRepository.ReadRegisterCombs(regNo).Where(x => x.PayStatus == PayStatus.冲销).ToList();
        var billCombs = _hisBillCombRepository.FindAll(regCombs[0].RegisterTime, x => !SqlFunc.ContainsArray(regCombs.Select(y => y.Id).ToArray(), x.RegCombId) && x.PayStatus == PayStatus.未收费&& x.RegNo == regNo).ToList();
        hisBill.BatchUpdate(x => x.FeeType = FeeType.正常);
        regCombs.BatchUpdate(x => x.PayStatus = PayStatus.收费);
        _dataTranRepository.ExecTran(() =>
        {
            _hisBillRepository.Update(hisBill);
            _peRegisterCombRepository.SplitTableUpdate(regCombs);
            _hisBillCombRepository.SplitTableDelete(billCombs);
        });
        _mediator.Publish(new SyncPeRegisterOrderHandle.Data(new string[] { regNo }));
        msg = "撤销退费申请成功";
        return true;
    }
    // private
    // PE
    /// <summary>
    /// 额外信息表创建
    /// </summary>
    /// <param name="register"></param>
    /// <returns></returns>
    private ExtHisOrderIndex SaveOrderIndex(PeRegister register)
    {
        var patientInfo = _hisOrderIndexRepository.First(x => x.RegNo == register.RegNo);
        if (patientInfo == null)
        {
            patientInfo = new ExtHisOrderIndex
            {
                RegNo = register.RegNo,
                Name = register.Name,
                PatCode = register.PatCode,
                Sex = register.Sex,
                Age = register.Age,
                Birthday = register.Birthday,
                IsCompanyCheck = register.IsCompanyCheck,
                Tel = register.Tel,
                CardType = register.CardType,
                CardNo = register.CardNo,
                CompanyCode = register.CompanyCode,
                PeCls = register.PeCls,
                IsVIP = register.IsVIP,
                BookType = register.BookType,
                OperatorCode = register.OperatorCode == "0000" || register.OperatorCode == "8888" ? "00870":register.OperatorCode,
                RegisterTime = register.ActiveTime,
                MarryStatus = register.MarryStatus,
                RegisterTimes = register.RegisterTimes,
                IsLisNoticed = true,
                IsPacsNoticed = true,
                IsEmrNoticed = false,
                PayStatus = PayStatus.未收费
            };
            _hisOrderIndexRepository.Insert(patientInfo);
        }
        else
        {
            if(patientInfo.PayStatus== PayStatus.未收费)
            {
                patientInfo.Name = register.Name;
                patientInfo.Sex = register.Sex;
                patientInfo.Age = register.Age;
                patientInfo.Birthday = register.Birthday;
                patientInfo.Tel = register.Tel;
                patientInfo.CardType = register.CardType;
                patientInfo.CardNo = register.CardNo;
                patientInfo.CompanyCode = register.CompanyCode;
                patientInfo.PeCls = register.PeCls;
                patientInfo.IsVIP = register.IsVIP;
                patientInfo.BookType = register.BookType;
                patientInfo.OperatorCode = register.OperatorCode == "0000" || register.OperatorCode == "8888" ? "00870" : register.OperatorCode;
                patientInfo.RegisterTime = register.ActiveTime;
                _hisOrderIndexRepository.Update(patientInfo);
            }
        }
        return patientInfo;
    }

    /// <summary>
    /// 额外订单明细表创建
    /// </summary>
    /// <param name="hisOrderIndex"></param>
    /// <param name="regCombAdds"></param>
    /// <param name="regCombDels"></param>
    private void SaveOrderCombs(ExtHisOrderIndex hisOrderIndex, PeRegisterComb[] regCombAdds, long[] regCombDels)
    {
        if (regCombAdds.Length == 0 && regCombDels.Length == 0)
            return;

        // 新增了检验检查需要重新通知
        if (regCombAdds.Any(x => x.CheckCls == CheckCls.检验检查))
            hisOrderIndex.IsLisNoticed = false;
        if (regCombAdds.Any(x => x.CheckCls == CheckCls.功能检查))
            hisOrderIndex.IsPacsNoticed = false;
        var register = _peRegisterRepository.FindInSingleKey(hisOrderIndex.RegNo);

        var billUnFee = _hisBillCombRepository.SplitTableAny(register.RegisterTime, x => x.RegNo == register.RegNo && x.PayStatus == PayStatus.收费);

        var billDels = _hisBillCombRepository.FindAll(register.RegisterTime, x => SqlFunc.ContainsArray(regCombDels, x.RegCombId)).ToArray();

        var combs = _codeItemCombRepository.FindAll(x => SqlFunc.Subqueryable<MapCodeItemCombHisOrderItem>().Where(order => order.CombCode == x.CombCode).NotAny()
                                                    && x.Price == 0).Select(x=>x.CombCode).ToArray();

        var billAdds = regCombAdds.Where(regComb => regComb.IsPayBySelf && !combs.Any(x => x == regComb.CombCode))
            .Select(regComb=> new ExtHisBillComb
                        {
                            RegCombId = regComb.Id,
                            RegNo = regComb.RegNo,
                            Name = hisOrderIndex.Name,
                            CreateTime = DateTime.Now,
                            CombCode = regComb.CombCode,
                            CombName = regComb.CombName,
                            Discount = regComb.Discount,
                            ClsCode = regComb.ClsCode,
                            ExamDeptCode = regComb.ExamDeptCode,
                            Price = regComb.Price,
                            PayStatus = PayStatus.未收费,
                            RegisterTime = register.RegisterTime
                        }).ToArray();
        //0元项目  
        var billZero = regCombAdds.Where(x => x.IsPayBySelf)
                            .Join(combs, 
                            regComb => regComb.CombCode,
                            comb => comb,
                            (regComb, comb) => regComb).ToArray();
        if (billDels.Length > 0)
            _hisBillCombRepository.SplitTableDelete(billDels);
        if (billAdds.Length > 0)
            _hisBillCombRepository.SplitTableInsert(billAdds);
        if (billZero.Length > 0 && billUnFee && billZero.Length == regCombAdds.Length)
        {
            billZero.BatchUpdate(x => x.PayStatus = PayStatus.收费);
            _peRegisterCombRepository.SplitTableUpdate(billZero);
            _mediator.Publish(new SyncPeRegisterOrderHandle.Data(billZero.Select(x => x.RegNo).ToArray()));
        }
        _hisOrderIndexRepository.Update(hisOrderIndex);
    }
    // BaiHui
    /// <summary>
    /// 门诊挂号通知
    /// </summary>
    /// <param name="hisOrderIndex"></param>
    private bool ExecuteMedicalRecord(ExtHisOrderIndex hisOrderIndex)
    {
        if (!string.IsNullOrEmpty(hisOrderIndex.HisCard))
        {
            return true;
        }
        var medicalRecordDTO = new MedicalRecordDto
        {
            RegNo = hisOrderIndex.RegNo,
            HospitalId = "3"
        };
        var bhRequestMsg = new BHRequestMsg(ResxExternal.BHApiMedicalRecord, XmlHelper.Serialize(medicalRecordDTO));
        var resInfo = _bhBasicService.PostMsgTransfer<string>(bhRequestMsg);
        if (!resInfo.IsSuccess)
        {
            throw new BusinessException(resInfo.ErrorInfo ?? ResxCommon.Fail);
        }

        var resDataInfo = XmlHelper.Deserialize<MedicalRecordResponseDto>(XmlHelper.Decode(resInfo.SyncResult));
        if (!resDataInfo.IsSuccess)
        {
            throw new BusinessException(resDataInfo.Note ?? ResxCommon.Fail);
        }

        var resultData = resDataInfo.VisitCardNo;
        if (string.IsNullOrEmpty(resultData))
        {
            throw new BusinessException(ResxCommon.NotExistRecord);
        }
        else
        {
            hisOrderIndex.HisCard = resultData;
        }
        return true;

    }
    /// <summary>
    /// 主数据建卡
    /// </summary>
    /// <param name="hisOrderIndex"></param>
    /// <returns></returns>
    private bool ExecuteRegistPat(ExtHisOrderIndex hisOrderIndex)
    {
        if (!string.IsNullOrEmpty(hisOrderIndex.EmpiId))
        {
            return true;
        }
        var registPatDTO = new RegistPatDto
        {
            PatientId = hisOrderIndex.HisCard,
            OutpNo = string.Empty,
            InpNo = string.Empty,
            PatientName = hisOrderIndex.Name,
            PyName = string.Empty,
            SexCode = hisOrderIndex.Sex.GetHashCode().ToString(),
            SexName = hisOrderIndex.Sex.ToString(),
            Birthday = hisOrderIndex.Birthday?.ToString("yyyy-MM-dd hh:mm:ss") ?? string.Empty,
            CardType = hisOrderIndex.CardType,
            CardNo = hisOrderIndex.CardNo,
            Company = string.Empty,
            StreetAddress = string.Empty,
            Zone = string.Empty,
            City = string.Empty,
            Province = string.Empty,
            PostCode = string.Empty,
            CountryCode = string.Empty,
            Mobile = hisOrderIndex.Tel ?? string.Empty,
            PhoneHome = string.Empty,
            PhoneBusiness = string.Empty,
            Marriage = hisOrderIndex.MarryStatus == null || hisOrderIndex.MarryStatus == MarryStatus.未知 ? string.Empty : hisOrderIndex.MarryStatus.GetHashCode().ToString(),
            Nation = string.Empty,
            BirthPlac = string.Empty,
            BloodType = string.Empty,
            Nationality = string.Empty,
            DeathDate = string.Empty,
            DeathId = string.Empty,
            Profession = string.Empty,
            ContactName1 = string.Empty,
            ContactName2 = string.Empty,
            ContactPhone1 = string.Empty,
            ContactPhone2 = string.Empty,
            ContactRelation1 = string.Empty,
            ContactRelation2 = string.Empty,
            DataSource = string.Empty,
            DomianId = string.Empty,
            SystemId = hisOrderIndex.RegNo
        };
        var bhRequestMsg = new BHRequestMsg(ResxExternal.BHApiRegistPat, XmlHelper.Serialize(registPatDTO));

        var resInfo = _bhBasicService.PostMsgTransfer<string>(bhRequestMsg);
        if (!resInfo.IsSuccess)
        {
            throw new BusinessException(resInfo.ErrorInfo ?? ResxCommon.Fail);
        }

        var resDataInfo = XmlHelper.Deserialize<RegistPatReturnDto>(XmlHelper.Decode(resInfo.SyncResult));
        if (!resDataInfo.IsSuccess)
        {
            throw new BusinessException(resDataInfo.ResultContent ?? ResxCommon.Fail);
        }

        var registerData = resDataInfo.Data.PatientInfos;
        var registerDataSame = resDataInfo.Data.PatientInfosSame;
        if (registerData.Count == 0)
        {
            if(registerDataSame.Count == 0)
            {
                throw new BusinessException(ResxCommon.NotExistRecord);
            }
            else
            {
                hisOrderIndex.EmpiId = registerDataSame[0].MpiId;
            }               
        }
        else
        {
            hisOrderIndex.EmpiId = registerData[0].MpiId;
        }
        return true;
    }
    /// <summary>
    /// 申请单同步通知 分类型通知
    /// </summary>
    /// <param name="hisOrderIndex"></param>
    /// <param name="type"></param>
    /// <returns></returns>
    private bool ExecuteSynchPEModifyNotice(ExtHisOrderIndex hisOrderIndex, string type)
    {
        var synchPEModifyNoticeDTO = new SynchPEModifyNoticeDto
        {
            visitCardNo = hisOrderIndex.HisCard,
            outhospNo = string.Empty,
            empiId = string.Empty,
            patIndexNo = string.Empty,
            peIndexNo = hisOrderIndex.PatCode,
            peNo = hisOrderIndex.RegNo,
            categCode = type
        };
        var bhRequestMsg = new BHRequestMsg(ResxExternal.BHApiSynchPEModifyNotice, synchPEModifyNoticeDTO.ToJson());

        var resInfo = _bhBasicService.PostMsgTransfer<string>(bhRequestMsg);
        if (!resInfo.IsSuccess)
        {
            return false;
        }

        var resDataInfo = JsonConvert.DeserializeObject<SynchPEModifyNoticeReturnDto>(resInfo.SyncResult);
        if (!resDataInfo.IsSuccess)
        {
            return false;
        }
        else
        {
            return true;
        }
    }
    /// <summary>
    /// 申请单同步通知 订单索引
    /// </summary>
    /// <param name="hisOrderIndex"></param>
    /// <returns></returns>
    private bool ExecuteSynchPEModifyNotice(ExtHisOrderIndex hisOrderIndex)
    {
        if (hisOrderIndex.PayStatus == PayStatus.收费||hisOrderIndex.IsCompanyCheck)
        {
            if (!hisOrderIndex.IsLisNoticed)
            {
                hisOrderIndex.IsLisNoticed = ExecuteSynchPEModifyNotice(hisOrderIndex, "LAB");
            }
            if (!hisOrderIndex.IsPacsNoticed)
            {
                hisOrderIndex.IsPacsNoticed = ExecuteSynchPEModifyNotice(hisOrderIndex, "EXAM");
            }
            return true;
        }
        return false;
    }
    /// <summary>
    /// 申请单同步通知 体检号
    /// </summary>
    /// <param name="regNo"></param>
    /// <returns></returns>
    private bool ExecuteSynchPEModifyNotice(string regNo)
    {
        var hisOrderIndex = _hisOrderIndexRepository.FindInSingleKey(regNo);
        hisOrderIndex.IsLisNoticed = false;
        hisOrderIndex.IsPacsNoticed = false;
        return ExecuteSynchPEModifyNotice(hisOrderIndex);
    }
    /// <summary>
    /// 推送订单通知（His、主索引、EMR）
    /// </summary>
    /// <param name="hisOrderIndex"></param>
    private bool SendNotice(ExtHisOrderIndex hisOrderIndex)
    {
        ExecuteSynchPEModifyNotice(hisOrderIndex);
        hisOrderIndex.IsEmrNoticed = true;
        _hisOrderIndexRepository.Update(hisOrderIndex);
        return true;
    }
    /// <summary>
    /// 门诊/主数据建卡
    /// </summary>
    /// <param name="hisOrderIndex"></param>
    /// <returns></returns>
    private bool CreatePatiendInfo(ExtHisOrderIndex hisOrderIndex)
    {
        if (!ExecuteMedicalRecord(hisOrderIndex))
            return false;
        if (!ExecuteRegistPat(hisOrderIndex))
            return false;
        if (hisOrderIndex.IsCompanyCheck)
            hisOrderIndex.IsEmrNoticed = false;
        else
            hisOrderIndex.IsEmrNoticed = true;
        _hisOrderIndexRepository.Update(hisOrderIndex);
        return true;
    }
    /// <summary>
    /// 自动通知任务
    /// </summary>
    public void SyncOrderAuto()
    {
        var queue = GetOrdersQueue();
        while(queue.TryDequeue(out var order))
        {
            try
            {
                SendNotice(order);
            }catch (Exception ex)
            {
                Console.WriteLine(ex.Message);  
            }                
        }
    }

    /// <summary>
    /// 根据HIS卡号获取病人信息
    /// </summary>
    /// <param name="hisCardNo"></param>
    /// <returns></returns>
    /// <exception cref="BusinessException"></exception>
    private HisCardQueryResult GetPatinedInfoFromHis(string hisCardNo)
    {
        if (string.IsNullOrEmpty(hisCardNo))
        {
            throw new BusinessException("卡号不能为空");
        }
        var query = new HisCardQuery
        {
            Child = new HisCardQueryChild
            {
                GrandChild = new HisCardQueryGrandChild
                {
                    splice = "and",
                    value = "'" + hisCardNo + "'",
                    compy = "=",
                    item = "TREAT_CARD_NO"
                }
            }
        };  
        var bhRequestMsg = new BHRequestMsg(ResxExternal.BHApiQueryCardInfo, XmlHelper.Serialize(query));
        var resInfo = _bhBasicService.PostMsgTransfer<string>(bhRequestMsg);
        if (!resInfo.IsSuccess)
        {
            throw new BusinessException(resInfo.ErrorInfo ?? ResxCommon.Fail);
        }

        var resDataInfo = XmlHelper.Deserialize<HisCardQueryResult>(XmlHelper.Decode(resInfo.SyncResult));
        if (!resDataInfo.IsSuccess)
        {
            throw new BusinessException(resDataInfo.ResultContent ?? ResxCommon.Fail);
        }
        return resDataInfo;           
    }

    public string GetHisIndex(string hisCardNo)
    {
        var hisInfo = GetPatinedInfoFromHis(hisCardNo).Child;
        return hisInfo.PAT_INDEX_NO;
    }

    public HistoryArchives GetHistoryArchivesByHisCard(string hisCardNo)
    {
        var hisInfo = GetPatinedInfoFromHis(hisCardNo).Child;
        Match match = Regex.Match(hisInfo.AGE, @"\d+");
        return new HistoryArchives
        {
            Index    = 1,
            Name     = hisInfo.PAT_NAME,
            Sex      = hisInfo.PHYSI_SEX_CODE == "1" ? Sex.男 : Sex.女,
            Age      = int.Parse(match.Value),
            AgeUnit  = AgeUnit.岁,
            Birthday = DateTime.Parse(hisInfo.DATE_BIRTH),
            CardType = ((int)CardType.居民身份证).ToString(),
            CardNo   = hisInfo.ID_NUMBER,
            Tel      = hisInfo.PHONE_NO,
            Address  = hisInfo.CONTACT_ADDR,
        } ;
    }

    private Queue<ExtHisOrderIndex> GetOrdersQueue()
    {
        var queue = CacheHelper.GetOrCreate<Queue<ExtHisOrderIndex>>("HisRequest", () =>
        {
            var queue = new Queue<ExtHisOrderIndex>();
            Enqueue(queue);
            return queue;
        });
        if (queue.Count == 0)
        {
            Enqueue(queue);
        }
        return queue;

        void Enqueue(Queue<ExtHisOrderIndex> queue)
        {
            _shenRegisterRepository.GetPlaformNoticeQueue()
                .Select(order => order)
                .ToList()
                .ForEach(x => queue.Enqueue(x));
        }
    }
}
