﻿using Peis.Model.Other.PeEnum;

namespace Peis.Model.DTO.DataQuery
{
    public class ExamItemEntry
    {
        /// <summary>
        /// 体检号
        /// </summary>
        public string RegNo { get; set; }
        /// <summary>
        /// 姓名
        /// </summary>
        public string Name { get; set; }
        /// <summary>
        /// 性别
        /// </summary>
        public Sex Sex { get; set; }
        /// <summary>
        /// 年龄
        /// </summary>
        public int Age { get; set; }
        /// <summary>
        /// 证件号
        /// </summary>
        public string CardNo { get; set; }
        /// <summary>
        /// 套餐名
        /// </summary>
        public string ClusterName { get; set; }
        /// <summary>
        /// 体检分类
        /// </summary>
        public PeCls PeCls { get; set; }
        /// <summary>
        /// 预约类型
        /// </summary>
        public BookType BookType { get; set; }
        /// <summary>
        /// 项目明细
        /// </summary>
        public string RegisterCombs { get; set; }

        /// <summary>
        /// 体检状态
        /// </summary>
        public PeStatus PeStatus { get; set; }
    }

    public class ExamItemEntryReport
    {
        public List<ExamItemEntry> ExamItemEntryReports { get; set; }
        public RecordStatus RecordStatus { get; set; }
    }
}
