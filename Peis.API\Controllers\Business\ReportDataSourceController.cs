﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Peis.Model.DataBaseEntity.External;
using Peis.Model.DTO.Occupation.Record;
using Peis.Model.DTO.ReportDataModel;
using Peis.Model.DTO.Sample;
using Peis.Model.ReportDataSource;
using Peis.Model.ReportDataSource.CompanyAnnualReport;
using Peis.Model.ReportDataSource.CompanyCompositeReport;
using Peis.Model.ReportDataSource.ExaminationReport;
using Peis.Model.ReportDataSource.GraphicReport;
using Peis.Model.ReportDataSource.GuidanceSheetReport;
using Peis.Model.ReportDataSource.PeLabelReport;
using Peis.Service.IService;
using Peis.Service.Service;
using System;
using System.Collections.Generic;

namespace Peis.API.Controllers.Business
{
    /// <summary>
    /// 报告数据源
    /// </summary>
    [Route("api/[controller]")]
    [ApiController]
    [AllowAnonymous]
    public class ReportDataSourceController : BaseApiController
    {
        private readonly IReportDataSourceService _reportDataSourceService;

        public ReportDataSourceController(IReportDataSourceService reportDataSourceService)
        {
            _reportDataSourceService = reportDataSourceService;
        }

        /// <summary>
        /// 获取体检报告数据源
        /// </summary>
        /// <param name="regNo"></param>
        /// <returns></returns>
        [HttpGet("GetExaminationReportSource")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        public ActionResult<ExaminationReport> GetExaminationReportSource([FromQuery] string regNo)
        {
            return _reportDataSourceService.GetExaminationReportSource(regNo);
        }

        /// <summary>
        /// 获取体检报告数据源（自定义数据模型）
        /// </summary>
        /// <param name="modelCode">模型代码</param>
        /// <param name="regNo"></param>
        /// <returns></returns>
        [HttpGet("GetCustomResultReportSource")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        public ActionResult<CustomResultReport> GetCustomResultReportSource([FromQuery] string modelCode, [FromQuery] string regNo)
        {
            return _reportDataSourceService.GetCustomResultReportSource(modelCode, regNo);
        }

        /// <summary>
        /// 获取图文报告列表（用于嵌套体检文字报告附带图文）
        /// </summary>
        /// <param name="regNo"></param>
        /// <returns></returns>
        [HttpGet("GetImageReportSource")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        public ActionResult<GraphicReportPath[]> GetImageReportSource([FromQuery] string regNo)
        {
            return _reportDataSourceService.GetImageReportSource(regNo);
        }

        /// <summary>
        /// 获取指引单报告数据源
        /// </summary>
        /// <param name="regNo"></param>
        /// <returns></returns>
        [HttpGet("GetGuidanceSheetReportSource")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        public ActionResult<GuidanceSheetReport> GetGuidanceSheetReportSource([FromQuery] string regNo)
        {
            return _reportDataSourceService.GetGuidanceSheetReportSource(regNo);
        }

        /// <summary>
        /// 单位综合报告数据源
        /// </summary>
        /// <param name="companycode"></param>
        /// <param name="beginDate"></param>
        /// <param name="endDate"></param>
        /// <returns></returns>
        [HttpGet("GetCompanyCompositeReportSource")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        public ActionResult<CompanyCompositeReport> GetCompanyCompositeReportSource([FromQuery] string companycode,DateTime beginDate,DateTime endDate)
        {
            return _reportDataSourceService.GetCompanyCompositeReport(companycode, beginDate, endDate);
        }

        /// <summary>
        /// 单位年度报告数据源
        /// </summary>
        /// <param name="companycode"></param>
        /// <param name="beginDate"></param>
        /// <param name="endDate"></param>
        /// <returns></returns>
        [HttpGet("GetCompanyAnnualReportSource")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        public ActionResult<CompanyAnnualReport> GetCompanyAnnualReportSource([FromQuery] string companycode, DateTime beginDate, DateTime endDate)
        {
            return _reportDataSourceService.GetCompanyAnnualReportSource(companycode, beginDate, endDate);
        }

        /// <summary>
        /// 获取体检标签数据源
        /// </summary>
        /// <param name="regNo"></param>
        /// <param name="customizePrintTimes">自定义打印数量（不按体检组合计算数量，直接打印通用的体检标签）</param>
        /// <param name="combCode"></param>
        /// <returns></returns>
        [HttpGet("GetPeLabelReportSource")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        public ActionResult<List<PeLabelInformation>> GetPeLabelReportSource([FromQuery] string regNo, [FromQuery] int customizePrintTimes, [FromQuery] string combCode)
        {
            return _reportDataSourceService.GetPeLabelReportSource(regNo, customizePrintTimes, combCode);
        }

        /// <summary>
        /// 获取药品标签数据源
        /// </summary>
        /// <param name="regNo"></param>
        /// <returns></returns>
        [HttpGet("GetPeMedicineLabelReportSource")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        public ActionResult<PeLabelInformation[]> GetPeMedicineLabelReportSource([FromQuery] string regNo)
        {
            return _reportDataSourceService.GetPeMedicineLabelReportSource(regNo);
        }

        /// <summary>
        /// 获取检验条码数据源
        /// </summary>
        /// <param name="regNo">体检号</param>
        /// <param name="sampleNo">条码号（可空）</param>
        /// <param name="type">类型（0全部 1血 2非血）</param>
        /// <returns></returns>
        [HttpGet("GetLisLabelReportSource")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        public ActionResult<List<PeLisLabel>> GetLisLabelReportSource([FromQuery] string regNo, [FromQuery] string sampleNo, [FromQuery] BarcodePrintType type)
        {
            return _reportDataSourceService.GetLisLabelReportSource(regNo,  sampleNo, type);
        }

        /// <summary>
        /// 获取胃肠镜申请单数据源
        /// </summary>
        /// <param name="regNo"></param>
        /// <param name="clsCode"></param>
        /// <returns></returns>
        [HttpGet("GetTechApplySheetReportSource")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        public ActionResult<TechApplySheetModel> GetTechApplySheetReportSource([FromQuery] string regNo, [FromQuery] string clsCode)
        {
            return _reportDataSourceService.GetTechApplySheetReportSource(regNo, clsCode);
        }

        /// <summary>
        /// 获取材料费申请单数据源
        /// </summary>
        /// <param name="regNo"></param>
        /// <returns></returns>
        [HttpGet("GetMaterialCostReportSource")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        public ActionResult<object> GetMaterialCostReportSource([FromQuery] string regNo)
        {
            return _reportDataSourceService.GetMaterialCostReportSource(regNo);
        }

        /// <summary>
        /// 获取职业病报告数据源
        /// </summary>
        /// <param name="regNo"></param>
        /// <returns></returns>
        [HttpGet("GetOccupationalExaminationReport")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        public ActionResult<OccupationalExaminationReport> GetOccupationalExaminationReport([FromQuery] string regNo)
        {
            return _reportDataSourceService.GetOccupationalExaminationReport(regNo);
        }

        /// <summary>
        /// 获取职业病症状询问数据源
        /// </summary>
        /// <param name="regNo"></param>
        /// <returns></returns>
        [HttpGet("GetOccupationalSymptomResult")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        public ActionResult<SymptomRecord> GetOccupationalSymptomResult([FromQuery] string regNo)
        {
            return _reportDataSourceService.GetOccupationalSymptomResult(regNo);
        }

        /// <summary>
        /// 获取职业问诊数据源
        /// </summary>
        /// <param name="regNo"></param>
        /// <returns></returns>
        [HttpGet("GetOccupationalConsultation")]
        public ActionResult<OccupationalConsultationReportDataSource> GetOccupationalConsultation([FromQuery] string regNo)
        {
            return _reportDataSourceService.GetOccupationalConsultation(regNo);
        }
        /// <summary>
        /// 纯音测听报告数据源
        /// </summary>
        /// <param name="regNo"></param>
        /// <returns></returns>
        [HttpGet("GetAuditoryReportDatasource")]
        public ActionResult<AuditoryReportDatasource> GetAuditoryReportDatasource([FromQuery] string regNo)
        {
            return _reportDataSourceService.GetAuditoryReportDatasource(regNo);
        }

        /// <summary>
        /// 获取医疗指导数据源
        /// </summary>
        /// <param name="regNo"></param>
        /// <returns></returns>
        [HttpGet("ReadMedicalAdvice")]
        public ActionResult<MedicalAdvice> ReadMedicalAdviceDatasource([FromQuery] string regNo)
        {
            return _reportDataSourceService.ReadMedicalAdviceDatasource(regNo);
        }
    }
}
