﻿namespace Peis.Model.DataBaseEntity.External;

///<summary>
/// 基础数据：His医嘱项目(来源系统对接数据提取)
///</summary>
[SugarTable(nameof(CodeHisOrderItem), TableDescription = "His医嘱项目(来源系统对接数据提取)")]
[SugarIndex("index_CodeHisOrderItem_CNName", nameof(OrderItemCNName), OrderByType.Asc)]
public class CodeHisOrderItem
{
    /// <summary>
    /// 数据PId
    /// </summary>        
    [SugarColumn(IsPrimaryKey = true, Length = 64)]
    public string PId { get; set; }

    /// <summary>
    /// 是否正式发布Y/N
    /// </summary>        
    [SugarColumn(Length = 1)]
    public string IsOfficial { get; set; }

    /// <summary>
    /// 是否最新Y/N
    /// </summary>        
    [SugarColumn(Length = 1)]
    public string IsLast { get; set; }

    /// <summary>
    /// 是否删除Y/N
    /// </summary>        
    [SugarColumn(Length = 1)]
    public string IsDeleted { get; set; }

    /// <summary>
    /// 是否归档Y/N
    /// </summary>        
    [SugarColumn(IsNullable = true, Length = 1)]
    public string IsArchived { get; set; }

    /// <summary>
    /// 标准版本UID
    /// </summary>        
    [SugarColumn(IsNullable = true, Length = 64)]
    public string StandardVersionPId { get; set; }

    /// <summary>
    /// 版本号
    /// </summary>    
    public int VersionNumber { get; set; }

    /// <summary>
    /// 医嘱项目ID
    /// </summary>        
    [SugarColumn(Length = 64)]
    public string OrderItemId { get; set; }

    /// <summary>
    /// 医嘱项目CODE
    /// </summary>        
    [SugarColumn(Length = 64)]
    public string OrderItemCode { get; set; }

    /// <summary>
    /// 医嘱项目中文名称
    /// </summary>        
    [SugarColumn(IsNullable = true, ColumnDataType = "nvarchar(256)")]
    public string OrderItemCNName { get; set; }

    /// <summary>
    /// 医嘱项目简称
    /// </summary>        
    [SugarColumn(IsNullable = true, ColumnDataType = "nvarchar(128)")]
    public string OrderItemSName { get; set; }

    /// <summary>
    /// 医嘱项目英文
    /// </summary>        
    [SugarColumn(IsNullable = true, Length = 128)]
    public string OrderItemENName { get; set; }

    /// <summary>
    /// 标准代码,用于同区域系统连接
    /// </summary>        
    [SugarColumn(IsNullable = true, Length = 128)]
    public string ItemGBCode { get; set; }

    /// <summary>
    /// 外部系统项目ID
    /// </summary>        
    [SugarColumn(IsNullable = true, Length = 64)]
    public string ExternalItemId { get; set; }

    /// <summary>
    /// 外部系统项目名称
    /// </summary>        
    [SugarColumn(IsNullable = true, ColumnDataType = "nvarchar(256)")]
    public string ExternalItemName { get; set; }

    /// <summary>
    /// 助记码
    /// </summary>        
    [SugarColumn(IsNullable = true, Length = 128)]
    public string MemoryCode { get; set; }

    /// <summary>
    /// 拼音输入码
    /// </summary>        
    [SugarColumn(IsNullable = true, Length = 128)]
    public string InputPinYin { get; set; }

    /// <summary>
    /// 项目备注
    /// </summary>        
    [SugarColumn(IsNullable = true, ColumnDataType = "nvarchar(256)")]
    public string OrderItemDesc { get; set; }

    /// <summary>
    /// 目录字典,树的分类,和分类表关联, 树不能拖动只能象药性一样属于哪个最末端分支
    /// </summary>        
    [SugarColumn(IsNullable = true, Length = 128)]
    public string CatalogTreePId { get; set; }

    /// <summary>
    /// 目录字典名称
    /// </summary>        
    [SugarColumn(IsNullable = true, ColumnDataType = "nvarchar(256)")]
    public string CatalogTreeName { get; set; }

    /// <summary>
    /// 医嘱类型ID
    /// </summary>        
    [SugarColumn(IsNullable = true, Length = 64)]
    public string IPOrderTypeId { get; set; }

    /// <summary>
    /// 医嘱类型名称
    /// </summary>        
    [SugarColumn(IsNullable = true, ColumnDataType = "nvarchar(256)")]
    public string IPOrderTypeName { get; set; }

    /// <summary>
    /// 医嘱类型ID
    /// </summary>        
    [SugarColumn(IsNullable = true, Length = 64)]
    public string OPOrderTypeId { get; set; }

    /// <summary>
    /// 医嘱类型名称
    /// </summary>        
    [SugarColumn(IsNullable = true, ColumnDataType = "nvarchar(256)")]
    public string OPOrderTypeName { get; set; }

    /// <summary>
    /// 项目规格
    /// </summary>        
    [SugarColumn(ColumnDataType = "nvarchar(256)")]
    public string ItemSpec { get; set; }

    /// <summary>
    /// 单位
    /// </summary>        
    [SugarColumn(ColumnDataType = "nvarchar(128)")]
    public string ItemUnit { get; set; }

    /// <summary>
    /// 单价，各收费子项相加
    /// </summary>        
    [SugarColumn(Length = 12, DecimalDigits = 4)]
    public decimal ItemPrice { get; set; }

    /// <summary>
    /// 是否打包项目Y/N
    /// </summary>        
    [SugarColumn(Length = 1)]
    public string IsPackaged { get; set; }

    /// <summary>
    /// 检验分类ID
    /// </summary>        
    [SugarColumn(IsNullable = true, Length = 64)]
    public string LabClassId { get; set; }

    /// <summary>
    /// 检验分类名称
    /// </summary>        
    [SugarColumn(IsNullable = true, ColumnDataType = "nvarchar(128)")]
    public string LabClassName { get; set; }

    /// <summary>
    /// 默认检验标本ID
    /// </summary>        
    [SugarColumn(IsNullable = true, Length = 64)]
    public string LabSpecimanId { get; set; }

    /// <summary>
    /// 默认检验标本名称
    /// </summary>        
    [SugarColumn(IsNullable = true, ColumnDataType = "nvarchar(128)")]
    public string LabSpecimanName { get; set; }

    /// <summary>
    /// 标本容器说明
    /// </summary>        
    [SugarColumn(IsNullable = true, ColumnDataType = "nvarchar(256)")]
    public string LabSpecimanContainer { get; set; }

    /// <summary>
    /// 采集要求
    /// </summary>        
    [SugarColumn(IsNullable = true, ColumnDataType = "nvarchar(256)")]
    public string LabSpecimanQuantity { get; set; }

    /// <summary>
    /// 采集注意事项
    /// </summary>        
    [SugarColumn(IsNullable = true, ColumnDataType = "nvarchar(256)")]
    public string LabColTime { get; set; }

    /// <summary>
    /// 结果说明
    /// </summary>        
    [SugarColumn(IsNullable = true, ColumnDataType = "nvarchar(256)")]
    public string LabReportTime { get; set; }

    /// <summary>
    /// 检验标本合并标记
    /// </summary>        
    [SugarColumn(IsNullable = true, ColumnDataType = "nvarchar(128)")]
    public string LabMergeFlag { get; set; }

    /// <summary>
    /// 检验标本拆分标记
    /// </summary>        
    [SugarColumn(IsNullable = true, ColumnDataType = "nvarchar(128)")]
    public string LabSplitFlag { get; set; }

    /// <summary>
    /// 是否外送
    /// </summary>        
    [SugarColumn(IsNullable = true, Length = 1)]
    public string IsOutsideLab { get; set; }

    /// <summary>
    /// 是否每天开展
    /// </summary>        
    [SugarColumn(IsNullable = true, Length = 1)]
    public string IsEverydayLab { get; set; }

    /// <summary>
    /// 开展时间说明
    /// </summary>        
    [SugarColumn(IsNullable = true, ColumnDataType = "nvarchar(256)")]
    public string LabWorkTime { get; set; }

    /// <summary>
    /// 病理分类ID
    /// </summary>        
    [SugarColumn(IsNullable = true, Length = 64)]
    public string PathologyClassId { get; set; }

    /// <summary>
    /// 病理分类名称
    /// </summary>        
    [SugarColumn(IsNullable = true, ColumnDataType = "nvarchar(256)")]
    public string PathologyClassName { get; set; }

    /// <summary>
    /// 病理标本ID
    /// </summary>        
    [SugarColumn(IsNullable = true, Length = 64)]
    public string PathologySpecimanId { get; set; }

    /// <summary>
    /// 病理标本名称
    /// </summary>        
    [SugarColumn(IsNullable = true, ColumnDataType = "nvarchar(256)")]
    public string PathologySpecimanName { get; set; }

    /// <summary>
    /// 检查大类ID
    /// </summary>        
    [SugarColumn(IsNullable = true, Length = 64)]
    public string ExamClassId { get; set; }

    /// <summary>
    /// 检查大类名称
    /// </summary>        
    [SugarColumn(IsNullable = true, ColumnDataType = "nvarchar(256)")]
    public string ExamClassName { get; set; }

    /// <summary>
    /// 检查子类ID
    /// </summary>        
    [SugarColumn(IsNullable = true, Length = 64)]
    public string ExamSubClassId { get; set; }

    /// <summary>
    /// 检查子类名称
    /// </summary>        
    [SugarColumn(IsNullable = true, ColumnDataType = "nvarchar(256)")]
    public string ExamSubClassName { get; set; }

    /// <summary>
    /// 检查部位名称
    /// </summary>        
    [SugarColumn(IsNullable = true, ColumnDataType = "nvarchar(256)")]
    public string ExamPartNameDesc { get; set; }

    /// <summary>
    /// 检查注意事项
    /// </summary>        
    [SugarColumn(IsNullable = true, ColumnDataType = "nvarchar(256)")]
    public string ExamNotice { get; set; }

    /// <summary>
    /// 描述
    /// </summary>        
    [SugarColumn(IsNullable = true, ColumnDataType = "nvarchar(256)")]
    public string Description { get; set; }

    /// <summary>
    /// 是否可用(Y:可用,N:不可用)
    /// </summary>        
    [SugarColumn(Length = 1)]
    public string IsAvailable { get; set; }

    /// <summary>
    /// 适应症
    /// </summary>        
    [SugarColumn(IsNullable = true, ColumnDataType = "nvarchar(256)")]
    public string ItemIndication { get; set; }

    /// <summary>
    /// 作用
    /// </summary>        
    [SugarColumn(IsNullable = true, ColumnDataType = "nvarchar(256)")]
    public string ItemUsage { get; set; }

    /// <summary>
    /// 创建日期
    /// </summary>        
    public DateTime CreatedTime { get; set; }

    /// <summary>
    /// 修改日期
    /// </summary>        
    [SugarColumn(IsNullable = true)]
    public DateTime? UpdatedTime { get; set; }
}
