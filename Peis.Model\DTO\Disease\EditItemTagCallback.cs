﻿using Peis.Model.DTO.DoctorStation;

namespace Peis.Model.DTO.Disease
{
    public class EditItemTagCallback
    {
        /// <summary>
        /// 当前项目标签
        /// </summary>
        public ItemTag CurrentItemTag { get; set; }

        /// <summary>
        /// 其他项目添加的标签
        /// </summary>
        public List<ItemTag> OtherAddItemTags { get; set; }

        /// <summary>
        /// 其他项目删除的标签
        /// </summary>
        public List<ItemTag> OtherDelItemTags { get; set; }

        /// <summary>
        /// 其他项目修改的标签
        /// </summary>
        public List<ItemTag> OtherAlterItemTags { get; set; }

        /// <summary>
        /// 完整的组合标签
        /// </summary>
        public List<CombTag> CombTags { get; set; }

        /// <summary>
        /// 异常提示
        /// </summary>
        public string Hint => CurrentItemTag?.Hint;

        public EditItemTagCallback()
        {
            OtherAddItemTags = new();
            OtherAlterItemTags = new();
            OtherDelItemTags = new();
        }
    }
}
