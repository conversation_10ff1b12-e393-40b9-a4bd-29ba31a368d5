﻿using SqlSugar;

namespace Peis.Model.DataBaseEntity
{
    ///<summary>
    ///菜单功能信息
    ///</summary>
    [SugarTable("SysFunc")]
    public class SysFunc
    {
        /// <summary>
        /// 功能代码
        /// </summary>        
        [SugarColumn(IsNullable = false, IsPrimaryKey = true, Length = 50)]
        public string FuncCode { get; set; }

        /// <summary>
        /// 关联的菜单Id
        /// </summary>        
        [SugarColumn(IsNullable = false, Length = 50)]
        public string MenuCode { get; set; }

        /// <summary>
        /// 功能名称
        /// </summary>        
        [SugarColumn(IsNullable = false, Length = 100)]
        public string Name { get; set; }

        /// <summary>
        /// 状态
        /// </summary>        
        [SugarColumn(IsNullable = false)]
        public bool IsEnabled { get; set; }

        /// <summary>
        /// 备注
        /// </summary>        
        [SugarColumn(IsNullable = true, Length = 100)]
        public string Note { get; set; }
    }
}