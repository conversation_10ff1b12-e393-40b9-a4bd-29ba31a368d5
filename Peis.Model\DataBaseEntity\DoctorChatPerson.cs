﻿using SqlSugar;

namespace Peis.Model.DataBaseEntity
{
    ///<summary>
    ///会诊参与人员
    ///</summary>
    [SugarT<PERSON>("Doctor<PERSON><PERSON>P<PERSON>")]
    public class Doctor<PERSON>hat<PERSON>erson
    {
        /// <summary>
        /// 会诊Id
        /// </summary>        
        [SugarColumn(IsNullable = false, IsPrimaryKey = true)]
        public long ChatId { get; set; }

        /// <summary>
        /// 医生编码
        /// </summary>        
        [SugarColumn(IsNullable = false, IsPrimaryKey = true, Length = 10)]
        public string DoctorCode { get; set; }

        /// <summary>
        /// 未读消息数
        /// </summary>        
        [SugarColumn(IsNullable = false)]
        public int UnReadMsgCount { get; set; }
    }
}