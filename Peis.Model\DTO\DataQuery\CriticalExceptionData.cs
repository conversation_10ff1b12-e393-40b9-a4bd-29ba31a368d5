﻿using Peis.Model.Other.PeEnum;
using System;

namespace Peis.Model.DTO.DataQuery
{
    /// <summary>
    /// 危急异常数据
    /// </summary>
    public class CriticalExceptionData
    {
        /// <summary>
        /// 危急异常信息Id
        /// </summary>
        public long Id { get; set; }

        /// <summary>
        /// 体检号
        /// </summary>
        public string RegNo { get; set; }

        /// <summary>
        /// 姓名
        /// </summary>
        public string Name { get; set; }

        /// <summary>
        /// 性别
        /// </summary>
        public Sex? Sex { get; set; }

        /// <summary>
        /// 年龄
        /// </summary>
        public int? Age { get; set; }

        /// <summary>
        /// 电话
        /// </summary>
        public string Tel { get; set; }

        /// <summary>
        /// 项目
        /// </summary>
        public string ItemName { get; set; }

        /// <summary>
        /// 项目结果
        /// </summary>
        public string ItemResult { get; set; }

        /// <summary>
        /// 生成人
        /// </summary>
        public string Creator { get; set; }

        /// <summary>
        /// 生成时间
        /// </summary>
        public DateTime? CreateTime { get; set; }

        /// <summary>
        /// 确认人
        /// </summary>
        public string Confirmer { get; set; }

        /// <summary>
        /// 确认时间
        /// </summary>
        public DateTime? ConfirmTime { get; set; }

        /// <summary>
        /// 回复内容
        /// </summary>
        public string ReplyContent { get; set; }

        /// <summary>
        /// 回复人
        /// </summary>
        public string ReplyPerson { get; set; }

        /// <summary>
        /// 回复时间
        /// </summary>
        public DateTime? ReplyTime { get; set; }

        /// <summary>
        /// 存在随访记录
        /// </summary>
        public bool HasFollowUp { get; set; }

        /// <summary>
        /// 二次随访员 
        /// </summary>        
        public string SecondaryNotifier { get; set; }

        /// <summary>
        /// 二次随访情况
        /// </summary>        
        public string SecondaryAfterFollowUp { get; set; }
        /// <summary>
        /// 后续随访员 
        /// </summary>        
        public string FinallyNotifier { get; set; }

        /// <summary>
        /// 后续随访情况
        /// </summary>        
        public string FinallyAfterFollowUp { get; set; }
    }
}
