﻿using SqlSugar;
using System;

namespace Peis.Model.DataBaseEntity
{
    ///<summary>
    ///流水号计数表
    ///</summary>
    [SugarTable("PeNoCount")]
    public class PeNoCount
    {
        /// <summary>
        /// 当前日期
        /// </summary>        
        [SugarColumn(IsNullable = false, IsPrimaryKey = true)]
        public DateTime CurrentDay { get; set; }

        /// <summary>
        /// 流水号业务类型
        /// </summary>        
        [SugarColumn(IsNullable = false, IsPrimaryKey = true, Length = 20)]
        public string NoType { get; set; }

        /// <summary>
        /// 子类型
        /// </summary>        
        [SugarColumn(IsNullable = false, IsPrimaryKey = true, Length = 10)]
        public string SubType { get; set; }

        /// <summary>
        /// 当前号
        /// </summary>        
        [SugarColumn(IsNullable = false)]
        public int CurrentNo { get; set; }
    }
}