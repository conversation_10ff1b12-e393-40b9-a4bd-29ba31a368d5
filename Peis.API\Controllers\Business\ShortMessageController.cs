﻿using AutoMapper;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Peis.Model;
using Peis.Model.DataBaseEntity.External;
using Peis.Model.DTO.External.ShortMessage;
using Peis.Repository.Repository.TransactionAttribute;
using Peis.Service.IService;
using Peis.Service.IService.ExternalSystem;
using Peis.Utility.Helper;
using SqlSugar;
using System.Collections.Generic;

namespace Peis.API.Controllers.Business
{
    /// <summary>
    /// 随访相关：短信记录
    /// </summary>
    [Route("api/[controller]")]
    [ApiController]
    public class ShortMessageController : BaseApiController
    {
        private readonly IExternalSystemShortMessageService _shortMessageService;
        private readonly IMapper _mapper;
        private readonly IShortMessageManagerService _shortMessageManagerService;

        public ShortMessageController(IExternalSystemShortMessageService shortMessageService, IMapper mapper, IShortMessageManagerService shortMessageManagerService)
        {
            _shortMessageService = shortMessageService;
            _mapper = mapper;
            _shortMessageManagerService = shortMessageManagerService;
        }

        #region 短信记录
        /// <summary>
        /// 批量新建短信记录信息
        /// </summary>
        /// <param name="regNos">体检号集合</param>
        /// <returns></returns>
        [HttpPost("BatchNewPeSendShortMsgRecord")]
        [ProducesResponseType(typeof(List<PeSendShortMsgRecordDto>), StatusCodes.Status200OK)]
        [UnitOfWork]
        public IActionResult BatchNewPeSendShortMsgRecord(string[] regNos)
        {
            string msg;
            var data = _shortMessageManagerService.BatchNewPeSendShortMsgRecord(regNos, out msg);
            var resData = _mapper.Map<List<PeSendShortMsgRecordDto>>(data);
            if (!resData.IsNullOrEmpty())
                resData.ForEach(x => x.SetCreatedTimeNull());

            result.Success = data is not null;
            result.ReturnData = resData;
            result.ReturnMsg = msg;

            return Ok(result);
        }

        /// <summary>
        /// 批量保存短信记录信息
        /// </summary>
        /// <param name="records">记录信息集合</param>
        /// <returns></returns>
        [HttpPost("BatchSavePeSendShortMsgRecord")]
        [ProducesResponseType(typeof(List<PeSendShortMsgRecordDto>), StatusCodes.Status200OK)]
        [UnitOfWork]
        public IActionResult BatchSavePeSendShortMsgRecord(PeSendShortMsgRecordDto[] records)
        {
            string msg;
            var data = _shortMessageManagerService.BatchSavePeSendShortMsgRecord(_mapper.Map<PeSendShortMsgRecord[]>(records), out msg);

            result.Success = data is not null;
            result.ReturnData = _mapper.Map<List<PeSendShortMsgRecordDto>>(data);
            result.ReturnMsg = msg;

            return Ok(result);
        }

        /// <summary>
        /// 获取短信记录信息
        /// </summary>
        /// <param name="id">记录id</param>
        /// <param name="regNo">体检号</param>
        /// <returns></returns>
        [HttpPost("GetPeSendShortMsgRecord")]
        [ProducesResponseType(typeof(PeSendShortMsgRecordDto), StatusCodes.Status200OK)]
        public IActionResult GetPeSendShortMsgRecord(long id, string regNo)
        {
            string msg;
            var data = _shortMessageManagerService.GetPeSendShortMsgRecord(id, regNo, out msg);
            result.Success = !data.IsNullOrEmpty();
            result.ReturnData = result.Success ? _mapper.Map<PeSendShortMsgRecordDto>(data) : null;
            result.ReturnMsg = msg;

            return Ok(result);
        }

        /// <summary>
        /// 获取短信记录信息集合
        /// </summary>
        /// <param name="query">查询实体</param>
        /// <returns></returns>
        [HttpPost("GetPeSendShortMsgRecords")]
        [ProducesResponseType(typeof(List<PeSendShortMsgRecordDto>), StatusCodes.Status200OK)]
        public IActionResult GetPeSendShortMsgRecords(PeSendShortMsgRecordQuery query)
        {
            string msg;
            int totalNumber = 0;
            int totaPage = 0;
            var data = _shortMessageManagerService.GetPeSendShortMsgRecords(query, ref totalNumber, ref totaPage, out msg);
            result.Success = data is not null;
            result.ReturnData = result.Success ? _mapper.Map<List<PeSendShortMsgRecordDto>>(data) : null;
            result.ReturnMsg = msg;
            result.TotalNumber = totalNumber;
            result.TotalPage = totaPage;

            return Ok(result);
        }

        /// <summary>
        /// 批量删除短信记录（已发送短信不能删除）
        /// </summary>
        /// <param name="keyRecords">删除短信内容记录</param>
        /// <returns></returns>
        [HttpPost("BatchRemovePeSendShortMsgRecord")]
        [ProducesResponseType(typeof(bool), StatusCodes.Status200OK)]
        public IActionResult BatchRemovePeSendShortMsgRecord(List<PeSendShortMsgKeyData> keyRecords)
        {
            string msg;
            result.Success = _shortMessageManagerService.BatchRemovePeSendShortMsgRecord(keyRecords, out msg);
            result.ReturnData = result.Success.ToString();
            result.ReturnMsg = msg;

            return Ok(result);
        }

        /// <summary>
        /// 批量审核短信记录
        /// </summary>
        /// <param name="keyRecords">审核短信内容记录</param>
        /// <returns></returns>
        [HttpPost("BatchAuditPeSendShortMsgRecord")]
        [ProducesResponseType(typeof(List<PeSendShortMsgRecordDto>), StatusCodes.Status200OK)]
        public IActionResult BatchAuditPeSendShortMsgRecord(List<PeSendShortMsgKeyData> keyRecords)
        {
            string msg;
            bool flag;
            var data = _shortMessageManagerService.BatchAuditPeSendShortMsgRecord(keyRecords, out flag, out msg);
            result.Success = flag;
            result.ReturnData = result.Success ? _mapper.Map<List<PeSendShortMsgRecordDto>>(data) : null;
            result.ReturnMsg = msg;

            return Ok(result);
        }

        /// <summary>
        /// 批量根据短信内容发送短信
        /// </summary>
        /// <param name="keyRecords">发送短信内容记录</param>
        /// <returns></returns>
        [HttpPost("BatchSendShortMsgByPeShortMsgRecord")]
        [ProducesResponseType(typeof(List<PeSendShortMsgRecordDto>), StatusCodes.Status200OK)]
        public IActionResult BatchSendShortMsgByPeShortMsgRecord(List<PeSendShortMsgKeyData> keyRecords)
        {
            string msg;
            bool flag;
            var data = _shortMessageService.BatchSendShortMsgByPeShortMsgRecord(keyRecords, out flag, out msg);
            result.Success = flag;
            result.ReturnData = result.Success ? _mapper.Map<List<PeSendShortMsgRecordDto>>(data) : null;
            result.ReturnMsg = msg;

            return Ok(result);
        }
        #endregion

        #region 短信模板

        /// <summary>
        /// 新建短信模板初始化内容
        /// </summary>
        /// <returns></returns>
        [HttpPost("NewShortMsgTemplate")]
        [ProducesResponseType(typeof(SysShortMsgTemplateDto), StatusCodes.Status200OK)]
        public IActionResult NewShortMsgTemplate()
        {
            var data = _shortMessageManagerService.NewShortMsgTemplate();
            result.Success = !data.IsNullOrEmpty();
            result.ReturnData = result.Success ? _mapper.Map<SysShortMsgTemplateDto>(data) : null;
            result.ReturnMsg = result.Success ? ResxCommon.Success : ResxCommon.Fail;

            return Ok(result);
        }

        /// <summary>
        /// 保存短信模板
        /// </summary>
        /// <param name="template">模板内容</param>
        /// <returns></returns>
        [HttpPost("SaveShortMsgTemplate")]
        [ProducesResponseType(typeof(SysShortMsgTemplateDto), StatusCodes.Status200OK)]
        public IActionResult SaveShortMsgTemplate(SysShortMsgTemplateDto template)
        {
            string msg;
            var data = _shortMessageManagerService.SaveShortMsgTemplate(_mapper.Map<SysShortMsgTemplate>(template), out msg);
            result.Success = !data.IsNullOrEmpty();
            result.ReturnData = result.Success ? _mapper.Map<SysShortMsgTemplateDto>(data) : null;
            result.ReturnMsg = msg;

            return Ok(result);
        }

        /// <summary>
        /// 获取短信模板
        /// </summary>
        /// <param name="code">短信模板code</param>
        /// <returns></returns>
        [HttpPost("GetShortMsgTemplate")]
        [ProducesResponseType(typeof(SysShortMsgTemplateDto), StatusCodes.Status200OK)]
        public IActionResult GetShortMsgTemplate(string code)
        {
            string msg;
            var data = _shortMessageManagerService.GetShortMsgTemplate(code, out msg);
            result.Success = !data.IsNullOrEmpty();
            result.ReturnData = result.Success ? _mapper.Map<SysShortMsgTemplateDto>(data) : null;
            result.ReturnMsg = msg;

            return Ok(result);
        }

        /// <summary>
        /// 获取短信模板集合
        /// </summary>
        /// <param name="query">查询条件</param>
        /// <returns></returns>
        [HttpPost("GetShortMsgTemplates")]
        [ProducesResponseType(typeof(List<SysShortMsgTemplateDto>), StatusCodes.Status200OK)]
        public IActionResult GetShortMsgTemplates(SysShortMsgTemplateQuery query)
        {
            string msg;
            int totalNumber = 0;
            int totalPage = 0;
            var data = _shortMessageManagerService.GetShortMsgTemplates(query, ref totalNumber, ref totalPage, out msg);
            result.Success = data is not null;
            result.ReturnData = result.Success ? _mapper.Map<List<SysShortMsgTemplateDto>>(data) : null;
            result.ReturnMsg = msg;
            result.TotalNumber = totalNumber;
            result.TotalPage = totalPage;

            return Ok(result);
        }

        /// <summary>
        /// 获取短信模板类型
        /// </summary>
        /// <returns></returns>
        [HttpPost("GetShortMsgTemplateTypes")]
        public IActionResult GetShortMsgTemplateTypes()
        {
            var data = _shortMessageManagerService.GetShortMsgTemplateTypes();
            result.Success = data is not null;
            result.ReturnData = data;
            result.ReturnMsg = ResxCommon.Success;

            return Ok(result);
        }
        #endregion
    }
}
