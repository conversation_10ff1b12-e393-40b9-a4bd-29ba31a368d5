﻿using MediatR;
using Microsoft.AspNetCore.Mvc;
using Peis.External.Platform.Service.Service.ServiceImpl.MediatR;
using Peis.Model;
using Peis.Model.DTO.ReportConclusion;
using Peis.Model.DTO.ReportConclusionNew;
using Peis.Service.IService;
using Peis.Service.Service.MediatR;
using Peis.Utility.CustomAttribute;
using Peis.Utility.Helper;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace Peis.API.Controllers.Business
{
    /// <summary>
    /// 体检报告主检审核
    /// </summary>
    [Route("api/[controller]")]
    [ApiController]
    public class ReportConclusionNewController : BaseApiController
    {
        private readonly IReportConclusionV2Service _reportConclusionService;
        private readonly IMediator _mediator;

        public ReportConclusionNewController(IReportConclusionV2Service reportConclusionV2Service, IMediator mediator)
        {
            _reportConclusionService = reportConclusionV2Service;
            _mediator = mediator;
        }

        #region 主检审核查询列表（不分配、分配、优先分配）
        /// <summary>
        /// 获取主检的人员列表（主检不分配）/审核的人员列表
        /// </summary>
        /// <param name="queryPatientList"></param>
        /// <returns></returns>
        [HttpPost("GetPatientList4NotAllocate")]
        [ProducesResponseType(typeof(List<PatientInfo>), 200)]
        public IActionResult GetPatientList4NotAllocate(QueryPatientList queryPatientList)
        {
            queryPatientList.IsOccupation = false;
            result.ReturnData = _reportConclusionService.GetPatientList4NotAllocate(queryPatientList);
            return Ok(result);
        }

        /// <summary>
        /// 登记、患者信息查询：主检、审核
        /// </summary>
        /// <param name="query">查询条件</param>
        /// <returns>ReportPeRegisterDtos</returns>
        [HttpPost("GetReportPeRegisters")]
        [ProducesResponseType(typeof(List<ReportPeRegisterDto>), 200)]
        public ActionResult GetReportPeRegisters([FromBody] ReportPeRegisterQuery query)
        {
            int totalNumber = 0;
            int totalPage = 0;
            query.IsOccupation = false;
            result.ReturnData = _reportConclusionService.GetReportPeRegisters(query, ref totalNumber,ref totalPage);
            result.TotalNumber = totalNumber;
            result.TotalPage = totalPage;
            result.ReturnMsg = ResxCommon.Success;

            return Ok(result);
        }
        #endregion

        #region 综述建议（读取、生成、编辑）

        /// <summary>
        /// 获取报告结论及综述建议
        /// </summary>
        /// <param name="regNo">体检号</param>
        /// <param name="oper">0 主检 1 审核</param>
        /// <returns></returns>
        [HttpPost("GetReportConclusion")]
        [ProducesResponseType(typeof(ReportConclusionNew), 200)]
        public ActionResult GetReportConclusion([FromQuery] string regNo, [FromQuery] CheckAuditOper oper)
        {
            result.ReturnData = _reportConclusionService.GetReportConclusion(regNo, oper,false);
            return Ok(result);
        }

        /// <summary>
        /// 根据结果记录生成综述建议
        /// </summary>
        /// <param name="regNo">体检号</param>
        /// <param name="combCodes">组合编号集合</param>
        /// <returns></returns>
        [HttpPost("GenerateSummarySuggestionFromRecord")]
        [ProducesResponseType(typeof(SummarySuggestion), 200)]
        public ActionResult GenerateSummarySuggestionFromRecord(
            [Required(ErrorMessage = "体检号不能为空！")][Regex(VerifyRegNo = true, ErrorMessage = "体检号格式错误！")][FromQuery] string regNo,
            [FromBody] string[] combCodes)
        {
            result.ReturnData = _reportConclusionService.GenerateSummarySuggestion(regNo, false, combCodes);
            return Ok(result);
        }

        /// <summary>
        /// 编辑综述标签
        /// </summary>
        /// <param name="editRequest">编辑的综述标签，及其关联的疾病标签</param>
        /// <returns></returns>
        [HttpPost("EditSummaryTag")]
        [ProducesResponseType(typeof(EditSummaryTagBack), 200)]
        public ActionResult EditSummaryTag([FromBody] EditSummaryTagRequest editRequest)
        {
            result.ReturnData = _reportConclusionService.EditSummaryTag(editRequest);
            return Ok(result);
        }

        /// <summary>
        /// 编辑疾病建议
        /// </summary>
        /// <param name="disease"></param>
        /// <returns></returns>
        [HttpPost("EditSuggesstionDisease")]
        [ProducesResponseType(typeof(SuggestedDisease), 200)]
        public ActionResult EditSuggesstionDisease([FromBody] SuggestedDisease disease)
        {
            result.ReturnData = _reportConclusionService.EditSuggesstionDisease(disease);
            return Ok(result);
        }
        #endregion

        #region 主检审核（暂存、确认主检审核、取消主检审核）

        /// <summary>
        /// 暂存主检审核
        /// </summary>
        /// <param name="repCon">报告结论及主检审核数据</param>
        /// <returns></returns>
        [HttpPost("TempSaveReportConclusion")]
        public ActionResult TempSaveReportConclusion([FromBody] ReportConclusionNew repCon)
        {
            repCon.IsOccupation = false;
            _reportConclusionService.TempSaveReportConclusion(repCon);
            return Ok(result);
        }

        /// <summary>
        /// 确认主检审核
        /// </summary>
        /// <param name="repCon">报告结论及主检审核数据</param>
        /// <param name="oper">0 主检 1 审核</param>
        /// <returns></returns>
        [HttpPost("ConfirmReportConclusion")]
        public ActionResult ConfirmReportConclusion([FromBody] ReportConclusionNew repCon, [FromQuery] CheckAuditOper oper)
        {
            repCon.IsOccupation = false;
            _reportConclusionService.ConfirmReportConclusion(repCon, oper);

            // 发布报告生成、短信通知体检人任务
            if (oper == CheckAuditOper.审核)
                AuditReportOtherBussHandle(repCon.Patient.RegNo);

            return Ok(result);
        }

        /// <summary>
        /// 取消主检审核
        /// </summary>
        /// <param name="regNo">体检号</param>
        /// <param name="doctorCode">操作医生代码</param>
        /// <param name="oper">0 主检 1 审核</param>
        /// <returns></returns>
        [HttpPost("CancelReportConclusion")]
        public ActionResult CancelReportConclusion([FromQuery] string regNo, [FromQuery] string doctorCode, [FromQuery] CheckAuditOper oper)
        {
            _reportConclusionService.CancelReportConclusion(regNo, doctorCode, oper,false);
            CancelReportOtherBussHandle(regNo);

            return Ok(result);
        }


        #region private
        /// <summary>
        /// 审核报告后其他业务处理
        /// </summary>
        /// <param name="regNo"></param>
        void AuditReportOtherBussHandle(string regNo)
        {
            if (regNo.IsNullOrEmpty()) return;

            _mediator.Send(new SavePeReportFileByRegNoHandle.Data(regNo));
            _mediator.Send(new AddSendShortMsgToPatForApprovedJobHandle.Data(regNo));
        }

        /// <summary>
        /// 取消审核报告后其他业务处理
        /// </summary>
        /// <param name="regNo"></param>
        void CancelReportOtherBussHandle(string regNo)
        {
            if (regNo.IsNullOrEmpty()) return;

            _mediator.Send(new RemovePeReportFileByRegNoHandle.Data(regNo));
            _mediator.Send(new RemoveSendShortMsgToPatForApprovedJobHandle.Data(regNo));
        } 
        #endregion

        #endregion
    }
}
