﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Peis.Model.DTO.Test;
using Peis.Service.IService;
using Peis.Utility.PeIM.Core;
using System.Collections.Generic;
using System.Linq;

namespace Peis.API.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    [AllowAnonymous]
    public class TestController : BaseApiController
    {
        protected readonly ITestService _testService;

        public TestController(ITestService testService)
        {
            _testService = testService;
        }

        /// <summary>
        /// 获取登录的用户列表
        /// </summary>
        /// <param name="peimClients"></param>
        /// <returns></returns>
        [HttpGet("LoginUsers")]
        public IEnumerable<string> LoginUsers([FromServices] IPeimClients peimClients)
        {
            foreach (var user in peimClients.All())
            {
                foreach (var client in user.Values)
                {
                    yield return $"{client.LoginTime} {client.ConnectionId} {client.IP.MapToIPv4()} {client.UserId} {client.UserName}";
                }
            }
        }

        /// <summary>
        /// 发送消息
        /// </summary>
        /// <param name="peimClients"></param>
        /// <param name="msg">发送内容</param>
        /// <param name="sendUsers">指定的用户列表，多个用户代码之间用英文逗号隔开</param>
        /// <returns></returns>
        [HttpGet("SendMsg")]
        public IActionResult SendMsg([FromServices] IPeimClients peimClients, [FromQuery] string msg, [FromQuery] string sendUsers)
        {
            if (string.IsNullOrEmpty(sendUsers))
                return Ok("发送消息缺少指定用户");

            peimClients.Users(sendUsers.Split(','))?.SendMsg(msg);
            return Ok($"发送成功：{msg}");
        }

        /// <summary>
        /// Ping
        /// </summary>
        /// <returns>bool</returns>
        [HttpGet("Ping")]
        public bool Ping()
        {
           return true;
        }

        /// <summary>
        /// 获取已采集登记列表
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        [HttpPost("GetGatheredRegisterList")]
        public IActionResult GetGatheredRegisterList(TestRegisterQuery query)
        {
            int totalNumber = 0;
            int totalPage = 0;
            result.ReturnData = _testService.GetGatheredRegisterList(query,ref totalNumber, ref totalPage);
            result.TotalNumber = totalNumber;
            result.TotalPage = totalPage;

            return Ok(result);
        }

        /// <summary>
        /// 获取请求的信息
        /// </summary>
        /// <returns></returns>
        [HttpGet("GetRequestInfo")]
        [HttpPost("GetRequestInfo")]
        public IActionResult GetRequestInfo()
        {
            var requestInfo = new Dictionary<string, object>
            {
                { "RemoteIpAddress" ,HttpContext.Connection.RemoteIpAddress.ToString()},
                { "Method"          ,HttpContext.Request.Method },
                { "Scheme"          ,HttpContext.Request.Scheme },
                { "Host"            ,HttpContext.Request.Host },
                { "Path"            ,HttpContext.Request.Path },
                { "QueryString"     ,HttpContext.Request.QueryString },
                { "ContentType"     ,HttpContext.Request.ContentType },
                { "Headers"         ,HttpContext.Request.Headers.ToDictionary(h => h.Key, h => h.Value.ToString()) },
            };

            return Ok(requestInfo);
        }

        /// <summary>
        /// 更新建议疾病绑定疾病组合数据（维护用）
        /// </summary>
        /// <returns></returns>
        [HttpPut("ModifySuggDiseaseBindSummTag")]
        public IActionResult ModifySuggDiseaseBindSummTag()
        {
            _testService.ModifySuggDiseaseBindSummTag();
            return Ok(result);
        }
    }
}
