using SqlSugar;
using System;

namespace Peis.Model.DataBaseEntity
{
    ///<summary>
    ///结算组合明细
    ///</summary>
    [SplitTable(SplitType.Year)]
    [SugarTable("FeeSettlementComb_{yyyy}", "结算组合明细")]
    [SugarIndex("index_SettlementNo_", nameof(SettlementNo), OrderByType.Asc)]
    public class FeeSettlementComb
    {
        /// <summary>
        /// 雪花Id
        /// </summary>        
        [SugarColumn(IsNullable = false, IsPrimaryKey = true)]
        public long Id { get; set; }

        /// <summary>
        /// 结算号
        /// </summary>        
        [SugarColumn(IsNullable = false, Length = 10)]
        public string SettlementNo { get; set; }

        /// <summary>
        /// 登记组合Id
        /// </summary>        
        [SugarColumn(IsNullable = false)]
        public long RegCombId { get; set; }

        /// <summary>
        /// 组合代码
        /// </summary>        
        [SugarColumn(IsNullable = false, Length = 8)]
        public string CombCode { get; set; }

        /// <summary>
        /// 组合名称
        /// </summary>        
        [SugarColumn(IsNullable = false, Length = 200)]
        public string CombName { get; set; }

        /// <summary>
        /// 原始单价
        /// </summary>        
        [SugarColumn(IsNullable = false, Length = 12, DecimalDigits = 2)]
        public decimal OriginalPrice { get; set; }

        /// <summary>
        /// 单价
        /// </summary>        
        [SugarColumn(IsNullable = false, Length = 12, DecimalDigits = 2)]
        public decimal Price { get; set; }

        /// <summary>
        /// 折扣
        /// </summary>        
        [SugarColumn(IsNullable = false, Length = 8, DecimalDigits = 2)]
        public decimal Discount { get; set; }

        /// <summary>
        /// 费用分类
        /// </summary>        
        [SugarColumn(IsNullable = false, Length = 10)]
        public string FeeCls { get; set; }

        /// <summary>
        /// 结算时间
        /// </summary>  
        [SplitField]
        [SugarColumn(IsNullable = false)]
        public DateTime SettlementTime { get; set; }
    }
}