﻿using Peis.Model.Other.PeEnum;

namespace Peis.External.Wx.Service.Model.DTO
{
    /// <summary>
    /// 报告详情
    /// </summary>
    public class ReportDetail
    {
        /// <summary>
        /// 姓名
        /// </summary>
        public string name { get; set; }

        /// <summary>
        /// 性别
        /// </summary>
        public Sex? sex { get; set; }

        /// <summary>
        /// 年龄
        /// </summary>
        public int? age { get; set; }

        /// <summary>
        /// 体检号
        /// </summary>
        public string regno { get; set; }

        /// <summary>
        /// 体检日期
        /// </summary>
        public string reg_date { get; set; }

        /// <summary>
        /// 审核日期
        /// </summary>
        public string con_date { get; set; }

        /// <summary>
        /// 单位
        /// </summary>
        public string lnc_name { get; set; }

        /// <summary>
        /// 主检医生
        /// </summary>
        public string doc_name { get; set; }

        /// <summary>
        /// 综述
        /// </summary>
        public string sumup { get; set; }

        /// <summary>
        /// 建议
        /// </summary>
        public string sugg_tag { get; set; }

        /// <summary>
        /// 结论
        /// </summary>
        public string conclusion { get; set; }

        /// <summary>
        /// 体检结果记录
        /// </summary>
        public List<PeRecord> itemlist { get; set; }
    }

    /// <summary>
    /// 体检结果记录
    /// </summary>
    public class PeRecord
    {
        /// <summary>
        /// 组合代码
        /// </summary>
        public string comb_code { get; set; }

        /// <summary>
        /// 组合名称
        /// </summary>
        public string comb_name { get; set; }

        /// <summary>
        /// 组合小结
        /// </summary>
        public string res_tag { get; set; }

        /// <summary>
        /// 检查类型
        /// </summary>
        public CheckCls check_cls { get; set; }

        /// <summary>
        /// 医生名称
        /// </summary>
        public string dept_doct { get; set; }

        /// <summary>
        /// 项目结果明细
        /// </summary>
        public List<ItemDetail> detailList { get; set; }
    }

    /// <summary>
    /// 项目结果明细
    /// </summary>
    public class ItemDetail
    {
        /// <summary>
        /// 项目代码
        /// </summary>
        public string item_code { get; set; }

        /// <summary>
        /// 项目名称
        /// </summary>
        public string item_name { get; set; }

        /// <summary>
        /// 项目结果
        /// </summary>
        public string rec_result { get; set; }

        /// <summary>
        /// 结果单位
        /// </summary>
        public string unit { get; set; }

        /// <summary>
        /// 结果下限
        /// </summary>
        public string ref_lower { get; set; }

        /// <summary>
        /// 结果上限
        /// </summary>
        public string ref_upper { get; set; }

        /// <summary>
        /// 提示符号
        /// </summary>
        public string hint { get; set; }
    }
}
