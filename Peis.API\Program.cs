using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Hosting;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Peis.Utility.Helper;
using Serilog;
using Serilog.Core;
using Serilog.Events;
using System;
using System.IO;

namespace Peis.API;

public class Program
{
    public static void Main(string[] args)
    {
        CreateLogger();

        try
        {
            Log.Information("Starting web host.");
            var builder = CreateWebApplicationBuilder(args);
            var app = builder.Build();
            app.InitializeApplication();
            ServiceLocator.Initialize(app);

            Log.Information("- {Urls}", Appsettings.GetSectionArraryValue("AppSettings:RunUrls"));
            app.Run();
        }
        catch (Exception ex)
        {
            Log.Fatal(ex, "Host terminated unexpectedly!");
            throw;
        }
        finally
        {
            Log.CloseAndFlush();
        }
    }

    static WebApplicationBuilder CreateWebApplicationBuilder(string[] args)
    {
        var builder = WebApplication.CreateBuilder(args);
        builder.WebHost.UseUrls(Appsettings.GetSectionArraryValue("AppSettings:RunUrls"));
        builder.Host.UseAutofac();
        builder.Host.UseSerilog();
        builder.Services.ReplaceConfiguration(builder.Configuration);
        builder.Services.AddApplication<PeisApiModule>();
        builder.WebHost.UseStaticWebAssets();

        return builder;
    }

    static void CreateLogger()
    {
#if DEBUG
        var levelSwitch = new LoggingLevelSwitch(LogEventLevel.Debug);
#else
        var levelSwitch = new LoggingLevelSwitch(LogEventLevel.Information);
#endif

        string seqUrl = Appsettings.GetSectionValue("ExternalSystem:SeqUrl") ?? "http://localhost:5341";
        Log.Logger = new LoggerConfiguration()
        .MinimumLevel.ControlledBy(levelSwitch)
        .MinimumLevel.Override("Microsoft", LogEventLevel.Warning)
        .MinimumLevel.Override("Volo.Abp", LogEventLevel.Warning)
        .MinimumLevel.Override("SoapCore", LogEventLevel.Warning)
        .MinimumLevel.Override("Quartz", LogEventLevel.Warning)
        .MinimumLevel.Override("Peis.Quartz", LogEventLevel.Warning)
        .Enrich.WithProperty("Application", "PeisHub")
        .Enrich.FromLogContext()
        .WriteTo.Async(lc =>
        {
            var consoleTemplate = "[{Timestamp:HH:mm:ss:fff} {Level:u3}] {SourceContext} {CorrelationId} {Message:lj} {NewLine}{Exception}";
            lc.Console(outputTemplate: consoleTemplate);
#if !DEBUG
            lc.Seq(serverUrl: seqUrl, apiKey: "SeqApiKeyPeisHub", controlLevelSwitch: levelSwitch);
            lc.File(path: Path.Combine(AppContext.BaseDirectory, "logs", "log_.log"), outputTemplate: consoleTemplate, rollingInterval: RollingInterval.Day);
#endif
        })
        .CreateLogger();
    }
}