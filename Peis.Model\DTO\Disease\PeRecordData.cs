﻿using Peis.Model.DTO.DoctorStation;
using Peis.Model.Other.PeEnum;

namespace Peis.Model.DTO.Disease
{
    /// <summary>
    /// 体检记录数据
    /// </summary>
    public class RecordCombData
    {
        /// <summary>
        /// 组合记录
        /// </summary>
        public RecordComb RecordComb { get; set; }

        /// <summary>
        /// 项目记录
        /// </summary>
        public List<RecordItem> RecordItems { get; set; }

        /// <summary>
        /// 组合小结
        /// </summary>
        public List<CombTag> CombTags { get; set; }

        /// <summary>
        /// 重大阳性结果
        /// </summary>
        public MajorPositiveDetail Major { get; set; }
    }

    /// <summary>
    /// 组合记录
    /// </summary>
    public class RecordComb
    {
        /// <summary>
        /// 体检号
        /// </summary>
        public string RegNo { get; set; }

        /// <summary>
        /// 登记组合Id
        /// </summary>
        public long RegCombId { get; set; }

        /// <summary>
        /// 组合代码
        /// </summary>
        public string CombCode { get; set; }

        /// <summary>
        /// 组合名称
        /// </summary>
        public string CombName { get; set; }

        /// <summary>
        /// 组合排序
        /// </summary>        
        public int SortIndex { get; set; }

        /// <summary>
        /// 项目分类
        /// </summary>
        public string ClsCode { get; set; }

        /// <summary>
        /// 检查科室代码
        /// </summary>
        public string ExamDeptCode { get; set; }

        /// <summary>
        /// 操作员名称
        /// </summary>
        public string OperName { get; set; }

        /// <summary>
        /// 检查日期
        /// </summary>
        public DateTime ExamTime { get; set; }

        /// <summary>
        /// 医生名称
        /// </summary>
        public string DoctorName { get; set; }

        /// <summary>
        /// 组合异常提醒标识
        /// </summary>
        public bool IsError { get; set; }
        /// <summary>
        /// 检查组合Id
        /// </summary>
        public long? RecCombId { get; set; }
    }

    /// <summary>
    /// 项目记录
    /// </summary>
    public class RecordItem
    {
        /// <summary>
        /// 项目代码
        /// </summary>
        public string ItemCode { get; set; }

        /// <summary>
        /// 项目名称
        /// </summary>
        public string ItemName { get; set; }

        /// <summary>
        /// 参考值
        /// </summary>
        public string ReferenceValue { get; set; }

        /// <summary>
        /// 项目结果类型
        /// </summary>
        public Other.PeEnum.ValueType ResultType { get; set; }

        /// <summary>
        /// 结果单位
        /// </summary>
        public string Unit { get; set; }

        /// <summary>
        /// 结果下限
        /// </summary>
        public string LowerLimit { get; set; }

        /// <summary>
        /// 结果上限
        /// </summary>
        public string UpperLimit { get; set; }

        /// <summary>
        /// 危急下限
        /// </summary>
        public string DangerLowerLimit { get; set; }

        /// <summary>
        /// 危急上限
        /// </summary>
        public string DangerUpperLimit { get; set; }

        /// <summary>
        /// 提示符号
        /// </summary>
        public string Hint { get; set; }

        /// <summary>
        /// 显示顺序
        /// </summary>
        public int SortIndex { get; set; }

        /// <summary>
        /// 项目结果的异常类型
        /// </summary>
        public AbnormalType AbnormalType { get; set; }

        /// <summary>
        /// 项目标签
        /// </summary>
        public List<ItemTag> ItemTags { get; set; }

        /// <summary>
        /// 上次结果
        /// </summary>
        public string LastItemResult { get; set; }

        /// <summary>
        /// 如果项目的ResultType是0: 前端则读取这个值显示
        /// </summary>
        public string NumberResult { get; set; }

        /// <summary>
        /// 前端字段
        /// </summary>
        public object[] HistoryTheads { get { return Array.Empty<object>(); } }
        /// <summary>
        /// 前端字段
        /// </summary>
        public object[] HistoryList { get { return Array.Empty<object>(); } }
        /// <summary>
        /// 参考范围
        /// </summary>
        public string ReferenceRange {  get; set; }
    }

    /// <summary>
    /// 项目记录以及小结(组装数据)
    /// </summary>
    public class ItemAndResult
    {
        /// <summary>
        /// 项目记录
        /// </summary>
        public List<PeRecordItem> RecordItem { get; set; }

        /// <summary>
        /// 项目小结
        /// </summary>
        public List<PeRecordItemTag> RecordItemTag { get; set; }
    }
}
