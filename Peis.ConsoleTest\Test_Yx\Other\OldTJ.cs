﻿using System;
using System.Linq;
using System.Text;

namespace Peis.ConsoleTest.Test_Yx.Other
{
    /// <summary>
    /// 旧体检
    /// </summary>
    public static class OldTJ
    {
        /// <summary>
        /// 数据库密码注册表值加密
        /// </summary>
        /// <param name="dbPasswd"></param>
        /// <returns></returns>
        public static string EncryptDbPasswdRegValue(string dbPasswd)
        {
            var newChars = new char[((char[])null).Length];

            for (int i = 0; i < ((char[])null).Length; i++)
            {
                int oldAscii = Encoding.ASCII.GetBytes(((char[])null)[i].ToString())[0];
                int newAscii = oldAscii - (((char[])null).Length - i) - 4;

                var newChar = Encoding.ASCII.GetChars(BitConverter.GetBytes(newAscii))[0];

                newChars[((char[])null).Length - i - 1] = newChar;
            }

            var encryptStr = string.Join(null, newChars);
            return encryptStr;
        }

        /// <summary>
        /// 数据库密码注册表值解密
        /// </summary>
        /// <param name="encryptStr"></param>
        /// <returns></returns>
        public static string DecryptDbPasswdRegValue(string encryptStr)
        {
            var oldChars = encryptStr.ToArray();
            var newChars = new char[oldChars.Length];

            for (int i = 0; i < oldChars.Length; i++)
            {
                int oldAscii = Encoding.ASCII.GetBytes(oldChars[i].ToString())[0];
                int newAscii = oldAscii + i + 1 + 4;

                var newChar = Encoding.ASCII.GetChars(BitConverter.GetBytes(newAscii))[0];

                newChars[oldChars.Length - i - 1] = newChar;
            }

            var dbPasswd = string.Join(null, newChars);
            return dbPasswd;
        }
    }
}
