﻿using Peis.Model.Constant;
using Peis.Model.DataBaseEntity.Occupation;
using Peis.Model.DTO;
using Peis.Model.DTO.External.HATM;
using Peis.Model.Other.PeEnum;
using Peis.Repository.IRepository;
using Peis.Service.IService;
using Peis.Utility.PeUser;
using System.Linq.Expressions;
using System.Net.Http;
using System.Net.Mime;
using System.Text;

namespace Peis.Service.Service;

/// <summary>
/// 打印文件服务
/// </summary>
public class PrintFileService : IPrintFileService
{
    private readonly IRegisterRepository _registerRepository;
    private readonly IHttpClientHelper _httpClientHelper;
    private readonly ISystemParameterService _systemParameterService;
    private readonly IRegisterNewService _registerService;
    private readonly IDataRepository<PeRegister> _registerDataRepository;
    private readonly IDataRepository<PeSample> _sampleDataRepository;
    private readonly IReportFileRepository _reportFileRepository;
    private readonly IHttpContextUser _httpContextUser;
    private readonly IDataRepository<PeRegisterOccupation> _registerOccupationRepository;
    public PrintFileService(IRegisterRepository registerRepository,
                            IHttpClientHelper httpClientHelper,
                            ISystemParameterService systemParameterService,
                            IRegisterNewService registerService,
                            IDataRepository<PeRegister> registerDataRepository,
                            IDataRepository<PeSample> sampleDataRepository,
                            IReportFileRepository reportFileRepository,
                            IHttpContextUser httpContextUser,
                            IDataRepository<PeRegisterOccupation> registerOccupationRepository)
    {
        _registerRepository = registerRepository;
        _httpClientHelper = httpClientHelper;
        _systemParameterService = systemParameterService;
        _registerService = registerService;
        _registerDataRepository = registerDataRepository;
        _sampleDataRepository = sampleDataRepository;
        _reportFileRepository = reportFileRepository;
        _httpContextUser = httpContextUser;
        _registerOccupationRepository = registerOccupationRepository;
    }


    /// <summary>
    /// 获取指引单Pdf文件流
    /// </summary>
    /// <param name="regNo">体检号</param>
    /// <returns>Stream</returns>
    public async Task<Stream> GetGuidancePdfStream(string regNo)
    {
        var register = _registerRepository.ReadRegister(regNo).First();
        if (register.IsNullOrEmpty() || !register.IsActive) return null;

        var printQuery = new PrintQuery
        {
            RegNo = regNo,
            FilterList = new List<PrintFileType> { PrintFileType.Guidance }
        };
        var prints = _registerService.CheckPrintables(printQuery);
        if (prints.IsNullOrEmpty()) return null;

        var paramObj = new
        {
            queryString = $"regNo={regNo}",
            reportCode = register.GuidanceType
        };
        var httpContent = new StringContent(paramObj.ToJson(), Encoding.UTF8, MediaTypeNames.Application.Json);
        return await _httpClientHelper.DownloadStreamAsync(ConstantUrl.ExportToPdfUrl, httpContent);
    }

    /// <summary>
    /// 获取体检标签Pdf文件流
    /// </summary>
    /// <param name="regNo">体检号</param>
    /// <returns>Stream</returns>
    public async Task<Stream> GetPeLabelPdfStream(string regNo)
    {
        var register = _registerRepository.ReadRegister(regNo).First();
        if (register.IsNullOrEmpty() || !register.IsActive) return null;

        var printQuery = new PrintQuery
        {
            RegNo = regNo,
            FilterList = new List<PrintFileType> { PrintFileType.PeLabel }
        };
        var prints = _registerService.CheckPrintables(printQuery);
        if (prints.IsNullOrEmpty()) return null;

        var paramObj = new
        {
            queryString = $"regNo={regNo}&customizePrintTimes=0",
            reportCode = _systemParameterService.PeLabelType
        };
        var httpContent = new StringContent(paramObj.ToJson(), Encoding.UTF8, MediaTypeNames.Application.Json);
        return await _httpClientHelper.DownloadStreamAsync(ConstantUrl.ExportToPdfUrl, httpContent);
    }

    /// <summary>
    /// 获取检验非采血条码Pdf文件流
    /// </summary>
    /// <param name="regNo">体检号</param>
    /// <returns>Stream</returns>
    public async Task<Stream> GetNonBloodBarcodePdfStream(string regNo)
    {
        var register = _registerRepository.ReadRegister(regNo).First();
        if (register.IsNullOrEmpty() || !register.IsActive) return null;

        var printQuery = new PrintQuery
        {
            RegNo = regNo,
            FilterList = new List<PrintFileType> { PrintFileType.NonBloodBarcode }
        };
        var prints = _registerService.CheckPrintables(printQuery);
        if (prints.IsNullOrEmpty()) return null;

        var paramObj = new
        {
            queryString = $"regNo={regNo}&type=2",
            reportCode = "30232e1288c544aba755930cbd7a6029"
        };
        var httpContent = new StringContent(paramObj.ToJson(), Encoding.UTF8, MediaTypeNames.Application.Json);
        return await _httpClientHelper.DownloadStreamAsync(ConstantUrl.ExportToPdfUrl, httpContent);
    }

    /// <summary>
    /// 获取可打印文件列表
    /// </summary>
    /// <param name="regNo"></param>
    /// <param name="printTypes"></param>
    /// <returns></returns>
    public List<HATMPrintOutputDto> QueryPrintFileList(string regNo, string[] printTypes)
    {
        var printQuery = new PrintQuery
        {
            RegNo = regNo,
            FilterList = printTypes.Select(t => t.ToEnum<PrintFileType>()).ToList()
        };
        var prints = _registerService.CheckPrintables(printQuery);
        var records = new List<HATMPrintOutputDto>();
        foreach (var item in prints)
        {
            var printType = item.ToEnum<PrintFileType>();
            var path = printType.GetApiPath();
            if (path.IsNullOrEmpty()) continue;

            path = $"{ConstantUrl.FileHostUrl}{path.Replace("{regNo}", regNo, StringComparison.OrdinalIgnoreCase)}";
            var record = new HATMPrintOutputDto
            {
                Type = printType.ToString(),
                TypeName = printType.GetDescription(),
                PdfUrl = path,
            };

            records.Add(record);
        }
        return records;
    }

    /// <summary>
    /// 获取打印Pdf文件流
    /// </summary>
    /// <param name="regNo"></param>
    /// <param name="printType"></param>
    /// <param name="updateTime"></param>
    /// <param name="reportCode"></param>
    /// <param name="sampleNo"></param>
    /// <returns></returns>
    /// <exception cref="BusinessException"></exception>
    public async Task<Stream> GetPrintPdfStream(string regNo, PrintFileType printType, bool updatePrintStatus = false, string reportCode = null, string sampleNo = null, bool isOccupation = false)
    {
        var register = _registerRepository.ReadRegister(regNo).First();
        if (register.IsNullOrEmpty()) return null;

        if (updatePrintStatus)
        {
#if DEBUG
            try
            {
                if (!UpdatePrintStatus(printType, register, sampleNo, isOccupation))
                    throw new BusinessException($"打印{printType.GetDescription()}失败，请稍后再试！");
            }
            catch
            {

            }
#else
             if (!UpdatePrintStatus(printType, register, sampleNo, isOccupation))
                            throw new BusinessException($"打印{printType.GetDescription()}失败，请稍后再试！");
#endif
        }

        //如果是报告，并且指定报告类型与登记的报告类型相同，则先查询是否有缓存的报告文件，有则直接返回
        if (printType == PrintFileType.Report && (reportCode == register.ReportType || reportCode.IsNullOrEmpty()))
        {
            var reportFile = _reportFileRepository.QueryPeReportFiles(regNo).First(r => r.IsWithStamp == false);
            if (reportFile != null)
            {
                var filePath = Path.Combine(Directory.GetCurrentDirectory(), "wwwroot", reportFile.FilePath);
                if (File.Exists(filePath))
                    return new FileStream(filePath, FileMode.Open);
            }
        }

        var externalInfo = GetReportExternalInfo(printType, register, sampleNo);
        var paramObj = new
        {
            queryString = $"regNo={regNo}{externalInfo.ExternalParam}",
            reportCode = !reportCode.IsNullOrEmpty() ? reportCode : externalInfo.ReportCode
        };
        var httpContent = new StringContent(paramObj.ToJson(), Encoding.UTF8, MediaTypeNames.Application.Json);
        return await _httpClientHelper.DownloadStreamAsync(ConstantUrl.ExportToPdfUrl, httpContent);
    }

    /// <summary>
    /// 更新打印状态
    /// </summary>
    /// <param name="printType"></param>
    /// <param name="regNo"></param>
    /// <param name="time"></param>
    /// <returns></returns>
    public bool UpdatePrintStatus(string printType, string regNo, string sampleNo, bool isOccupation = false)
    {
        var register = _registerRepository.ReadRegister(regNo).First();
        if (register.IsNullOrEmpty() || !register.IsActive) return false;
        var type = printType.ToEnum<PrintFileType>();
        return UpdatePrintStatus(type, register, sampleNo, isOccupation);
    }

    #region 私有方法
    /// <summary>
    /// 获取不同分类的附加参数和报告代码
    /// </summary>
    /// <param name="printType"></param>
    /// <param name="register"></param>
    /// <param name="sampNo"></param>
    /// <returns></returns>
    /// <exception cref="BusinessException"></exception>
    private ReportPrintTypeExternalInfo GetReportExternalInfo(PrintFileType printType, PeRegister register, string sampNo)
    {
        switch (printType)
        {
            case PrintFileType.Guidance:
                return new ReportPrintTypeExternalInfo { ExternalParam = string.Empty, ReportCode = register.GuidanceType };
            case PrintFileType.PeLabel:
                return new ReportPrintTypeExternalInfo { ExternalParam = "&customizePrintTimes=0", ReportCode = _systemParameterService.PeLabelType };
            case PrintFileType.Barcode:
                return new ReportPrintTypeExternalInfo { ExternalParam = $"&type=0&sampleNo={sampNo}", ReportCode = "30232e1288c544aba755930cbd7a6029" };
            case PrintFileType.NonBloodBarcode:
                return new ReportPrintTypeExternalInfo { ExternalParam = $"&type=2&sampleNo={sampNo}", ReportCode = "30232e1288c544aba755930cbd7a6029" };
            case PrintFileType.BloodBarcode:
                return new ReportPrintTypeExternalInfo { ExternalParam = $"&type=1&sampleNo={sampNo}", ReportCode = "30232e1288c544aba755930cbd7a6029" };
            case PrintFileType.Colonoscopy:
                return new ReportPrintTypeExternalInfo { ExternalParam = string.Empty, ReportCode = "320b2821bd784e99ae9423b9d8e75d3d" };
            case PrintFileType.Gastroscope:
                return new ReportPrintTypeExternalInfo { ExternalParam = string.Empty, ReportCode = "2671f91f2e5a46dca739696557c19b19" };
            case PrintFileType.Report:
                return new ReportPrintTypeExternalInfo { ExternalParam = string.Empty, ReportCode = register.ReportType };
            case PrintFileType.AnesthesiaCost:
                return new ReportPrintTypeExternalInfo { ExternalParam = string.Empty, ReportCode = "9559c2289f014ff4ad51df504efead60" };
            default:
                throw new BusinessException($"{printType}格式不存在！");
        }
    }

    /// <summary>
    /// 更新打印状态及时间
    /// </summary>
    /// <param name="printType"></param>
    /// <param name="register"></param>
    /// <param name="sampleNo"></param>
    /// <returns></returns>
    private bool UpdatePrintStatus(PrintFileType printType, PeRegister register, string sampleNo, bool isOccupation = false)
    {
        Expression<Func<PeSample, bool>> sampleWhere = s => s.RegNo == register.RegNo;
        if (!sampleNo.IsNullOrEmpty())
        {
            sampleWhere = sampleWhere.And(s => s.SampleNo == sampleNo);
        }

        switch (printType)
        {
            case PrintFileType.Guidance:
                return _registerDataRepository.Update(r => new PeRegister { GuidancePrintTime = DateTime.Now, GuidancePrinted = true },
                                                      r => r.RegNo == register.RegNo);
            case PrintFileType.Barcode:
                return _sampleDataRepository.Update(s => new PeSample { PrintTime = DateTime.Now }, sampleWhere);
            case PrintFileType.NonBloodBarcode:
                return _sampleDataRepository.Update(s => new PeSample { PrintTime = DateTime.Now },
                                                    sampleWhere.And(s => SqlFunc.Subqueryable<CodeSample>().Where(r => r.SampCode == s.SampCode && r.SampName.Contains("血")).NotAny()));
            case PrintFileType.BloodBarcode:
                return _sampleDataRepository.Update(s => new PeSample { PrintTime = DateTime.Now },
                                                    sampleWhere.And(s => SqlFunc.Subqueryable<CodeSample>().Where(r => r.SampCode == s.SampCode && r.SampName.Contains("血")).Any()));
            case PrintFileType.Report:
                //新增了更新报告打印人，只有当打印人为空时才更新
                if (isOccupation)
                    return _registerOccupationRepository.Update(r => new PeRegisterOccupation { ReportPrintedTime = DateTime.Now, ReportPrinted = true },
                                                                r => r.RegNo == register.RegNo);
                else
                    return _registerDataRepository.Update(r => new PeRegister { ReportPrintedTime = DateTime.Now, ReportPrinted = true, ReportPrintedOperator = string.IsNullOrEmpty(register.ReportPrintedOperator) ? _httpContextUser.UserId : register.ReportPrintedOperator },
                                                      r => r.RegNo == register.RegNo);
            default:
                return true; //不需要更新的打印类型，不更新
        }
    }
    #endregion
}

