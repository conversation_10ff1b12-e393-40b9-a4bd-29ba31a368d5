﻿using Peis.Model.DataBaseEntity.Occupation;
using Peis.Model.Other.PeEnum.Occupation;

namespace Peis.Model.DTO.CompanyData;

/// <summary>
/// 单位信息Dto
/// </summary>
public class CodeCompanyDto
{
    /// <summary>
    /// 单位编码
    /// </summary>        
    public string CompanyCode { get; set; }

    /// <summary>
    /// 单位名称
    /// </summary>        
    public string CompanyName { get; set; }

    /// <summary>
    /// 单位别名
    /// </summary>        
    public string CompanyAlias { get; set; }

    /// <summary>
    /// 单位分类
    /// </summary>        
    public string CompanyClsCode { get; set; }

    /// <summary>
    /// 父级
    /// </summary>        
    public string Parent { get; set; }

    /// <summary>
    /// 院内联系人
    /// </summary>        
    public string HospContact { get; set; }

    /// <summary>
    /// 单位联系人
    /// </summary>        
    public string CompanyContact { get; set; }

    /// <summary>
    /// 电话
    /// </summary>        
    public string Tel { get; set; }

    /// <summary>
    /// 传真
    /// </summary>        
    public string Fax { get; set; }

    /// <summary>
    /// 地址
    /// </summary>        
    public string Address { get; set; }

    /// <summary>
    /// 邮编
    /// </summary>        
    public string PostCode { get; set; }

    /// <summary>
    /// 开户银行
    /// </summary>        
    public string OpenBank { get; set; }

    /// <summary>
    /// 银行账号
    /// </summary>        
    public string BankAccount { get; set; }

    /// <summary>
    /// 拼音码
    /// </summary>        
    public string PinYinCode { get; set; }

    /// <summary>
    /// 五笔码
    /// </summary>        
    public string WuBiCode { get; set; }

    /// <summary>
    /// 备注
    /// </summary>        
    public string Note { get; set; }

    /// <summary>
    /// 显示顺序
    /// </summary>
    public int SortIndex { get; set; }
    /// <summary>
    /// 院区编码
    /// </summary>        
    public string HospCode { get; set; }

    /// <summary>
    /// 社会信用代码
    /// </summary>
    public string CreditCode { get; set; }

    /// <summary>
    /// 职工人数
    /// </summary>
    public int EmployeesNumber { get; set; }

    /// <summary>
    /// 接触危害因素职工人数
    /// </summary>
    public int ContactHazardEmployeesNumber { get; set; }

    /// <summary>
    /// 行业分类代码
    /// </summary>
    public string IndustryCode { get; set; }

    /// <summary>
    /// 经济类型代码
    /// </summary>
    public string EconomicCode { get; set; }

    /// <summary>
    /// 企业规模代码
    /// </summary>
    public OccupationalEnterpriseSize EnterpriseSizeCode { get; set; }

    /// <summary>
    /// 地区编码
    /// </summary>
    public string AddressCode { get; set; }

    /// <summary>
    /// 是否已上传职业病平台
    /// </summary>
    public bool IsOccupationUploaded { get; set; }

    #region Ext
    /// <summary>
    /// 父级单位名称
    /// </summary>        
    public string ParentCompanyName { get; set; }

    /// <summary>
    /// 单位次数
    /// </summary>
    public List<CodeCompanyTimesDto> CodeCompanyTimes { get; set; }

    public Dictionary<string,string> AddreassRelation { get; set; }

    /// <summary>
    /// 
    /// </summary>        
    public CodeOccupationalIndustry CodeOccupationalIndustry { get; set; }

    /// <summary>
    /// 
    /// </summary>        
    public CodeOccupationalEconomicType CodeOccupationalEconomicType { get; set; }
    #endregion
}
