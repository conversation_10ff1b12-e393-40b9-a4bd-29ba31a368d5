global using Autofac;
global using AutoMapper;
global using Microsoft.Extensions.DependencyInjection;
global using Newtonsoft.Json;
global using Peis.Model;
global using Peis.Model.DataBaseEntity;
global using Peis.Model.DataBaseEntity.External;
global using Peis.Model.DataBaseViewModel.External;
global using Peis.Model.DTO.External;
global using Peis.Model.DTO.External.BaiHui;
global using Peis.Model.DTO.External.DataMaintenances;
global using Peis.Model.DTO.External.Permission;
global using Peis.Repository.IRepository.Helper;
global using Peis.Repository.Repository.TransactionAttribute;
global using Peis.Utility.CustomException;
global using Peis.Utility.Helper;
global using Peis.Utility.SqlSugarExtension;
global using SqlSugar;
global using System;
global using System.Collections.Generic;
global using System.IO;
global using System.Linq;
global using System.Reflection;
global using System.Threading.Tasks;
global using System.Xml;
global using Peis.External.Platform.Service.Repository.IRepository;
global using Peis.External.Platform.Service.Service.IService;