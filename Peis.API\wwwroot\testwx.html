<html>
<head></head>
<body>
    <div class="jumbotron">
        <h1>接口测试</h1>
        <p class="lead">This is the interface of the app in the michole</p>
    </div>
    <div>
        <input type="text" id="username" value="m1" placeholder="m1" /><br />
        <input type="text" id="password" value="password" placeholder="micExternal" /><br />
        <br />
 
        <input type="button" value="测试" onclick="testwx()" />
    </div>

    <script src="https://libs.baidu.com/jquery/1.7.0/jquery.min.js"></script>
   
    <script type="text/javascript">
 
        var uri = 'https://localhost:5001';
 
        function testwx() {
            var data = {
                operCode:"sdsad"
            };

            $.ajax({
                type: "POST",
                url: uri + '/api/SystemSettingApi/GetMenuList',
			    contentType: "application/json",
                data: JSON.stringify(data),
                success: function (data) {
                    console.log(data);
                },
                error: function (e) {
                    console.log(e);
                }

            })
        }
    </script>
</body>

</html>