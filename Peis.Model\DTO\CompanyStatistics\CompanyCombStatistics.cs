﻿using System.Collections.Generic;

namespace Peis.Model.DTO.CompanyStatistics
{
    /// <summary>
    /// 单位组合统计
    /// </summary>
    public class CompanyCombStatistics
    {
        /// <summary>
        /// 总人数
        /// </summary>
        public int TotalCount { get; set; }

        /// <summary>
        /// 到检人数
        /// </summary>
        public int CheckCount { get; set; }

        /// <summary>
        /// 未检人数
        /// </summary>
        public int NoCheckCount { get; set; }

        /// <summary>
        /// 人数统计(表格数据)
        /// </summary>
        public List<CompanyCombAmount> CompanyCombAmount { get; set; }
    }

    /// <summary>
    /// 单位组合
    /// </summary>
    public class CompanyCombAmount
    {
        /// <summary>
        /// 组合名称
        /// </summary>
        public string CombName { get; set; }

        /// <summary>
        /// 组合单价
        /// </summary>
        public decimal Price { get; set; }

        /// <summary>
        /// 组合总费用
        /// </summary>
        public decimal Amount { get; set; }

        /// <summary>
        /// 总检查人次
        /// </summary>
        public int TotalTimes { get; set; }

        /// <summary>
        /// 已检查人次
        /// </summary>
        public int CheckedTimes { get; set; }

        /// <summary>
        /// 未检查人次
        /// </summary>
        public int NoCheckedTimes { get; set; }
    }
}
