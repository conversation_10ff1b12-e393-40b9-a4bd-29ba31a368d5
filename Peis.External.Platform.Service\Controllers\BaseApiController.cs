﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Peis.Model;
using Peis.Model.Other.Output;
using System.Text;

namespace Peis.External.Platform.Service.Controllers
{
    [Route("base")]
    [Authorize]
    [ServiceFilter(typeof(ShieldApiResourceFilter))]
    public class BaseApiController : ControllerBase
    {
        protected ResponsResult result = new ResponsResult();

        /// <summary>
        /// 设置session(使用前先注入sessionService)
        /// </summary>
        /// <param name="key"></param>
        /// <param name="obj"></param>
        /// <returns></returns>
        protected bool SetSession<T>(string key, T obj)
        {
            try
            {
                string jsonstr = JsonConvert.SerializeObject(obj);
                byte[] byteArray = Encoding.Default.GetBytes(jsonstr);
                HttpContext.Session.Set(key, byteArray);
                return true;
            }
            catch (Exception)
            {
                return false;
            }
        }

        /// <summary>
        /// 获取session (使用前先注入sessionService)
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="key"></param>
        /// <returns></returns>
        protected T GetSession<T>(string key)
        {
            try
            {
                HttpContext.Session.TryGetValue(key, out byte[] byteArray);

                if (byteArray == null)
                {
                    return default;
                }
                string jsonstr = Encoding.Default.GetString(byteArray, 0, byteArray.Length);
                return JsonConvert.DeserializeObject<T>(jsonstr);
            }
            catch (Exception)
            {
                return default;
            }
        }
    }
}
