﻿using Peis.Model.Other.PeEnum;
using System;
using System.Text.Json.Serialization;

namespace Peis.Model.DTO.DataQuery
{
    /// <summary>
    /// 患者基本信息
    /// </summary>
    public class PatientBasicInfo
    {
        /// <summary>
        /// 相片地址
        /// </summary>
        public string PhotoUrl { get; set; }

        /// <summary>
        /// 体检号
        /// </summary>
        public string RegNo { get; set; }

        /// <summary>
        /// 档案号
        /// </summary>
        public string PatCode { get; set; }

        /// <summary>
        /// 登记次数
        /// </summary>
        public int RegisterTimes { get; set; }

        /// <summary>
        /// 姓名
        /// </summary>
        public string Name { get; set; }

        /// <summary>
        /// 性别（1男 2女）
        /// </summary>
        public Sex? Sex { get; set; }

        /// <summary>
        /// 体检分类
        /// </summary>
        public PeCls PeCls { get; set; }

        /// <summary>
        /// 出生日期
        /// </summary>
        public DateTime? Birthday { get; set; }

        /// <summary>
        /// 年龄
        /// </summary>
        public int? Age { get; set; }

        ///// <summary>
        ///// 年龄单位
        ///// </summary>
        //public AgeUnit? AgeUnit { get; set; }

        /// <summary>
        /// 籍贯
        /// </summary>
        public string NativePlace { get; set; }

        /// <summary>
        /// 证件类型
        /// </summary>
        public string CardType { get; set; }

        /// <summary>
        /// 证件号
        /// </summary>
        public string CardNo { get; set; }

        /// <summary>
        /// 婚姻状态
        /// </summary>
        public MarryStatus? MarryStatus { get; set; }

        /// <summary>
        /// 联系电话
        /// </summary>
        public string Tel { get; set; }

        /// <summary>
        /// 职业
        /// </summary>
        public string JobName { get; set; }

        /// <summary>
        /// 职业历史
        /// </summary>
        public string JobHistory { get; set; }

        /// <summary>
        /// 地址
        /// </summary>
        public string Address { get; set; }

        /// <summary>
        /// 病史
        /// </summary>
        public string MedicalHistory { get; set; }

        ///// <summary>
        ///// 体检内容
        ///// </summary>
        //public string 体检内容 { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        public string Note { get; set; }

        /// <summary>
        /// 单位
        /// </summary>
        public string CompanyName { get; set; }

        /// <summary>
        /// 单位部门
        /// </summary>
        public string DeptName { get; set; }

        ///// <summary>
        ///// 工号
        ///// </summary>
        //public string 工号 { get; set; }

        ///// <summary>
        ///// 工作岗位状态（岗前 岗中 岗后）
        ///// </summary>
        //public JobStatus? JobStatus { get; set; }

        /// <summary>
        /// 团体联系人
        /// </summary>
        public string CompanyContact { get; set; }

        /// <summary>
        /// 介绍人
        /// </summary>
        public string Introducer { get; set; }

        ///// <summary>
        ///// 开单医生
        ///// </summary>
        //public string 开单医生 { get; set; }

        /// <summary>
        /// 操作员编码
        /// </summary>
        public string OperatorCode { get; set; }

        /// <summary>
        /// 复查号
        /// </summary>
        public string RecheckNo { get; set; }

        /// <summary>
        /// 体检费
        /// </summary>

        public decimal PePrice { get; set; }

        /// <summary>
        /// 加项费
        /// </summary>

        public decimal AddItemPrice { get; set; }

        /// <summary>
        /// 登记时间
        /// </summary>
        public DateTime RegisterTime { get; set; }

        /// <summary>
        /// 体检时间
        /// </summary>
        public DateTime? ActiveTime { get; set; }

        /// <summary>
        /// 发票名称
        /// </summary>
        public string InvoiceNo { get; set; }

        /// <summary>
        /// 领导标识
        /// </summary>
        public bool? IsLeader { get; set; }

        /// <summary>
        /// 复查标识
        /// </summary>
        public bool? IsRecheck { get; set; }

        /// <summary>
        /// VIP标识
        /// </summary>
        public bool? IsVIP { get; set; }

        /// <summary>
        /// 体质辨识标识
        /// </summary>
        public bool? IsConstitution { get; set; }

        /// <summary>
        /// 团检标识
        /// </summary>
        [JsonIgnore]
        public bool IsCompanyCheck { get; set; }
    }
}
