﻿using Microsoft.AspNetCore.Mvc;
using Peis.Model.DTO.FinancialStatistics;
using Peis.Model.Other.Input;
using Peis.Model.Other.Input.FinancialStatistics;
using Peis.Service.IService;
using System.Collections.Generic;

namespace Peis.API.Controllers.Statistics
{
    /// <summary>
    /// 财务统计报表
    /// </summary>
    [Route("api/[controller]")]
    [ApiController]
    public class FinancialStatisticsController : BaseApiController
    {
        private readonly IFinancialStatisticsService _financialService;

        public FinancialStatisticsController(IFinancialStatisticsService financialService)
        {
            _financialService = financialService;
        }

        /// <summary>
        /// 缴款报表统计
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        [HttpPost("PaymentReportStatistics")]
        [ProducesResponseType(typeof(PaymentReportStatistics), 200)]
        public IActionResult PaymentReportStatistics([FromBody] FeeClsAmountsStatisticsQuery query)
        {
            PaymentReportStatistics report = new();
            string msg = string.Empty;
            result.Success = _financialService.PaymentReportStatistics(query, ref report, ref msg);
            result.ReturnMsg = msg;
            result.ReturnData = report;
            return Ok(result);
        }

       /// <summary>
       /// 财务分类统计
       /// </summary>
       /// <param name="query"></param>
       /// <returns></returns>
       [HttpPost("FeeClsAmountsStatistics")]
        [ProducesResponseType(typeof(FeeClsAmountsStatisticsData), 200)]
        public IActionResult FeeClsAmountsStatistics([FromQuery] FeeClsAmountsStatisticsQuery query)
        {
            string msg = string.Empty;
            var data = new List<FeeClsAmountsStatisticsData>();
            result.Success = _financialService.GetFeeClsAmountsStatistics(query, ref data, ref msg);
            result.ReturnData = data;
            result.ReturnMsg = msg;
            return Ok(result);
        }

        /// <summary>
        /// 单位个人记账报表
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        [HttpPost("CompanyPersonAccountingReport")]
        [ProducesResponseType(typeof(CompanyPersonAccounting), 200)]
        public IActionResult CompanyPersonAccountingReport([FromBody] CompanyPersonAccountQuery query)
        {
            CompanyPersonAccounting companyData = new()
            {
                PersonAccount = new List<PersonAccount>()
            };

            string msg = string.Empty;
            result.Success = _financialService.CompanyPersonAccountingReport(query, ref companyData, ref msg);
            result.ReturnData = companyData;
            result.ReturnMsg = msg;
            return Ok(result);
        }

        /// <summary>
        /// 团体结算(按实际项目)
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        [HttpPost("CompanySettlementByCombReport")]
        [ProducesResponseType(typeof(CompanySettlementByComb),200)]
        public IActionResult CompanySettlementByCombReport([FromBody] CompanySettlementQuery query)
        {
            CompanySettlementByComb companyData = new();
            string msg = string.Empty;
            result.Success = _financialService.CompanySettlementByCombReport(query, ref companyData, ref msg);
            result.ReturnMsg = msg;
            result.ReturnData = companyData;
            return Ok(result);
        }

        /// <summary>
        /// 团体结算(按费用分类)
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        [HttpPost("CompanySettlementByFeeClsReport")]
        [ProducesResponseType(typeof(CompanySettlementByFeeCls), 200)]
        public IActionResult CompanySettlementByFeeClsReport([FromBody] CompanySettlementQuery query)
        {
            CompanySettlementByFeeCls companyData = new();
            string msg = string.Empty;
            result.Success = _financialService.CompanySettlementByFeeClsReport(query, ref companyData, ref msg);
            result.ReturnMsg = msg;
            result.ReturnData = companyData;
            return Ok(result);
        }

        /// <summary>
        /// 团体个人明细结算
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        [HttpPost("CompanySettlementByPersonDetails")]
        [ProducesResponseType(typeof(CompanySettlementByPersonDetails), 200)]
        public IActionResult CompanySettlementByPersonDetails([FromBody] CompanySettlementQuery query)
        {
            CompanySettlementByPersonDetails companyData = new();
            string msg = string.Empty;
            result.Success = _financialService.CompanySettlementByPersonDetails(query, ref companyData, ref msg);
            result.ReturnMsg = msg;
            result.ReturnData = companyData;
            return Ok(result);
        }

        /// <summary>
        /// 团体套餐结算 
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        [HttpPost("CompanySettlementByClusterReport")]
        [ProducesResponseType(typeof(CompanySettlementByCluster), 200)]
        public IActionResult CompanySettlementByClusterReport([FromBody] CompanySettlementQuery query)
        {
            CompanySettlementByCluster companyData = new();
            string msg = string.Empty;
            result.Success = _financialService.CompanySettlementByClusterReport(query, ref companyData, ref msg);
            result.ReturnData = companyData;
            result.ReturnMsg = msg;
            return Ok(result);
        }

        /// <summary>
        /// 团体明细费用报表
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        [HttpPost("CompanySettlementByFeeDetailReport")]
        [ProducesResponseType(typeof(CompanySettlementByFeeDetails), 200)]
        public IActionResult CompanySettlementByFeeDetailReport([FromBody] CompanySettlementQuery query)
        {
            CompanySettlementByFeeDetails companyData = new();
            string msg = string.Empty;
            result.Success = _financialService.CompanySettlementByFeeDetailsReport(query, ref companyData, ref msg);
            result.ReturnData = companyData;
            result.ReturnMsg = msg;
            return Ok(result);
        }
    }
}
