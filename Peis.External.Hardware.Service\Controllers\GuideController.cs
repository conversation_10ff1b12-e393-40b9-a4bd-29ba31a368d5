﻿namespace Peis.External.Hardware.Service.Controllers;

/// <summary>
/// 导检-外部
/// </summary>
[ApiController]
[Route("api/[controller]/[action]")]
public class GuideController : <PERSON><PERSON><PERSON><PERSON><PERSON>ontroller
{
    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="registerService"></param>
    /// <param name="httpClient"></param>
    /// <param name="systemParameterService"></param>
    public GuideController(IRegisterNewService registerService, IHttpClientHelper httpClient, ISystemParameterService systemParameterService) : base(registerService, httpClient, systemParameterService)
    {

    }
}
