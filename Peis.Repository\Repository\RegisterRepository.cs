﻿using Peis.Model.DataBaseEntity;
using Peis.Model.DataBaseEntity.Occupation;
using Peis.Model.DTO;
using Peis.Model.Other.PeEnum;
using Peis.Model.TableFilter;
using Peis.Repository.IRepository;
using Peis.Repository.IRepository.Helper;
using Peis.Utility.Helper;
using SqlSugar;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;

namespace Peis.Repository.Repository
{
    public class RegisterRepository : IRegisterRepository
    {
        private readonly ISqlSugarClient _db;
        private readonly ISplitTableRepository<PeRegisterComb> _peRegisterCombRepository;
        private readonly ISplitTable _splitTable;

        public RegisterRepository(
            ISqlSugarClient db,
            ISplitTableRepository<PeRegisterComb> peRegisterCombRepository,
            ISplitTable splitTable)
        {
            _db = db;
            _peRegisterCombRepository = peRegisterCombRepository;
            _splitTable = splitTable;
        }

        #region 重构

        public PeRegister GetByNo(string regNo)
        {
            return _db.Queryable<PeRegister>()
                .Where(x => x.RegNo == regNo)
                .First();
        }

        public bool HasNotYetSummarized(string patCode, PeCls cls)
        {
            return _db.Queryable<PeRegister>()
                .Where(r => r.PatCode == patCode && r.PeCls == cls)
                .Where(r => r.PeStatus == PeStatus.未检查 || r.PeStatus == PeStatus.正在检查)
                .Where(r => r.IsActive)
                .Any();
        }

        #endregion

        /// <summary>
        /// 获取订单的资料
        /// </summary>
        /// <returns></returns>
        public ISugarQueryable<PeRegister> ReadRegister()
        {
            return _db.Queryable<PeRegister>();
        }

        /// <summary>
        /// 获取订单的资料（不按院区过滤）
        /// </summary>
        /// <returns></returns>
        public ISugarQueryable<PeRegister> ReadRegisterNoHosp()
        {
            return _db.Queryable<PeRegister>().ClearFilter<IHospCodeFilter>();
        }

        /// <summary>
        /// 获取订单的资料
        /// </summary>
        /// <param name="regNo"></param>
        /// <returns></returns>
        public ISugarQueryable<PeRegister> ReadRegister(string regNo)
        {
            return _db.Queryable<PeRegister>()
                .Where(x => x.RegNo == regNo);
        }

        /// <summary>
        /// 获取订单的资料（不按院区过滤）
        /// </summary>
        /// <param name="regNo"></param>
        /// <returns></returns>
        public ISugarQueryable<PeRegister> ReadRegisterNoHosp(string regNo)
        {
            return _db.Queryable<PeRegister>().ClearFilter<IHospCodeFilter>()
                .Where(x => x.RegNo == regNo);
        }

        /// <summary>
        /// 获取回收订单的资料
        /// </summary>
        /// <param name="regNo"></param>
        /// <returns></returns>
        public ISugarQueryable<DeletePeRegister> ReadDeleteRegister()
        {
            return _db.Queryable<DeletePeRegister>();
        }

        /// <summary>
        /// 获取职业病信息
        /// </summary>
        /// <returns></returns>
        public ISugarQueryable<PeRegisterOccupation> ReadRegisterOccupation()
        {
            return _db.Queryable<PeRegisterOccupation>();
        }

        /// <summary>
        /// 获取职业病危害因素
        /// </summary>
        /// <returns></returns>
        public ISugarQueryable<PeRegisterOccupationHazard> ReadRegisterOccupationHazard()
        {
            return _db.Queryable<PeRegisterOccupationHazard>();
        }

        /// <summary>
        /// 获取订单资料
        /// </summary>
        /// <param name="companyCode"></param>
        /// <param name="companyTimes"></param>
        /// <param name="clusterCode"></param>
        /// <returns></returns>
        public ISugarQueryable<PeRegister> ReadRegister(string companyCode, int companyTimes, string clusterCode)
        {
            return _db
                .Queryable<PeRegister>()
                .Where(x =>
                    x.CompanyCode == companyCode &&
                    x.CompanyTimes == companyTimes &&
                    SqlFunc.Subqueryable<PeRegisterCluster>()
                        .Where(clus => clus.RegNo == x.RegNo && clus.ClusCode == clusterCode)
                        .Any()
                );
        }

        /// <summary>
        /// 根据身份证号获取登记记录
        /// </summary>
        /// <param name="cardNos">身份证号码</param>
        /// <returns></returns>
        public ISugarQueryable<PeRegister> ReadRegister(params string[] cardNos)
        {
            cardNos = cardNos.Distinct().ToArray();
            return _db.Queryable<PeRegister>()
                .InnerJoin(_db.Reportable(cardNos).ToQueryable<string>(), (x, y) => x.CardNo == y.ColumnName)
                .Select(x => x);
        }

        /// <summary>
        /// 获取订单的资料(聚合查询)
        /// </summary>
        /// <typeparam name="TResult"></typeparam>
        /// <param name="queryValue"></param>
        /// <param name="queryPatNo"></param>
        /// <param name="queryRegNo"></param>
        /// <param name="queryName"></param>
        /// <param name="queryCardNo"></param>
        /// <param name="queryTel"></param>
        /// <param name="selectColumns"></param>
        /// <returns></returns>
        public ISugarQueryable<TResult> ReadRegister<TResult>(
            string queryValue,
            bool queryPatNo,
            bool queryRegNo,
            bool queryName,
            bool queryCardNo,
            bool queryTel,
            Expression<Func<PeRegister, TResult>> selectColumns) where TResult : class, new()
        {
            var query = _db.Queryable<PeRegister>();

            var queryables = new List<ISugarQueryable<TResult>>();

            if (queryPatNo)
                queryables.Add(query.Clone().Where(x => x.PatCode == queryValue).Select(selectColumns));

            if (queryRegNo)
                queryables.Add(query.Clone().Where(x => x.RegNo == queryValue).Select(selectColumns));

            if (queryName)
                queryables.Add(query.Clone().Where(x => SqlFunc.Contains(x.Name, queryValue)).Select(selectColumns));

            if (queryCardNo)
                queryables.Add(query.Clone().Where(x => x.CardNo == queryValue).Select(selectColumns));

            if (queryTel)
                queryables.Add(query.Clone().Where(x => x.Tel == queryValue).Select(selectColumns));

            return queryables.Count switch
            {
                0 => throw new ArgumentNullException("参数至少有一个"),
                1 => queryables[0],
                _ => _db.UnionAll(queryables),
            };
        }

        /// <summary>
        /// 获取订单的资料(聚合查询)
        /// </summary>
        /// <typeparam name="TResult"></typeparam>
        /// <param name="isCompanyCheck"></param>
        /// <param name="queryValue"></param>
        /// <param name="queryPatNo"></param>
        /// <param name="queryRegNo"></param>
        /// <param name="queryName"></param>
        /// <param name="queryCardNo"></param>
        /// <param name="queryTel"></param>
        /// <param name="selectColumns"></param>
        /// <returns></returns>
        public ISugarQueryable<TResult> ReadRegister<TResult>(
            bool isCompanyCheck,
            string queryValue,
            bool queryPatNo,
            bool queryRegNo,
            bool queryName,
            bool queryCardNo,
            bool queryTel,
            Expression<Func<PeRegister, TResult>> selectColumns) where TResult : class, new()
        {
            var query = _db.Queryable<PeRegister>().Where(x => x.IsCompanyCheck == isCompanyCheck);

            var queryables = new List<ISugarQueryable<TResult>>();

            if (queryPatNo)
                queryables.Add(query.Clone().Where(x => x.PatCode == queryValue).Select(selectColumns));

            if (queryRegNo)
                queryables.Add(query.Clone().Where(x => x.RegNo == queryValue).Select(selectColumns));

            if (queryName)
                queryables.Add(query.Clone().Where(x => SqlFunc.Contains(x.Name, queryValue)).Select(selectColumns));

            if (queryCardNo)
                queryables.Add(query.Clone().Where(x => x.CardNo == queryValue).Select(selectColumns));

            if (queryTel)
                queryables.Add(query.Clone().Where(x => x.Tel == queryValue).Select(selectColumns));

            return queryables.Count switch
            {
                0 => throw new ArgumentNullException("参数至少有一个"),
                1 => queryables[0],
                _ => _db.UnionAll(queryables),
            };
        }

        /// <summary>
        /// 获取登记时间
        /// </summary>
        /// <param name="regNo"></param>
        /// <returns></returns>
        public DateTime? ReadRegisterTime(string regNo)
        {
            var regQuery = ReadRegister(regNo)
                .Select(x => new
                {
                    x.RegNo,
                    x.RegisterTime
                })
                .First();

            return regQuery?.RegisterTime;
        }

        /// <summary>
        /// 获取登记时间
        /// </summary>
        /// <param name="regNos"></param>
        /// <param name="startTime"></param>
        /// <param name="endTime"></param>
        /// <returns></returns>
        public bool ReadRegisterTime(string[] regNos, out DateTime startTime, out DateTime endTime)
        {
            _splitTable.GetRegisterTime(out startTime, out endTime, regNos);
            return true;
        }

        /// <summary>
        /// 获取登记时间区间
        /// </summary>
        /// <param name="regNo">体检号</param>
        /// <returns>RegisterDateRange</returns>
        public RegisterDateRange GetRegisterDateRange(string regNo)
        {
            _splitTable.GetRegisterTime(out DateTime minTime, out DateTime maxTime, regNo);
            return new RegisterDateRange
            {
                MinRegTime = minTime,
                MaxRegTime = maxTime
            };
        }

        /// <summary>
        /// 根据激活日期范围获取登记日期范围
        /// </summary>
        /// <param name="activeStartDate"></param>
        /// <param name="activeEndDate"></param>
        /// <returns></returns>
        public RegisterDateRange ReadRegDateRangeByActiveTime(DateTime activeStartDate, DateTime activeEndDate)
        {
            activeStartDate = activeStartDate.Date;
            activeEndDate = activeEndDate.Date.Add(new TimeSpan(23, 59, 59));

            _splitTable.GetRegisterTime(activeStartDate, activeEndDate, out DateTime minTime, out DateTime maxTime);
            return new RegisterDateRange
            {
                MinRegTime = minTime,
                MaxRegTime = maxTime
            };
        }

        /// <summary>
        /// 获取订单数据及套餐
        /// </summary>
        /// <returns></returns>
        public ISugarQueryable<PeRegister, PeRegisterCluster> ReadRegisterAndMainCluster(string regNo = "")
        {
            return _db.Queryable<PeRegister>()
                .LeftJoin<PeRegisterCluster>((reg, clus) => reg.RegNo == clus.RegNo && clus.IsMain)
                .WhereIF(!string.IsNullOrEmpty(regNo), (reg, clus) => reg.RegNo == regNo);
        }

        /// <summary>
        /// 获取订单的资料
        /// </summary>
        /// <returns></returns>
        public ISugarQueryable<PeRegister, PeRegisterCluster> ReadRegisterOrder()
        {
            return _db.Queryable<PeRegister>()
                .LeftJoin<PeRegisterCluster>((reg, clus) => reg.RegNo == clus.RegNo);
        }

        /// <summary>
        /// 获取订单的套餐
        /// </summary>
        /// <returns></returns>
        public ISugarQueryable<PeRegisterCluster> ReadRegisterClusters()
        {
            return _db.Queryable<PeRegisterCluster>();
        }

        /// <summary>
        /// 获取订单的套餐
        /// </summary>
        /// <param name="regNo"></param>
        public ISugarQueryable<PeRegisterCluster> ReadRegisterClusters(string regNo)
        {
            return _db.Queryable<PeRegisterCluster>().Where(reg => reg.RegNo == regNo);
        }

        /// <summary>
        /// 获取订单的组合
        /// </summary>
        /// <param name="regNo"></param>
        /// <returns></returns>
        public ISugarQueryable<PeRegisterComb> ReadRegisterCombs(string regNo)
        {
            var peRegisterComb = _splitTable.GetSplitSugarQueryable<PeRegisterComb>(regNo);

            return _db.Queryable(peRegisterComb).Where(x => x.RegNo == regNo);
        }

        /// <summary>
        /// 获取订单的组合
        /// </summary>
        /// <param name="regNo"></param>
        /// <param name="regItemIds"></param>
        /// <returns></returns>
        public ISugarQueryable<PeRegisterComb> ReadRegisterCombs(string regNo, params long[] regItemIds)
        {
            var peRegisterComb = _splitTable.GetSplitSugarQueryable<PeRegisterComb>(regNo);

            return _db.Queryable(peRegisterComb)
               .Where(x => x.RegNo == regNo)
               .Where(x => SqlFunc.ContainsArray(regItemIds, x.Id));
        }

        /// <summary>
        /// 获取订单的组合
        /// </summary>
        /// <param name="regNo"></param>
        /// <param name="combCodes"></param>
        /// <returns></returns>
        public ISugarQueryable<PeRegisterComb> ReadRegisterCombs(string regNo, params string[] combCodes)
        {
            var peRegisterComb = _splitTable.GetSplitSugarQueryable<PeRegisterComb>(regNo);

            return _db.Queryable(peRegisterComb)
               .Where(x => x.RegNo == regNo)
               .WhereIF(!combCodes.IsNullOrEmpty(), x => SqlFunc.ContainsArray(combCodes, x.CombCode));
        }

        /// <summary>
        /// 获取订单的组合（收费状态过滤）
        /// </summary>
        /// <param name="regNo"></param>
        /// <param name="payStatus"></param>
        /// <returns></returns> 
        public ISugarQueryable<PeRegisterComb> ReadRegisterCombs(string regNo, PayStatus payStatus, params long[] regItemIds)
        {
            DateTime regTime = ReadRegisterTime(regNo) ?? DateTime.Now;
            string tableName = _db.SplitHelper<PeRegisterComb>().GetTableName(regTime);

            return _db.Queryable<PeRegisterComb>().AS(tableName)
               .Where(x => x.RegNo == regNo)
               .Where(x => x.PayStatus == payStatus)
               .WhereIF(regItemIds.Length > 0, x => SqlFunc.ContainsArray(regItemIds, x.Id));
        }

        /// <summary>
        /// 获取订单的组合（获取自费的组合）
        /// </summary>
        /// <param name="regNo"></param>
        /// <param name="payStatus"></param>
        /// <returns></returns> 
        public ISugarQueryable<PeRegisterComb> ReadRegisterCombs(string regNo, PayStatus payStatus)
        {
            DateTime regTime = ReadRegisterTime(regNo) ?? DateTime.Now;
            string tableName = _db.SplitHelper<PeRegisterComb>().GetTableName(regTime);

            return _db.Queryable<PeRegisterComb>().AS(tableName)
               .Where(x => x.RegNo == regNo)
               .Where(x => x.PayStatus == payStatus && x.IsPayBySelf);
        }

        /// <summary>
        /// 获取订单的组合（收费状态、组合码过滤）
        /// </summary>
        /// <param name="regNo"></param>
        /// <param name="comCodes"></param>
        /// <param name="payStatus"></param>
        /// <returns></returns>
        public ISugarQueryable<PeRegisterComb> ReadRegisterCombs(string regNo, string[] comCodes, PayStatus payStatus)
        {
            DateTime regTime = ReadRegisterTime(regNo) ?? DateTime.Now;
            string tableName = _db.SplitHelper<PeRegisterComb>().GetTableName(regTime);

            return _db.Queryable<PeRegisterComb>().AS(tableName)
               .Where(x => x.RegNo == regNo && x.PayStatus == payStatus && SqlFunc.ContainsArray(comCodes, x.CombCode));
        }

        /// <summary>
        /// 获取订单的组合
        /// </summary>
        /// <param name="regNos"></param>
        /// <returns></returns>
        public ISugarQueryable<PeRegisterComb> ReadRegisterCombs(string[] regNos)
        {
            return _splitTable.GetSplitSugarQueryable<PeRegisterComb>(regNos)
                .Where(x => SqlFunc.ContainsArray(regNos, x.RegNo));
        }

        /// <summary>
        /// 获取订单的组合
        /// </summary>
        /// <param name="start"></param>
        /// <param name="end"></param>
        /// <returns></returns>
        public ISugarQueryable<PeRegisterComb> ReadRegisterCombs(DateTime start, DateTime end)
        {
            return _splitTable.GetTableOrDefault<PeRegisterComb>(start, end);
        }

        /// <summary>
        /// 获取订单的组合
        /// </summary>
        /// <param name="start"></param>
        /// <param name="end"></param>
        /// <param name="whereExpression"></param>
        /// <returns></returns>
        public ISugarQueryable<PeRegisterComb> ReadRegisterCombs(DateTime start, DateTime end, Expression<Func<PeRegisterComb, bool>> whereExpression)
        {
            return _splitTable.GetTableOrDefault<PeRegisterComb>(start, end, whereExpression);
        }

        public ISugarQueryable<PeRegisterComb, SingleColumnEntity<string>> ReadRegisterCombs2(string[] regNos)
        {
            return _splitTable.GetSplitSugarQueryable<PeRegisterComb>(regNos)
                .InnerJoin(_db.Reportable(regNos).ToQueryable<string>(), (comb, arr) => comb.RegNo == arr.ColumnName);
        }

        /// <summary>
        /// 获取订单的组合费用分类
        /// </summary>
        /// <param name="regNos"></param>
        /// <returns></returns>
        public ISugarQueryable<CodeItemComb, PeRegisterComb> ReadRegisterCombsFeeCls(string[] regNos)
        {
            ReadRegisterTime(regNos, out DateTime startTime, out DateTime endTime);
            var peRegisterComb = _splitTable.GetTableOrDefault<PeRegisterComb>(startTime, endTime);

            return _db.Queryable<CodeItemComb>()
                .InnerJoin(peRegisterComb, (comb, regComb) => regComb.CombCode == comb.CombCode);
        }


        /// <summary>
        /// 更新试管组合的归属关系
        /// </summary>
        /// <param name="testTubes"></param>
        public void UpdateTestTubeBeFrom(List<PeRegisterComb> testTubes)
        {
            _db.Updateable(testTubes).UpdateColumns(x => x.BeFrom).SplitTable().ExecuteCommand();
        }

        /// <summary>
        /// 更新组合
        /// </summary>
        /// <param name="regNo"></param>
        /// <returns></returns>
        public IUpdateable<PeRegisterComb> UpdateRegisterComb(string regNo)
        {
            var tableName = _db.SplitHelper<PeRegisterComb>()
                .GetTableName(ReadRegisterTime(regNo) ?? DateTime.Now);

            return _db.Updateable<PeRegisterComb>().AS(tableName);
        }

        /// <summary>
        /// 更新组合
        /// </summary>
        /// <param name="combs"></param>
        /// <param name="updateColumns"></param>
        public void UpdateColumnsRegisterComb(PeRegisterComb[] combs, Expression<Func<PeRegisterComb, object>> updateColumns)
        {
            _peRegisterCombRepository.SplitTableUpdate(combs, updateColumns);
        }

        /// <summary>
        /// 删除登记组合项目
        /// </summary>
        /// <param name="startTime"></param>
        /// <param name="endTime"></param>
        /// <param name="regNos"></param>
        public void DeleteRegisterComb(DateTime startTime, DateTime endTime, string[] regNos)
        {
            _peRegisterCombRepository
                .SplitTableDelete(startTime, endTime, x => SqlFunc.ContainsArray(regNos, x.RegNo));
        }

        /// <summary>
        /// 添加登记组合项目
        /// </summary>
        /// <param name="peRegisterCombs"></param>
        public void InsertRegisterComb(List<PeRegisterComb> peRegisterCombs)
        {
            _peRegisterCombRepository.SplitTableInsert(peRegisterCombs);
        }

        /// <summary>
        /// 获取订单数据
        /// </summary>
        /// <param name="registerTime"></param>
        /// <returns></returns>
        public ISugarQueryable<PeRegister, PeRegisterComb> ReadRegisterOrder(DateTime registerTime)
        {
            var tableName = _db.SplitHelper<PeRegisterComb>().GetTableName(registerTime);//根据时间获取表名
            var query = _db.Queryable<PeRegisterComb>().AS(tableName);

            return _db.Queryable<PeRegister>()
                .InnerJoin(query, (reg, comb) => comb.RegNo == reg.RegNo);
        }

        /// <summary>
        /// 获取订单数据
        /// </summary>
        /// <param name="beginDate"></param>
        /// <param name="endDate"></param>
        /// <returns></returns>
        public ISugarQueryable<PeRegister, PeRegisterComb> ReadRegisterOrder(DateTime beginDate, DateTime endDate)
        {
            var peRegisterComb = _splitTable.GetTableOrDefault<PeRegisterComb>(beginDate, endDate);

            return _db.Queryable<PeRegister>()
               .InnerJoin(peRegisterComb, (reg, regComb) => regComb.RegNo == reg.RegNo)
               .Where((reg, regComb) => regComb.PayStatus == PayStatus.收费 || (reg.IsCompanyCheck && !regComb.IsPayBySelf)
               );
        }

        /// <summary>
        /// 获取登记组合明细
        /// </summary>
        /// <param name="startTime">登记时间-开始</param>
        /// <param name="endTime">登记时间-结束</param>
        /// <returns>ISugarQueryable</returns>
        public ISugarQueryable<PeRegister, PeRegisterComb> QueryPeRegCombs(DateTime startTime, DateTime endTime)
        {
            var peRegisterComb =
                _splitTable.GetTableOrDefault<PeRegisterComb>(startTime, endTime);

            return _db
               .Queryable<PeRegister>()
               .InnerJoin(peRegisterComb, (a, b) => a.RegNo.Equals(b.RegNo));
        }

        /// <summary>
        /// 读取组合与费用分类明细
        /// </summary>
        /// <param name="regNo"></param>
        /// <returns></returns>
        public ISugarQueryable<PeRegisterComb, CodeItemComb, CodeFeeCls> ReadCombAndFeeClsDetails(string regNo)
        {
            DateTime regTime = ReadRegisterTime(regNo) ?? DateTime.Now;
            string tableName = _db.SplitHelper<PeRegisterComb>().GetTableName(regTime);

            return _db.Queryable<PeRegisterComb>().AS(tableName)
                .LeftJoin<CodeItemComb>((regComb, itemComb) => regComb.CombCode == itemComb.CombCode)
                .LeftJoin<CodeFeeCls>((regComb, itemComb, feeCls) => itemComb.FeeBasicCls == feeCls.FeeClsCode)
                .Where(regComb => regComb.RegNo == regNo);
        }

        /// <summary>
        /// 读取登记套餐组合数据
        /// </summary>
        /// <param name="beginDate"></param>
        /// <param name="endDate"></param>
        /// <returns></returns>
        public ISugarQueryable<PeRegister, PeRegisterCluster, MapClusterComb, PeRegisterComb> ReadClusterAndComb(DateTime beginDate, DateTime endDate)
        {
            var peRegisterComb = _splitTable.GetTableOrDefault<PeRegisterComb>(beginDate, endDate);

            return _db.Queryable<PeRegister>()
                .InnerJoin<PeRegisterCluster>((reg, clus) => clus.RegNo == reg.RegNo)
                .InnerJoin<MapClusterComb>((reg, clus, mapClusComb) => mapClusComb.ClusCode == clus.ClusCode)
                .InnerJoin(peRegisterComb, (reg, clus, mapClusComb, regComb) => regComb.RegNo == reg.RegNo && regComb.CombCode == mapClusComb.CombCode);
        }

        /// <summary>
        /// 体检人员费用列表使用
        /// </summary>
        /// <param name="beginDate"></param>
        /// <param name="endDate"></param>
        /// <returns></returns>
        public ISugarQueryable<PeRegister, PeRegisterComb, PeRegisterCluster, MapClusterComb> ReadPersonnelFeeList(DateTime beginDate, DateTime endDate)
        {
            var peRegisterComb = _splitTable.GetTableOrDefault<PeRegisterComb>(beginDate, endDate);

            return _db.Queryable<PeRegister>()
                .InnerJoin(peRegisterComb, (reg, regComb) => regComb.RegNo == reg.RegNo)
                .LeftJoin<PeRegisterCluster>((reg, regComb, clus) => clus.RegNo == reg.RegNo)
                .LeftJoin<MapClusterComb>((reg, regComb, clus, mapClusComb) => mapClusComb.ClusCode == clus.ClusCode && regComb.CombCode == mapClusComb.CombCode);
        }

        /// <summary>
        /// 获取检查项目的组合明细信息（非材料、治疗费等）
        /// </summary>
        /// <param name="regNo"></param>
        /// <param name="reportShow"></param>
        /// <returns></returns>
        public ISugarQueryable<PeRegisterComb, CodeItemComb, CodeBarcodeType> ReadExamTypeCombDetail(string regNo, bool reportShow = true)
        {
            var regCombQuery = ReadRegisterCombs(regNo);

            return _db.Queryable(regCombQuery)
                .LeftJoin<CodeItemComb>((regComb, comb) => regComb.CombCode == comb.CombCode)
                .LeftJoin<CodeBarcodeType>((regComb, comb, barcode) => comb.BarCodeType == barcode.Barcode)
                .Where((regComb, comb, barcode) => regComb.RegNo == regNo)
                .Where((regComb, comb, barcode) => comb.ReportShow == reportShow);
        }

        /// <summary>
        /// 获取登记组合的项目分类
        /// </summary>
        /// <param name="regNo"></param>
        /// <returns></returns>
        public ISugarQueryable<PeRegisterComb, CodeItemCls> ReadRegCombsItemCls(string regNo)
        {
            var regCombQuery = ReadRegisterCombs(regNo);

            return regCombQuery
                .InnerJoin<CodeItemCls>((regComb, itemCls) => regComb.ClsCode == itemCls.ClsCode)
                .Where(regComb => regComb.RegNo == regNo);
        }

        /// <summary>
        /// 获取单位人员登记数据
        /// </summary>
        /// <returns></returns>
        public ISugarQueryable<PeRegister, PeRegisterCluster, CodeCompany> ReadCompanyRegData()
        {
            return _db.Queryable<PeRegister>()
                .InnerJoin<PeRegisterCluster>((reg, clus) => clus.RegNo == reg.RegNo)
                .InnerJoin<CodeCompany>((reg, clus, comp) => comp.CompanyCode == reg.CompanyCode);
        }

        public string ReadSuperiorRegNo(string regNo)
        {
            var recheckNoTmp = _db.Queryable<PeRegister>()
                .Where(x => x.RegNo == regNo).Select(x => x.RecheckNo).First();
            var recheckNo = string.Empty;
            while (!recheckNoTmp.IsNullOrEmpty())
            {
                recheckNo = recheckNoTmp;
                recheckNoTmp = _db.Queryable<PeRegister>()
                .Where(x => x.RegNo == recheckNoTmp).Select(x => x.RecheckNo).First();
            }
            return recheckNo;
        }
    }
}
