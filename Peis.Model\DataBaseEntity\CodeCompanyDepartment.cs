﻿using SqlSugar;

namespace Peis.Model.DataBaseEntity
{
    ///<summary>
    ///单位部门信息
    ///</summary>
    [SugarTable("CodeCompanyDepartment")]
    public class CodeCompanyDepartment
    {
        /// <summary>
        /// 部门代码
        /// </summary>        
        [SugarColumn(IsNullable = false, IsPrimaryKey = true, Length = 8)]
        public string DeptCode { get; set; }

        /// <summary>
        /// 部门名称
        /// </summary>        
        [SugarColumn(IsNullable = false, Length = 100)]
        public string DeptName { get; set; }

        /// <summary>
        /// 单位编码
        /// </summary>        
        [SugarColumn(IsNullable = false, Length = 6)]
        public string CompanyCode { get; set; }
    }
}