﻿using Peis.Model.Other.PeEnum;
using SqlSugar;

namespace Peis.Model.DataBaseEntity.External
{
    [SugarTable("ExtHisOrderIndex", "HIS订单索引表")]
    public class ExtHisOrderIndex
    {
        /// <summary>
        /// 体检号
        /// </summary>
        [SugarColumn(IsNullable = false, IsPrimaryKey = true, Length = 12)]
        public string RegNo { get; set; }

        /// <summary>
        /// 姓名
        /// </summary>
        [SugarColumn(IsNullable = false, Length = 50)]
        public string Name { get; set; }

        /// <summary>
        /// 档案卡号
        /// </summary>
        [SugarColumn(IsNullable = false, Length = 12)]
        public string PatCode { get; set; }

        /// <summary>
        /// 性别
        /// </summary>
        [SugarColumn(IsNullable = false)]
        public Sex Sex { get; set; }

        /// <summary>
        /// 年龄
        /// </summary>        
        [SugarColumn(IsNullable = false)]
        public int Age { get; set; }
        /// <summary>
        /// 生日
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public DateTime? Birthday { get; set; }
        /// <summary>
        /// 是否团检
        /// </summary>
        [SugarColumn(IsNullable = false)]
        public bool IsCompanyCheck { get; set; }

        /// <summary>
        /// 电话
        /// </summary>
        [SugarColumn(IsNullable = true, Length = 32)]
        public string Tel { get; set; }
        /// <summary>
        /// 01=居民身份证,06=香港居民身份证,07=台湾居民身份证,08=澳门居民身份证,99=其他法定有效证件
        /// </summary>
        [SugarColumn(IsNullable = true, Length = 2)]
        public string CardType { get; set; }

        /// <summary>
        /// 证件号
        /// </summary>
        [SugarColumn(IsNullable = true, Length = 18)]
        public string CardNo { get; set; }

        /// <summary>
        /// 单位代码
        /// </summary>
        [SugarColumn(IsNullable = true, Length = 6)]
        public string CompanyCode { get; set; }

        /// <summary>
        /// 体检分类
        /// </summary>        
        [SugarColumn(IsNullable = false)]
        public PeCls PeCls { get; set; }

        /// <summary>
        /// 预约类型
        /// </summary>        
        [SugarColumn(IsNullable = true)]
        public BookType BookType { get; set; }

        /// <summary>
        /// VIP标识
        /// </summary>        
        [SugarColumn(IsNullable = true)]
        public bool? IsVIP { get; set; }

        /// <summary>
        /// 操作员代码
        /// </summary>
        [SugarColumn(IsNullable = false, Length = 10)]
        public string OperatorCode { get; set; }

        /// <summary>
        /// 登记次数
        /// </summary>
        [SugarColumn(IsNullable = false)]
        public int RegisterTimes { get; set; }

        /// <summary>
        /// 登记时间
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public DateTime? RegisterTime { get; set; }

        [SugarColumn(IsNullable = true)]
        public MarryStatus? MarryStatus { get; set; }
        /// <summary>
        /// HIS卡号
        /// </summary>
        [SugarColumn(IsNullable = true, Length = 20)]
        public string HisCard { get; set; }

        /// <summary>
        /// 主数据卡号
        /// </summary>
        [SugarColumn(IsNullable = true, Length = 50)]
        public string EmpiId { get; set; }

        /// <summary>
        /// Lis通知标识
        /// </summary>
        [SugarColumn(IsNullable = false)]
        public bool IsLisNoticed { get; set; }

        /// <summary>
        /// Pacs通知标识
        /// </summary>
        [SugarColumn(IsNullable = false)]
        public bool IsPacsNoticed { get; set; }

        /// <summary>
        /// 建档通知标识
        /// </summary>
        [SugarColumn(IsNullable = false)]
        public bool IsEmrNoticed { get; set; }

        [SugarColumn]
        public PayStatus PayStatus { get; set; }
    }
}
