﻿using System;
using Peis.Model.DataBaseEntity;
using Peis.Repository.Cache.Providers;
using Peis.Utility.Cache;
using SqlSugar;

namespace Peis.Repository.Cache;

public class ProvidersInjector
{
    ISqlSugarClient _db;

    public ProvidersInjector(ISqlSugarClient db)
    {
        _db = db;
    }

    public void Inject(SimpleCache cache)
    {
        Random random = new Random();

        cache.AddConfig(new ProviderConfig(nameof(CodeDisease), new CodeDiseaseProvider(_db), 300));
    }
}
