﻿namespace Peis.External.Wx.Service.Model.Response
{
    public class ResponsResult
    {
        public ResponsResult()
        {
            Success = true;
            ReturnData = new();
            ReturnMsg = "";
            TotalPage = 0;
            TotalNumber = 0;
        }

        /// <summary>
        /// 返回状态代码0 成功,其他失败
        /// </summary>
        public bool Success { get; set; }

        /// <summary>
        /// 调用错误返回信息
        /// </summary>
        public string ReturnMsg { get; set; }

        /// <summary>
        /// 调用成功后返回信息
        /// </summary>
        public object ReturnData { get; set; }

        /// <summary>
        /// 多少条
        /// </summary>
        public int TotalNumber { get; set; }

        /// <summary>
        /// 多少页
        /// </summary>
        public int TotalPage { get; set; }
    }
}
