﻿using Microsoft.AspNetCore.Mvc;
using Peis.Model.DataBaseEntity;
using Peis.Model.DTO;
using Peis.Model.DTO.DoctChat;
using Peis.Model.Other.Input.DoctChat;
using Peis.Service.IService;

namespace Peis.API.Controllers.Business
{
    /// <summary>
    /// 医生会诊
    /// </summary>
    [Route("api/[controller]")]
    [ApiController]
    public class DoctorChatController : BaseApiController
    {
        private readonly IDoctorChatService _doctorChatService;
        public DoctorChatController(IDoctorChatService doctorChatService)
        {
            _doctorChatService = doctorChatService;
        }

        /// <summary>
        /// 发起会诊
        /// </summary>
        /// <param name="chat"></param>
        /// <returns></returns>
        [HttpPost("CreateDoctorChat")]
        public IActionResult CreateDoctorChat([FromBody] LaunchChat chat)
        {
            string msg = string.Empty;
            result.Success = _doctorChatService.CreateDoctorChat(chat, ref msg);
            result.ReturnMsg = msg;
            return Ok(result);
        }

        /// <summary>
        /// 发送医生信息
        /// </summary>
        /// <returns></returns>
        [HttpPost("SendDoctorMsg")]
        [ProducesResponseType(typeof(DoctorChatRecord), 200)]
        public IActionResult SendDoctorMsg([FromBody] ChatRecord record)
        {
            result.ReturnData = _doctorChatService.SendDoctorMsg(record);
            return Ok(result);
        }

        /// <summary>
        /// 会诊列表
        /// </summary>
        /// <param name="operCode">operCode:当前登录用户编码</param>
        /// <returns></returns>
        [HttpPost("ReadDoctorChat")]
        [ProducesResponseType(typeof(ConsultationData), 200)]
        public IActionResult ReadDoctorChat([FromQuery] string operCode)
        {
            result.ReturnData = _doctorChatService.ReadDoctorChat(operCode);
            return Ok(result);
        }

        /// <summary>
        /// 会诊回复列表(关于此会诊的所有记录)
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost("ReadDoctorChatReply")]
        [ProducesResponseType(typeof(DoctorChatReply[]), 200)]
        public IActionResult ReadDoctorChatReply([FromBody] DoctorChatInput input)
        {
            result.ReturnData = _doctorChatService.ReadDoctorChatReply(input);
            return Ok(result);
        }

        /// <summary>
        /// 更新消息状态为已读(医生会诊)
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost("UpdateChatMsgStauts")]
        public IActionResult UpdateChatMsgStauts([FromBody] DoctorChatInput input)
        {
            result.Success = _doctorChatService.UpdateChatMsgStauts(input);
            return Ok(result);
        }

        /// <summary>
        /// 读取最新的会诊
        /// </summary>
        /// <param name="chatId">会诊Id {"chatId":1}</param>
        /// <returns></returns>
        [HttpPost("ReadLatestChat")]
        [ProducesResponseType(typeof(ChatData), 200)]
        public IActionResult ReadLatestChat([FromQuery] long chatId)
        {
            result.ReturnData = _doctorChatService.ReadLatestChat(chatId);
            return Ok(result);
        }
    }
}
