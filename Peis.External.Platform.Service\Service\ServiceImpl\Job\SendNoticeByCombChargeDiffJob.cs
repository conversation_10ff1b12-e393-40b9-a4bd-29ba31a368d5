﻿using Peis.Quartz.UI.BaseService;
using Peis.External.Platform.Service.Service.IService.System;

namespace Peis.External.Platform.Service.Service.ServiceImpl.Job;

/// <summary>
/// 作业：发消息通知：收费项目与医嘱项目明细不一致通知
/// </summary>
public class SendNoticeByCombChargeDiffJob : IJobService
{
    readonly INoticeBusinessService _businessNoticeService;

    public SendNoticeByCombChargeDiffJob(INoticeBusinessService businessNoticeService)
    {
        _businessNoticeService = businessNoticeService;
    }

    public string ExecuteService(string parameter)
    {
        string msg = "执行成功";
        _businessNoticeService.SendNoticeByCombChargeDiff(ref msg);
        return msg;
    }
}
