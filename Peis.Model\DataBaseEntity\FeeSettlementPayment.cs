﻿using SqlSugar;
using System;

namespace Peis.Model.DataBaseEntity
{
    ///<summary>
    ///结算支付明细
    ///</summary>
    [SugarTable("FeeSettlementPayment")]
    public class FeeSettlementPayment
    {
        /// <summary>
        /// 自增Id
        /// </summary>        
        [SugarColumn(IsNullable = false, IsPrimaryKey = true, IsIdentity = true)]
        public int Id { get; set; }

        /// <summary>
        /// 结算号
        /// </summary>        
        [SugarColumn(IsNullable = false, Length = 10)]
        public string SettlementNo { get; set; }

        /// <summary>
        /// 结算支付类型（除微信、支付宝，其他和现金一样仅记录支付方式及金额）
        /// </summary>        
        [SugarColumn(IsNullable = false, Length = 10)]
        public string PaymentType { get; set; }

        /// <summary>
        /// 支付类型名称
        /// </summary>        
        [SugarColumn(IsNullable = false, Length = 40)]
        public string PaymentName { get; set; }

        /// <summary>
        /// 支付流水号
        /// </summary>        
        [SugarColumn(IsNullable = true, Length = 60)]
        public string TrasactionID { get; set; }

        /// <summary>
        /// 结算金额
        /// </summary>        
        [SugarColumn(IsNullable = false, Length = 12, DecimalDigits = 2)]
        public decimal Price { get; set; }

        /// <summary>
        /// 支付时间
        /// </summary>        
        [SugarColumn(IsNullable = false)]
        public DateTime PaymentTime { get; set; }
    }
}