﻿using Peis.Model.DataBaseEntity.External;
using Peis.Model.DTO.External.DataMaintenances;
using Peis.Service.IService.ExternalSystem;
using Peis.Utility.PeUser;

namespace Peis.External.Platform.Service.Service.ServiceImpl.MainData;

/// <summary>
/// 维护第三方系统的字典相关
/// </summary>
public class ExternalSystemDataService : IExternalSystemDataService
{
    #region 仓储       
    readonly IMapper _mapper;
    readonly IDataRepository<CodeItemComb> _codeItemCombRepository;
    readonly IDataRepository<CodeHisChargeItem> _codeHisChargeItemRepository;
    readonly IDataRepository<CodeHisDrugItem> _codeHisDrugItemRepository;
    readonly IDataRepository<MapCodeItemCombHisChargeItem> _mapCodeItemCombChargeRepository;
    readonly IDataRepository<CodeHisOrderItem> _codeHisOrderItemRepository;
    readonly IDataRepository<MapCodeLisItem> _mapCodeLisItemRepository;
    readonly IShenShanMainDataRepository _shenShanMainDataRepository;
    readonly IExternalSystemDataSyncService _externalSystemDataSyncService;
    readonly IHttpContextUser _httpContextUser;
    private readonly IDataRepository<MapCodeItemCombHisOrderItem> _mapCodeCombOrderRepository;
    #endregion

    #region 构造函数
    public ExternalSystemDataService(
        IMapper mapper,
        IDataRepository<CodeItemComb> codeItemCombRepository,
        IDataRepository<CodeHisChargeItem> codeHisChargItemRepository,
        IDataRepository<MapCodeItemCombHisChargeItem> mapCodeItemCombHisChargeItemRepository,
        IDataRepository<CodeHisOrderItem> codeHisOrderItemRepository,
        IDataRepository<MapCodeLisItem> mapCodeLisItemRepository,
        IShenShanMainDataRepository shenShanMainDataRepository,
        IExternalSystemDataSyncService externalSystemDataSyncService,
        IHttpContextUser httpContextUser,
        IDataRepository<CodeHisDrugItem> codeHisDrugItemRepository,
        IDataRepository<MapCodeItemCombHisOrderItem> mapCodeCombOrderRepository)
    {
        _mapper = mapper;
        _codeItemCombRepository = codeItemCombRepository;
        _codeHisChargeItemRepository = codeHisChargItemRepository;
        _mapCodeItemCombChargeRepository = mapCodeItemCombHisChargeItemRepository;
        _codeHisOrderItemRepository = codeHisOrderItemRepository;
        _mapCodeLisItemRepository = mapCodeLisItemRepository;
        _shenShanMainDataRepository = shenShanMainDataRepository;
        _externalSystemDataSyncService = externalSystemDataSyncService;
        _httpContextUser = httpContextUser;
        _codeHisDrugItemRepository = codeHisDrugItemRepository;
        _mapCodeCombOrderRepository = mapCodeCombOrderRepository;
    }
    #endregion

    #region 体检类代码

    #region 体检项目
    /// <summary>
    /// 获取体检项目对应Lis项目集合查询
    /// </summary>
    /// <param name="query">查询条件</param>
    /// <param name="totalNumber">总数出参</param>
    /// <param name="totalPage">总页码出参</param>
    /// <returns> CodeHisChargItemDTOs </returns>
    public List<CodeLisItemDto> GetMapCodeLisItems(CodeLisItemQuery query, ref int totalNumber, ref int totalPage)
    {
        query.NotNullAndEmpty(nameof(query));
        query.ItemCode.NotNullAndEmpty(AttributeHelper.GetDesc<CodeLisItemQuery>(nameof(query.ItemCode)));

        var queryable = _shenShanMainDataRepository.QueryMapCodeLisItems()
            .Where(a => a.ItemCode.Equals(query.ItemCode))
            .WhereIF(!query.Keyword.IsNullOrWhiteSpace(), a => a.LisItemCode.Contains(query.Keyword) || a.LisItemCNName.Contains(query.Keyword))
            .Select(a => a);

        var list = queryable.ToMyPageList(query.PageNumber, query.PageSize, ref totalNumber, ref totalPage);

        return _mapper.Map<List<CodeLisItemDto>>(list);
    }

    /// <summary>
    /// 获取Lis体检项目集合查询
    /// </summary>
    /// <param name="query">查询条件</param>
    /// <param name="totalNumber">总数出参</param>
    /// <param name="totalPage">总页码出参</param>
    /// <returns> CodeHisChargItemDTOs </returns>
    public List<CodeLisItemDto> GetCodeLisItems(CodeLisItemQuery query, ref int totalNumber, ref int totalPage)
    {
        query.NotNullAndEmpty(nameof(query));
        var queryable = _shenShanMainDataRepository.QueryViewCodeLisItems()
            .WhereIF(!query.Keyword.IsNullOrWhiteSpace(), a => a.LisItemCode.Contains(query.Keyword) || a.LisItemCNName.Contains(query.Keyword));

        var list = queryable.ToMyPageList(query.PageNumber, query.PageSize, ref totalNumber, ref totalPage);

        return _mapper.Map<List<CodeLisItemDto>>(list);
    }

    /// <summary>
    /// 保存体检项目对应信息
    /// </summary>
    /// <param name="codeLisItems">集合</param>
    /// <param name="msg">消息</param>
    /// <returns>bool</returns>
    public bool SaveMapCodeLisItems(List<CodeLisItemDto> codeLisItems, out string msg)
    {
        codeLisItems.NotNullAndEmpty(nameof(codeLisItems));
        var insertMapLisItems = new List<MapCodeLisItem>();
        foreach (var item in codeLisItems)
        {
            if (item.ItemCode.IsNullOrWhiteSpace()) continue;

            if (item.LisCombCode.IsNullOrWhiteSpace() || item.LisItemCode.IsNullOrWhiteSpace()) continue;

            var existMapCodeLisItem = _mapCodeLisItemRepository
                .Any(x => x.ItemCode.Equals(item.ItemCode)
                && x.LisCombCode.Equals(item.LisCombCode)
                && x.LisItemCode.Equals(item.LisItemCode));
            if (!existMapCodeLisItem)
            {
                var mapCodeLisItem = new MapCodeLisItem(item);
                insertMapLisItems.Add(mapCodeLisItem);
            }
        }

        if (!insertMapLisItems.IsNullOrEmpty())
            _mapCodeLisItemRepository.Insert(insertMapLisItems);

        msg = ResxCommon.Success;
        return true;
    }

    /// <summary>
    /// 移除体检项目对应
    /// </summary>
    /// <param name="codeLisItems">集合</param>
    /// <param name="msg">消息</param>
    /// <returns>bool</returns>
    public bool RemoveMapCodeLisItems(List<CodeLisItemDto> codeLisItems, out string msg)
    {
        codeLisItems.NotNullAndEmpty(nameof(codeLisItems));
        var batchDeleteMapCodeLisItemEntities = new List<MapCodeLisItem>();
        foreach (var item in codeLisItems)
        {
            if (item.LisCombCode.IsNullOrWhiteSpace()
                || item.LisItemCode.IsNullOrWhiteSpace()
                || item.ItemCode.IsNullOrWhiteSpace()) continue;

            var mapCodeLisItemOldEnt = _mapCodeLisItemRepository
                .First(x => x.ItemCode.Equals(item.ItemCode)
                && x.LisCombCode.Equals(item.LisCombCode)
                && x.LisItemCode.Equals(item.LisItemCode));
            if (!mapCodeLisItemOldEnt.IsNullOrEmpty())
                batchDeleteMapCodeLisItemEntities.Add(mapCodeLisItemOldEnt);
        }

        var flag = _mapCodeLisItemRepository.Delete(batchDeleteMapCodeLisItemEntities);
        msg = flag ? ResxCommon.Success : ResxCommon.Fail;
        return flag;
    }

    #endregion

    #region 体检组合信息 CodeItemComb

    #region 体检组合与His收费项目信息
    /// <summary>
    /// 获取His收费项目集合
    /// </summary>
    /// <param name="query">查询条件</param>
    /// <param name="totalNumber">总数出参</param>
    /// <param name="totalPage">总页码出参</param>
    /// <returns> CodeHisChargItemDTOs </returns>
    public List<CodeHisChargeItemDto> GetCodeHisChargeItems(CodeHisChargeItemQuery query, ref int totalNumber, ref int totalPage)
    {
        query.NotNullAndEmpty(nameof(query));
        var queryable = _shenShanMainDataRepository.QueryCodeHisChargeItems()
              .WhereIF(!string.IsNullOrWhiteSpace(query.ChargeItemCode), x => x.ChargeItemCode.Contains(query.ChargeItemCode))
              .WhereIF(!string.IsNullOrWhiteSpace(query.ChageItemCNName), x => x.ChageItemCNName.Contains(query.ChageItemCNName))
              .WhereIF(!string.IsNullOrWhiteSpace(query.Keyword), x => x.ChargeItemCode.Contains(query.Keyword) || x.ChageItemCNName.Contains(query.Keyword));

        var list = queryable.ToMyPageList(query.PageNumber, query.PageSize, ref totalNumber, ref totalPage);

        return _mapper.Map<List<CodeHisChargeItemDto>>(list);
    }

    /// <summary>
    /// 获取体检组合下对应的His收费项目信息
    /// </summary>
    /// <param name="query">查询条件</param>
    /// <param name="totalNumber">总数出参</param>
    /// <param name="totalPage">总页码出参</param>
    /// <returns>MapCodeItemCombHisChargeItemDtos</returns>
    public List<MapCodeItemCombHisChargeItemDto> GetMapCodeItemCombHisChargeItems(MapCodeItemCombHisChargeItemQuery query, ref int totalNumber, ref int totalPage)
    {
        query.NotNullAndEmpty(nameof(query));
        var queryable = _shenShanMainDataRepository.QueryCodeMapChargeItems(query.CombCode)
              .WhereIF(!string.IsNullOrWhiteSpace(query.HisChargeItemCode), (a, b) => a.HisChargeItemCode.Contains(query.HisChargeItemCode))
              .WhereIF(!string.IsNullOrWhiteSpace(query.HisChargeItemCNName), (a, b) => a.HisChargeItemCNName.Contains(query.HisChargeItemCNName))
              .WhereIF(!string.IsNullOrWhiteSpace(query.Keyword), (a, b) => a.HisChargeItemCode.Contains(query.Keyword) || a.HisChargeItemCNName.Contains(query.Keyword))
              .Select((a, b, c) =>
              new
              {
                  MapCodeCombHisCharge = a,
                  ChargeItem = b,
                  DrugItem = c,
              });

        var list = queryable.ToMyPageList(query.PageNumber, query.PageSize, ref totalNumber, ref totalPage);

        // 拼装DTO
        var orderCharges = _shenShanMainDataRepository.QueryCodeItemCombMapOrders(new List<string>() { query.CombCode })
            .Where((a, b, c) => !SqlFunc.IsNullOrEmpty(c.ChargeItemId))
            .Select((a, b, c) => c)
            .ToList();

        var mapCodeItemCombHisCharges = new List<MapCodeItemCombHisChargeItemDto>();
        foreach (var item in list)
        {
            var mapChargeItem = _mapper.Map<MapCodeItemCombHisChargeItemDto>(item.MapCodeCombHisCharge);
            mapChargeItem.CodeHisChargeItem =  _mapper.Map<CodeHisChargeItemDto>(item.ChargeItem);
            mapChargeItem.CodeHisDrugItem = _mapper.Map<CodeHisDrugItemDto>(item.DrugItem);

            if (item.MapCodeCombHisCharge.ItemType.Equals(EnumCombHisItemType.Exam))
            {
                // 已绑定医嘱项目明细
                var orderCharge = orderCharges.FirstOrDefault(x => x.ChargeItemId.Equals(mapChargeItem.CodeHisChargeItem.ChargeItemId));
                if (!orderCharge.IsNullOrEmpty())
                    mapChargeItem.CodeHisOrderItemCharge = _mapper.Map<CodeHisOrderItemChargeDto>(orderCharge);
            }

            mapCodeItemCombHisCharges.Add(mapChargeItem);
        }

        return mapCodeItemCombHisCharges;
    }

    /// <summary>
    /// 保存体检组合与His收费项目信息
    /// </summary>
    /// <param name="mapChargeItems">体检组合与His收费项目关系信息</param>
    /// <param name="msg">消息</param>
    /// <returns>Boolean</returns>
    public bool SaveMapCodeItemCombHisChargeItems(List<MapCodeItemCombHisChargeItemDto> mapChargeItems, out string msg)
    {
        if (mapChargeItems.IsNullOrEmpty(nameof(mapChargeItems), out msg)) return false;

        var batchInsertEntities = new List<MapCodeItemCombHisChargeItem>();
        var batchUpdateEntities = new List<MapCodeItemCombHisChargeItem>();
        foreach (var item in mapChargeItems.GroupBy(x => x.CombCode))
        {
            if (item.Key.IsNullOrEmpty()) continue;

            var codeItemComb = _codeItemCombRepository.First(x => x.CombCode.Equals(item.Key));

            if (codeItemComb.IsNullOrEmpty()) continue;

            // 已有的
            var existMapChargeItems = _mapCodeItemCombChargeRepository.FindAll(x => x.CombCode.Equals(item.Key));

            // 新增与更新内容
            AddRangSaveMapChargeItems(batchInsertEntities, batchUpdateEntities, item, codeItemComb, existMapChargeItems);

        }

        if (batchInsertEntities.IsNullOrEmpty() &&
            batchUpdateEntities.IsNullOrEmpty())
        {
            msg = ResxCommon.NotExistRecordForSave;
            return false;
        }

        // 新增
        if (!batchInsertEntities.IsNullOrEmpty())
            _mapCodeItemCombChargeRepository.Insert(batchInsertEntities);

        // 更新
        if (!batchUpdateEntities.IsNullOrEmpty())
            _mapCodeItemCombChargeRepository.Update(batchUpdateEntities);

        // 同步组合价
        var combCodes = batchInsertEntities
            .Select(x => x.CombCode).ToList();
        combCodes.AddRange(batchUpdateEntities
            .Select(x => x.CombCode).ToList());
        _externalSystemDataSyncService.SyncCodeItemCombPrice(combCodes, out msg);

        msg = ResxCommon.Success;
        return true;
    }

    private void AddRangSaveMapChargeItems(List<MapCodeItemCombHisChargeItem> batchInsertEntities, List<MapCodeItemCombHisChargeItem> batchUpdateEntities, IGrouping<string, MapCodeItemCombHisChargeItemDto> item, CodeItemComb codeItemComb, IEnumerable<MapCodeItemCombHisChargeItem> existMapChargeItems)
    {
        foreach (var mapChargeItem in item)
        {
            var mapChargeItemEntity = _mapper.Map<MapCodeItemCombHisChargeItem>(mapChargeItem);
            mapChargeItemEntity.SetCodeItemComb(codeItemComb);
            switch (mapChargeItem.ItemType)
            {
                case EnumCombHisItemType.Drug:
                    var drugItem = _codeHisDrugItemRepository.First(x => x.DrugCode.Equals(mapChargeItem.HisChargeItemCode));
                    if (drugItem.IsNullOrEmpty()) continue;

                    mapChargeItemEntity.SetDrugItem(drugItem);
                    break;

                default:
                    var chargeItem = _codeHisChargeItemRepository.First(x => x.ChargeItemCode.Equals(mapChargeItem.HisChargeItemCode));
                    if (chargeItem.IsNullOrEmpty()) continue;

                    mapChargeItemEntity.SetChargeItem(chargeItem);
                    break;
            }

            var exitMapChargeItem = existMapChargeItems.Any(x => x.HisChargeItemCode.Equals(mapChargeItem.HisChargeItemCode) && x.ItemType.Equals(mapChargeItem.ItemType));
            if (exitMapChargeItem)
            {
                // 更新
                mapChargeItemEntity.SetUpdate(_httpContextUser.UserId, _httpContextUser.UserName);
                batchUpdateEntities.Add(mapChargeItemEntity);
            }
            else
            {
                // 新增
                mapChargeItemEntity.SetCreate(_httpContextUser.UserId, _httpContextUser.UserName);
                batchInsertEntities.Add(mapChargeItemEntity);
            }
        }
    }

    /// <summary>
    /// 移除体检组合项目对应
    /// </summary>
    /// <param name="mapCodeItemCombs">集合</param>
    /// <param name="msg">消息</param>
    /// <returns>bool</returns>
    public bool RemoveCodeHisChargeItems(List<MapCodeItemCombHisChargeItemDto> mapCodeItemCombs, out string msg)
    {
        mapCodeItemCombs.NotNullAndEmpty(nameof(mapCodeItemCombs));
        var batchDeleteMapCodeChargeItemEntities = new List<MapCodeItemCombHisChargeItem>();
        foreach (var item in mapCodeItemCombs)
        {
            var mapCodeLisItemOldEnt = _mapCodeItemCombChargeRepository
                .First(x => x.CombCode.Equals(item.CombCode)
                && x.HisChargeItemId.Equals(item.HisChargeItemId)
                && x.HisChargeItemCode.Equals(item.HisChargeItemCode));
            if (!mapCodeLisItemOldEnt.IsNullOrEmpty())
                batchDeleteMapCodeChargeItemEntities.Add(mapCodeLisItemOldEnt);
        }

        if (batchDeleteMapCodeChargeItemEntities.IsNullOrEmpty())
        {
            msg = ResxCommon.NotExistRecordForSave;
            return false;
        }

        var flag = _mapCodeItemCombChargeRepository.Delete(batchDeleteMapCodeChargeItemEntities);

        // 同步组合价
        var combCodes = batchDeleteMapCodeChargeItemEntities
            .Select(x => x.CombCode).ToList();
        _externalSystemDataSyncService.SyncCodeItemCombPrice(combCodes, out msg);

        msg = flag ? ResxCommon.Success : ResxCommon.Fail;
        return flag;
    }
    #endregion

    #region 体检组合与His医嘱项目信息

    /// <summary>
    /// 获取His医嘱项目集合
    /// </summary>
    /// <param name="query">查询条件</param>
    /// <param name="totalNumber">总数出参</param>
    /// <param name="totalPage">总页码出参</param>
    /// <returns> CodeHisChargItemDTOs </returns>
    public List<CodeHisOrderItemDto> GetCodeHisOrderItems(CodeHisOrderItemQuery query, ref int totalNumber, ref int totalPage)
    {
        query.NotNullAndEmpty(nameof(query));
        var queryable = _shenShanMainDataRepository.QueryCodeHisOrderItems()
             .WhereIF(!string.IsNullOrWhiteSpace(query.OrderItemCode), x => x.OrderItemCode.Equals(query.OrderItemCode))
             .WhereIF(!string.IsNullOrWhiteSpace(query.OrderItemCNName), x => x.OrderItemCNName.Equals(query.OrderItemCNName))
              .WhereIF(!string.IsNullOrWhiteSpace(query.Keyword), x => x.OrderItemCode.Contains(query.Keyword) || x.OrderItemCNName.Contains(query.Keyword));

        var list = queryable.ToMyPageList(query.PageNumber, query.PageSize, ref totalNumber, ref totalPage);

        var resList = _mapper.Map<List<CodeHisOrderItemDto>>(list);
        foreach (var item in resList)
        {
            var codeHisOrderItemCharges = _shenShanMainDataRepository.QueryCodeHisOrderItemCharges(item.PId).ToList();
            item.CodeHisOrderItemCharges = _mapper.Map<List<CodeHisOrderItemChargeDto>>(codeHisOrderItemCharges);
        }

        return resList;
    }

    #endregion

    #region 药品信息

    /// <summary>
    /// 获取His药品信息集合
    /// </summary>
    /// <param name="query">查询条件</param>
    /// <param name="totalNumber">总数出参</param>
    /// <param name="totalPage">总页码出参</param>
    /// <returns> CodeHisChargItemDTOs </returns>
    public List<CodeHisDrugItem> GetCodeHisDrugItems(CodeHisDrugItemQuery query, ref int totalNumber, ref int totalPage)
    {
        query.NotNullAndEmpty(nameof(query));
        var queryable = _shenShanMainDataRepository.QueryCodeHisDrugItems()
             .WhereIF(!string.IsNullOrWhiteSpace(query.DrugCode), x => x.DrugCode.Equals(query.DrugCode))
             .WhereIF(!string.IsNullOrWhiteSpace(query.DrugCNName), x => x.DrugCNName.Equals(query.DrugCNName))
              .WhereIF(!string.IsNullOrWhiteSpace(query.Keyword), x => x.DrugCode.Contains(query.Keyword) || x.DrugCNName.Contains(query.Keyword));

        return queryable.ToMyPageList(query.PageNumber, query.PageSize, ref totalNumber, ref totalPage);
    }

    #endregion

    /// <summary>
    /// 获取组合对应项目类型
    /// </summary>
    /// <returns></returns>
    public List<object> GetCodeHisItemTypes()
    {
        var list = new List<object>();
        foreach (EnumCombHisItemType item in Enum.GetValues(typeof(EnumCombHisItemType)))
        {
            string description = EnumHelper.GetDescription(item);
            list.Add(new
            {
                Type = (int)item,
                TypeName = description
            });
        }

        return list;
    }

    /// <summary>
    /// 保存体检组合关联医嘱信息
    /// </summary>
    /// <param name="codeItemComb">组合信息</param>
    /// <returns>MapCodeItemCombHisOrderItem</returns>
    public MapCodeItemCombHisOrderItem saveMapCombHisOrderItem(CodeItemComb codeItemComb)
    {
        if (codeItemComb.HisOrderCode.IsNullOrEmpty())
        {
            if ((codeItemComb.CheckCls == Model.Other.PeEnum.CheckCls.功能检查 || codeItemComb.CheckCls == Model.Other.PeEnum.CheckCls.检验检查) && codeItemComb.ReportShow)
            {
                throw new BusinessException("检验检查/功能检查项目必须维护医嘱项目代码！");
            }
            else
            {
                return null;
            }
        }

        MapCodeItemCombHisOrderItem mapCodeCombOrder = null;
        if (!codeItemComb.HisOrderCode.IsNullOrEmpty())
        {
            var codeHisOrder = _codeHisOrderItemRepository
                .First(x => x.OrderItemCode.Equals(codeItemComb.HisOrderCode));
            if (codeHisOrder.IsNullOrEmpty())
                throw new BusinessException("不存在此医嘱项目信息！");

            mapCodeCombOrder = new MapCodeItemCombHisOrderItem(codeItemComb, codeHisOrder);
        }

        removeMapCombHisOrderItem(new[] { codeItemComb.CombCode });
        if (!mapCodeCombOrder.IsNullOrEmpty())
            _mapCodeCombOrderRepository.Insert(mapCodeCombOrder);

        return mapCodeCombOrder;
    }

    /// <summary>
    /// 删除体检组合关联医嘱信息
    /// </summary>
    /// <param name="combCodes">组合代码</param>
    /// <returns>bool</returns>
    public bool removeMapCombHisOrderItem(string[] combCodes)
    {
        if (combCodes.IsNullOrEmpty()) return false;

        foreach (var combCode in combCodes)
            _mapCodeCombOrderRepository.Delete(x => x.CombCode.Equals(combCode));

        return true;
    }
    #endregion

    #endregion
}

