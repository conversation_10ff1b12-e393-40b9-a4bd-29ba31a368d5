﻿using AutoMapper;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Peis.Model;
using Peis.Model.DTO.External.DataMaintenances;
using Peis.Repository.Repository.TransactionAttribute;
using Peis.Service.IService.ExternalSystem;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace Peis.API.Controllers.DataMaintenance
{
    /// <summary>
    /// 基础代码维护扩展
    /// </summary>
    [Route("api/[controller]/[action]")]
    [ApiController]
    public class ExtenseBasicCodeController : BaseApiController
    {
        readonly IExternalSystemDataService _externalSystemDataService;
        readonly IExternalSystemDataSyncService _externalSystemDataSyncService;
        readonly IMapper _mapper;

        public ExtenseBasicCodeController(
            IExternalSystemDataService externalSystemDataService,
            IExternalSystemDataSyncService externalSystemDataSyncService,
            IMapper mapper)
        {
            _externalSystemDataService = externalSystemDataService;
            _externalSystemDataSyncService = externalSystemDataSyncService;
            _mapper = mapper;
        }

        #region 体检类代码

        #region 体检项目CodeItem
        /// <summary>
        /// 获取体检项目对应Lis项目集合查询
        /// </summary>
        /// <param name="query">查询模型</param>
        /// <returns></returns>
        [HttpPost]
        [ProducesResponseType(typeof(List<CodeLisItemDto>), StatusCodes.Status200OK)]
        public IActionResult GetMapCodeLisItems(CodeLisItemQuery query)
        {
            int totalNumber = 0;
            int totalPage = 0;
            result.ReturnData = _externalSystemDataService.GetMapCodeLisItems(query, ref totalNumber, ref totalPage);
            result.TotalNumber = totalNumber;
            result.TotalPage = totalPage;

            return Ok(result);
        }

        /// <summary>
        /// 获取Lis体检项目集合查询
        /// </summary>
        /// <param name="query">查询模型</param>
        /// <returns></returns>
        [HttpPost]
        [ProducesResponseType(typeof(List<CodeLisItemDto>), StatusCodes.Status200OK)]
        public IActionResult GetCodeLisItems(CodeLisItemQuery query)
        {
            int totalNumber = 0;
            int totalPage = 0;
            result.ReturnData = _externalSystemDataService.GetCodeLisItems(query, ref totalNumber, ref totalPage);
            result.TotalNumber = totalNumber;
            result.TotalPage = totalPage;

            return Ok(result);
        }

        /// <summary>
        /// 保存体检项目对应信息Lis项目信息
        /// </summary>
        /// <param name="codeLisItems">体检项目对应Lis项目信息集合</param>
        /// <returns></returns>
        [HttpPost]
        [UnitOfWork]
        public IActionResult SaveMapCodeLisItems(List<CodeLisItemDto> codeLisItems)
        {
            string msg;
            result.Success = _externalSystemDataService.SaveMapCodeLisItems(codeLisItems, out msg);
            result.ReturnMsg = msg;

            return Ok(result);
        }

        /// <summary>
        /// 移除体检项目对应Lis项目信息
        /// </summary>
        /// <param name="codeLisItems">体检项目对应Lis项目信息集合</param>
        /// <returns></returns>
        [HttpPost]
        public IActionResult RemoveMapCodeLisItems(List<CodeLisItemDto> codeLisItems)
        {
            string msg;
            result.Success = _externalSystemDataService.RemoveMapCodeLisItems(codeLisItems, out msg);
            result.ReturnMsg = msg;

            return Ok(result);
        }

        #endregion

        #region 体检组合信息 CodeItemComb
        /// <summary>
        /// 获取His收费项目集合
        /// </summary>
        /// <param name="query">查询模型</param>
        /// <returns></returns>
        [HttpPost]
        [ProducesResponseType(typeof(List<CodeHisChargeItemDto>), StatusCodes.Status200OK)]
        public IActionResult GetCodeHisChargeItems(CodeHisChargeItemQuery query)
        {
            int totalNumber = 0;
            int totalPage = 0;
            result.ReturnData = _externalSystemDataService.GetCodeHisChargeItems(query, ref totalNumber, ref totalPage);
            result.TotalNumber = totalNumber;
            result.TotalPage = totalPage;

            return Ok(result);
        }

        /// <summary>
        /// 获取体检组合下对应的His收费项目信息
        /// </summary>
        /// <param name="query">查询模型</param>
        /// <returns></returns>
        [HttpPost]
        [ProducesResponseType(typeof(List<MapCodeItemCombHisChargeItemDto>), StatusCodes.Status200OK)]
        public IActionResult GetMapCodeItemCombHisChargeItems(MapCodeItemCombHisChargeItemQuery query)
        {
            int totalNumber = 0;
            int totalPage = 0;
            result.ReturnData = _externalSystemDataService.GetMapCodeItemCombHisChargeItems(query, ref totalNumber, ref totalPage);
            result.TotalNumber = totalNumber;
            result.TotalPage = totalPage;

            return Ok(result);
        }

        /// <summary>
        /// 保存体检组合与His收费项目信息
        /// </summary>
        /// <param name="mapChargeItems">体检组合与His收费项目关系信息</param>
        /// <returns></returns>
        [HttpPost]
        [UnitOfWork]
        public IActionResult SaveMapCodeItemCombHisChargeItems(List<MapCodeItemCombHisChargeItemDto> mapChargeItems)
        {
            string msg;
            result.Success = _externalSystemDataService.SaveMapCodeItemCombHisChargeItems(mapChargeItems, out msg);
            result.ReturnMsg = msg;

            return Ok(result);
        }

        /// <summary>
        /// 删除体检组合与His收费项目信息
        /// </summary>
        /// <param name="mapChargeItems">体检组合与His收费项目关系信息</param>
        /// <returns></returns>
        [HttpPost]
        [UnitOfWork]
        public IActionResult RemoveCodeHisChargeItems(List<MapCodeItemCombHisChargeItemDto> mapChargeItems)
        {
            string msg;
            result.Success = _externalSystemDataService.RemoveCodeHisChargeItems(mapChargeItems, out msg);
            result.ReturnMsg = msg;

            return Ok(result);
        }

        /// <summary>
        /// 同步更新体检组合价格
        /// </summary>
        /// <param name="combCodes">组合代码，为空时，更新全部</param>
        /// <returns></returns>
        [HttpPost]
        [UnitOfWork]
        public IActionResult SyncCodeItemCombPrice(List<string> combCodes)
        {
            string msg;
            result.Success = _externalSystemDataSyncService.SyncCodeItemCombPrice(combCodes, out msg);
            result.ReturnMsg = msg;

            return Ok(result);
        }

        /// <summary>
        /// 同步His系统的收费项目至本库（按分页循环同步）
        /// </summary>
        /// <param name="pageSize">每页大小值只能从整数1~100内填写！</param>
        /// <returns></returns>
        [HttpPost]
        [UnitOfWork]
        public IActionResult SyncCodeHisChargeItems([Range(1, 100)] int pageSize)
        {
            string msg;
            result.Success = _externalSystemDataSyncService.SyncCodeHisChargeItems(pageSize, out msg);
            result.ReturnMsg = msg;

            return Ok(result);
        }

        /// <summary>
        /// 同步His医嘱项目至本库（按分页循环同步）
        /// </summary>
        /// <param name="pageSize">每页大小值只能从整数1~100内填写！</param>
        /// <returns></returns>
        [HttpPost]
        [UnitOfWork]
        public IActionResult SyncCodeHisOrderItems([Range(1, 100)] int pageSize)
        {
            string msg;
            result.Success = _externalSystemDataSyncService.SyncCodeHisOrderItems(pageSize, out msg);
            result.ReturnMsg = msg;

            return Ok(result);
        }

        /// <summary>
        /// 获取His医嘱项目集合
        /// </summary>
        /// <param name="query">查询模型</param>
        /// <returns></returns>
        [HttpPost]
        [ProducesResponseType(typeof(List<CodeHisOrderItemDto>), StatusCodes.Status200OK)]
        public IActionResult GetCodeHisOrderItems(CodeHisOrderItemQuery query)
        {
            int totalNumber = 0;
            int totalPage = 0;
            result.ReturnData = _externalSystemDataService.GetCodeHisOrderItems(query, ref totalNumber, ref totalPage);
            result.TotalNumber = totalNumber;
            result.TotalPage = totalPage;

            return Ok(result);
        }

        /// <summary>
        /// 获取His药品集合
        /// </summary>
        /// <param name="query">查询模型</param>
        /// <returns></returns>
        [HttpPost]
        [ProducesResponseType(typeof(List<CodeHisDrugItemDto>), StatusCodes.Status200OK)]
        public IActionResult GetCodeHisDrugItems(CodeHisDrugItemQuery query)
        {
            int totalNumber = 0;
            int totalPage = 0;
            var data = _externalSystemDataService.GetCodeHisDrugItems(query, ref totalNumber, ref totalPage);
            result.ReturnData = _mapper.Map<List<CodeHisDrugItemDto>>(data);
            result.TotalNumber = totalNumber;
            result.TotalPage = totalPage;

            return Ok(result);
        }

        /// <summary>
        /// 获取组合项目对应类型
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public IActionResult GetCodeHisItemTypes()
        {
            result.Success = true;
            result.ReturnData = _externalSystemDataService.GetCodeHisItemTypes();
            result.ReturnMsg = ResxCommon.Success;

            return Ok(result);
        }
        #endregion

        #endregion
    }
}
