﻿using SqlSugar;

namespace Peis.Model.DataBaseEntity
{
    ///<summary>
    ///报表模板表
    ///</summary>
    [SugarTable("CodeReportTemplate")]
    public class CodeReportTemplate
    {
        /// <summary>
        /// 报表代码
        /// </summary>        
        [SugarColumn(IsNullable = false, IsPrimaryKey = true, Length = 32)]
        public string ReportCode { get; set; }

        /// <summary>
        /// 报表名称
        /// </summary>        
        [SugarColumn(IsNullable = false, Length = 100)]
        public string ReportName { get; set; }

        /// <summary>
        /// 报表类型代码
        /// </summary>        
        [SugarColumn(IsNullable = false, Length = 20)]
        public string TypeCode { get; set; }

        /// <summary>
        /// 报表类型名称
        /// </summary>        
        [SugarColumn(IsNullable = false, Length = 100)]
        public string TypeName { get; set; }
    }
}