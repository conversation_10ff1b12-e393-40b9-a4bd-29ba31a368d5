using Peis.Model.DataBaseEntity;
using Peis.Model.Other.PeEnum;
using SqlSugar;
using System.Collections.Generic;

namespace Peis.Repository.IRepository
{
    /// <summary>
    /// 疾病数据访问接口
    /// </summary>
    public interface IDiseaseRepository
    {
        /// <summary>
        /// 获取项目结果绑定的疾病信息
        /// </summary>
        /// <param name="itemCodes">项目代码数组</param>
        /// <returns>项目结果疾病信息</returns>
        List<ItemResultDiseaseInfo> GetItemResultDiseases(string[] itemCodes);

        /// <summary>
        /// 获取疾病审核条件项目信息
        /// </summary>
        /// <param name="itemCodes">项目代码数组</param>
        /// <returns>疾病审核条件项目信息</returns>
        List<DiseaseCriteriaItemDto> GetDiseaseCriteriaItems(string[] itemCodes);

        /// <summary>
        /// 获取疾病审核条件逻辑信息
        /// </summary>
        /// <param name="diseaseCodes">疾病代码数组</param>
        /// <returns>疾病代码和逻辑关系的字典</returns>
        Dictionary<string, int> GetDiseaseCriteriaLogics(string[] diseaseCodes);

        /// <summary>
        /// 获取疾病表达式代码
        /// </summary>
        /// <param name="itemCodes">项目代码数组</param>
        /// <returns>表达式代码数组</returns>
        string[] GetDiseaseExpressionCodes(string[] itemCodes);

        /// <summary>
        /// 获取疾病表达式详细信息
        /// </summary>
        /// <param name="expCodes">表达式代码数组</param>
        /// <returns>疾病表达式信息</returns>
        List<DiseaseExpressionInfo> GetDiseaseExpressions(string[] expCodes);

        /// <summary>
        /// 根据疾病代码获取疾病基本信息
        /// </summary>
        /// <param name="diseaseCodes">疾病代码数组</param>
        /// <returns>疾病基本信息</returns>
        List<DiseaseBasicInfo> GetDiseaseBasicInfo(string[] diseaseCodes);

        /// <summary>
        /// 获取疾病包含关系
        /// </summary>
        /// <param name="parentDiseaseCodes">父疾病代码数组</param>
        /// <returns>疾病包含关系</returns>
        List<MapDiseaseDisease> GetDiseaseRelations(string[] parentDiseaseCodes);

        /// <summary>
        /// 获取所有疾病词条
        /// </summary>
        /// <returns>疾病词条列表</returns>
        List<CodeDiseaseEntry> GetAllDiseaseEntries();
    }

    /// <summary>
    /// 项目结果疾病信息
    /// </summary>
    public class ItemResultDiseaseInfo
    {
        public string ItemCode { get; set; }
        public string ResultDesc { get; set; }
        public string DiseaseCode { get; set; }
        public string DiseaseName { get; set; }
        public AbnormalType AbnormalType { get; set; }
    }

    /// <summary>
    /// 疾病审核条件项目信息
    /// </summary>
    public class DiseaseCriteriaItemDto
    {
        public string DiseaseCode { get; set; }
        public string DiseaseName { get; set; }
        public string ItemCode { get; set; }
        public int Operator { get; set; }
        public string Value { get; set; }
        public int ValueType { get; set; }
    }

    /// <summary>
    /// 疾病表达式信息
    /// </summary>
    public class DiseaseExpressionInfo
    {
        public string ExpCode { get; set; }
        public string ExpText { get; set; }
        public string DiseaseCode { get; set; }
        public string ItemCode { get; set; }
        public ValueType ValueType { get; set; }
    }

    /// <summary>
    /// 疾病基本信息
    /// </summary>
    public class DiseaseBasicInfo
    {
        public string DiseaseCode { get; set; }
        public string DiseaseName { get; set; }
    }
}
